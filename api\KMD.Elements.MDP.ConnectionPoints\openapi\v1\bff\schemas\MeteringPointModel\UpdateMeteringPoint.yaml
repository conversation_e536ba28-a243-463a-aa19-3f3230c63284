type: object
description: |
  Extended metering point version.
additionalProperties: false
required:
  - occurrence
  - meteringPointDomainLocationId
  - meteringPointVersionId
properties:
  meteringPointDomainLocationId:
    type: string
    nullable: false
    description: Value of domain location identifier
    maxLength: 18
    minLength: 1
    pattern: ""
  meteringPointVersionId:
    description: Id of Metering Point Version. Metering Point Version Id contains of {GSRN}_{DATE}. so <MeteringPointId>_<Occurrence>
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    example: 317575025980776095_2022-01-02
  occurrence:
    $ref: '../DataTypes/DateTimeIso8601.yaml'
  meterReadingOccurrence:
    nullable: true
    description: Mapped from DH 'meterReadingOccurrence' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  locationDescription:
    nullable: true
    description: Mapped from DH 'locationDescription' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/LongString.yaml'
  typeOfMeteringPoint:
    description: TypeOfMeteringPoint field from Metering Point (non-versioned) object.
    allOf:
      - $ref: '../MeteringPointModel/DataTypes/TypeOfMeteringPoint.yaml'
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
  maximumCurrentInAmperes:
    pattern: "^\\d+$"
    nullable: true
    type: string
    description: Is mapped to `MaximumCurrent`
    maxLength: 6
  maximumPowerInKiloWatts:
    pattern: "^(\\d*(?:[.]\\d+)?)?$"
    nullable: true
    type: string
    description: Is mapped to `MaximumPower`
    maxLength: 128
