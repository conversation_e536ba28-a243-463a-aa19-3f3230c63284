type: object
description: Model containing electricity attributes of Meter Frame Default Value Set.
properties:
  connectionType:
    description: 'DK: Tilslutningstype.'
    allOf:
      - $ref: '../../DataTypes/Guid.yaml'
    nullable: true
  breakerBeforeMeter:
    description: 'DK: AfbryderFørMåler.'
    allOf:
      - $ref: '../../DataTypes/Boolean.yaml'
    nullable: true
  lossFactor:
    type: number
    description: |-
      DK: NettabsFaktor.
      Nettabsfaktor som bruges på målerrammen i MDM. Vi skal lige gennemgå den fra MDM siden. Skal indgå både på fuldttidserie niveau så faktoren ganges på både 15/60 forbrug samt
      tællerstande.
      Mains loss factor used on the measuring frame in MDM. We just need to review it from the MDM page. Must be included both at full-time series level so the factor is multiplied by
      both 15/60 consumption and meter readings.
    format: decimal
    nullable: true
  meterFrameFuse:
    description: |-
      DK: MålerRammeForsikring.
      Forsikring (tarifsikring, T-ret sikring etc) som ikke er stikledningssikringen eller tilslutningsrettigheden. Kunden kan selv bestemme størrelsen på denne.
      Hvis kunden ikke har oplyst en forsikring, så er stikledningssikringen kundens forsikring.
      Kunden kan selv sikrer op eller ned (stikledningssikringen begrænser ham selvfølgelig).
      Vi har den kun i systemet for at kunne rumme data fra blanketten.
      EN:
      Insurance (tariff fuse, T-right fuse etc) which is not the branch line fuse or the connection right. The customer can decide the size of this.
      If the customer has not stated an insurance, then the branch line insurance is the customer's insurance.
      The customer can secure up or down himself (the branch line protection limits him of course).
      We only have it in the system to be able to hold data from the form.
    allOf:
      - $ref: '../../DataTypes/IntegerNullable.yaml'
  meterDisplaySettings:
    description: |-
      DK: VisningMåler.
      En bruger skal kunne stille krav til visning af alle målerer som installeres i målerrammen.
      Skal medfører målerrammekrav på målerammen som gennemtvinger specifik display visning.
      EN:
      A user must be able to set requirements for displaying all meters that are installed in the meter frame.
      Must entail meter frame requirements on the meter frame which enforces specific display.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  connectionRemark:
    description: |-
      DK: TilslutningsBemærkning.
      Specielle forhold omkring tilslutning, f.eks. adapter, kvadrat, klemmetype etc.
      EN:
      Special conditions regarding connection, e.g. adapter, square, terminal type etc.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  purpose:
    description: 'DK: Formål.'
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  ratioCt:
    description: 'DK: OmsætningsforholdCT.'
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  ratioVt:
    description: 'DK: OmsætningsforholdVT.'
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  tarifConnectionPoint:
    description: 'DK: TarifTilslutningspunkt.'
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
