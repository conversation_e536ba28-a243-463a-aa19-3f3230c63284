openapi: 3.0.3
info:
  title: KMD.Elements.MeterManagement.Bfc
  x-maintainers: Team-MO-2
  description: Meter management api  bfc service.
  termsOfService: "https://www.kmd.net/terms-of-use"
  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>
  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"
  version: "1.76-preview"
servers:
  - url: https://localhost:5050/bfc
security:
  - Jwt: []
tags:
  - name: ElectricityMeters
    description: |-
      API supporting electricity meters management functionality.
  - name: WaterMeters
    description: |-
      API supporting water meters management functionality.
  - name: HeatMeters
    description: |-
      API supporting heat meters management functionality.
  - name: MeterConfigurations
    description: |-
      Api supporting meter configurations management functionality.
  - name: MeterInputs
    description: |-
      API supporting meter inputs management functionality.
paths:
  /v1/electricity-meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All
      tags:
        - ElectricityMeters
      summary: Gets the electricity meter data by meter number and meter type id.
      description: Endpoint serving functionality of getting electricity meter by meter number and meter type id.
      operationId: getElectricityMeterAllData
      parameters:
        - $ref: "#/components/parameters/MeterNumber"
        - $ref: "#/components/parameters/MeterTypeId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Details of electricity meter successfully returned.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ElectricityMeterDetails"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v1/water-meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Water.All
      tags:
        - WaterMeters
      summary: Gets the water meter data by meter number and meter type id.
      description: Endpoint serving functionality of getting water meter by meter number and meter type id.
      operationId: getWaterMeterAllData
      parameters:
        - $ref: "#/components/parameters/MeterNumber"
        - $ref: "#/components/parameters/MeterTypeId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Details of water meter successfully returned.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WaterMeterDetails"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v1/heat-meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Heating.All
      tags:
        - HeatMeters
      summary: Gets the Heat meter data by meter number and meter type id.
      description: Endpoint serving functionality of getting Heat meter by meter number and meter type id.
      operationId: getHeatMeterAllData
      parameters:
        - $ref: "#/components/parameters/MeterNumber"
        - $ref: "#/components/parameters/MeterTypeId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Details of heat meter successfully returned.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HeatMeterDetails"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v1/meter-configurations/{meterConfigurationId}:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterConfigurations
      summary: Get meter configuration base data.
      description: Get meter configuration base data.
      operationId: getMeterConfigurationBaseData
      parameters:
        - $ref: "#/components/parameters/MeterConfigurationId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Returns meter configuration base data.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterConfigurationBase"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v1/meter-configurations/{meterConfigurationId}/meter-inputs:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterConfigurations
      summary: Returns list of all meter inputs.
      description: Returns list of all meter inputs related with meter configuration.
      operationId: getMeterInputsForMeterConfiguration
      parameters:
        - $ref: "#/components/parameters/MeterConfigurationId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: List of all meter inputs related with meter configuration.
          content:
            application/json:
              schema:
                type: array
                description: Return collection of meter inputs
                items:
                    $ref: "#/components/schemas/MeterInputBase"
                minItems: 0
                maxItems: 100
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v1/meter-inputs/{meterInputId}/meter-registers:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterInputs
      summary: Returns list of all meter registers.
      description: Returns list of all meter registers related with meter input.
      operationId: getMeterRegistersByMeterInputId
      parameters:
        - $ref: "#/components/parameters/MeterInputId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: List of all meter registers related with meter input.
          content:
            application/json:
              schema:
                description: Return collection of meter registers
                type: array
                items:
                    $ref: "#/components/schemas/MeterRegister"
                minItems: 0
                maxItems: 100
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
components:
  parameters:
    "MeterNumber":
      $ref: "./parameters/MeterNumber.yaml"
    "MeterTypeId":
      $ref: "./parameters/MeterTypeId.yaml"
    "MeterConfigurationId":
      $ref: "./parameters/MeterConfigurationId.yaml"
    "MeterInputId":
      $ref: "./parameters/MeterInputId.yaml"
    "EsMessageIdInHeader":
      $ref: "./parameters/EsMessageIdInHeader.yaml"
    "EsCorrelationIdInHeader":
      $ref: "./parameters/EsCorrelationIdInHeader.yaml"
  responses:
    "400":
      $ref: "./responses/400.yaml"
    "401":
      $ref: "./responses/401.yaml"
    "403":
      $ref: "./responses/403.yaml"
    "404":
      $ref: "./responses/404.yaml"
    "422":
      $ref: "./responses/422.yaml"
    "429":
      $ref: "./responses/429.yaml"
    "499":
      $ref: "./responses/499.yaml"
    "500":
      $ref: "./responses/500.yaml"
    "503":
      $ref: "./responses/503.yaml"
    "504":
      $ref: "./responses/504.yaml"
  schemas:
    Guid:
      $ref: ./schemas/DataTypes/Guid.yaml
    OneWordString:
      $ref: ./schemas/DataTypes/OneWordString.yaml
    ShortString:
      $ref: ./schemas/DataTypes/ShortString.yaml
    DescriptionString:
      $ref: ./schemas/DataTypes/DescriptionString.yaml
    Date:
      $ref: ./schemas/DataTypes/Date.yaml
    DateTime:
      $ref: ./schemas/DataTypes/DateTime.yaml
    ProblemDetails:
      $ref: ./schemas/ProblemDetails.yaml
    ValidationProblemDetails:
      $ref: ./schemas/ValidationProblemDetails.yaml
    SupplyType:
      $ref: "./schemas/SupplyType.yaml"
    MeterControlTypeCode:
      $ref: "./schemas/MeterControlTypeCode.yaml"
    Meter:
      $ref: "./schemas/Meter.yaml"
    ElectricityMeterProperties:
      $ref: "./schemas/ElectricityMeterProperties.yaml"
    ElectricityMeter:
      $ref: "./schemas/ElectricityMeter.yaml"
    MeterInputType:
      $ref: "./schemas/MeterInputType.yaml"
    MeterReadingType:
      $ref: "./schemas/MeterReadingType.yaml"
    MeasuringUnit:
      $ref: "./schemas/MeasuringUnit.yaml"
    MeterRegister:
      $ref: "./schemas/MeterRegister.yaml"
    MeterInput:
      $ref: "./schemas/MeterInput.yaml"
    MeterConfiguration:
      $ref: "./schemas/MeterConfiguration.yaml"
    MeterCommandTypeCode:
      $ref: "./schemas/MeterCommandTypeCode.yaml"
    MeterCommandSubTypeCode:
      $ref: "./schemas/MeterCommandSubTypeCode.yaml"
    MeterCommandDetails:
      $ref: "./schemas/MeterCommandDetails.yaml"
    MeterComponent:
      $ref: "./schemas/MeterComponent.yaml"
    WaterMeter:
      $ref: "./schemas/WaterMeter.yaml"
    WaterMeterProperties:
      $ref: "./schemas/WaterMeterProperties.yaml"
    HeatMeter:
      $ref: "./schemas/HeatMeter.yaml"
    HeatMeterProperties:
      $ref: "./schemas/HeatMeterProperties.yaml"
    ElectricityMeterDetails:
      $ref: "./schemas/ElectricityMeterDetails.yaml"
    WaterMeterDetails:
      $ref: "./schemas/WaterMeterDetails.yaml"
    HeatMeterDetails:
      $ref: "./schemas/HeatMeterDetails.yaml"
    MeterConfigurationBase:
      $ref: "./schemas/MeterConfigurationBase.yaml"
    MeterInputBase:
      $ref: "./schemas/MeterInputBase.yaml"
    MeterApprovedAfter:
      $ref: "./schemas/MeterApprovedAfter.yaml"
    StartTimeForMeterBatch:
      $ref: "./schemas/StartTimeForMeterBatch.yaml"
  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
