type: object
nullable: false
description: |
  Formula for metering point.
additionalProperties: false
required:
  - isSimpleFormula
properties:
  name:
    description: The formula short name.
    allOf:
      - $ref: "../DataTypes/ShortStringNullable.yaml"
  isSimpleFormula:
    description: |
      Indicates whether a formula is calculated on the basis of a formula expression or calculated on the basis
      of a meter reading on a specific meter frame register requirement (simple formula).
      When it is a "simple formula" the formula will not have a reference to formulaType.
    allOf:
      - $ref: "../DataTypes/Boolean.yaml"
  formula:
    description: |
      The formula that describes how data for the measuring point's time series is calculated using data
      from the individual formula parameters. The formula is sent to MDM, and all the parameters' data
      must be found in MDM in order for MDM to calculate the result profile.
    minLength: 0
    allOf:
      - $ref: "../DataTypes/DescriptionEmptyStringNullable.yaml"
  formulaParameters:
    description: Metering Point Formula Parameters collection.
    nullable: true
    type: array
    minItems: 0
    maxItems: 10000
    items:
      $ref: "../MeteringPointModel/FormulaParameter.yaml"
  description:
    description: A description of what resulting data the formula generates.
    allOf:
      - $ref: "../DataTypes/DescriptionStringNullable.yaml"
  meterFrameId:
    description: Meter Frame id.
    allOf:
      - $ref: "../DataTypes/GuidNullable.yaml"
  meterFrameNumber:
    nullable: true
    description: Meter frame, which new metering point will belong to.
    allOf:
      - $ref: "../DataTypes/MediumString.yaml"
  registerRequirementId:
    description: Register Requirement id.
    allOf:
      - $ref: "../DataTypes/ShortStringNullable.yaml"
  registerRequirementName:
    description: Register Requirement Name.
    allOf:
      - $ref: "../DataTypes/ShortStringNullable.yaml"
