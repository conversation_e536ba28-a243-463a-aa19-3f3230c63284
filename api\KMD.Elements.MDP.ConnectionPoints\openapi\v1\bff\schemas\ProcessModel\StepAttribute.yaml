type: object
description: Step attribute object.
required:
  - attributeType
  - attributeGroup
properties:
  attributeType:
    allOf:
      - $ref: './StepAttributeType.yaml'
    description: Attribute type describing step.
    nullable: false
  attributeValue:
    allOf:
      - $ref: '../DataTypes/LongStringNullable.yaml'
    description: Attribute value.
    nullable: true
  attributeGroup:
    allOf:
      - $ref: './StepAttributeGroup.yaml'
    description: Attribute group.
    nullable: false
  displayedValue:
    allOf:
      - $ref: '../DataTypes/LongStringNullable.yaml'
    description: Displayed value.
    nullable: true
  actionKey:
    allOf:
      - $ref: '../DataTypes/LongStringNullable.yaml'
    description: Action key correspondent with step.
    nullable: true
additionalProperties: false

