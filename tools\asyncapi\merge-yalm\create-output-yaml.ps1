<#
.SYNOPSIS
This script merges and dereferences a JSON schema or YAML schema from an input file into a new YAML file.

.DESCRIPTION
This script takes a single input file (JSON or YAML) containing a schema or OpenAPI definition.
# It resolves all internal and external references (eg. $ref: './otherfile.yaml#/myValue) within that file and
# then outputs the fully merged content as a new YAML file.
# output of this directory is <input-file-directory>/merge/<input-file-name>

The script requires **Node.js** to be installed on your system.

.PARAMETER InputFile
**Path to the input JSON or YAML file** that needs to be dereferenced. This parameter is mandatory.
#>
param (
  [Parameter(Mandatory = $true)]
  [string]$InputFile
)

$directory = Split-Path $InputFile -Parent
$filename = Split-Path $InputFile -Leaf
if ($directory.Length -eq 0) {
  $OutputDir = "$PSScriptRoot/merged"
} elseif ($directory.StartsWith("C:\") -Or $directory.StartsWith("/")) {
  $OutputDir = "$directory/merged"
} else {
  $OutputDir = "$PSScriptRoot/$directory/merged"
}

$OutputFile = "$OutputDir/$filename"
if (! (Test-Path $OutputDir) ) {
  New-Item -ItemType Directory -Path "$OutputDir"
}

# Ensure Node.js is installed
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
  Write-Error "Node.js is not installed or not in PATH."
  exit 1
}

npm instal json-schema-ref-parser js-yaml --prefix "$PSScriptRoot"

# Create a temporary Node script
$TempScript = @"
const refParser = require('json-schema-ref-parser');
const fs = require('fs');
const yaml = require('js-yaml');

const inputFile = process.argv[2];
const outputFile = process.argv[3];

refParser.dereference(inputFile)
  .then(schema => {
    const yamlStr = yaml.dump(schema, { noRefs: true });
    fs.writeFileSync(outputFile, yamlStr, 'utf8');
    console.log('Resolved successfully:', outputFile);
  })
  .catch(err => {
    console.error('Error:', err);
    process.exit(1);
  });
"@


# Write the Node script to a temp file
$TempFile = "$PSScriptRoot/New-TemporaryFile.js"
Set-Content -Path $TempFile -Value $TempScript

# Run the Node script with arguments
node $TempFile $InputFile $OutputFile

# Clean up
Remove-Item $TempFile
Remove-Item node_modules -Recurse
Remove-Item package.json
Remove-Item package-lock.json

