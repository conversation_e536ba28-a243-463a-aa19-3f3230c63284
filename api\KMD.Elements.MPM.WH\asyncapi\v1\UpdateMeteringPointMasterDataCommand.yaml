asyncapi: 2.6.0
id: https://async.api.kmdelements.com/send-request-update-metering-point-master-data
info:
  title: Command for send update metering point master data to EWII
  x-maintainers: Team-MC-1
  version: 1.0.11
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    API to initiate the process of update metering point master data.
    Descriptions in properties are connected with RSM-VV021 contract

tags:
  - name: Team-MC-1
    description: Maintainer

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.command.mpm-wh.update-metering-point.v1:
    description: This topic is used for update metering point by BRS-VV006 and exchanging RSM-VV021 to be sent to EWII
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Channel for send data of new version Metering point to EWII.
      description: Command to send metering master data to EWII.
      operationId: sendUpdateMeteringPointMasterDataCommand
      message:
        $ref: "#/components/messages/UpdateMeteringPointMasterDataCommand"

components:
  messages:
    UpdateMeteringPointMasterDataCommand:
      title: Update metering point
      name: UpdateMeteringPointMasterDataCommand
      contentType: application/json
      headers:
        $ref: "#/components/schemas/MessageHeaders"
      payload:
        $ref: "#/components/schemas/UpdateMeteringPointMasterDataCommandPayload"

  schemas:
    MessageHeaders:
      $ref: "./schemas/Shared/CommandHeaders.yaml"

    UpdateMeteringPointMasterDataCommandPayload:
      title: UpdateMeteringPointMasterDataCommandPayload
      description: Payload start process for update metering point in EWII.
      type: object
      additionalProperties: false
      required:
        - meteringPointPayload
        - parentProcessData
      properties:
        meteringPointPayload:
          $ref: "#/components/schemas/MeteringPointPayload"
        parentProcessData:
          $ref: './schemas/Shared/ParentProcessData.yaml'

    MeteringPointPayload:
      type: object
      description: Metering Point Master Data Payload - This is sent to EVII
      additionalProperties: false
      required:
        - meteringPointId
        - occurrence
      properties:
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        parentMeteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        occurrence:
          type: string
          description: When metering points starts being valid. Also known as validFrom.
          format: date-time
          example: "2023-07-01T22:00:00Z"
        occurrenceTo:
          type: string
          description: Also known as "Valid To". End of Given Version Validity date.
          format: date-time
          nullable: true
          example: "2023-07-01T22:00:00Z"
        meteringPointDetails:
          $ref: "#/components/schemas/MeteringPointDetails"
        meteringPointAddress:
          $ref: "#/components/schemas/MeteringPointAddress"
        meter:
          $ref: "./schemas/MeteringPoints/Meter.yaml"

    MeteringPointDetails:
      type: object
      description: The characteristic of the metering point
      additionalProperties: false
      properties:
        meteringGridAreaId:
          type: integer
          format: int32
          nullable: true
          minimum: 0
          maximum: 2147483647
          example: 111
          description: |-
              Network area is a term for a network that is managed by a network company.
        locationDescription:
          type: string
          description: Free text detailed description of location. Can be used if there is something completely extraordinary about the location.
          maxLength: 60
        disconnectKind:
          description: |-
            Type of Heat Metering Point Disconnect Kind.
            | CodeListName                            | DisplayName            | Code | Name              | Translation         |
            |-----------------------------------------|------------------------|------|-------------------|---------------------|
            | System-HeatMeteringPoint-DisconnectKind | D01 – fjernafbrydelig  | D01  | Fjern afbrydelig  | Remote disconnection|
            | System-HeatMeteringPoint-DisconnectKind | D02 – Manual afbrydelig| D02  | Manual afbrydelig | Manual disconnection|
          type: [ 'string', 'null' ]
          minLength: 3
          maxLength: 3
          pattern: "^(D01|D02)$"
          example: D01
        supplyInfoBilling: #todo rename
          pattern: "^.*$"
          type: string
          nullable: true
          minLength: 0
          maxLength: 100
          description: Supply billing info for the Metering Point.
          example: A01
        commonReading:
          $ref: "./schemas/MeteringPoints/MpCommonReading.yaml"
        readingFrequency:
          $ref: "./schemas/MeteringPoints/MpReadingFrequency.yaml"
        connectionStatus:
          $ref: "./schemas/MeteringPoints/MpConnectionStatus.yaml"
        expectedConsumptionPerYear:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
          description: The expected consumption in the period.
          example: 15
        subTypeOfMeteringPoint:
          $ref: "./schemas/MeteringPoints/MpSubTypeOfMeteringPoint.yaml"
        applicationCode:
          pattern: "^.*$"
          type: string
          minLength: 0
          maxLength: 50
          description: Indicates the application code for the single device. By default, it comes from the meter frame's property element, but must be able to be overwritten manually.
          example: A01
        noMeter:
          type: boolean
          nullable: false
          description: Indicates whether it is a meter-free installation (subscription only).
          example: true
        heatingMeteringPointAttributes:
          $ref: "./schemas/MeteringPoints/Heating/HeatingUpdateMeteringPointAttributes.yaml"
        waterMeteringPointAttributes:
          $ref: "./schemas/MeteringPoints/Water/WaterUpdateMeteringPointAttributes.yaml"
        inheritDisconnectionTypeFromMeter:
          type: boolean
          nullable: true
          description: Inherit Disconnection Type From Meter.

    MeteringPointId:
      type: string
      description: Metering Point Container Id.
      minLength: 18
      maxLength: 18
      pattern: ^\d{18}$
      example: "571313190000020132"

    MeteringPointAddress:
      description: The detailed address
      type: object
      additionalProperties: false
      properties:
        streetName:
          type: string
          description: Is mapped to `StreetName`
          maxLength: 40
        streetCode:
          type: string
          maxLength: 4
          description: Is mapped to `StreetCode`
          pattern: ^$|^\d{4}$
        buildingNumber:
          type: string
          description: Is mapped to `BuildingNumber`
          maxLength: 6
        floorIdentification:
          type: string
          description: Is mapped to `FloorIdentification`
          maxLength: 4
        roomIdentification:
          type: string
          description: Is mapped to `RoomIdentification`
          maxLength: 4
        citySubDivisionName:
          type: string
          description: Is mapped to `CitySubDivisionName`
          maxLength: 34
        postCode:
          type: string
          description: Is mapped to `PostCode`
          maxLength: 10
        cityName:
          type: string
          description: Is mapped to `CityName`
          maxLength: 25
        municipalityCode:
          type: string
          description: Is mapped to `MunicipalityCode`
          pattern: ^$|^.{3}$
          maxLength: 3
        countryCode:
          type: string
          maxLength: 2
          description: Is mapped to `CountryName`
          pattern: ^$|^[A-Z]{2}$
        addressWashInstruction:
          type: string
          maxLength: 3
          description: Is mapped to `AddressWashInstruction`
          pattern: ^(D00|D01|D02|D03)$
        darReference:
          type: string
          description: Is mapped to `DARReference`
          format: uuid
        carId:
          type: string
          format: uuid
          description: Central address registry identifier.
        postBox:
          description: Post box number / location.
          type: [ 'string', 'null' ]
          pattern: "^.*$"
          minLength: 0
          maxLength: 25
          example: 'N/A'
        protectedAddress:
          description: Is address protected.
          type: [ 'boolean', 'null' ]
          example: true

  parameters:
    TenantId:
      $ref: "./parameters/TenantId.yaml"
