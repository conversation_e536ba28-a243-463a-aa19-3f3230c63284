type: object
description: Change settlement method on Metering point.
required:
  - meteringPointVersionId
  - meteringPointId
  - supplyType
  - occurrence
additionalProperties: false
properties:
  meteringPointVersionId:
    description: Id of Metering Point Version. Metering Point Version Id contains of {GSRN}_{DATE}. so <MeteringPointId>_<Occurrence>
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    example: 317575025980776095_2022-01-02
  settlementMethod:
    type: string
    maxLength: 3
    nullable: true
    description: Is mapped to `SettlementMethod`
    pattern: ^(D01|E02)$
  meteringPointId:
    type: string
    description: Metering Point Container Id (GSRN).
    minLength: 18
    maxLength: 18
    pattern: ^\d{18}$
    example: "571313190000020132"
  supplyType:
    type: string
    description: Allowed values depends on tenant configuration.
    enum:
      - Electricity
      - Water
      - Heating
    example: Electricity
  occurrence:
    $ref: '../DataTypes/DateTimeIso8601.yaml'
  meterReadingOccurrence:
    type: string
    maxLength: 5
    nullable: true
    description: Meter reading occurence attribute
    pattern: ^(-?)P(?=\d|T\d)(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)([DW]))?(?:T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?)?$
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
