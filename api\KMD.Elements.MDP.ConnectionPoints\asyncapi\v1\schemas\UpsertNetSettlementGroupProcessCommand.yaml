title: UpsertNetSettlementGroupProcessCommand
type: object
required:
  - processId
  - connectionPointId
  - targetDate
  - autostart
  - processType
properties:
  processId:
    description: Id of process in MDP Connection Points domain.
    type: string
    format: uuid
    nullable: false
  connectionPointId:
    type: string
    format: uuid
    description: Connection Point Id
    nullable: false
  targetDate:
    $ref: "./DataTypes/DateTime.yaml"
    nullable: false
    description: Target date for process execution
  parentProcess:
    $ref: "./ParentProcess.yaml"
  templateId:
    type: string
    format: uuid
    description: Master data process template Id
    nullable: false
  autostart:
    type: boolean
    description: Indicate if process should start automatically
    nullable: false
  processType:
    $ref: "./ProcessType.yaml"
  meterFrames:
    type: array
    maxItems: 100
    items:
      title: meterFrame
      type: object
      required:
        - id
        - productionCapacity
      properties:
        id:
          description: Id of the existing meter frame.
          $ref: "./DataTypes/Guid.yaml"
          nullable: false
        productionCapacity:
          type: number
          description: Desired production capacity value.
          format: decimal
          nullable: false
additionalProperties: false
