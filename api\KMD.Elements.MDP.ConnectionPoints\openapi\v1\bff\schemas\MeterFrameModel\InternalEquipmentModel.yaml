type: object
additionalProperties: false
description: Internal Equipment model.
required:
 - installationPosibility
 - equipmentNumber
 - equipmentType
 - vendor
properties:
  externalId:
    description: External Id of equipment.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  installationPosibility:
    $ref: './DataTypes/InstallationPosibility.yaml'
  equipmentNumber:
    description: Equipment number
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
  equipmentType:
    description: Equipment type.
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
  vendor:
    description: Vendor.
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
  description:
    description: Description
    allOf:
      - $ref: '../DataTypes/LongStringNullable.yaml'
  installedDate:
    description: Installation date.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  removedDate:
    description: Removal date.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
