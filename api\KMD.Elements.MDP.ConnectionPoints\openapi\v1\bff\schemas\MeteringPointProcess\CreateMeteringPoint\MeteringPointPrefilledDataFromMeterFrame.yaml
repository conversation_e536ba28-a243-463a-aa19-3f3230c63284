type: object
description: Prefilled data of metering point from meter frame.
additionalProperties: false
properties:
  locationDescription:
    description: Location description taken from meter frame.
    allOf:
      - $ref: '../../DataTypes/DescriptionStringNullable.yaml'
  carId:
    description: Central address registry identifier used on meter frame.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  maximumCurrent:
    description: Maximum current in amperes taken from meter frame.
    allOf:
      - $ref: '../../DataTypes/PositiveDecimalNullable.yaml'
  maximumPower:
    description: Maximum power in kilowatts taken from meter frame.
    allOf:
      - $ref: '../../DataTypes/PositiveDecimalNullable.yaml'
  returnHeatConnected:
    nullable: true
    description: Reaturn heat connected taken from meter frame.
    allOf:
      - $ref: '../../DataTypes/Boolean.yaml'
  commonReading:
    nullable: true
    description: Common reading taken from meter frame.
    allOf:
      - $ref: '../../DataTypes/OneWordString.yaml'
  m2Total:
    description: Indicates the total residential area from BBR.
    allOf:
      - $ref: '../../DataTypes/PositiveIntegerNullable.yaml'
  m2Business:
    description: Indicates the total business area from BBR.
    allOf:
      - $ref: '../../DataTypes/PositiveIntegerNullable.yaml'
  m2Warehouse:
    description: Indicates the total warehouse area from BBR.
    allOf:
      - $ref: '../../DataTypes/PositiveIntegerNullable.yaml'
  meterSize:
    type: [ 'string', 'null' ]
    description: Meter size
    minLength: 0
    maxLength: 3
    pattern: "^(2,5|6|10|15|25|40|100|150)$"
