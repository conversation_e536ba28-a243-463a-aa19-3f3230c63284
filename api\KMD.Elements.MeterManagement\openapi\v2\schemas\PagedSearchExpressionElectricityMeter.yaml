type: object
description: Search criteria for searching meters.
required:
  - page
  - electricityMeterSearchExpressionCollection
properties:
  page:
    allOf:
      - $ref: '../kmd-elements-meter-management.yaml#/components/schemas/Page'
    description: Page properties.
  electricityMeterSearchExpressionCollection:
    description: Search expression collection.
    type: array
    items:
      type: object
      allOf:
        - $ref: "../kmd-elements-meter-management.yaml#/components/schemas/SearchExpression"
      description: Search filter for meters.
      required:
        - propertyName
      properties:
        propertyName:
          description: Property name. It will be used for search purpose.
          pattern: "^(Id|MeterNumber|MeterTypeId|AlternativeMeterNumber|BarCodeEanNumber|CommunicationMethodId|FirstCommissioningDate|HesSystemId|ProductionYear|PurchaseDate|RemoteDisconnectable|RemoteReadable|SerialNumber|ManufacturerId|FabricationNumber|FabricationTypeId|MeterControlTypeCode|MainControlMeterRoleCode|RemoteRegisterConfigurationId|RemoteDisplayConfigurationId|MeterStatusId|SecondaryStatusId|MeterConfigurationId|MeterBatchTechnicalId|MeterBatchId|AccuracyClassId|CurrentId|CustomerOutputStateId|CustomerDataOutputActivationDate|CustomerDataOutputEncryptionKey|ElectricityConnectionTypeId|ElectricityConnectionRemarkId|ElectricityMeteringPrincipleId|ImpulsesPerkWhVarh|ItemNameId|MeterRoleCode|MaxPlcDistanceId|MaxRadioDistanceId|NoOfPulseInputsId|NumberOfPhasesId|OrderNumber|RatioCtId|RatioVtId|RelatedMeter|Temporary|TypeApproval|VendorOrderNumber|VoltageId|TechnicalCurrentId|TechnicalVoltageId|MeterTagIds|StartTimeForMeterBatchCode|MeterApprovedAfterCode)$"
          minLength: 1
          maxLength: 50
          type: string
          example: "Id"
      additionalProperties: false
    minItems: 0
    maxItems: 255
additionalProperties: false
