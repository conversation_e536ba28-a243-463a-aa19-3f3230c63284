openapi: 3.0.3
info:
  title: KMD.Elements.PriceManagement
  description: |-
    KMD Elements Price Management
    Stability level: PREVIEW<br/>
    <br/>
    The KMD Elements Price Management is part of the KMD Element product.<br/>

    ## Capabilities
    This API allows user to generate data points in a specific way. <br/>
    Rules for generating data points are defined within PeriodDefinition and PriceDefinition objects. <br/>

  termsOfService: "https://www.kmd.net/terms-of-use"

  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>

  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"

  version: "1.4"

  x-maintainers: Team-SE-1

servers:
  - url: "https://localhost:12001"
    description: Localhost

security:
  - Jwt: []

tags:
  - name: Dictionaries
    description: Various key name collections used across whole API
  - name: PeriodDefinition
    description: API to manage period definitions
  - name: PriceDefinition
    description: API to manage price definitions

paths:
  /v1/period-definitions/commands/search:
    post:
      tags:
        - PeriodDefinition
      operationId: searchPeriodDefinition
      description: Endpoint allowing to fetch period definitions by various critierias
      summary: Endpoint allowing to fetch period definitions by various critierias
      x-authorization: PriceManagement.Read
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetPeriodDefinitionsRequest"
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: 200 Process initialized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetPeriodDefinitionsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/period-definitions:
    post:
      tags:
        - PeriodDefinition
      operationId: createPeriodDefinition
      description: Endpoint allowing to create new period definition
      summary: Endpoint allowing to create new period definition
      x-authorization: PriceManagement.Write
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePeriodDefinitionRequest"
      responses:
        "201":
          description: 201 Period definition created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreatePeriodDefinitionResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/price-definitions/commands/search:
    post:
      tags:
        - PriceDefinition
      operationId: searchPriceDefinition
      description: Endpoint allowing to fetch price definitions by various critierias
      summary: Endpoint allowing to fetch price definitions by various critierias
      x-authorization: PriceManagement.Read
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetPriceDefinitionsRequest"
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: 200 Process initialized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetPriceDefinitionsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/price-definitions/commands/search-simple:
    post:
      tags:
        - PriceDefinition
      operationId: searchSimplePriceDefinitions
      description: Endpoint allowing to fetch approved simple price definitions with price at specific point of time.
      summary: Endpoint allowing to fetch price definitions with price at specific point of time.
      x-authorization: PriceManagement.Read
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchSimplePriceDefinitionsRequest"
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: 200 Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchSimplePriceDefinitionsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/price-definitions/{priceDefinitionId}:
    get:
      tags:
        - PriceDefinition
      operationId: getPriceDefinition
      description: Endpoint allowing to fetch price definition by identifier
      summary: Endpoint allowing to fetch price definition by identifier
      x-authorization: PriceManagement.Read
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
        - $ref: "#/components/parameters/PriceDefinitionId"
      responses:
        "200":
          description: 200 Process initialized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetPriceDefinitionResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/dictionaries/supply-types:
    get:
      tags:
        - Dictionaries
      operationId: getSupplyTypes
      description: Supply types used within period and price definitions
      summary: Supply types used within period and price definitions
      x-authorization: PriceManagement.Read
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: 200 Ok
          content:
            application/json:
              schema:
                type: array
                description: Array of supply type properties
                nullable: false
                maxItems: 1024
                items:
                  $ref: "#/components/schemas/SupplyTypeDictionaryModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/dictionaries/granularities:
    get:
      tags:
        - Dictionaries
      operationId: getGranularities
      description: Granularities used within period and price definitions
      summary: Granularities used within period and price definitions
      x-authorization: PriceManagement.Read
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: 200 Ok
          content:
            application/json:
              schema:
                type: array
                description: Array of granularity properties
                nullable: false
                maxItems: 1024
                items:
                  $ref: "#/components/schemas/GranularityDictionaryModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/dictionaries/price-types:
    get:
      tags:
        - Dictionaries
      operationId: getPriceTypes
      description: Price types used within period and price definitions
      summary: Price types used within period and price definitions
      x-authorization: PriceManagement.Read
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: 200 Ok
          content:
            application/json:
              schema:
                type: array
                description: Array of price type properties
                nullable: false
                maxItems: 1024
                items:
                  $ref: "#/components/schemas/PriceTypeDictionaryModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/dictionaries/units:
    get:
      tags:
        - Dictionaries
      operationId: getUnits
      description: Units used within period and price definitions
      summary: Units used within period and price definitions
      x-authorization: PriceManagement.Read
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: 200 Ok
          content:
            application/json:
              schema:
                type: array
                description: Array of unit properties
                nullable: false
                maxItems: 1024
                items:
                  $ref: "#/components/schemas/UnitDictionaryModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/dictionaries/resolutions:
    get:
      tags:
        - Dictionaries
      operationId: getResolutions
      description: Resolutions used within period and price definitions
      summary: Resolutions used within period and price definitions
      x-authorization: PriceManagement.Read
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: 200 Ok
          content:
            application/json:
              schema:
                type: array
                description: Array of resolution properties
                nullable: false
                maxItems: 1024
                items:
                  $ref: "#/components/schemas/ResolutionDictionaryModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/dictionaries/price-categories:
    get:
      tags:
        - Dictionaries
      operationId: getPriceCategories
      description: Categories used within connection fee price definitions
      summary: Categories used within connection fee price definitions
      x-authorization: PriceManagement.Read
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: 200 Ok
          content:
            application/json:
              schema:
                type: array
                description: Array of price categories properties
                nullable: false
                maxItems: 1024
                items:
                  $ref: "#/components/schemas/PriceCategoryDictionaryModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

components:
  parameters:
    EsMessageId:
      name: es-message-id
      description: Unique message ID. The same message id is used when resending the message.
      in: header
      schema:
        type: string
        format: uuid
      required: true
      example: 3849be75-76c2-440b-860e-32e72a2e1836
    EsCorrelationId:
      name: es-correlation-id
      description: |
        This is used to 'link' messages together. This can be supplied on a request, so
        that the client can correlate a corresponding reply message.
        The server will place the incoming X-Correlation-ID value as the X-Correlation-ID
        on the outgoing reply. If not supplied on the request, the X-Correlation-ID of the
        reply should be set to the value of the X-Message-ID that was used on the request, if present.
        Given that the X-Correlation-ID is used to `link` messages together,
        it may be reused on more than one message.
      in: header
      schema:
        type: string
        format: uuid
      required: false
      example: f998d378-3c61-4404-b7d2-32cfd106f4d0
    EsSourceId:
      name: es-source-id
      description: The name of the source system sending the message.
      in: header
      schema:
        type: string
        maxLength: *********7
        pattern: ""
      required: false
      example: 51f06e9b-20f0-4ede-b85b-4e6e60eb6b8f
    PriceDefinitionId:
      in: path
      name: priceDefinitionId
      schema:
        type: integer
        format: int32
        minimum: 0
        maximum: *********7
      description: Identifier of price definition
      required: true
      example: 123
  schemas:
    GetPeriodDefinitionsRequest:
      type: object
      additionalProperties: false
      description: The model contains properties for gathering specific period definitions.
      properties:
        pageInfo:
          $ref: "#/components/schemas/PageInfo"
        sortInfo:
          $ref: "#/components/schemas/SortInfo"
        filter:
          $ref: "#/components/schemas/GetPeriodDefinitionsSearchFilter"
    PageInfo:
      type: object
      additionalProperties: false
      nullable: true
      description: Page details.
      required:
        - page
        - pageSize
      properties:
        page:
          type: integer
          format: int32
          description: Number of page
          minimum: 0
          maximum: *********7
        pageSize:
          type: integer
          format: int32
          description: Size of page
          minimum: 0
          maximum: *********7
    SortInfo:
      type: object
      additionalProperties: false
      nullable: true
      description: Sorting details.
      required:
        - direction
      properties:
        sortBy:
          type: string
          nullable: true
          description: Determines property name to sort by.
          maxLength: *********7
          pattern: ""
        direction:
          $ref: "#/components/schemas/SortDirection"
    SortDirection:
      type: string
      pattern: "^(Asc|Desc)$"
      description: Sort direction
      maxLength: *********7
    GetPeriodDefinitionsSearchFilter:
      nullable: true
      type: object
      description: The model used to determine what period definitions to look for.
      additionalProperties: false
      properties:
        name:
          type: string
          nullable: true
          description: Period definition name
          maxLength: *********7
          pattern: ""
        supplyTypeId:
          type: string
          nullable: true
          description: Period definition supply type identifier
          maxLength: *********7
          pattern: ""
        granularityId:
          type: string
          nullable: true
          description: Period definition granularity identifier
          maxLength: *********7
          pattern: ""
    GetPeriodDefinitionsResponse:
      type: object
      additionalProperties: false
      description: The model contains period definitions searched by filter.
      required:
        - periodDefinitions
        - totalCount
      properties:
        periodDefinitions:
          type: array
          maxItems: 1024
          items:
            $ref: "#/components/schemas/GetPeriodDefinitionsPeriod"
          description: List of period definitions
        totalCount:
          type: integer
          format: int32
          description: Total count of all period definitions
          minimum: 0
          maximum: *********7
        pageInfo:
          $ref: "#/components/schemas/PageInfo"
    GetPeriodDefinitionsPeriod:
      type: object
      additionalProperties: false
      description: The model contains period for period definition.
      required:
        - id
        - name
        - supplyTypeId
        - supplyTypeDisplayName
        - granularityId
        - granularityDisplayName
      properties:
        id:
          type: integer
          format: int32
          description: Period identifier
          minimum: 0
          maximum: *********7
        name:
          type: string
          description: Period name
          maxLength: *********7
          pattern: ""
        supplyTypeId:
          type: string
          description: Period supply type identifier
          maxLength: *********7
          pattern: ""
        supplyTypeDisplayName:
          type: string
          description: Period supply type translated name
          maxLength: *********7
          pattern: ""
        granularityId:
          type: string
          description: Period granularity identifier
          maxLength: *********7
          pattern: ""
        granularityDisplayName:
          type: string
          description: Period granularity translated name
          maxLength: *********7
          pattern: ""

    CreatePeriodDefinitionRequest:
      type: object
      additionalProperties: false
      description: The model contains properties to create a period definition.
      required:
        - periods
      properties:
        name:
          type: string
          nullable: true
          description: Period definition name
          maxLength: *********7
          pattern: ""
        supplyTypeId:
          type: string
          nullable: true
          description: Period definition supply type identifier
          maxLength: *********7
          pattern: ""
        granularityId:
          type: string
          nullable: true
          description: Period definition granularity identifier
          maxLength: *********7
          pattern: ""
        periods:
          type: array
          maxItems: 1024
          items:
            $ref: "#/components/schemas/CreatePeriodModel"
          description: List of periods to attach to the period definition
    CreatePeriodModel:
      type: object
      additionalProperties: false
      description: The model contains properties to create a period for a specific period definition.
      properties:
        name:
          type: string
          nullable: true
          description: Period name
          maxLength: *********7
          pattern: ""
        start:
          type: string
          nullable: true
          description: Period start date
          maxLength: *********7
          pattern: ""
        end:
          type: string
          nullable: true
          description: Period end date
          maxLength: *********7
          pattern: ""
    CreatePeriodDefinitionResponse:
      type: object
      additionalProperties: false
      description: The model contains identifier of created period definition.
      required:
        - id
      properties:
        id:
          type: integer
          format: int32
          description: Created period definition identifier
          minimum: -*********7
          maximum: *********7
    GetPriceDefinitionsRequest:
      type: object
      additionalProperties: false
      description: The model is used for gathering price definitions by specific filters.
      properties:
        pageInfo:
          $ref: "#/components/schemas/PageInfo"
        sortInfo:
          $ref: "#/components/schemas/SortInfo"
        filter:
          $ref: "#/components/schemas/GetPriceDefinitionsSearchCriterias"
    GetPriceDefinitionsSearchCriterias:
      type: object
      nullable: true
      additionalProperties: false
      description: The model used to determine what price definitions to look for.
      properties:
        isMarket:
          type: boolean
          description: IsMarket
          nullable: true
        isSimple:
          type: boolean
          description: IsSimple
          nullable: true
        name:
          type: string
          nullable: true
          description: Name
          maxLength: *********7
          pattern: ""
        supplyTypeId:
          type: string
          nullable: true
          description: Supply type identifier
          maxLength: *********7
          pattern: ""
        priceTypeId:
          type: string
          nullable: true
          description: Price type identifier
          maxLength: *********7
          pattern: ""
        priceId:
          type: string
          nullable: true
          description: Price identifier
          maxLength: *********7
          pattern: ""
        vatClass:
          type: string
          format: uuid
          nullable: true
          description: Vat class value identifier
        transparentInvoicing:
          type: string
          format: uuid
          nullable: true
          description: Transparent invoicing value identifier
        taxIndicator:
          type: string
          format: uuid
          nullable: true
          description: Tax indicator value identifier
        ownerGlnId:
          type: string
          maxLength: 64
          pattern: ""
          nullable: true
          description: Owner GLN number
        unitId:
          type: string
          nullable: true
          description: Unit identifier
          maxLength: *********7
          pattern: ""
        resolutionId:
          type: string
          nullable: true
          description: Resolution identifier
          maxLength: *********7
          pattern: ""
        description:
          type: string
          nullable: true
          description: Description
          maxLength: *********7
          pattern: ""
        comment:
          type: string
          nullable: true
          description: Comment
          maxLength: *********7
          pattern: ""
        priceCategoryId:
          type: string
          nullable: true
          description: Connection Fee price category
          maxLength: *********7
          pattern: ""
        startDate:
          type: string
          format: date-time
          nullable: true
          description: The price definition valid from date of the oldest accepted version.
          example: "2025-01-01T00:00:00+00:00"
        stopDate:
          type: string
          format: date-time
          nullable: true
          description: The date on which the price definition was successfully stopped.
          example: "2025-01-01T00:00:00+00:00"
    GetPriceDefinitionsResponse:
      type: object
      additionalProperties: false
      description: The model contains price definitions searched by filter.
      required:
        - priceDefinitions
        - totalCount
      properties:
        priceDefinitions:
          type: array
          maxItems: 1024
          items:
            $ref: "#/components/schemas/SearchPriceDefinitionResponseModel"
          description: List of price definitions
        totalCount:
          type: integer
          format: int32
          description: Total count of all price definitions
          minimum: -*********7
          maximum: *********7
        pageInfo:
          $ref: "#/components/schemas/PageInfo"
    SearchPriceDefinitionResponseModel:
      type: object
      additionalProperties: false
      description: The model contains specific price definition properties.
      required:
        - id
        - isSimple
        - isMarket
        - name
        - supplyTypeId
        - supplyTypeDisplayName
        - unitId
        - unitDisplayName
        - resolutionId
        - resolutionDisplayName
        - priceTypeId
        - priceTypeDisplayName
        - priceId
        - vatClass
        - transparentInvoicing
        - taxIndicator
        - ownerGlnId
      properties:
        id:
          type: integer
          format: int32
          description: Identifier
          minimum: -*********7
          maximum: *********7
        isSimple:
          type: boolean
          description: IsSimple
        isMarket:
          type: boolean
          description: IsMarket
        name:
          type: string
          description: Name
          maxLength: *********7
          pattern: ""
        supplyTypeId:
          type: string
          description: Supply type identifier
          maxLength: *********7
          pattern: ""
        supplyTypeDisplayName:
          type: string
          description: Supply type translated name
          maxLength: *********7
          pattern: ""
        unitId:
          type: string
          description: Unit identifier
          maxLength: *********7
          pattern: ""
        unitDisplayName:
          type: string
          description: Unit translated name
          maxLength: *********7
          pattern: ""
        resolutionId:
          type: string
          description: Resolution identifier
          maxLength: *********7
          pattern: ""
        resolutionDisplayName:
          type: string
          description: Resolution translated name
          maxLength: *********7
          pattern: ""
        priceTypeId:
          type: string
          description: Price type identifier
          maxLength: *********7
          pattern: ""
        priceTypeDisplayName:
          type: string
          description: Type translated name
          maxLength: *********7
          pattern: ""
        priceId:
          type: string
          description: Price identifier
          maxLength: *********7
          pattern: ""
        vatClass:
          type: string
          format: uuid
          description: Vat class value identifier
        transparentInvoicing:
          type: string
          format: uuid
          description: Transparent invoicing value identifier
        taxIndicator:
          type: string
          format: uuid
          description: Tax indicator value identifier
        ownerGlnId:
          type: string
          maxLength: 64
          pattern: ""
          description: Owner GLN number
        description:
          type: string
          nullable: true
          description: Description
          maxLength: *********7
          pattern: ""
        comment:
          type: string
          nullable: true
          description: Comment
          maxLength: *********7
          pattern: ""
        priceCategoryId:
          type: string
          nullable: true
          description: Connection Fee price category
          maxLength: *********7
          pattern: ""
        priceCategoryDisplayName:
          type: string
          nullable: true
          description: Price category translated name
          maxLength: *********7
          pattern: ""
        startDate:
          type: string
          format: date-time
          nullable: true
          description: The price definition valid from date of the oldest accepted version.
          example: "2025-01-01T00:00:00+00:00"
        stopDate:
          type: string
          format: date-time
          nullable: true
          description: The date on which the price definition was successfully stopped.
          example: "2025-01-01T00:00:00+00:00"
    SearchSimplePriceDefinitionsRequest:
      type: object
      additionalProperties: false
      description: The model is used for gathering simple price definitions by specific filters.
      required:
        - filter
      properties:
        pageInfo:
          $ref: "#/components/schemas/PageInfo"
        filter:
          $ref: "#/components/schemas/SearchSimplePriceDefinitionsSearchCriterias"
    SearchSimplePriceDefinitionsSearchCriterias:
      type: object
      additionalProperties: false
      description: The model used to determine what simple price definitions to look for.
      required:
        - supplyTypeId
      properties:
        supplyTypeId:
          type: string
          nullable: false
          description: Price definition supply type identifier
          maxLength: 64
          pattern: "^(Electricity|Heating|Water)$"
          example: Electricity
        validityDate:
          type: string
          format: date-time
          nullable: true
          description: Point in time when prices should be searched. Defaults to 'now'.
          example: "2024-01-01T00:00:00+00:00"
        priceDefinitionName:
          type: string
          nullable: true
          description: Price definition name filter
          maxLength: *********7
          pattern: ""
          example: ExamplePrice
        priceTypeId:
          type: string
          nullable: true
          description: Price type identifier
          maxLength: *********7
          pattern: "^(Tariff|Subscription|Fee|ConnectionFee)$"
          example: Fee
        ownerGlnId:
          type: string
          maxLength: 64
          pattern: ""
          nullable: true
          description: Owner GLN number
        internalOrMarket:
          type: string
          nullable: true
          description: Enum that specifies wheter to search market prices only, internal prices only, or all prices.
          x-enumNames:
            - Market
            - Internal
            - All
          enum:
            - Market
            - Internal
            - All
    SearchSimplePriceDefinitionsResponse:
      type: object
      additionalProperties: false
      description: The model contains price definitions searched by filter.
      required:
        - priceDefinitions
        - totalCount
      properties:
        priceDefinitions:
          type: array
          maxItems: 1024
          items:
            $ref: "#/components/schemas/SearchSimplePriceDefinitionResponseModel"
          description: List of simple price definitions.
        totalCount:
          type: integer
          format: int32
          description: Total count of all price definitions matching search criteria.
          minimum: 0
          maximum: *********7
        pageInfo:
          $ref: "#/components/schemas/PageInfo"
    SearchSimplePriceDefinitionResponseModel:
      type: object
      additionalProperties: false
      description: The model contains simple price definition properties.
      required:
        - priceDefinitionId
        - priceDefinitionName
        - supplyTypeId
        - priceTypeId
        - priceId
        - ownerGlnId
        - unitId
        - price
        - isTax
        - description
      properties:
        priceDefinitionId:
          type: integer
          format: int32
          description: Price definition identifier
          minimum: 0
          maximum: *********7
          example: 2
        priceDefinitionName:
          type: string
          description: Price definition name
          maxLength: *********7
          pattern: ""
          example: PriceDefinition
        supplyTypeId:
          type: string
          description: Supply type identifier
          maxLength: *********7
          pattern: "^(Electricity|Heating|Water)$"
          example: Electricity
        priceTypeId:
          type: string
          description: Price type identifier
          maxLength: *********7
          pattern: "^(Tariff|Subscription|Fee|ConnectionFee)$"
          example: ConnectionFee
        priceId:
          type: string
          description: Price identifier
          maxLength: *********7
          pattern: ""
          example: "321"
        ownerGlnId:
          type: string
          maxLength: 64
          pattern: ""
          description: Owner GLN number
        price:
          type: string
          description: Price value at specified date
          maxLength: *********
          pattern: ""
          example: "20.00"
        unitId:
          type: string
          description: Unit identifier
          maxLength: *********7
          pattern: ""
          example: DKKPerkWh
        isTax:
          type: boolean
          description: Tax indicator
          example: true
        description:
          type: string
          description: Description
          maxLength: *********7
          pattern: ""
          example: Example description
        priceCategoryId:
          type: string
          nullable: true
          description: Connection fee category
          maxLength: *********7
          pattern: ""
    GetPriceDefinitionResponse:
      type: object
      additionalProperties: false
      description: The model contains specific price definition properties.
      required:
        - id
        - isSimple
        - isMarket
        - name
        - supplyTypeId
        - unitId
        - resolutionId
        - priceTypeId
        - priceId
        - vatClass
        - transparentInvoicing
        - taxIndicator
        - ownerGlnId
        - changedByUserId
        - changeDate
        - isStopped
        - rowVersion
        - versions
      properties:
        id:
          type: integer
          format: int32
          description: Identifier
          minimum: -*********7
          maximum: *********7
        isSimple:
          type: boolean
          description: IsSimple
        isMarket:
          type: boolean
          description: IsMarket
        name:
          type: string
          description: Name
          maxLength: *********7
          pattern: ""
        supplyTypeId:
          type: string
          description: Supply type identifier
          maxLength: *********7
          pattern: ""
        unitId:
          type: string
          description: Unit identifier
          maxLength: *********7
          pattern: ""
        resolutionId:
          type: string
          description: Resolution identifier
          maxLength: *********7
          pattern: ""
        priceTypeId:
          type: string
          description: Price type identifier
          maxLength: *********7
          pattern: ""
        priceId:
          type: string
          description: Price identifier
          maxLength: *********7
          pattern: ""
        vatClass:
          type: string
          format: uuid
          description: Vat class value identifier
        transparentInvoicing:
          type: string
          format: uuid
          description: Transparent invoicing value identifier
        taxIndicator:
          type: string
          format: uuid
          description: Tax indicator value identifier
        ownerGlnId:
          type: string
          maxLength: 64
          pattern: ""
          description: Owner GLN number
        description:
          type: string
          nullable: true
          description: Description
          maxLength: *********7
          pattern: ""
        comment:
          type: string
          nullable: true
          description: Comment
          maxLength: *********7
          pattern: ""
        changeReason:
          type: string
          nullable: true
          description: Change reason
          maxLength: *********7
          pattern: ""
        changedByUserId:
          type: string
          format: uuid
          description: Price definition changed by user id
        changeDate:
          type: string
          format: date-time
          description: Last change date
        isStopped:
          type: boolean
          description: Is price definition stopped
        priceCategoryId:
          type: string
          nullable: true
          description: Connection Fee price category
          maxLength: *********7
          pattern: ""
        rowVersion:
          type: string
          format: byte
          description: Row version
        versions:
          type: array
          maxItems: 1024
          items:
            $ref: "#/components/schemas/GetPriceDefinitionsVersionModel"
          description: List of price definition versions
    GetPriceDefinitionsVersionModel:
      type: object
      additionalProperties: false
      description: The model contains version properties for the specific price definition.
      required:
        - id
        - validFrom
        - validTo
        - isStopVersion
        - marketStatusId
        - onGoingProcessId
        - priceLevelValues
      properties:
        id:
          type: integer
          format: int32
          description: Price definition version identifier
          minimum: -*********7
          maximum: *********7
        validFrom:
          type: string
          format: date-time
          description: Price definition version valid from date
        validTo:
          type: string
          format: date-time
          description: Price definition version valid to date
        isStopVersion:
          type: boolean
          description: Determines whether it is stop version
        marketStatusId:
          type: string
          description: Price definition version state
          example: "Draft"
          maxLength: *********7
          pattern: ""
        onGoingProcessId:
          type: string
          format: uuid
          nullable: true
          description: Price definition going process identifier
        priceLevelValues:
          type: array
          maxItems: 1024
          items:
            $ref: "#/components/schemas/GetPriceDefinitionsPriceLevelValue"
          description: List of attached price levels to price definition version
    GetPriceDefinitionsPriceLevelValue:
      type: object
      additionalProperties: false
      description: The model contains price level value properties attached to the specific price definition.
      required:
        - priceLevelId
        - value
      properties:
        priceLevelId:
          type: integer
          format: int32
          description: Price level identifier
          minimum: -*********7
          maximum: *********7
        value:
          type: string
          description: Price level value
          maxLength: *********7
          pattern: ""
    SupplyTypeDictionaryModel:
      type: object
      additionalProperties: false
      description: The model contains supply type properties.
      required:
        - id
        - name
      properties:
        id:
          type: string
          description: Supply type identifier
          maxLength: *********7
          pattern: ""
        name:
          type: string
          description: Supply type name
          maxLength: *********7
          pattern: ""
    GranularityDictionaryModel:
      type: object
      additionalProperties: false
      description: The model contains granularity properties.
      required:
        - id
        - name
      properties:
        id:
          type: string
          description: Granularity identifier
          maxLength: *********7
          pattern: ""
        name:
          type: string
          description: Granularity name
          maxLength: *********7
          pattern: ""
    PriceTypeDictionaryModel:
      type: object
      additionalProperties: false
      description: The model contains price type properties.
      required:
        - id
        - name
      properties:
        id:
          type: string
          description: Price type identifier
          maxLength: *********7
          pattern: ""
        name:
          type: string
          description: Price type name
          maxLength: *********7
          pattern: ""
    UnitDictionaryModel:
      type: object
      additionalProperties: false
      description: The model contains unit properties.
      required:
        - id
        - name
      properties:
        id:
          type: string
          description: Unit identifier
          maxLength: *********7
          pattern: ""
        name:
          type: string
          description: Unit name
          maxLength: *********7
          pattern: ""
    ResolutionDictionaryModel:
      type: object
      additionalProperties: false
      description: The model contains resolution properties.
      required:
        - id
        - name
      properties:
        id:
          type: string
          description: Resolution identifier
          maxLength: *********7
          pattern: ""
        name:
          type: string
          description: Resolution name
          maxLength: *********7
          pattern: ""
    PriceCategoryDictionaryModel:
      type: object
      additionalProperties: false
      description: The model contains category data for connection fees.
      required:
        - id
        - name
      properties:
        id:
          type: string
          description: Category identifier
          maxLength: *********7
          pattern: ""
        name:
          type: string
          description: Category name
          maxLength: *********7
          pattern: ""
        supplyTypeId:
          type: string
          description: Supply type identifier
          maxLength: *********7
          pattern: ""
    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          nullable: true
          example: "https://errors.kmdelements.com/500"
          maxLength: *********7
          pattern: ""
        title:
          description: "A short, human-readable summary of the problem type."
          type: string
          nullable: true
          example: Error short description
          maxLength: *********7
          pattern: ""
        status:
          description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
          type: integer
          format: int32
          nullable: true
          example: 500
          minimum: -*********7
          maximum: *********7
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          nullable: true
          example: Description what exactly happened
          maxLength: *********7
          pattern: ""
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          nullable: true
          example: /resources-path/1
          maxLength: *********7
          pattern: ""
    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1024
              additionalProperties:
                type: array
                maxItems: 1024
                description: Array of validation error messages.
                items:
                  type: string
                  pattern: ""
                  maxLength: *********7
              nullable: true
    ProblemDetailsWithErrors:
      description: Problem details extended with array of errors
      title: ProblemDetailsWithErrors
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          additionalProperties: false
          properties:
            errors:
              type: array
              maxItems: 1024
              items:
                $ref: "#/components/schemas/MessageDetails"
              nullable: true
              description: List of errors
    MessageDetails:
      title: MessageDetails
      type: object
      description: Provides details for the message
      additionalProperties: false
      required:
        - code
        - defaultMessage
      properties:
        propertyNavigation:
          type: string
          nullable: true
          description: Property navigation
          maxLength: *********7
          pattern: ""
        code:
          type: string
          description: Error code
          maxLength: *********7
          pattern: ""
        defaultMessage:
          type: string
          nullable: true
          description: Error default message
          maxLength: *********7
          pattern: ""
        parameters:
          type: object
          nullable: true
          description: Error message parameters
          maxProperties: 100
          additionalProperties:
            $ref: '#/components/schemas/ErrorMessageParameter'
    ErrorMessageParameter:
      title: ErrorMessageParameter
      type: object
      description: Error message parameter
      additionalProperties: false
      required:
        - value
        - type
      properties:
        value:
          type: string
          nullable: false
          description: Message parameter value
          maxLength: *********7
          pattern: ""
        type:
          type: string
          nullable: false
          description: Message parameter type
          maxLength: *********7
          pattern: ""

  responses:
    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ValidationProblemDetails"
          examples:
            It-is-a-bad-request:
              value:
                type: "https://errors.kmdelements.com/400"
                title: Bad Request
                status: 400
                detail: "Invalid request"
                instance: /resouces-path/1
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            You-shall-not-pass:
              value:
                type: "https://errors.kmdelements.com/401"
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources-path/1
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ForbiddenExample:
              value:
                type: "https://errors.kmdelements.com/403"
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: "/resources/1"
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/404"
                title: Not Found
                status: 404
                detail: Not Found
                instance: "/resources/1"
    "422":
      description: 422 Unprocessable Entinty
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetailsWithErrors"
          examples:
            UnprocessableEntity:
              value:
                errors:
                  - code: SthIsWrongCode
                    defaultMessage: "This {object} is incorrect"
                    parameters:
                      "object": "PriceDefinition"
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 2678400 # 31 days
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            Too-fast-too-quickly-too-soon:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 360
                detail: Rate limit is exceeded.
                instance: /resources-path/1
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            This-should-not-happen:
              value:
                type: "https://errors.kmdelements.com/500"
                title: Internal Server Error
                status: 500
                detail: "body.0.age: Value `Not Int` does not match format `int32`"
                instance: /resources-path/1
    "503":
      description: 503 Service Unavailable.
    "504":
      description: 504 Gateway Timeout.

  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
