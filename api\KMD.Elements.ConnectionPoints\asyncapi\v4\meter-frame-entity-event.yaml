asyncapi: 2.6.0
id: 'https://async.api.kmdelements.com/meter-frame'
info:
  title: MeterFrame changed flattened event
  version: 4.1.4
  contact:
    name: KMD Elements
    url: >-
      https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: 'https://www.kmd.net/terms-of-use'
  description: >
    MeterFrame entity is changed (created/edited/deleted) - an event is sent.
  x-maintainers: Team-MD-2
tags:
  - name: Team-MD-2
    description: Maintained by Team MD 2.
servers:
  local:
    url: localhost
    description: Local
    protocol: kafka
    protocolVersion: 2.6.0

defaultContentType: application/json

channels:
  'kmd.elements.{tenantId}.event.connection-points.meter-frame-entity-event.v4':
    description: Topic for changes on meter frames.
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: Information about MeterFrame changes (created/updated/deleted).
      description: Information about MeterFrame changes (created/updated/deleted).
      operationId: publishMeterFrameBusinessEntityEvent
      message:
        $ref: '#/components/messages/MeterFrameBusinessEntityEventMessage'
    subscribe:
      summary: Information about MeterFrame changes (created/updated/deleted).
      description: Information about MeterFrame changes (created/updated/deleted).
      operationId: receiveMeterFrameBusinessEntityEvent
      message:
        $ref: '#/components/messages/MeterFrameBusinessEntityEventMessage'
components:
  messages:
    MeterFrameBusinessEntityEventMessage:
      name: MeterFrameBusinessEntityEventMessage
      title: MeterFrameBusinessEntityEventMessage
      summary: Business Entity Event on change in MeterFrame
      contentType: application/json
      headers:
        $ref: "#/components/schemas/MessageHeaders"
      payload:
        $ref: '#/components/schemas/MessagePayload'
  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      required:
        - tenantId
        - esMessageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        esMessageId:
          name: es-message-id
          description: Unique message ID. The same message's ID is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    MessagePayload:
      title: MessagePayload
      name: MessagePayload
      additionalProperties: false
      type: object
      required:
        - eventType
        - entityFamily
        - entityType
        - timestamp
        - version
        - data
      properties:
        eventType:
          type: string
          pattern: "^(Updated|Deleted)$"
          description: Business event type.
          maxLength: 7
          minLength: 7
          example: "Updated"
        entityFamily:
          type: string
          pattern: "^(MeterFrame)$"
          description: Business event entity family.
          maxLength: 50
          minLength: 1
          example: MeterFrame
        entityType:
          type: string
          pattern: "^(MeterFrame)$"
          description: Business event entity type.
          maxLength: 150
          minLength: 1
          example: MeterFrame
        timestamp:
          allOf:
            - $ref: '#/components/schemas/DateTime'
          description: Business event timestamp.
        version:
          type: string
          pattern: "^(4\\.1\\.\\d+)$"
          description: Business entity event schema version
          nullable: false
          example: 4.1.0
        sysStartTime:
          name: es-sys-start-time
          description: System start time from SQL versioned table. The header is automatically set by TransactionalOutboxWorker.
          allOf:
            - $ref: "#/components/schemas/DateTimeNullable"
        previousSysStartTime:
          description: Previous system start time from SQL versioned table
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        data:
          $ref: "#/components/schemas/MeterFrame"

    # --------------------------------------------------------- ACTUAL DATA OBJECT -------------------------------------------------------------------------
    MeterFrame:
      $ref: "./schemas/meter-frame/MeterFrame.schema.yaml#/MeterFrame"
    # --------------------------------------------------------- COMMON DATA TYPES --------------------------------------------------------------------------
    DateTime:
      description: 'Date in UTC ISO 8601 format.'
      type: string
      format: date-time
      example: 2019-11-14T00:55:31.820Z

    DateTimeNullable:
      description: 'Date in UTC ISO 8601 format.'
      type: ['string', 'null']
      format: date-time
      example: 2019-11-14T22:00:00.000Z

  parameters:
    TenantId:
      description: Identifier of a tenant.
      schema:
        type: number
