{"name": "java-async-template", "version": "0.0.1", "description": "Java Async Spring Boot Kafka code template", "private": true, "generator": {"renderer": "react", "apiVersion": "v2", "supportedProtocols": ["kafka"], "parameters": {"package": {"description": "package name, if not exists use dk.kmd", "required": false, "default": "dk.kmd"}, "instantDatePreset": {"description": "Enable Instant date preset", "required": false, "default": "false"}, "additionalPropertiesActive": {"description": "Enable additional properties", "required": false, "default": "false"}, "serializablePreset": {"description": "Enable Serializable preset", "required": false, "default": "false"}, "sparkDtoLocation": {"description": "Package location for SparkDTO interface", "required": false}}}, "scripts": {"debug:generator": "asyncapi generate fromTemplate ./test/fixtures/asyncapi.yml ./ -o test/project --force-write", "format": "prettier --plugin=prettier-plugin-java --write \"./generated/**/*.java\""}, "dependencies": {"@asyncapi/generator-hooks": "^0.1.0", "@asyncapi/generator-react-sdk": "^1.1.2", "@asyncapi/modelina": "^5.2.3"}, "devDependencies": {"prettier": "3.6.2", "prettier-plugin-java": "2.7.1"}}