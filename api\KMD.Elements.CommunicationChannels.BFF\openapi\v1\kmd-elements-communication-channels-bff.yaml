openapi: 3.0.3
info:
  title: KMD.Elements.CommunicationChannels.BFF
  description: |-
    # KMD Elements CommunicationChannels Backend For Frontend API

    Stability level: PREVIEW<br/>
    <br/>
    The **KMD Elements Communication Channels BFF** is part of the KMD Element product.<br/>

    ## Capabilities
    The API allows to:
    - prepare content of an email
    - send email
    ---

  termsOfService: "https://www.kmd.net/terms-of-use"

  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>

  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"

  version: "0.19"

  x-maintainers: Team-MO-1

servers:
  - url: "https://localhost:5081"
    description: Localhost

security:
  - Jwt: []

tags:
  - name: CommunicationChannelsBFF
    description: Enables communication between frontend and backend services.
  - name: Users
    description: |-
      Methods allow to
        * Retrieve users list
paths:
  /v1/merged-contents:
    post:
      tags:
        - CommunicationChannelsBFF
      operationId: orderContentMergingProcess
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
      x-authorization:
      description: Puts command on kafka to prepare message content file with merged data, based on `templateId`, `fileId` and provided specific data.
      summary: Puts command on kafka to prepare message content file with merged data.
      requestBody:
        description: Puts command on kafka to prepare message content file with merged data, based on TemplateId, FileId and provided specific data.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PrepareMessageContent"
            examples:
              Sample-of-request-body:
                $ref: "#/components/examples/PrepareMessageContentExample"
        required: true
      responses:
        "202":
          description: The operation of merging the content with the template has been queued..
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PrepareMessageContentResponse"
              examples:
                Prepare-message-content-response-example:
                  $ref: "#/components/examples/PrepareMessageContentResponseExample"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v1/merged-contents/{fileId}/status:
    get:
      tags:
        - CommunicationChannelsBFF
      operationId: checkContentMergingResultStatus
      x-authorization:
      description: Checks if file the content has been merged with the template, based on `fileId``.
      summary: Endpoint called from frontend to check if file is merged.
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/FileIdInPath"
      responses:
        "200":
          description: Success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FileStatus"
              examples:
                Check-if-file-ready-response-example:
                  $ref: "#/components/examples/FileStatusExample"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v1/emails:
    post:
      tags:
        - CommunicationChannelsBFF
      operationId: sendEmail
      x-authorization: CommunicationChannelEmail.Write
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
      description: Endpoint called to place event on kafka with all data required.
      summary: Endpoint called to send an email
      requestBody:
        description: Email content with meta data needed to send an email
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmailEnvelope"
            examples:
              Sample-of-request-body:
                $ref: "#/components/examples/SendEmailExample"
        required: true
      responses:
        "202":
          description: The email sending operation has been queued..
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v1/merged-emails:
    post:
      tags:
        - CommunicationChannelsBFF
      operationId: saveMetadataAndMergeEmail
      x-authorization: CommunicationChannelEmail.Write
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
      description: Endpoint called to merge an email and save it's metadata to Redis.
      summary: Endpoint called to merge an email and save it's metadata to Redis. Returns id of merged file that will be stored in DocumentStorage.
      requestBody:
        description: Email data
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmailMetadata"
            examples:
              Sample-of-request-body:
                $ref: "#/components/examples/EmailMetadataExample"
        required: true
      responses:
        "202":
          description: The email merging operation has been queued
          content:
            application/json:
              schema:
                type: string
                format: uuid
                description: Id of merged file that will be stored in DocumentStorage.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v1/merged-emails/{fileId}/metadata:
    get:
      tags:
        - CommunicationChannelsBFF
      operationId: getEmailMetadata
      x-authorization: CommunicationChannelEmail.Write
      description: Gets email's metadata, based on `fileId`.
      summary: Endpoint called from frontend to get email's metadata.
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/FileIdInPath"
      responses:
        "200":
          description: Success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmailMetadataResponse"
              examples:
                Get-email-metadata-example:
                  $ref: "#/components/examples/EmailMetadataResponseExample"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v1/communication-processes/{processId}:
    get:
      tags:
        - CommunicationChannelsBFF
      operationId: getCommunicationProcess
      x-authorization: CommunicationChannelEmail.Read
      description: Gets data about specific process.
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/ProcessId"
      summary: Gets data about specific process.
      responses:
        "200":
          description: Success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommunicationProcessDetails"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

    patch:
      summary: Updates process details of communication process, by process id.
      operationId: updateCommunicationProcessDetails
      description: Updates details of communication process with given payload, by process id.
      tags:
        - CommunicationChannelsBFF
      x-authorization: CommunicationChannelEmail.Write
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/ProcessId"
      requestBody:
        required: true
        description: Model with new process details.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchProcessDetailsModel"
      responses:
        "204":
          description: "Communication process has been updated successfully"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]

  /v1/application-users/list:
    post:
      tags:
        - Users
      summary: Returns paged list of users.
      description: Returns paged list of users.
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
      operationId: getApplicationUsersList
      x-authorization: CommunicationChannelEmail.Read
      requestBody:
        description: Search criteria.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Browse'
        required: true
      responses:
        '200':
          description: Application users found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsersListPagedResult'
        "400":
          $ref: "#/components/responses/400"
        '401':
          $ref: '#/components/responses/401'
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/communication-processes/{processId}/commands/cancel-process:
    post:
      tags:
        - CommunicationChannelsBFF
      x-authorization: CommunicationChannelEmail.Write
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/ProcessId"
      operationId: cancelProcess
      description: Cancels process.
      summary: Cancels process.
      responses:
        "204":
          description: 204 Cancel process request handled successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/communication-processes/{processId}/commands/complete-process:
    post:
      tags:
        - CommunicationChannelsBFF
      x-authorization: CommunicationChannelEmail.Write
      parameters:
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/ProcessId"
      operationId: completeProcess
      description: Complete process.
      summary: Completes process.
      responses:
        "204":
          description: 204 Complete process request handled successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
components:
  parameters:
    FileIdInPath:
      name: fileId
      in: path
      description: External id.
      required: true
      schema:
        description: External id.
        type: string
        format: uuid
      example: 30c26e2f-1662-4117-b6a1-3be5f5cff116
    ProcessId:
      required: true
      name: processId
      in: path
      description: Id of process in process center.
      schema:
        type: string
        format: uuid
      example: '09001C06-6C61-4D71-A5B3-ECB0E0E230F0'
    MessageId:
      name: es-message-id
      description: Unique message ID. The same message id is used when resending the message.
      in: header
      schema:
        type: string
        format: uuid
      required: true
      example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
    CorrelationId:
      name: es-correlation-id
      description: |
        This is used to "link" messages together. This can be supplied on a request, so
        that the client can correlate a corresponding reply message.
        The server will place the incoming X-Correlation-ID value as the X-Correlation-ID
        on the outgoing reply. If not supplied on the request, the X-Correlation-ID of the
        reply should be set to the value of the X-Message-ID that was used on the request, if present.
        Given that the X-Correlation-ID is used to ‘link’ messages together,
        it may be reused on more than one message.
      in: header
      schema:
        type: string
        format: uuid
      required: false
      example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d
  schemas:
    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          pattern: ".*"
          maxLength: 64
          nullable: true
          example: "https://errors.kmdelements.com/500"
        title:
          description: "A short, human-readable summary of the problem type."
          type: string
          pattern: ".*"
          maxLength: 1024
          nullable: true
          example: Error short description
        status:
          description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
          type: integer
          format: int32
          minimum: 400
          maximum: 599
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          pattern: ".*"
          maxLength: 1024
          nullable: true
          example: Description what exactly happened
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          pattern: ".*"
          maxLength: 1024
          nullable: true
          example: /resources-path/1
      additionalProperties: false
    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          properties:
            error:
              type: string
              description: Error message.
              pattern: ".*"
              maxLength: 1000

    PrepareMessageContent:
      title: PrepareMessageContent
      type: object
      description: An object that contains data TemplateId and data to merge into template file
      properties:
        templateId:
          type: string
          format: uuid
          description: Id of template file, it can be obtained from Communication Templates Service
          example: 30c26e2f-1662-4117-b6a1-3be5f5cff116
        mergeDataObject:
          type: object
          description: Data to merge into template file
          example:
            Name: ExampleName,
            Surname: ExampleSurname,
            Phonenumber: ***********,
            Birthdate: 12-10-1990,
            Address:
              City: Warsaw,
              Region: Mazowieckie,
              Country: Poland,
              PostalCode: 00-100,
              Street: ExampleStreet,
              HomeNumber: 11,
              NestedObject:
                nestedfield: test1
      additionalProperties: false
      required:
        - templateId
        - mergeDataObject

    EmailEnvelope:
      title: EmailEnvelope
      type: object
      description: Object with content and meta-data needed to send an email
      properties:
        content:
          type: string
          maxLength: 1000000
          description: Content of the email
          example: This is an example of email content that will be sent
          pattern: ""
        sender:
          type: string
          minLength: 1
          maxLength: 320
          description: Sender of the email
          format: email
          oneOf:
            - $ref: "#/components/schemas/EmailAddress"
        subject:
          type: string
          description: Subject of the email (RFC 2822 states that the maximum number of characters in a subject line is 998 characters. However, a lot of email clients will impose a 255/256 character limit).
          minLength: 0
          maxLength: 998
          pattern: ".*"
          nullable: false
          example: This is a test email subject
        recipients:
          type: array
          description: Array of email recipients
          maxItems: 50
          items:
            $ref: "#/components/schemas/EmailAddress"
        correlationId:
          type: string
          format: uuid
          description: Technical id used for identifying email. It's created by domain which is calling this endpoint.
          example: 30c26e2f-1662-4117-b6a1-3be5f5cff111
        templateId:
          type: string
          format: uuid
          description: Id of template file from Communication Templates Service
          example: 27ee281d-97f8-4599-942f-ce053c74c83e
        relatedObjectId:
            $ref: '#/components/schemas/RelatedObjectId'
        relatedObjectType:
            $ref: '#/components/schemas/RelatedObjectType'

      additionalProperties: false
      required:
        - content
        - sender
        - subject
        - recipients
        - correlationId
        - templateId
        - relatedObjectId
        - relatedObjectType

    EmailMetadata:
      title: EmailMetadata
      type: object
      description: Object with metadata of an email and data to merge
      properties:
        templateId:
          type: string
          format: uuid
          description: Id of template file, it can be obtained from Communication Templates Service
          example: 30c26e2f-1662-4117-b6a1-3be5f5cff116
        subject:
          type: string
          description: Subject of the email (RFC 2822 states that the maximum number of characters in a subject line is 998 characters. However, a lot of email clients will impose a 255/256 character limit).
          minLength: 0
          maxLength: 998
          nullable: false
          pattern: ".*"
          example: This is a test email subject
        recipients:
          type: array
          description: Array of email recipients
          maxItems: 50
          items:
            $ref: "#/components/schemas/EmailAddress"
        mergeDataObject:
          type: object
          description: Data to merge into template file
          example:
            Name: ExampleName,
            Surname: ExampleSurname,
            Phonenumber: ***********,
            Birthdate: 12-10-1990,
            Address:
              City: Warsaw,
              Region: Mazowieckie,
              Country: Poland,
              PostalCode: 00-100,
              Street: ExampleStreet,
              HomeNumber: 11,
              NestedObject:
                nestedfield: test1
        relatedObjectId:
          $ref: '#/components/schemas/RelatedObjectId'
        relatedObjectType:
          $ref: '#/components/schemas/RelatedObjectType'
        parentProcessDetails:
          $ref: "#/components/schemas/ParentProcessDetails"
        attachments:
          type: array
          description: Array of attachment IDs
          maxItems: 50
          items:
            $ref: "#/components/schemas/Guid"
      additionalProperties: false
      required:
        - templateId
        - subject
        - mergeDataObject
        - recipients
        - relatedObjectId
        - relatedObjectType
        - parentProcessDetails

    EmailMetadataResponse:
      title: EmailMetadataResponse
      type: object
      description: Object with metadata of an email
      properties:
        senders:
          description: Possible senders of an email
          type: array
          items:
            type: string
            format: email
            minLength: 1
            maxLength: 320
            example: <EMAIL>
          maxItems: 50
        templateName:
          description: Name of communication template
          type: string
          minLength: 1
          maxLength: 256
          pattern: ".*"
          example: CustomerTemplate1
        templateId:
          type: string
          format: uuid
          description: Id of template file from Communication Templates Service
          example: 30c26e2f-1662-4117-b6a1-3be5f5cff116
        subject:
          type: string
          description: Subject of the email (RFC 2822 states that the maximum number of characters in a subject line is 998 characters. However, a lot of email clients will impose a 255/256 character limit).
          minLength: 0
          maxLength: 998
          nullable: false
          pattern: ".*"
          example: This is a test email subject
        recipients:
          type: array
          description: Array of email recipients
          maxItems: 50
          items:
            $ref: "#/components/schemas/EmailAddress"
        mergeDataObject:
          type: object
          description: Data to merge into template file
          example:
            Name: ExampleName,
            Surname: ExampleSurname,
            Phonenumber: ***********,
            Birthdate: 12-10-1990,
            Address:
              City: Warsaw,
              Region: Mazowieckie,
              Country: Poland,
              PostalCode: 00-100,
              Street: ExampleStreet,
              HomeNumber: 11,
              NestedObject:
                nestedfield: test1
        relatedObjectId:
            $ref: '#/components/schemas/RelatedObjectId'
        relatedObjectType:
            $ref: '#/components/schemas/RelatedObjectType'
      additionalProperties: false
      required:
        - templateId
        - subject
        - mergeDataObject
        - recipients
        - relatedObjectId
        - relatedObjectType

    PrepareMessageContentResponse:
      title: PrepareMessageContentResponse
      type: object
      description: Object with fileId used to get output file from document storage
      properties:
        fileId:
          type: string
          format: uuid
          description: External id used to download output file
          example: 30c26e2f-1662-4117-b6a1-3be5f5cff111
      additionalProperties: false

    FileStatus:
      title: FileStatus
      type: object
      description: An object with information if file is merged
      properties:
        mergingProcessFinished:
          type: boolean
          description: True or false value indicating if merging process is finished
          example: true
        resultStatus:
          $ref: "#/components/schemas/ResultStatus"
      additionalProperties: false
    ResultStatus:
      enum:
        - None
        - Success
        - MissingData
        - TemplateNotFound
        - ServerError
      type: string
      description: The status of the data merge operation to the template.
      example: Success

    EmailAddress:
      title: EmailAddress
      format: email
      type: string
      description: Email address
      minLength: 1
      maxLength: 320
      nullable: false
      example: <EMAIL>

    RelatedObjectId:
        description: Id of object passed for merging.
        type: string
        minLength: 1
        maxLength: 128
        pattern: ""

    RelatedObjectType:
        type: string
        description: Related business types
        enum:
          - ConnectionPoint
          - MeteringPoint
          - Meterframe
          - Interruption
          - Customer
          - Meter
          - InstallationForm
          - TimeSeries
          - PriceGroup
          - Process
    CommunicationProcessDetails:
      title: CommunicationProcessDetails
      type: object
      description: Object containing communication process details.
      additionalProperties: false
      properties:
        channel:
          $ref: "#/components/schemas/Channel"
        supplyType:
          $ref: "#/components/schemas/SupplyType"
        processState:
          $ref: "#/components/schemas/ProcessState"
        processStateReason:
          $ref: "#/components/schemas/ProcessStateReason"
        started:
          description: Process started date.
          format: date-time
          type: string
        completed:
          description: Process finished date.
          format: date-time
          type: string
          nullable: true
        assignedTo:
          $ref: "#/components/schemas/NullableLongString"
        assignedToUserId:
          $ref: "#/components/schemas/NullableGuid"
        startedBy:
          $ref: "#/components/schemas/LongString"
        processId:
          $ref: '#/components/schemas/Guid'
        area:
          $ref: '#/components/schemas/Area'
        template:
          $ref: "#/components/schemas/LongString"
        sendEmailMilestone:
            $ref: "#/components/schemas/SendEmailMilestone"
        receivedEmails:
          description: Received emails of communication process.
          type: array
          maxItems: 100
          items:
            $ref: "#/components/schemas/ReceivedEmail"
        processSteps:
          type: array
          description: List of process steps.
          maxItems: 20
          items:
            $ref: "#/components/schemas/ProcessStep"
        parentProcess:
            $ref: "#/components/schemas/ParentProcessDetails"

    ReceivedEmail:
      type: object
      description: Received email object.
      additionalProperties: false
      properties:
        from:
          $ref: "#/components/schemas/LongString"
        to:
          $ref: "#/components/schemas/LongString"
        subject:
          $ref: "#/components/schemas/Subject"
        content:
          $ref: "#/components/schemas/Content"
        timestamp:
          description: Milestone started date.
          format: date-time
          type: string
        isRead:
          type: boolean
          description: Indicates if email is read.
          example: false

    ProcessStep:
      type: object
      additionalProperties: false
      description: Process step
      properties:
        id:
          description: Identifier of process step.
          type: integer
          format: int32
          minimum: 1
          maximum: 2147483647
        processStepType:
          $ref: "#/components/schemas/ProcessStepType"
        processStepStatus:
          $ref: "#/components/schemas/ProcessStepStatus"
        stateReason:
          $ref: "#/components/schemas/ProcessStepStateReason"
        createdAt:
          description: A date when process step was created.
          format: date-time
          type: string
          nullable: false
        updatedAt:
          description: A date of the latest update.
          format: date-time
          type: string
          nullable: true
        cancelledBy:
          description: Cancelled by user is used for identifying who cancelled the process. Format - Name Surname (<EMAIL>). It's filled by this service.
          type: string
          nullable: true
          minLength: 1
          maxLength: 256
          pattern: "^.*$"

    ProcessStepType:
      type: string
      nullable: false
      description: |
        | Type of process step
        | Code name               | Description            |
        |-------------------------|------------------------|
        | SendEmail               | Send email             |
        | ReceiveEmail            | Receive email          |
        | ProcessCancelled        | Process cancelled      |
      enum:
       - SendEmail
       - ReceiveEmail
       - ProcessCancelled

    ProcessStepStatus:
      type: string
      nullable: true
      description: |
        | Status of process step
        | Code name               | Description                                                                                                                              |
        |-------------------------|---------------------------------------------------------------------------------------------------------|
        | Success                 | Defines the state of the process step based on the result of successful action defined by process type. |
        | Failed                  | Defines the failed state of the process step           .                                                |
      enum:
        - Success
        - Failed

    ProcessStepStateReason:
      type: string
      nullable: true
      description: |
        The reason why ProcessStep is in its current state. Each reason is associated with a specific code name and has a corresponding description that clarifies the context or issue leading to the state.
        | Code name                                             | Description                                                        |
        |-------------------------------------------------------|--------------------------------------------------------------------|
        | Sent                                                  | Email sent                                                         |
        | Delivered                                             | Email delivered                                                    |
        | Bounced                                               | Email bounced                                                      |
        | Deferred                                              | Email deferred                                                     |
      enum:
        - Sent
        - Delivered
        - Bounced
        - Deferred

    SupplyType:
      type: string
      nullable: false
      description: |-
        Specifies the type selection type when process was created. The options are as follows:
        | Code name   | Description       |
        |-------------|-------------------|
        | All         | All supply types.   |
        | Electricity | Electricity supply. |
        | Water       | Water supply.       |
        | Heating     | Heating supply.     |
      enum:
        - All
        - Electricity
        - Water
        - Heating
    ProcessState:
      type: string
      nullable: false
      description: |
        Describes the current state of a process within a system. Each state signifies a specific phase or condition of the process, as outlined below:

        | Code name               | Description                                 |
        |-------------------------|---------------------------------------------|
        | InProgress              | The process is currently underway.               |
        | Failed                  | The process has encountered an error and failed.  |
        | RequiresManualHandling  | The process requires manual intervention to proceed. |
        | Complete                | The process has successfully completed.            |
        | Cancelled               | The process has been successfully cancelled.            |
      enum:
        - InProgress
        - Failed
        - RequiresManualHandling
        - Complete
        - Cancelled
    ProcessStateReason:
      type: string
      nullable: true
      description: |
        Describes the reason of current process state:

        | Code name               | Description                                 |
        |-------------------------|---------------------------------------------|
        | Unread                  | The email is unread.                        |
        | CancelledByUser         | The email is cancelled by user.             |
        | TechnicalError          | Technical error occurred.                   |
        | Email sent              | The email sent successfully.                |

      enum:
        - Unread
        - CancelledByUser
        - TechnicalError
        - EmailSent
    Channel:
      type: string
      nullable: false
      description: Defines which channel the process was created for.
      enum:
        - Email
        - SMS
        - Document
    Area:
      type: string
      nullable: false
      description: Defines which area the process was created for.
      enum:
        - BailiffCases
        - Elections
        - Interruptions
        - InstallationForms
        - Meters
        - WorkOrders
        - General
    LongString:
      description: Max 128 characters.
      type: string
      nullable: false
      maxLength: 128
      pattern: "^.*$"
    NullableLongString:
      description: Max 128 characters.
      type: string
      nullable: true
      maxLength: 128
      pattern: "^.*$"
    Guid:
      description: Guid identifier.
      type: string
      nullable: false
      maxLength: 128
      format: uuid
    NullableGuid:
      description: Guid identifier.
      type: string
      nullable: true
      maxLength: 128
      format: uuid

    SendEmailMilestone:
      description: Send email milestone of communication process.
      type: object
      additionalProperties: false
      properties:
        sentBy:
          $ref: "#/components/schemas/NullableLongString"
        from:
          $ref: "#/components/schemas/LongString"
        to:
          description: Array of recipients
          type: array
          maxItems: 50
          items:
            $ref: "#/components/schemas/LongString"
        subject:
          $ref: "#/components/schemas/Subject"
        content:
          $ref: "#/components/schemas/Content"
        attachments:
          description: Array of attachments
          type: array
          maxItems: 100
          items:
            $ref: "#/components/schemas/LongString"
        timestamp:
          description: Milestone started date.
          format: date-time
          type: string
        attachmentIds:
          description: Array of attachments ids
          type: array
          maxItems: 20
          items:
            $ref: "#/components/schemas/Guid"
    Subject:
      type: string
      description: Subject of the email (RFC 2822 states that the maximum number of characters in a subject line is 998 characters. However, a lot of email clients will impose a 255/256 character limit).
      minLength: 0
      maxLength: 998
      pattern: ".*"
      nullable: false
      example: This is a test email subject
    Content:
      type: string
      maxLength: 1000000
      description: Content of the email
      example: This is an example of email content that will be sent
      pattern: ""
    Browse:
      description: Browse object.
      allOf:
        - $ref: '#/components/schemas/PagedQueryModel'
        - type: object
          description: Browse object.
          properties:
            filter:
              $ref: '#/components/schemas/Filter'
          additionalProperties: false

    UsersListPagedResult:
      description: Users list paged result.
      allOf:
        - $ref: '#/components/schemas/PagedResult'
        - type: object
          description: User details search result.
          properties:
            results:
              type: array
              description: Array of users objects.
              items:
                $ref: '#/components/schemas/User'
              maxItems: 256
          additionalProperties: false
    PagedResult:
      type: object
      description: Object to provide pagination in results.
      properties:
        currentPage:
          type: integer
          description: Number of current page.
          format: int32
          minimum: 0
          maximum: 2147483647
        pageCount:
          type: integer
          description: Number of page count.
          format: int32
          minimum: 0
          maximum: 2147483647
        pageSize:
          type: integer
          description: Number of page size.
          format: int32
          minimum: 1
          maximum: 1000
        rowCount:
          type: integer
          description: Number of row count.
          format: int32
          minimum: 0
          maximum: 2147483647
      additionalProperties: false

    PagedQueryModel:
      type: object
      description: Paged query object.
      properties:
        page:
          $ref: '#/components/schemas/Page'
        sort:
          $ref: '#/components/schemas/Sort'
      additionalProperties: false

    Page:
      type: object
      description: Page object.
      properties:
        number:
          type: integer
          description: Number of page.
          format: int32
          minimum: 0
          maximum: 2147483647
        size:
          type: integer
          description: Size of page.
          format: int32
          minimum: 10
          maximum: 1000
      additionalProperties: false

    Sort:
      type: object
      description: Sort object.
      properties:
        propertyName:
          type: string
          pattern: "^.*$"
          maxLength: 255
          description: Field name for sort.
        direction:
          $ref: '#/components/schemas/SortDirection'
      additionalProperties: false

    SortDirection:
      type: string
      description: Direction of sort (asc/desc).
      enum:
        - Asc
        - Desc

    Filter:
      type: object
      description: Filter object.
      properties:
        properties:
          type: array
          description: Array of property filters.
          items:
            $ref: '#/components/schemas/PropertyFilter'
          maxItems: 255
      additionalProperties: false

    PropertyFilter:
      type: object
      description: Property filter object.
      properties:
        name:
          type: string
          pattern: "^.*$"
          maxLength: 255
          description: Name of property filter.
        condition:
          $ref: '#/components/schemas/FilterCondition'
        value:
          type: object
          description: Property filter value.
      additionalProperties: false

    FilterCondition:
      type: string
      description: Type of filter condition.
      enum:
        - Like
        - Equal
        - In
        - LessThan
        - LessThanOrEqual
        - GreaterThan
        - GreaterThanOrEqual
        - StartsWith

    User:
      type: object
      description: User object.
      properties:
        userId:
          type: string
          description: User identifier.
          maxLength: 256
          pattern: "^.*$"
        name:
          type: string
          description: User name.
          maxLength: 256
          pattern: "^.*$"
          nullable: true
      additionalProperties: false

    PatchProcessDetailsModel:
      type: object
      additionalProperties: false
      description: Model with properties to update.
      properties:
        assignedTo:
          $ref: "#/components/schemas/NullableLongString"
        assignedToUserId:
          type: string
          format: uuid
          description: Id of user assigned to process.

    ParentProcessDetails:
      type: object
      additionalProperties: false
      description: Parent process which wants to start a new communication process.
      nullable: true
      properties:
        id:
          type: string
          description: Id of parent process.
          nullable: true
          minLength: 1
          maxLength: 128
          pattern: "^.*$"
        source:
          type: string
          description: Name of domain which wants to start a new communication process.
          nullable: false
          minLength: 1
          maxLength: 128
          pattern: "^(InstallationForms|ManualProcess)$"
        action:
          type: string
          description: Type of action to be performed on parent process.
          nullable: true
          minLength: 1
          maxLength: 128
          pattern: "^(NewInstallation|Extension|SealBreach|ChangeMeter|MoveMeter|Termination|ChangeBranchLine|EnergyProduction)$"
        onBehalfOf:
          description: The name of the user, process, or system on whose behalf the process was created.
          type: string
          minLength: 1
          maxLength: 256
          pattern: "^.*$"
          nullable: false

  responses:
    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ValidationProblemDetails"
          examples:
            BadRequestExample:
              value:
                type: "https://errors.kmdelements.com/400"
                title: Bad Request
                status: 400
                detail: "Invalid request"
                instance: /resouces-path/1
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            UnauthorizedExample:
              value:
                type: "https://errors.kmdelements.com/401"
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources-path/1
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ForbiddenExample:
              value:
                type: "https://errors.kmdelements.com/403"
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources-path/1
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/404"
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources-path/1
    "422":
      description: 422 Unprocessable Entity.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            UnprocessableEntityExample:
              value:
                type: "https://errors.kmdelements.com/422"
                title: Unprocessable Entity
                status:
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            format: int64
            example: 360
            minimum: 1
            maximum: 1000
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            TooManyRequestsExample:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 400
                detail: Rate limit is exceeded.
                instance: /resources-path/1
    "499":
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/499"
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: /resources-path/1
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            InternalServerErrorExample:
              value:
                type: "https://errors.kmdelements.com/500"
                title: Internal Server Error
                status: 500
                detail: "body.0.age: Value `Not Int` does not match format `int32`"
                instance: /resources-path/1
    "503":
      description: 503 Service Unavailable.
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            type: integer
            format: int64
            example: 360
            minimum: 1
            maximum: 1000
    "504":
      description: 504 Gateway Timeout.

  examples:
    SendEmailExample:
      value:
        {
          content: This is an example of email content that will be sent,
          sender: <EMAIL>,
          subject: This is a test email subject,
          recipients: ["<EMAIL>", "<EMAIL>"],
          correlationId: 6c9d8a3a-32bd-4a84-8cbb-12ab5ab57e31,
          templateId: 00ec7577-cc51-49b1-97be-f0262e2eb29b,
          relatedObjectId: 30c26e2f-1662-4117-b6a1-3be5f5cff111,
          relatedObjectType: "ConnectionPoint"
        }

    EmailMetadataExample:
      value:
        {
          templateId: 30c26e2f-1662-4117-b6a1-3be5f5cff111,
          subject: Company meeting,
          recipients: ["<EMAIL>", "<EMAIL>"],
          mergeDataObject:
            {
              "Name": "Examplename",
              "Surname": "Examplesurname",
              "Phonenumber": "***********",
              "Birthdate": "12-10-1990",
              "Address":
                {
                  "City": "Warsaw",
                  "Region": "Mazowieckie",
                  "Country": "Poland",
                  "PostalCode": "00-100",
                  "Street": "ExampleStreet",
                  "HomeNumber": "11",
                  "NestedObject": { "nestedfield": "test1" },
                },
            },
          relatedObjectId: 30c26e2f-1662-4117-b6a1-3be5f5cff111,
          relatedObjectType: "ConnectionPoint",
          parentProcessDetails:
            {
              "id": "cd0133ae-be17-4771-b652-a72d570fa3ce",
              "source": "InstallationForms",
              "action": "NewInstallation",
              "onBehalfOf": "cd0133ae-4771-b652-be17-a72d570fa3ce"
            },
          attachments: ["9bd0a99b-93c2-4e08-a58b-9b4b898fc8bd", "70cb3cc8-5b85-4e37-b5a4-daca2d1c8627"]
        }

    EmailMetadataResponseExample:
      value:
        {
          senders: ["<EMAIL>", <EMAIL>],
          templateName: customerTemplate1,
          templateId: 30c26e2f-1662-4117-b6a1-3be5f5cff111,
          subject: Company meeting,
          recipients: ["<EMAIL>", "<EMAIL>"],
          mergeDataObject:
            {
              "Name": "Examplename",
              "Surname": "Examplesurname",
              "Phonenumber": "***********",
              "Birthdate": "12-10-1990",
              "Address":
                {
                  "City": "Warsaw",
                  "Region": "Mazowieckie",
                  "Country": "Poland",
                  "PostalCode": "00-100",
                  "Street": "ExampleStreet",
                  "HomeNumber": "11",
                  "NestedObject": { "nestedfield": "test1" },
                },
            },
          relatedObjectId: 30c26e2f-1662-4117-b6a1-3be5f5cff111,
          relatedObjectType: "ConnectionPoint"
        }

    PrepareMessageContentExample:
      value:
        templateId: 30c26e2f-1662-4117-b6a1-3be5f5cff111
        mergeDataObject:
          {
            "Name": "Examplename",
            "Surname": "Examplesurname",
            "Phonenumber": "***********",
            "Birthdate": "12-10-1990",
            "Address":
              {
                "City": "Warsaw",
                "Region": "Mazowieckie",
                "Country": "Poland",
                "PostalCode": "00-100",
                "Street": "ExampleStreet",
                "HomeNumber": "11",
                "NestedObject": { "nestedfield": "test1" },
              },
          }

    PrepareMessageContentResponseExample:
      value:
        fileId: 6c9d8a3a-32bd-4a84-8cbb-12ab5ab57e31

    FileStatusExample:
      value:
        mergingProcessFinished: true
        resultStatus: Success

  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
