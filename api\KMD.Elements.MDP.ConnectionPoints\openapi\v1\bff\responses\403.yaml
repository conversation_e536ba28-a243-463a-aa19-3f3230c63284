description: Forbidden
content:
  application/problem+json:
    schema:
      description: Forbidden
      allOf:
        - $ref: "../schemas/ProblemDetailsWithErrors.yaml"
    examples:
      ForbiddenExample:
        value:
          type: https://errors.kmdelements.com/403
          title: Forbidden
          status: 403
          detail: User is not authorized to access this resource.
          instance: /resouces-path/1
          errors:
            - errorCode: "YouDoNotHavePermissionsToDeleteARegister"
              errorMessageTemplate: "You do not have permissions to delete a register."
              errorMessage: "You do not have permissions to delete a register."
