TechnicalMeteringPoint:
  type: object
  description: |
    Technical Metering Point root / container object. Identified with MeteringPointId.
    There can be multiple Technical Metering Point Versions for single Technical Metering Point "container", which is identified within meteringPointProperty.
    Technical Metering Point can also have another Technical Metering Point as a Parent - identified by parentMeteringPointId.
    If this property is null, it means, that Metering Point has no parent assigned.
  additionalProperties: false
  required:
    - entityId
    - supplyType
  properties:
    # - REQUIRED FOR BUSINESS EVENTS
    entityId:
      description: Id of Metering Point (GSRN number).
      type: string
      pattern: ^\d{18}$
      minLength: 18
      maxLength: 18
      example: '571313190000020132'
    # - END OF REQUIRED FOR BUSINESS EVENTS
    supplyType:
      type: string
      description: Type of Supply - Electricity / Heating / Water.
      pattern: "^(Electricity|Heating|Water)$"
      minLength: 5
      maxLength: 11
      example: Electricity
    connectionPointId:
      description: Connection point ID.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130002
      x-reference:
        entityFamily: "ConnectionPoint"
        entityType: "ConnectionPoint"
    connectionPointNumber:
      description: Connection point number.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 123456789
    parentMeteringPointId:
      description: Parent Metering Point Id.
      type: [ 'string', 'null' ]
      pattern: ^\d{18}$
      minLength: 18
      maxLength: 18
      example: '571313190000020132'
      x-reference:
        entityFamily: "TechnicalMeteringPoint"
        entityType: "TechnicalMeteringPoint"
    typeOfMeteringPoint:
      description: Type of metering point value list value.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130002
