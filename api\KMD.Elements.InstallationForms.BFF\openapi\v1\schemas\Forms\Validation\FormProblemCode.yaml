title: FormProblemCode
description: Form problem code
type: string
nullable: false
x-extensible-enum:
  - Unknown
  - CarUpdateChangedDarId
  - NullValidator
  - NotNullValidator
  - EanNumberValidator
  - CvrNumberValidator
  - NotEmptyValidator
  - LengthValidator
  - EmailValidator
  - PhoneValidator
  - WorkOrderValidationFailed
  - WorkOrderCreationProblem
  - CarNotFound
  - CarInvalidSupplyType
  - CarHasNoDetails
  - CarNoDarIdAssigned
  - CarInvalidDarStatus
  - CarError
  - CarNotActive
  - CarMultipleMatching
  - ConnectionPointNotFound
  - ConnectionPointInvalidSupplyType
  - ConnectionPointNotActive
  - ConnectionPointMultipleMatching
  - ConnectionPointError
  - MeterFrameNotFound
  - MeterFrameInvalidSupplyType
  - MeterFrameMeterMissing
  - MeteringPointMultipleMatching
  - MeteringPointNotFound
  - MeteringPointError
  - InvoicingError
  - AddressDetailsValidator
  - MasterDataByRowIdNotFound
  - MasterDataValidationFailed
  - MasterDataCreationProblem
  - AttachmentFromFormSystemHasInvalidFileName
  - AttachmentFromFormSystemHasNoFileName
  - AttachmentFromFormSystemExceedsSizeLimit
  - AttachmentFromFormSystemWithoutFileNameExceedsSizeLimit
  - AttachmentFromFormSystemAlreadyExists
  - AttachmentFromFormSystemWithoutFileNameAlreadyExists
  - AttachmentSyncFailedAccessForbidden
  - AttachmentSyncFailedAttachmentTypeNotEnabled
  - AttachmentFromFormSystemFailed
  - AttachmentFromFormSystemWithoutFileNameFailed
  - AttachmentFromElementsFailed
  - AttachmentFromElementsWithoutFileNameFailed
  - FormSystemValidationFailed
  - FormSystemError
  - FlagMeterSetupCannotBeUnsetByDso
  - FlagReadyForMeterCannotBeUnsetByDso
  - MeterInstalledFlagFailedToUpdateInFormSystem
  - SupplierSelectedFlagFailedToUpdateInFormSystem
  - ConnectionFeePaidFlagFailedToUpdateInFormSystem
  - ReadyForMeterFlagFailedToUpdateInFormSystem
  - SubmittedForProjectPlanningFlagFailedToUpdateInFormSystem
  - FormDataUpdateFailedInFormSystemDuringArchive
  - FormInstructionDataUpdateFailedInFormSystemDuringArchive
  - ChatMessageSyncFailed
  - ValueListItemMappingFromEltilmeldingCodeFailed
  - ValueListItemMappingToEltilmeldingCodeFailed
  - CommunicationTemplatesNoTemplateFound
  - CommunicationTemplatesMoreThanOneTemplateFound
  - RangeValidator
  - CarEmptyAddress
  - MarketParticipantsError
  - MarketParticipantsNoGridAreaFound
  - MarketParticipantsMoreThanOneGridAreaFound
  - MarketParticipantsCouldNotSetDefaultGridArea
  - ApplicationsMultipleWithSameType
  - MeterTransformerIdSetToOther
  - MultipleActiveMeterInputConnections
  - FlagDoesNotExist
  - MeterFrameMultipleMatching
  - MeterFrameMultipleMatchingByConnectionPointAndMeterNumber
  - MDPConnectionPointTemplateError
  - MDPConnectionPointTemplateNotFound
  - MeterFrameNotFoundByConnectionPointAndMeterNumber
  - ConnectionPointErrorSearchMeterFrame
  - ConnectionRightsReadError
  - InvoiceCreationFailedNoConnectionRightsToInvoice
  - InvoiceCreationFailedPricesDefinitionFetchError
  - InvoiceCreationFailedPriceElementForConnectionRightsCategoryIsMissing
  - InvoiceCreationFailedPriceElementForConnectionRightsCategoryIsDuplicated
  - InvoiceCreationFailedSingleInvoicingCreateInvoiceError
  - FormShouldNotHaveMeterNumberUponReceival
  - MeterNeedsReconfiguration
  - MeterNeedsChange
  - ConnectionFeeUpdateErrorInvalidInvoiceCurrency
  - ManuallySetConnectionFeeDiffersFromInvoiceTotals
  - ConnectionFeeUpdatedAfterInstruction
  - ConnectionPointSetButItWasNotExpected
  - SharedBranchLineMeterFrameMissingOwnedMainBranchLine
  - SharedBranchLineNestingCorrected
  - SharedBranchLineInfo
  - AddressEmpty
  - MeterFrameInvalidElectricityPurpose
  - NoConnectionPointFoundOnTheAddress
  - MeterNumberDoesNotMatchConnectionPoint
  - MeteringPointIdDoesNotMatchConnectionPoint
  - ConnectionPointNumberDoesNotMatchConnectionPoint
  - NotPossibleToMatchUniqueConnectionPoint
  - MeterNumberWasNotProvidedByFormSystemButRequired
  - MeterFramesError
  - MeterFramesNotFound
  - MeterRemovalNotStarted
  - ScopeOfDeliveryMissingError
  - MatchingConnectionPointWithNewlyCreatedElectricityConnectionStatusExistsInfo
  - ConnectionRightsBelowScopeOfDeliveryAfterConnectionFeePaidFlagSet
  - InvoiceCreationFailedPriceElementForStandardFeeIsMissing
  - InvoicingPriceDoesNotHavePriceDefinition
enum:
  - Unknown
  - CarUpdateChangedDarId
  - NullValidator
  - NotNullValidator
  - EanNumberValidator
  - CvrNumberValidator
  - NotEmptyValidator
  - LengthValidator
  - EmailValidator
  - PhoneValidator
  - WorkOrderValidationFailed
  - WorkOrderCreationProblem
  - CarNotFound
  - CarInvalidSupplyType
  - CarHasNoDetails
  - CarNoDarIdAssigned
  - CarInvalidDarStatus
  - CarError
  - CarNotActive
  - CarMultipleMatching
  - ConnectionPointNotFound
  - ConnectionPointInvalidSupplyType
  - ConnectionPointNotActive
  - ConnectionPointMultipleMatching
  - ConnectionPointError
  - MeterFrameNotFound
  - MeterFrameInvalidSupplyType
  - MeterFrameMeterMissing
  - MeteringPointMultipleMatching
  - MeteringPointNotFound
  - MeteringPointError
  - InvoicingError
  - AddressDetailsValidator
  - MasterDataByRowIdNotFound
  - MasterDataValidationFailed
  - MasterDataCreationProblem
  - AttachmentFromFormSystemHasInvalidFileName
  - AttachmentFromFormSystemHasNoFileName
  - AttachmentFromFormSystemExceedsSizeLimit
  - AttachmentFromFormSystemWithoutFileNameExceedsSizeLimit
  - AttachmentFromFormSystemAlreadyExists
  - AttachmentFromFormSystemWithoutFileNameAlreadyExists
  - AttachmentSyncFailedAccessForbidden
  - AttachmentSyncFailedAttachmentTypeNotEnabled
  - AttachmentFromFormSystemFailed
  - AttachmentFromFormSystemWithoutFileNameFailed
  - AttachmentFromElementsFailed
  - AttachmentFromElementsWithoutFileNameFailed
  - FormSystemValidationFailed
  - FormSystemError
  - FlagMeterSetupCannotBeUnsetByDso
  - FlagReadyForMeterCannotBeUnsetByDso
  - MeterInstalledFlagFailedToUpdateInFormSystem
  - SupplierSelectedFlagFailedToUpdateInFormSystem
  - ConnectionFeePaidFlagFailedToUpdateInFormSystem
  - ReadyForMeterFlagFailedToUpdateInFormSystem
  - SubmittedForProjectPlanningFlagFailedToUpdateInFormSystem
  - FormDataUpdateFailedInFormSystemDuringArchive
  - FormInstructionDataUpdateFailedInFormSystemDuringArchive
  - ChatMessageSyncFailed
  - ValueListItemMappingFromEltilmeldingCodeFailed
  - ValueListItemMappingToEltilmeldingCodeFailed
  - CommunicationTemplatesNoTemplateFound
  - CommunicationTemplatesMoreThanOneTemplateFound
  - RangeValidator
  - CarEmptyAddress
  - MarketParticipantsError
  - MarketParticipantsNoGridAreaFound
  - MarketParticipantsMoreThanOneGridAreaFound
  - MarketParticipantsCouldNotSetDefaultGridArea
  - ApplicationsMultipleWithSameType
  - MeterTransformerIdSetToOther
  - MultipleActiveMeterInputConnections
  - FlagDoesNotExist
  - MeterFrameMultipleMatching
  - MeterFrameMultipleMatchingByConnectionPointAndMeterNumber
  - MDPConnectionPointTemplateError
  - MDPConnectionPointTemplateNotFound
  - MeterFrameNotFoundByConnectionPointAndMeterNumber
  - ConnectionPointErrorSearchMeterFrame
  - ConnectionRightsReadError
  - InvoiceCreationFailedNoConnectionRightsToInvoice
  - InvoiceCreationFailedPricesDefinitionFetchError
  - InvoiceCreationFailedPriceElementForConnectionRightsCategoryIsMissing
  - InvoiceCreationFailedPriceElementForConnectionRightsCategoryIsDuplicated
  - InvoiceCreationFailedSingleInvoicingCreateInvoiceError
  - FormShouldNotHaveMeterNumberUponReceival
  - MeterNeedsReconfiguration
  - MeterNeedsChange
  - ConnectionFeeUpdateErrorInvalidInvoiceCurrency
  - ManuallySetConnectionFeeDiffersFromInvoiceTotals
  - ConnectionFeeUpdatedAfterInstruction
  - ConnectionPointSetButItWasNotExpected
  - SharedBranchLineMeterFrameMissingOwnedMainBranchLine
  - SharedBranchLineNestingCorrected
  - SharedBranchLineInfo
  - AddressEmpty
  - MeterFrameInvalidElectricityPurpose
  - NoConnectionPointFoundOnTheAddress
  - MeterNumberDoesNotMatchConnectionPoint
  - MeteringPointIdDoesNotMatchConnectionPoint
  - ConnectionPointNumberDoesNotMatchConnectionPoint
  - NotPossibleToMatchUniqueConnectionPoint
  - MeterNumberWasNotProvidedByFormSystemButRequired
  - MeterFramesError
  - MeterFramesNotFound
  - MeterRemovalNotStarted
  - ScopeOfDeliveryMissingError
  - MatchingConnectionPointWithNewlyCreatedElectricityConnectionStatusExistsInfo
  - ConnectionRightsBelowScopeOfDeliveryAfterConnectionFeePaidFlagSet
  - InvoiceCreationFailedPriceElementForStandardFeeIsMissing
  - InvoicingPriceDoesNotHavePriceDefinition