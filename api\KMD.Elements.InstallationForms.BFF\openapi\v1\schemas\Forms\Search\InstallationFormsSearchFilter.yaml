title: InstallationFormsSearchFilter
type: object
description: Defines pagination, search, and sorting parameters for querying installation forms.
additionalProperties: false
required:
  - paginationParameters
properties:
  paginationParameters:
    $ref: "./PaginationParameters.yaml"
    description: Pagination parameters including page number and page size.
  searchExpressions:
    description:  A collection of search expressions used to filter the results.
    type: array
    items:
      $ref: "./SearchExpression.yaml"
    nullable: true
    minItems: 0
    maxItems: 255
  sortBy:
    allOf:
      - $ref: "./SortBy.yaml"
    nullable: true
    description: >
      Sorting parameters that define the order of returned results.
      If not provided (null), results will be ordered by UpdatedDate descending, then CreatedDate descending.
