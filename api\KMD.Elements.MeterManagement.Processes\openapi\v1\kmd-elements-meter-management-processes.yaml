openapi: 3.0.3
info:
  title: KMD.Elements.MeterManagement.Processes
  x-maintainers: Team-MO-2
  description: Meter management Processes service.
  termsOfService: "https://www.kmd.net/terms-of-use"
  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>
  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"
  version: "1.105-preview"
servers:
  - url: https://localhost:8407
security:
  - Jwt: []
tags:
  - name: BulkCreateMetersFromFile
    description: |-
      API supporting bulk create of meters from file.
  - name: MeterBatchesProcesses
    description: |-
      API supporting processes management for meter batches: assignment, merge, split.
  - name: MeterCommandProcesses
    description: |-
      API supporting meter command processes functionality.
  - name: MeterProcesses
    description: |-
      API supporting meter processes functionality.
paths:
  /v1/meter-command-processes:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute and send a meter command on a single meter.
      description: Execute and send a meter command on a single meter.
      operationId: executeMeterCommandProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "201":
          description: Details of execution meter command with steps.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Read
      tags:
        - MeterCommandProcesses
      summary: Retrieve details of execution of meter command with steps.
      description: Retrieve details of execution of meter command with steps.
      operationId: getMeterCommandProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
      responses:
        "200":
          description: Details of execution meter command with steps.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandProcessBody.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Updates meter command process.
      description: Updates meter command process.
      operationId: updateMeterCommandProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterCommandProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully returned the meter command process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Reruns failed meter command process.
      description: Reruns failed meter command process.
      operationId: rerunMeterCommandProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully returned the meter command process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Cancels meter command process.
      description: Cancels meter command process.
      operationId: cancelMeterCommandProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully cancelled the meter command process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Marks as handled meter command process.
      description: Marks as handled meter command process.
      operationId: markAsHandledMeterCommandProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: ./responses/400.yaml
        "401":
          $ref: ./responses/401.yaml
        "403":
          $ref: ./responses/403.yaml
        "404":
          $ref: ./responses/404.yaml
        "422":
          $ref: ./responses/422.yaml
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: ./responses/500.yaml
        "503":
          $ref: ./responses/503.yaml
        "504":
          $ref: ./responses/504.yaml
      security:
        - Jwt: []
  /v1/meter-command-processes/commands/execute-on-multiple-meters:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute meter command processes on multiple meters.
      description: Execute meter command processes on multiple meters.
      operationId: executeMeterCommandProcessOnMultipleMeters
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandProcessesOnMultipleMetersBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "202":
          description: Execution of meter command processes on multiple meters successfully started.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/bulk-create-meters-manually-processes:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterProcesses
      summary: Schedule process to bulk create meters.
      description: Schedule process to bulk create meters.
      operationId: bulkCreateMetersManually
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/BulkCreateMeterProcess.yaml"
        required: true
      responses:
        "202":
          content:
            application/json:
              schema:
                $ref: "./schemas/BulkCreateMetersManuallyProcessCreated.yaml"
          description: -|
            Schedule bulk create meters process was accepted.
            Return identifier of process in process center.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/bulk-create-meters-manually-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterProcesses
      summary: Cancels bulk create meters manually process.
      description: Cancels bulk create meters manually process.
      operationId: cancelBulkCreateMetersManually
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/bulk-create-meters-manually-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterProcesses
      summary: Marks as handled the second step of bulk create meters manually process.
      description: -|
        Changes the state of the second step of bulk create meters manually process to Manually handled.
        Changes the process state to Completed.
      operationId: markAsHandledBulkCreateMetersManuallyProcessStep
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      responses:
        "204":
          description: Process step Marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/bulk-create-meters-manually-processes/{processId}/commands/generate-csv-report:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterProcesses
      summary: Triggers a job that generates a report with all meters.
      description: -|
        Triggers a job that generates a report with all meters.
        CSV file includes such columns as Serial number, State and optional Error message
      operationId: generateCsvReportForBulkCreateMetersManuallyProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "202":
          content:
            application/json:
              schema:
                $ref: "./schemas/CsvReportDetailsForBulkCreateMetersManuallyProcess.yaml"
          description: -|
            Request was accepted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/bulk-create-meters-manually-processes/{processId}/commands/download-csv-report:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterProcesses
      summary: Downloads the report as a CSV file.
      description: Downloads the report as a CSV file.
      operationId: downloadCsvReportForBulkCreateMetersManuallyProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/bulk-create-meters-manually-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterProcesses
      summary: Gets specific bulk create meters manually process
      description: Gets specific bulk create meters manually process by identifier
      operationId: getBulkCreateMetersManuallyProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
      responses:
        "200":
          description: Found bulk create meters manually process.
          content:
            application/json:
              schema:
                $ref: "./schemas/BulkCreateMetersManuallyProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterProcesses
      summary: Update specific bulk create meters manually process
      description: Update specific bulk create meters manually process by identifier
      operationId: updateBulkCreateMetersManuallyProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateBulkCreateMetersManuallyProcess.yaml"
        required: true
      responses:
        "204":
          description: Process updated successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v1/bulk-create-meters-manually-processes/{processId}/update-additional-information:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterProcesses
      summary: Update additional information fields of a specific bulk create meters manually process
      description: Update additional information fields of a specific bulk create meters manually process by identifier
      operationId: updateBulkCreateMetersManuallyProcessAdditionalInformationById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateBulkCreateMetersManuallyProcessAdditionalInformation.yaml"
        required: true
      responses:
        "204":
          description: Additional information of the bulk create meters manually process updated successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v1/bulk-create-meters-manually-processes/{processId}/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterProcesses
      summary: Rerun bulk create meters manually process.
      description: Rerun bulk create meters manually process.
      operationId: rerunBulkCreateMetersManually
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      responses:
        "204":
          description: Process restarted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/bulk-create-meters-from-file-processes:
    post:
      description: |-
        ### Remarks
        - Create process for bulk creating Meters from file
      summary: Endpoint that allows to create process for bulk creating Meters from file
      tags:
        - BulkCreateMetersFromFile
      operationId: bulkCreateMetersFromFile
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      responses:
        "202":
          content:
            application/json:
              schema:
                $ref: "./schemas/BulkCreateMetersFromFileProcessCreated.yaml"
          description: |-
            Bulk create Meters from file process was created.
            Returns identifier of newly created process in process center.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/BulkCreateMetersFromFileProcess.yaml"
            examples:
              ValidRequest:
                value:
                  asOfDate: "2023-10-04T10:27:33.0309131Z"
                  file:
                    - fileId: aa502c9b-2c74-4691-87d4-010ce8c45880
                    - fileName: "meters.csv"
                  supplyTypeCode: "Electricity"
                  configurationTemplateId: 12334e81-3fa0-4cff-bc37-3b836ff7b0cc
                  productionYear: "2020-12-31"
                  purchaseYear: "2022-05-31"

  /v1/bulk-create-meters-from-file-processes/template:
    get:
      tags:
        - BulkCreateMetersFromFile
      operationId: getBulkCreateMetersFromFileCsvTemplate
      description:
        Endpoint for retrieving csv template. User can download a file, |-
        which consists of empty table with predefined meter attributes names as headers. |-
        User fills the template on local device with data of meters to be created during bulk create from file process.
      summary: Endpoint for retrieving csv template for bulk creating meters from file.
      x-authorization: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      parameters:
        - $ref: "./parameters/SupplyTypeCodeQueryString.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: 200 Success
          content:
            application/octet-stream:
              schema:
                description: CSV file with results
                maxLength: 2147483647
                type: string
                format: binary
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v1/bulk-create-meters-from-file-processes/commands/generate-upload-url:
    post:
      description: |-
        ### Remarks
        - Generate URL for upload file to the public storage
      summary: Endpoint that allows to generate URL for upload file to public storage
      tags:
        - BulkCreateMetersFromFile
      operationId: getUploadUrl
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      responses:
        "200":
          $ref: "./schemas/GetUploadUrlResponse.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v1/bulk-create-meters-from-file-processes/{processId}/commands/download-csv-validation-report:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - BulkCreateMetersFromFile
      summary: Downloads the validation outcome as a CSV file.
      description: Downloads the validation outcome as a CSV file.
      operationId: downloadCsvValidationReportForBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/bulk-create-meters-from-file-processes/{processId}/commands/download-csv-creation-report:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - BulkCreateMetersFromFile
      summary: Downloads the creation outcome as a CSV file.
      description: Downloads the creation outcome as a CSV file.
      operationId: downloadCsvCreationReportForBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/bulk-create-meters-from-file-processes/{processId}/commands/start-meter-creation:
    post:
      description: |-
        ### Remarks
        - Starts creation of meters
      summary: Endpoint that allows to start creation of meters
      tags:
        - BulkCreateMetersFromFile
      operationId: startMeterCreation
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      responses:
        "202":
          description: "Meter creation started"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v1/bulk-create-meters-from-file-processes/{processId}/commands/select-meter-template:
    post:
      description: |-
        ### Remarks
        - Select meter template for bulk creating Meters from file process
      summary: Endpoint that allows to select meter template for bulk creating Meters from file process
      tags:
        - BulkCreateMetersFromFile
      operationId: selectMeterTemplate
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SelectMeterTemplateForProcess.yaml"
            examples:
              ValidRequest:
                value:
                  meterTemplateId: 123
      responses:
        "204":
          description: Meter template selected successfully
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v1/bulk-create-meters-from-file-processes/{processId}:
    get:
      description: |-
        ### Remarks
        - Get bulk create meters from file specific process by id
      summary: Endpoint that allows to get bulk create meters from file specific process by its identifier
      tags:
        - BulkCreateMetersFromFile
      operationId: getBulkCreateMetersFromFileProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      responses:
        "200":
          description: Found bulk create meters from file process.
          content:
            application/json:
              schema:
                $ref: "./schemas/BulkCreateMetersFromFileProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Update additional information fields of a specific bulk create meters from file process
      description: Update additional information fields of a specific bulk create meters from file process by identifier
      operationId: updateBulkCreateMetersFromFileProcessAdditionalInformationById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateBulkCreateMetersFromFileProcessAdditionalInformation.yaml"
        required: true
      responses:
        "204":
          description: Additional information of the bulk create meters manually process updated successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v1/bulk-create-meters-from-file-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Cancels bulk create meters from file process.
      description: Cancels bulk create meters from file process.
      operationId: cancelBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/bulk-create-meters-from-file-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Marks as handled bulk create meters from file process.
      description: Changes the process state to Manually Handled.
      operationId: markAsHandledBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process Marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/bulk-create-meters-from-file-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Rerun bulk create meters from file process.
      description: Rerun bulk create meters from file process.
      operationId: rerunBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process restarted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/bulk-create-meters-from-file-processes/{processId}/commands/reset-to-meter-template-selection-step:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Reset drafts validation and go back to meter template selection step in bulk create meters from file process.
      description: Reset drafts validation and go back to meter template selection step in bulk create meters from file process.
      operationId: resetToMeterTemplateSelectionStepBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process reset to meter template selection step.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/meter-command-group-processes:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute and send a meter command group on single meter.
      description: Execute and send a meter command group on single meter.
      operationId: executeMeterCommandGroupProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandGroupProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "201":
          description: Execution of meter command group successfully started.
          content:
            application/json:
              schema:
                $ref: "./schemas/ExecuteMeterCommandGroupProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-processes/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Read
      tags:
        - MeterCommandProcesses
      summary: Search meter command group processes.
      description: Search meter commands group processes.
      operationId: searchMeterCommandGroupProcesses
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterCommandGroupProcesses.yaml"
      responses:
        "200":
          description: Details of execution meter command group with steps.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterCommandGroupProcessesPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Read
      tags:
        - MeterCommandProcesses
      summary: Retrieve details of execution of meter command group with steps.
      description: Retrieve details of execution of meter command group with steps.
      operationId: getMeterCommandGroupProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
      responses:
        "200":
          description: Details of execution meter command group with steps.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandGroupProcessBody.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Updates meter command group process.
      description: Updates meter command group process.
      operationId: updateMeterCommandGroupProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterCommandGroupProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully updated the meter command group bulk process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Reruns failed meter command group process.
      description: Reruns failed meter command group process.
      operationId: rerunMeterCommandGroupProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully returned the meter command group process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Cancels meter command group process.
      description: Cancels meter command group process.
      operationId: cancelMeterCommandGroupProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully cancelled the meter command group process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Marks as handled meter command group process.
      description: Marks as handled meter command group process.
      operationId: markAsHandledMeterCommandGroupProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-processes/commands/execute-on-multiple-meters:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute meter command group processes with individual meter command group on multiple meters.
      description: Execute meter command group processes on multiple meters.
      operationId: executeMeterCommandGroupProcessesOnMultipleMeters
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandGroupProcessesOnMultipleMetersBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "202":
          description: Request to start meter command group processes for multiple meters successfully accepted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-bulk-processes:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute a meter command group process on multiple meters.
      description: Execute a meter command group process on multiple meters.
      operationId: executeMeterCommandGroupBulkProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandGroupBulkProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "201":
          description: Execution of meter command group successfully started.
          content:
            application/json:
              schema:
                $ref: "./schemas/ExecuteMeterCommandGroupBulkProcessCreated.yaml"

        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-bulk-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Read
      tags:
        - MeterCommandProcesses
      summary: Retrieve details of bulk execution of meter command group.
      description: Retrieve details of bulk execution of meter command group.
      operationId: getMeterCommandGroupBulkProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
      responses:
        "200":
          description: Details of execution meter command group with steps.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandGroupBulkProcessBody.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Updates meter command group bulk process.
      description: Updates meter command group bulk process.
      operationId: updateMeterCommandGroupBulkProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterCommandGroupProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully updated the meter command group bulk process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-bulk-processes/{processId}/commands/attach-meters:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Attach meters to already existing bulk meter command group execution.
      description: Attach meters to already existing bulk meter command group execution.
      operationId: attachMetersToMeterCommandGroupBulkProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/AttachMetersToMeterCommandGroupBulkProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully attached meters to already existing bulk meter command group execution.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-bulk-processes/{processId}/commands/detach-meters:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Detach meters from already existing bulk meter command group execution.
      description: Detach meters from already existing bulk meter command group execution.
      operationId: detachMetersFromMeterCommandGroupBulkProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/DetachMetersFromMeterCommandGroupBulkProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully detached meters from already existing bulk meter command group execution.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-bulk-processes/{processId}/commands/start:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Start meter command group bulk process.
      description: Start meter command group bulk process.
      operationId: startMeterCommandGroupBulkProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process started.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-bulk-processes/{processId}/commands/re-run-failed-process-creation:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Re-runs failed process creation.
      description: Re-runs failed process creation.
      operationId: reRunFailedMeterCommandGroupBulkProcessCreation
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "202":
          description: Operation was scheduled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-bulk-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Mark as handled failed process creation.
      description: Mark as handled failed process creation.
      operationId: markAsHandledMeterCommandGroupBulkProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/meter-command-group-bulk-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Cancel the running process and attempt to cancel related subprocesses.
      description: Cancel the running process and attempt to cancel related subprocesses.
      operationId: cancelMeterCommandGroupBulkProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/detach-meters-from-batch-processes:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Create detach meter from batch process.
      description: Create detach meter from batch process.
      operationId: createDetachMetersFromBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateDetachMetersFromBatchProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "201":
          description: Bulk detach meters from batch process created.
          content:
            application/json:
              schema:
                $ref: "./schemas/DetachMetersFromBatchProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/detach-meters-from-batch-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Gets specific detach meters from batch process
      description: Gets specific detach meters from batch process by identifier
      operationId: getDetachMetersFromBatchProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Found detach meters from batch process.
          content:
            application/json:
              schema:
                $ref: "./schemas/DetachMetersFromBatchProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Updates detach meters from batch process.
      description: Updates detach meters from batch process.
      operationId: updateDetachMetersFromBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./schemas/UpdateProcessBody.yaml
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully updated the detach meters from batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/detach-meters-from-batch-processes/{processId}/meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - MeterBatchesProcesses
      summary: Downloads the list of meters taking part in the process as a CSV file.
      description: Downloads the list of meters taking part in the process as a CSV file.
      operationId: getDetachMetersFromBatchProcessMetersCsv
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            text/csv:
              schema:
                description: File content
                type: string
                maxLength: 2147483647
                format: binary
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/detach-meters-from-batch-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Reruns failed detach meters from batch process.
      description: Reruns failed detach meters from batch process.
      operationId: rerunDetachMetersFromBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully cancelled the detach meters from batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/detach-meters-from-batch-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Cancels detach meters from batch process.
      description: Cancels detach meters from batch process.
      operationId: cancelDetachMetersFromBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully cancelled the detach meters from batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v1/detach-meters-from-batch-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Marks as handled detach meters from batch process.
      description: Marks as handled detach meters from batch process.
      operationId: markAsHandledDetachMetersFromBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: ./responses/400.yaml
        "401":
          $ref: ./responses/401.yaml
        "403":
          $ref: ./responses/403.yaml
        "404":
          $ref: ./responses/404.yaml
        "422":
          $ref: ./responses/422.yaml
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: ./responses/500.yaml
        "503":
          $ref: ./responses/503.yaml
        "504":
          $ref: ./responses/504.yaml
      security:
        - Jwt: []
  /v1/assign-meters-to-batch-processes/{processId}/commands/search-suggested-meter-batches:
    post:
      x-authorization-1: MeterBatch.Read
      x-authorization-2: Meters.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Gets the list of meter batches suggested for the assign meters to batch process.
      description: Gets the list of meter batches suggested for the assign meters to batch process.
      operationId: assignMetersToBatchProcessSearchSuggestedMeterBatches
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ./schemas/AssignMetersToBatchProcessSearchSuggestedMeterBatchesBody.yaml
      responses:
        "200":
          description: List of meter batches suggested for the assign meters to batch process.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterBatchesPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/assign-meters-to-batch-processes/{processId}/commands/select-meter-batch:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Selects meter batch in the assign meters to batch process.
      description: Selects meter batch in the assign meters to batch process.
      operationId: assignMetersToBatchProcessSelectMeterBatch
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AssignMetersToBatchProcessSelectMeterBatchBody.yaml"
      responses:
        "204":
          description: Successfully selected a meter batch to the assign meters to batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/assign-meters-to-batch-processes/{processId}/commands/select-max-size-exceeded-strategy:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Selects the strategy to use when the meter batch max size is exceeded.
      description: Selects the strategy to use when the meter batch max size is exceeded.
      operationId: assignMetersToBatchProcessSelectMaxSizeExceededStrategy
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AssignMetersToBatchProcessSelectMaxSizeExceededStrategy.yaml"
      responses:
        "204":
          description: Successfully selected max size exceeded strategy.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/assign-meters-to-batch-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Reruns assign meters to batch process.
      description: Reruns assign meters to batch process.
      operationId: rerunAssignMetersToBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully triggered the assign meters to batch process rerun.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/assign-meters-to-batch-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Cancels assign meters to batch process.
      description: Cancels assign meters to batch process.
      operationId: cancelAssignMetersToBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully cancelled the assign meters to batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/assign-meters-to-batch-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Marks as handled assign meters to batch process.
      description: Marks as handled assign meters to batch process.
      operationId: markAsHandledAssignMetersToBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: ./responses/400.yaml
        "401":
          $ref: ./responses/401.yaml
        "403":
          $ref: ./responses/403.yaml
        "404":
          $ref: ./responses/404.yaml
        "422":
          $ref: ./responses/422.yaml
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: ./responses/500.yaml
        "503":
          $ref: ./responses/503.yaml
        "504":
          $ref: ./responses/504.yaml
      security:
        - Jwt: []
  /v1/assign-meters-to-batch-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Retrieve details of assign meters to batch process.
      description: Retrieve details of assign meters to batch process.
      operationId: getAssignMetersToBatchProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Details of assign meters to batch process.
          content:
            application/json:
              schema:
                $ref: "./schemas/AssignMetersToBatchProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Updates assign meters to batch process.
      description: Updates assign meters to batch process.
      operationId: updateAssignMetersToBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./schemas/UpdateProcessBody.yaml
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully updated the assign meters to batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/assign-meters-to-batch-processes/{processId}/meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - MeterBatchesProcesses
      summary: Downloads the list of meters taking part in the process as a CSV file.
      description: Downloads the list of meters taking part in the process as a CSV file.
      operationId: getAssignMetersToBatchProcessMetersCsv
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            text/csv:
              schema:
                description: File content
                type: string
                maxLength: 2147483647
                format: binary
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/assign-meters-to-batch-processes:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Create assign meters to batch process.
      description: Create assign meters to batch process.
      operationId: createAssignMetersToBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/AssignMetersToBatchProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "201":
          description: Assign meters to batch process successfully created.
          content:
            application/json:
              schema:
                $ref: "./schemas/AssignMetersToBatchProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/merge-batches-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Retrieve details of merge meter batches process.
      description: Retrieve details of merge meter batches process.
      operationId: getMergeBatchesProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Details of merge meter batches process.
          content:
            application/json:
              schema:
                $ref: "./schemas/MergeBatchesProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Updates merge batches process.
      description: Updates merge batches process.
      operationId: updateMergeBatchesProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./schemas/UpdateProcessBody.yaml
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully updated the merge batches process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/merge-batches-processes/{processId}/commands/select-meter-batch:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Begins the process step of meters to a newly created meter batch assignment in merge meter batches process.
      description: Begins the process step of meters to a newly created meter batch assignment in merge meter batches process.
      operationId: mergeBatchesProcessSelectMeterBatch
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/MergeBatchesProcessSelectMeterBatchBody.yaml"
      responses:
        "204":
          description: Successfully began assignment meters to a batch process step.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/merge-batches-processes/{processId}/attached-meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - MeterBatchesProcesses
      summary: Returns a csv file consisting of data from meters attached to both batches to merge.
      description: Returns a csv file consisting of data from meters attached to both batches to merge.
      operationId: getAttachedMetersCsvMergeBatchesProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            text/csv:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/split-meter-batch-processes:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Creates two new meter batches as a part of a split meter batch process.
      description: Endpoint serving functionality of creating two meter batches and assigning meters evenly between them as a part of a split meter batch process.
      operationId: createSplitMeterBatchProcess
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateSplitMeterBatchProcess.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "201":
          description: Details of newly created split meter batch process.
          content:
            application/json:
              schema:
                $ref: "./schemas/SplitMeterBatchProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/split-meter-batch-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Retrieve details of split meter batch process.
      description: Retrieve details of split meter batch process.
      operationId: getSplitMeterBatchProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Details of split meter batches process.
          content:
            application/json:
              schema:
                $ref: "./schemas/SplitMeterBatchProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Updates split meter batch process.
      description: Updates split meter batch process.
      operationId: updateSplitMeterBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./schemas/UpdateProcessBody.yaml
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully updated the split meter batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/export/split-meter-batch-processes/{processId}/attached-meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - MeterBatchesProcesses
      summary: Returns a csv file consisting of data from meters attached to meter batch.
      description: Returns a csv file consisting of data from meters attached to meter batch.
      operationId: getAttachedMetersForSplitMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            text/csv:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/split-meter-batch-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Marks as handled split meter batch process.
      description: Marks as handled split meter batch process.
      operationId: markAsHandledSplitMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/split-meter-batch-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Cancels split meter batch process.
      description: Cancels split meter batch process.
      operationId: cancelSplitMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/split-meter-batch-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Reruns split meter batch process.
      description: Reruns split meter batch process.
      operationId: rerunSplitMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "202":
          description: Process scheduled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/split-meter-batch-processes/{processId}/commands/continue-create-new-meter-batches-process-step:
    post:
      x-authorization-1: MeterBatch.Read
      x-authorization-2: MeterBatch.Write
      x-authorization-3: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Continue process step of creation of new meter batches.
      description: Continue process step of creation of new meter batches for process of splitting meter batch.
      operationId: continueCreateNewMeterBatchesProcessStepForSplitMeterBatchProcess
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/ContinueCreateNewMeterBatchesProcessStepForSplitMeterBatchProcess.yaml"
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process step completed successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/merge-batches-processes:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Create merge meter batches process.
      description: Create merge meter batches process.
      operationId: createMergeMeterBatchesProcess
      requestBody:
        content:
          application/json:
            schema:
                $ref: "./schemas/CreateMergeMeterBatchesProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "201":
          description: Merge meter batches process successfully created.
          content:
            application/json:
              schema:
                $ref: "./schemas/MergeMeterBatchesProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/merge-batches-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Marks as handled merge meter batch process.
      description: Marks as handled merge meter batch process.
      operationId: markAsHandledMergeMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/merge-batches-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Cancels merge meter batch process.
      description: Cancels merge meter batch process.
      operationId: cancelMergeMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v1/merge-batches-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Reruns merge meter batch process.
      description: Reruns merge meter batch process.
      operationId: rerunMergeMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully triggered the merge batches process rerun.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
components:
  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
