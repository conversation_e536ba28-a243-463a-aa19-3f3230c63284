asyncapi: 2.6.0
id: https://async.api.kmdelements.com/fix-id/
info:
  title: fix-title
  x-maintainers: Architects-Team
  version: "0.0.1-preview"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    This is just a template. Please take care to update this description.

    **TODO:**
      - replace `fix-id`
      - replace `fix-title`
      - replace `fix-description`
      - replace `fix-maintainer-team`
      - replace `sampleCommand` with your command
      - replace `samplePayload` with payload for your command
      - replace `sampleProperty` with property for your payload or remove it
      - replace `fix.topic-suffix` see https://dev.azure.com/kmddk/COMBAS/_wiki/wikis/COMBAS.wiki/29664/Kafka-Architectural-decisions-Messaging-Topic-Naming-Rules-Command-Event-?anchor=topics-naming
    # Sample Description
    Sample AsyncAPI allows you to remotely push update notifications.
    fix-description

tags:
  - name: fix-maintainer-team
    description: Maintained by

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.command.fix.topic-suffix.v1:
    description: Sample command topic description.
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Summary about the channel.
      description: Sample command description
      operationId: sampleCommandOperationId
      message:
        $ref: "#/components/messages/SampleCommand"

components:
  messages:
    SampleCommand:
      name: SampleCommand
      title: Sample Command
      summary: Inform about sample command.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/SamplePayload"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    SamplePayload:
      title: SamplePayload
      type: object
      additionalProperties: false
      required:
        - sampleProperty
      properties:
        id:
          type: string
          format: uuid
          example: 6a6018e8-98a7-4f41-a3ee-127fd34eeb37
        value:
          type: number
          format: decimal
        sampleProperty:
          type: string
          description: Required property
          minLength: 1
        sampleDate:
          description: Sample date of payload
          type:
            - "null"
            - string
          format: date-time
      examples:
        [
          {
            sampleProperty: "example value for sampleProperty",
            sampleDate: "2023-01-12T00:00:00.000Z",
          },
          {
            sampleProperty: "another value for sampleProperty",
            sampleDate: "9999-12-24T00:00:00.000Z",
          },
        ]
  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
