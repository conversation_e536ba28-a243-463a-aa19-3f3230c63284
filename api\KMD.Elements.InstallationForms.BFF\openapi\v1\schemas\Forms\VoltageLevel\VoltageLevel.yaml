title: VoltageLevel
description: Voltage Level Information
type: object
additionalProperties: false
nullable: true
required:
  - voltageLevelId
properties:
  voltageLevelId:
    description: Voltage level value id (MeterFrame-Electricity-TarifConnectionPoint) for high and low voltage.
    $ref: "../../ValueLists/ValueListTypes/MeterFrameElectricityTarifConnectionPoint.yaml"
  voltageTransformer:
    $ref: "./VoltageTransformer.yaml"
  currentTransformer:
    $ref: "./CurrentTransformer.yaml"
  currentTransformerId:
    $ref: "../../ValueLists/ValueListTypes/CurrentTransformerId.yaml"
  voltageTransformerId:
    $ref: "../../ValueLists/ValueListTypes/VoltageTransformerId.yaml"
