type: object
description: Model containing electricity attributes of Connection Point Default Value Set.
properties:
  connectionPointCategoryValueId:
    description: Categorization of a ConnectionPoint.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  installationTypeValueId:
    description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  installationDescription:
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
    description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
