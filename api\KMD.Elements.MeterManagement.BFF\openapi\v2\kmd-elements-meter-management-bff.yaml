openapi: 3.0.3
info:
  title: KMD.Elements.MeterManagement.BFF
  x-maintainers: Team-MO-2
  description: Meter management bff service.
  termsOfService: "https://www.kmd.net/terms-of-use"
  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>
  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"
  version: "2.174-preview"
servers:
  - url: https://localhost:6001
security:
  - Jwt: []
tags:
  - name: BulkCreateMetersFromFile
    description: |-
      API supporting bulk create of meters from file.
  - name: ElectricityMeters
    description: |-
      API supporting electricity meters management functionality.
  - name: ElectricityMeterBatches
    description: |-
      API supporting electricity meter batches management functionality.
  - name: HeatMeters
    description: |-
      API supporting heat meters management functionality.
  - name: HeatMeterBatches
    description: |-
      API supporting heat meter batches management functionality.
  - name: MeterBatches
    description: |-
      API supporting common meter batches management functionality for different kinds of supply types electricity, heat, water.
  - name: MeterBatchesProcesses
    description: |-
      API supporting processes management for meter batches: assignment, merge, split.
  - name: MeterCommandProcesses
    description: |-
      API supporting meter command processes functionality.
  - name: MeterCommands
    description: |-
      API supporting common meter command functionality.
  - name: MeterComponents
    description: |-
      Api allows to create, get, update, delete and search components for meter of chosen supply type
  - name: MeterConfigurations
    description: |-
      Api supporting meter configurations management functionality.
  - name: MeterInputs
    description: |-
      API supporting meter inputs management functionality.
  - name: MeterManagementValueLists
    description: |-
      API supporting value lists functionality used in meter management process.
  - name: MeterRegisters
    description: |-
      API supporting meter registers management functionality.
  - name: MeterTemplates
    description: |-
      Api allows to create, get, update, delete and search templates for meter of chosen supply type.
  - name: Meters
    description: API supporting common meter management functionality for different kinds of meters (electricity, heat, water).
  - name: Users
    description: |-
      Methods allow to
        * Retrieve users list
  - name: WaterMeterBatches
    description: |-
      API supporting water meter batches management functionality.
  - name: WaterMeters
    description: |-
      API supporting water meters management functionality.
  - name: MeterJobs
    description: |-
      API supporting meter jobs management functionality for different kinds of supply types electricity, heat, water.

paths:
  /v2/value-lists/{valueListSystemIdentifier}:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterManagementValueLists
      summary: Get value list used by Value List System Identifier.
      description: |-
        Endpoint for retrieve value list data.
        Supported value lists on meter management domain
        | Usage | System identifiers |
        | - | - |
        | Meter&nbsp;&nbsp; | MeterStatus, ComponentStatus |
        | Meter&nbsp;&nbsp; | MeterType, MeterCommunicationMethod, HesSystem, Manufacturer, FabricationType, RemoteRegisterConfiguration, RemoteDisplayConfiguration |
        | Electricity meter&nbsp;&nbsp; | ElectricityMeterAccuracyClass, ElectricityMeterCurrent, ElectricityMeterCustomerOutputState, MeterFrameElectricityConnectionType, ElectricityMeterElectricityConnectionRemark, ElectricityMeterElectricityMeteringPrinciple, ElectricityMeterItemName, ElectricityMeterMaxPlcDistance, ElectricityMeterMaxRadioDistance, ElectricityMeterNoOfPulseInputs, ElectricityMeterNumberOfPhases, MeterFrameElectricityAttributesRatioCT, MeterFrameElectricityAttributesRatioVT, ElectricityMeterVoltage, ElectricityMeterTechnicalCurrent, ElectricityMeterTechnicalVoltage
        | Heating meter&nbsp;&nbsp; | HeatingMeterHeatKindType, HeatingMeterHeatMeterAssemblyType, HeatingMeterHeatMeterPowerSupply, HeatingMeterHeatMeterSensorType, HeatingMeterPSensorType, HeatingMeterPUnitType, HeatingMeterFlowDirection, HeatingMeterVoltageType, HeatingMeterHeatingMeterSize, HeatingMeterHeatingMeasuringPrinciple
        | Water meter&nbsp;&nbsp; | WaterMeterDataOutput, WaterMeterWaterMeterConnectionType, WaterMeterWaterMeasuringPrinciple, WaterMeterWaterMeterSize
        | Meter register&nbsp;&nbsp; | MeterRegisterMeteringComponent
        | Meter component&nbsp;&nbsp; | ComponentStatus
        | Electricity meter component&nbsp;&nbsp; | ElectricityMeterComponentComponentType
        | Heating meter component&nbsp;&nbsp; | HeatingMeterComponentComponentType
        | Water meter component&nbsp;&nbsp; | WaterMeterComponentComponentType
        | Common;&nbsp; | ValueSharedSupplyType
      operationId: getValueList
      parameters:
        - $ref: "./parameters/ValueListSystemIdentifier.yaml"
      responses:
        "200":
          $ref: "./responses/MeterManagementValueList.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/dictionaries/supply-types:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets supply types values.
      description: Endpoint supporting supply types dictionary values functionality used in meter management process.
      operationId: getAllSupplyTypes
      responses:
        "200":
          $ref: "./responses/DictionarySupplyTypes.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-commands:
    post:
      x-authorization: MeterCommand.Write
      tags:
        - MeterCommands
      summary: Creates a new meter command.
      description: Creates a new meter command.
      operationId: createMeterCommand
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterCommandData.yaml"
      responses:
        "201":
          $ref: "./responses/CreateMeterCommandResponse.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-commands/commands/get-tenant-configuration:
    post:
      x-authorization: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Gets configuration for meter commands for current tenant.
      description: Gets details of meter commands' configuration.
      operationId: getMeterCommandsConfiguration
      parameters:
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "200":
          description: Configuration of meter commands.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandsConfiguration.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-commands/{meterCommandId}:
    get:
      x-authorization: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Gets details of a meter command.
      description: Gets details of a meter command.
      operationId: getMeterCommandDetails
      parameters:
        - $ref: "./parameters/MeterCommandId.yaml"
      responses:
        "200":
          description: Details of meter command successfully returned.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization: MeterCommand.Write
      tags:
        - MeterCommands
      summary: Update details of a meter command.
      description: Update details of a meter command.
      operationId: updateMeterCommandMasterData
      parameters:
        - $ref: "./parameters/MeterCommandId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/MeterCommandUpdate.yaml"
      responses:
        "204":
          description: Update of meter command finished successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization: MeterCommand.Write
      tags:
        - MeterCommands
      summary: Delete a meter command.
      description: Delete a meter command.
      operationId: deleteMeterCommandMasterData
      parameters:
        - $ref: "./parameters/MeterCommandId.yaml"
      responses:
        "204":
          description: Delete of meter command finished successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-commands/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterCommands
      summary: Search meter commands.
      description: Search meter commands. Used during creating meter
      operationId: searchMeterCommands
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterCommands.yaml"
      responses:
        "200":
          description: Returns paged list of searched meter commands.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandPagedCollection.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-configurations:
    post:
      x-authorization-1: MeterConfigurationTemplate.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Create meter configuration template.
      description: Create meter configuration template.
      operationId: createMeterConfiguration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterConfiguration.yaml"
      responses:
        "201":
          description: Successfully created.
          content:
            application/json:
              schema:
                description: Identifier of newly created meter configuration
                type: string
                format: uuid
              example: "FE1FEA7B-78F1-44DC-B88D-39BBB8E12B12"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-configurations/{meterConfigurationId}:
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Get meter configuration template or meter configuration instance master data.
      description: |-
        Get meter configuration template or meter configuration instance master data.
        In order to get meter configuration template master data, MeterConfigurationTemplate.Read permission is required.
        In order to get meter configuration instance master data, Meters.Read permission is required
      operationId: getMeterConfigurationBaseData
      parameters:
        - $ref: "./parameters/MeterConfigurationId.yaml"
      responses:
        "200":
          description: Returns meter configuration base data.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterConfigurationBaseData.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Update meter configuration via instance or template.
      description: |-
        Update meter configuration via instance or template.
        In order to update meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to update meter configuration instance, Meters.Write permission is required
      operationId: updateMeterConfiguration
      parameters:
        - $ref: "./parameters/MeterConfigurationId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterConfiguration.yaml"
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization-1: MeterConfigurationTemplate.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Delete meter configuration template.
      description: Delete meter configuration template.
      operationId: deleteMeterConfigurationTemplate
      parameters:
        - $ref: "./parameters/MeterConfigurationId.yaml"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-configurations/{meterConfigurationId}/commands/retrieve-attached-commands:
    post:
      x-authorization: MeterCommand.Read
      tags:
        - MeterConfigurations
      summary: Retrieve list of meter commands attached to a specific meter configuration
      description: Retrieve list of meter commands attached to a specific meter configuration.
      operationId: retrieveMeterCommandsAttachedToMeterConfiguration
      parameters:
        - $ref: "./parameters/MeterConfigurationId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/RetrieveSortedPagedData.yaml"
      responses:
        "200":
          description: Returns paged list of searched meter commands.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandPagedCollection.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/attach-meter-commands:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Meters.Write
      tags:
        - MeterConfigurations
      summary: Attach meter commands to a meter configuration template/instance.
      description: Attach meter commands to a meter configuration template/instance.
      operationId: attachMeterCommandsToMeterConfiguration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AttachMeterCommandsToMeterConfiguration.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully attached meter commands to a meter configuration template/instance.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/detach-meter-commands:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Meters.Write
      tags:
        - MeterConfigurations
      summary: Detach meter commands from a meter configuration template/instance.
      description: Detach meter commands from a meter configuration template/instance.
      operationId: detachMeterCommandsFromMeterConfiguration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/DetachMeterCommandsFromMeterConfiguration.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully detached meter commands from a meter configuration template/instance.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-configurations/{meterConfigurationId}/meter-inputs:
    post:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Create meter input for meter configuration template or instance.
      description: |-
        Create meter input for meter configuration template or instance.
        In order to create meter input for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to create meter input for meter configuration instance, Meters.Write permission is required
      operationId: createMeterInput
      parameters:
        - $ref: "./parameters/MeterConfigurationId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterInput.yaml"
      responses:
        "201":
          description: Successfully created.
          content:
            application/json:
              schema:
                description: Identifier of newly created meter input
                type: string
                format: uuid
              example: "FE1FEA7B-78F1-44DC-B88D-39BBB8E12B12"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Returns list of all meter inputs.
      description: |-
        Returns list of all meter inputs related with meter configuration template or meter configuration instance.
        In order to return list all meter inputs for meter configuration template, MeterConfigurationTemplate.Read permission is required.
        In order to return list all meter inputs for meter configuration template instance, Meters.Read permission is required
      operationId: getMeterInputsForMeterConfiguration
      parameters:
        - $ref: "./parameters/MeterConfigurationId.yaml"
      responses:
        "200":
          description: List of all meter inputs related with meter configuration.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterInputsForMeterConfiguration.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/batch-attach:
    post:
      x-authorization: Meters.Write
      tags:
        - MeterConfigurations
      summary: Attach meter configuration template to collection of meters by strategy.
      description: Attach meter configuration template to collection of meters by strategy.
      operationId: batchAttachMeterConfigurationTemplateByStrategy
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/BatchAttachMeterConfigurationTemplateByStrategy.yaml"
      responses:
        "202":
          description: Attach request accepted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/batch-delete:
    post:
      x-authorization: MeterConfigurationTemplate.Write
      tags:
        - MeterConfigurations
      summary: Batch delete meter configuration templates.
      description: Batch delete meter configuration templates.
      operationId: batchDeleteMeterConfigurationTemplates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/BatchDeleteMeterConfigurationTemplates.yaml"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/search:
    post:
      x-authorization: MeterConfigurationTemplate.Read,Meters.Read
      tags:
        - MeterConfigurations
      summary: Search meter configurations templates or instances.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of meter configurations templates according to passed filters.
        In order to search meter configuration templates, MeterConfigurationTemplate.Read permission is required.
        In order to search meter configuration instances, Meters.Read permission is required
      operationId: searchMeterConfigurations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterConfigurations.yaml"
      responses:
        "200":
          description: Meter configurations returned successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterConfigurationsPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
  /v2/meter-inputs/{meterInputId}:
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Get meter input base data.
      description: |-
        Get meter input master data for meter configuration template or meter configuration instance.
        In order to get meter input master data for meter configuration template, MeterConfigurationTemplate.Read permission is required.
        In order to get meter input master data for meter configuration instance, Meters.Read permission is required.
      operationId: getMeterInputBaseData
      parameters:
        - $ref: "./parameters/MeterInputId.yaml"
      responses:
        "200":
          description: Returns meter input base data.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterInputBaseData.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Update meter input via instance or template.
      description: |-
        Update meter input via instance or template.
        In order to update  meter input for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to update  meter input for meter configuration instance, Meters.Write permission is required
      operationId: updateMeterInput
      parameters:
        - $ref: "./parameters/MeterInputId.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterInput.yaml"
        required: true
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Delete meter input via instance or template.
      description: |-
        Delete meter input via instance or template.
        In order to delete meter input for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to delete meter input for meter configuration instance, Meters.Write permission is required.
      operationId: deleteMeterInput
      parameters:
        - $ref: "./parameters/MeterInputId.yaml"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-inputs/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Returns paged list of meter inputs.
      description: Returns paged list of meter inputs related with meter.
      operationId: searchMeterInputs
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterInputs.yaml"
      responses:
        "200":
          description: Paged list of meter inputs related with meter.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterInputsPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-inputs/{meterInputId}/meter-registers:
    post:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Create meter register for meter input.
      description: |-
        Create meter register for meter input.
        In order to create meter register for configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to create meter register for meter configuration instance, Meters.Write permission is required
      operationId: createMeterRegister
      parameters:
        - $ref: "./parameters/MeterInputId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterRegister.yaml"
      responses:
        "201":
          description: Successfully created.
          content:
            application/json:
              schema:
                description: Identifier of newly created meter register
                type: string
                format: uuid
              example: "FE1FEA7B-78F1-44DC-B88D-39BBB8E12B12"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Returns list of all meter registers.
      description: |-
        Returns list of all meter registers related with meter input.
        In order to return list of all meter registers for configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to return list of all meter registers for configuration instance, Meters.Write permission is required
      operationId: getMeterRegistersByMeterInputId
      parameters:
        - $ref: "./parameters/MeterInputId.yaml"
      responses:
        "200":
          description: List of all meter registers related with meter input.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterRegistersForMeterInput.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-registers/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterRegisters
      summary: Returns paged list of meter registers.
      description: Returns paged list of meter registers related with meter.
      operationId: searchMeterRegisters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterRegisters.yaml"
      responses:
        "200":
          description: Paged list of meter registers related with meter.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterRegistersPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-registers/{meterRegisterId}:
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterRegisters
      summary: Get meter register base data.
      description: |-
        Retrieve meter register related with meter register id.
        In order to get meter register for meter configuration template, MeterConfigurationTemplate.Read permission is required.
        In order to get meter register for meter configuration instance, Meters.Read permission is required
      operationId: getMeterRegisterBaseData
      parameters:
        - $ref: "./parameters/MeterRegisterId.yaml"
      responses:
        "200":
          description: Returns meter register data related with given id.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterRegisterBaseData.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterRegisters
      summary: Update meter register via instance or configuration template.
      description: |-
        Update meter register via instance or configuration template.
        In order to update meter register for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to update meter register for meter configuration instance, Meters.Write permission is required.
      operationId: updateMeterRegister
      parameters:
        - $ref: "./parameters/MeterRegisterId.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterRegister.yaml"
        required: true
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterRegisters
      summary: Delete meter register (instance or via configuration template).
      description: |-
        Delete meter register (instance or via configuration template).
        In order to delete meter register for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to delete meter register for meter configuration instance, Meters.Write permission is required
      operationId: deleteMeterRegister
      parameters:
        - $ref: "./parameters/MeterRegisterId.yaml"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/commands/batch-attach-meter-commands:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Meters.Write
      tags:
        - Meters
      summary: Batch attach meter commands to meters.
      description: Batch attach meter commands to a list of selected meters.
      operationId: batchAttachMeterCommandsToMeters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/BatchAttachMeterCommandsToMeters.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "202":
          description: Request of attaching meter commands to list of meters is accepted and scheduled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/commands/batch-detach-meter-commands:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Meters.Write
      tags:
        - Meters
      summary: Batch detach meter commands from meters.
      description: Batch detach meter commands from list of selected meters.
      operationId: batchDetachMeterCommandsFromMeters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/BatchDetachMeterCommandsFromMeters.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "202":
          description: Request of detaching meter commands from list of meters is accepted and scheduled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/commands/attach-meter-batch:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Attaches multiple meters to a meter batch.
      description: Attaches multiple meters to a meter batch or detaches them from the current meter batch and attaches it to a new meter batch.
      operationId: attachMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AttachMeterBatch.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "202":
          description: Request to attach meters to the selected meter batch is accepted and scheduled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/commands/detach-meter-batch:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Detach multiple meters to a meter batch.
      description: Detach multiple meters to a meter batch.
      operationId: detachMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/DetachMeterBatch.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "202":
          description: Request to detach selected meters from the meter batch is accepted and scheduled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/users/list:
    get:
      tags:
        - Users
      summary: Returns paged list of users.
      description: Returns paged list of users.
      operationId: getApplicationUsers
      x-authorization: Meters.Read
      responses:
        "200":
          description: Application users found.
          content:
            application/json:
              schema:
                type: array
                description: Collection of Users.
                minItems: 0
                maxItems: 1000
                items:
                  $ref: "./schemas/User.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
  /v2/users/{userId}:
    get:
      tags:
        - Users
      summary: Returns base user data.
      description: Returns base user data.
      operationId: getApplicationBaseUserData
      x-authorization-1: Meters.Read
      parameters:
        - $ref: "./parameters/UserId.yaml"
      responses:
        "200":
          description: Application users found.
          content:
            application/json:
              schema:
                $ref: "./schemas/User.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
  /v2/electricity-meters/{meterId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: Gets the electricity meter by *id*.
      description: Endpoint serving functionality of getting electricity meter by id.
      operationId: getElectricityMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "200":
          description: Return electricity meter master data.
          content:
            application/json:
              schema:
                $ref: "./schemas/ElectricityMeter.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: Deletes the electricity meter by *id*.
      description: Endpoint serving functionality of deleting electricity meter by id.
      operationId: deleteElectricityMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    patch:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: Updates the electricity meter.
      description: Endpoint serving functionality of updating electricity meter.
      operationId: updateElectricityMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateElectricityMeterData.yaml"
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/electricity-meters:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: Creates the electricity meter.
      description: Endpoint serving functionality of creating electricity meter.
      operationId: createElectricityMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateElectricityMeter.yaml"
      responses:
        "201":
          description: "Returns created electricity meter id"
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Id of created electricity meter.
                minimum: 0
                maximum: 2147483647
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/electricity-meters/{meterId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: List of electricity meter batches related to meter.
      description: Gets the list of meter batches related to meter.
      operationId: getRelatedMeterBatchesForElectricityMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "200":
          description: List of electricity meter batches related to meter.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesForMeterResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/water-meters/{meterId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: Gets the water meter by *id*.
      description: Endpoint serving functionality of getting water meter by id.
      operationId: getWaterMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "200":
          description: Return water meter master data.
          content:
            application/json:
              schema:
                $ref: "./schemas/WaterMeter.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization-1: Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: Deletes the water meter by *id*.
      description: Endpoint serving functionality of deleting water meter by id.
      operationId: deleteWaterMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    patch:
      x-authorization-1: Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: Updates the water meter.
      description: Endpoint serving functionality of updating water meter.
      operationId: updateWaterMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateWaterMeterData.yaml"
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/water-meters:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: Creates the water meter.
      description: Endpoint serving functionality of creating water meter.
      operationId: createWaterMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateWaterMeter.yaml"
      responses:
        "201":
          description: "Returns created water meter id"
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Id of created electricity meter.
                minimum: 0
                maximum: 2147483647
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/water-meters/{meterId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: List of water meter batches related to meter.
      description: Gets the list of meter batches related to meter.
      operationId: getRelatedMeterBatchesForWaterMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "200":
          description: List of water meter batches related to meter.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesForMeterResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/heat-meters/{meterId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: Gets the heat meter by *id*.
      description: Endpoint serving functionality of getting heat meter by id.
      operationId: getHeatMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "200":
          description: Return heat meter master data.
          content:
            application/json:
              schema:
                $ref: "./schemas/HeatMeter.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization-1: Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: Deletes the heat meter by *id*.
      description: Endpoint serving functionality of deleting heat meter by id.
      operationId: deleteHeatMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    patch:
      x-authorization-1: Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: Updates the heat meter.
      description: Endpoint serving functionality of updating heat meter.
      operationId: updateHeatMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateHeatMeterData.yaml"
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/heat-meters:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: Creates the heat meter.
      description: Endpoint serving functionality of creating heat meter.
      operationId: createHeatMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateHeatMeter.yaml"
      responses:
        "201":
          description: "Returns created heat meter id"
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Id of created electricity meter.
                minimum: 0
                maximum: 2147483647
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/heat-meters/{meterId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: List of heat meter batches related to meter.
      description: Gets the list of meter batches related to meter.
      operationId: getRelatedMeterBatchesForHeatMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "200":
          description: List of heat meter batches related to meter.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesForMeterResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/commands/export:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Csv export of meters list
      description: Gets the list of meters in form of csv file.
      operationId: exportMeters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/ExportMetersBody.yaml"
      responses:
        "200":
          description: Csv of list of meters successfully exported.
          content:
            text/csv:
              schema:
                description: File content
                type: string
                maxLength: 100000
                format: binary
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/commands/search-related-meter-frames:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: ConnectionPoints.Read
      tags:
        - Meters
      summary: Fetches list of meter frames related to the given meter.
      description: Returns of meter frames related to the given meter
      operationId: searchRelatedMeterFrames
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./schemas/SearchRelatedMeterFrames.yaml
        required: true
      responses:
        "200":
          description: Related Meter frames response
          content:
            application/json:
              schema:
                $ref: ./responses/GetRelatedMeterFrames.yaml
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
  /v2/meters/commands/batch-delete:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Search of meters list
      description: Allows to batch delete meters by Identifiers or SearchQuery strategy.
      operationId: batchDelete
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/BatchDeleteMetersByStrategy.yaml"
      parameters:
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      responses:
        "202":
          description: Delete meters request accepted.
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/{meterId}/configurations/attach/{meterConfigurationId}:
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Attach meter configuration template to existing meter.
      description: Attach meter configuration template to existing meter.
      operationId: attachMeterConfiguration
      parameters:
        - $ref: "./parameters/MeterId.yaml"
        - $ref: "./parameters/MeterConfigurationId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "201":
          description: Successfully attached.
          content:
            application/json:
              schema:
                description: Identifier of attached meter configuration instance.
                type: string
                format: uuid
              example: "FE1FEA7B-78F1-44DC-B88D-39BBB8E12B12"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/next-available-statuses/{meterStatusId}:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Get list of available statuses that provided status can be switched to.
      description: Get list of available statuses that provided status can be switched to.
      operationId: getNextAvailableMeterStatuses
      parameters:
        - $ref: "./parameters/MeterStatusId.yaml"
      responses:
        "200":
          description: List of available meter statuses.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterStatusesIdsCollection.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/commands/advance-search:
    post:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: List of meters.
      description: Gets the list of meters matching advance search criteria.
      operationId: advanceSearchMeters
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/PagedSearchExpressionMeter.yaml"
        required: true
      responses:
        "200":
          description: List of meters matching search criteria.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMetersPagedResultWithColumnHeaders.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/dictionaries:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets all dictionaries.
      description: Endpoint supporting dictionaries functionality used in meter management process.
      operationId: getAllDictionaries
      responses:
        "200":
          description: Dictionaries successfully returned.
          content:
            application/json:
              schema:
                $ref: "./schemas/GetAllDictionaries.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-templates:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterTemplates
      summary: Creates meter template.
      description: Creates meter template data. Meter templates support easy and correct creation of meters. The meter template can provide default values for most of the attributes that can be set during creation of meter.
      operationId: createMeterTemplate
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterTemplateBody.yaml"
        required: true
      responses:
        "201":
          description: Successfully created Meter Template.
          content:
            application/json:
              schema:
                type: integer
                format: int32
                minimum: 1
                maximum: 2147483647
                description: Created Meter Template identifier.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-templates/{meterTemplateId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterTemplates
      summary: Gets specific Meter Template
      description: Gets specific Meter Template by identifier.
      operationId: getMeterTemplateById
      parameters:
        - $ref: "./parameters/MeterTemplateId.yaml"
      responses:
        "200":
          description: Found Meter Template.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterTemplate.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterTemplates
      summary: Updates specific Meter Template
      description: Updates specific Meter Template by identifier.
      operationId: updateMeterTemplate
      parameters:
        - $ref: "./parameters/MeterTemplateId.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterTemplateBody.yaml"
        required: true
      responses:
        "204":
          description: Updated meter template.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterTemplates
      summary: Deletes specific Meter Template
      description: Deletes specific Meter Template by identifier.
      operationId: deleteMeterTemplateById
      parameters:
        - $ref: "./parameters/MeterTemplateId.yaml"
      responses:
        "204":
          description: Deleted meter template.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-templates/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterTemplates
      summary: Search Meter Templates by name.
      description: |-
        ### Result
        Returns filtered, paged list of Meter Templates according to passed name.
      operationId: searchMeterTemplates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterTemplates.yaml"
      responses:
        "200":
          description: Meter Templates returned successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterTemplatesPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
  /v2/meter-command-processes:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute and send a meter command on a single meter.
      description: Execute and send a meter command on a single meter.
      operationId: executeMeterCommandProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "201":
          description: Details of execution meter command with steps.
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Meter command process center identifier.
                minimum: 1
                maximum: 2147483647
                example: 543535
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Read
      tags:
        - MeterCommandProcesses
      summary: Retrieve details of execution of meter command with steps.
      description: Retrieve details of execution of meter command with steps.
      operationId: getMeterCommandProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
      responses:
        "200":
          description: Details of execution meter command with steps.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandProcessBody.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Updates meter command process.
      description: Updates meter command process.
      operationId: updateMeterCommandProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterCommandProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully updated the meter command process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Reruns failed meter command process.
      description: Reruns failed meter command process.
      operationId: rerunMeterCommandProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully rerun the meter command process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Cancels meter command process.
      description: Cancels meter command process.
      operationId: cancelMeterCommandProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully cancelled the meter command process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Marks as handled meter command process.
      description: Marks as handled meter command process.
      operationId: markAsHandledMeterCommandProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: ./responses/400.yaml
        "401":
          $ref: ./responses/401.yaml
        "403":
          $ref: ./responses/403.yaml
        "404":
          $ref: ./responses/404.yaml
        "422":
          $ref: ./responses/422.yaml
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: ./responses/500.yaml
        "503":
          $ref: ./responses/503.yaml
        "504":
          $ref: ./responses/504.yaml
      security:
        - Jwt: []
  /v2/meter-command-processes/commands/execute-on-multiple-meters:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute meter command processes with individual meter command on multiple meters.
      description: Execute meter command processes on multiple meters.
      operationId: executeMeterCommandProcessesOnMultipleMeters
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandProcessesOnMultipleMetersBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "202":
          description: Request to start meter command processes for multiple meters successfully accepted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/meter-components/next-available-statuses/{componentStatusId}:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterComponents
      summary: Get list of available statuses that provided status can be switched to.
      description: Get list of available statuses that provided status can be switched to.
      operationId: getNextAvailableStatuses
      parameters:
        - $ref: "./parameters/ComponentStatusId.yaml"
      responses:
        "200":
          description: List of available statuses.
          content:
            application/json:
              schema:
                $ref: "./schemas/StatusesIdCollection.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-components:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Get components related with related meter id.
      description: Get meter relations data.
      operationId: getMeterRelations
      parameters:
        - in: query
          name: meterId
          required: true
          description: Meter identifier.
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 21474
          example: 1
        - $ref: "./parameters/SupplyTypeCodeQueryString.yaml"
      responses:
        "200":
          description: Successfully fetched Meter components.
          content:
            application/json:
              schema:
                description: Meter components data.
                type: array
                items:
                  $ref: "./schemas/MeterComponentPayload.yaml"
                minItems: 0
                maxItems: 255
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Creates meter component.
      description: Creates meter component data. Meter component represents a component that is installed with or in a physical meter.
      operationId: createMeterComponent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterComponent.yaml"
      responses:
        "201":
          description: Successfully created Meter Component.
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Id of created electricity meter.
                minimum: 1
                maximum: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-components/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Search Components
      description: |-
        ### Result
        Returns filtered, paged list of Meter Components according to passed filters.
      operationId: searchMeterComponents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterComponentsBody.yaml"
      responses:
        "200":
          description: Filtered, paged list of Meter Components
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterComponentsPagedResultWithColumnHeaders.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/electricity-meter-batches/{technicalId}:
    get:
      x-authorization-1: MeterBatch.Read
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: Gets the electricity meter batch by it's *technical id*.
      description: Endpoint serving functionality of getting electricity meter batch by meter batch technical id.
      operationId: getElectricityMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      responses:
        "200":
          description: Return electricity meter batch details.
          content:
            application/json:
              schema:
                $ref: "./schemas/ElectricityMeterBatchDetails.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: Updates an electricity meter batch
      description: Endpoint for updating an electricity meter batch
      operationId: updateElectricityMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateElectricityMeterBatch.yaml"
      responses:
        "204":
          description: Electricity meter batch successfully updated.
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/electricity-meter-batches/{technicalId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: List of electricity meter batches related to meter batch.
      description: Gets the list of meter batches related to meter batch.
      operationId: getRelatedMeterBatchesForElectricityMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      responses:
        "200":
          description: List of electricity meter batches related to meter batch.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/water-meter-batches/{technicalId}:
    get:
      x-authorization-1: MeterBatch.Read
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: Gets the water meter batch by it's *technical id*.
      description: Endpoint serving functionality of getting water meter batch by meter batch technical id.
      operationId: getWaterMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      responses:
        "200":
          description: Return water meter batch details.
          content:
            application/json:
              schema:
                $ref: "./schemas/WaterMeterBatchDetails.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: Updates a water meter batch
      description: Endpoint for updating a water meter batch
      operationId: updateWaterMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateWaterMeterBatch.yaml"
      responses:
        "204":
          description: Water meter batch successfully updated.
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/water-meter-batches/{technicalId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: List of water meter batches related to meter batch.
      description: Gets the list of meter batches related to meter batch.
      operationId: getRelatedMeterBatchesForWaterMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      responses:
        "200":
          description: List of water meter batches related to meter batch.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/heat-meter-batches/{technicalId}:
    get:
      x-authorization-1: MeterBatch.Read
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: Gets the electricity meter batch by it's *technical id*.
      description: Endpoint serving functionality of getting heat meter batch by meter batch technical id.
      operationId: getHeatMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      responses:
        "200":
          description: Return heat meter batch details.
          content:
            application/json:
              schema:
                $ref: "./schemas/HeatMeterBatchDetails.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: Updates a heat meter batch
      description: Endpoint for updating a heat meter batch
      operationId: updateHeatMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateHeatMeterBatch.yaml"
      responses:
        "204":
          description: Heat meter batch successfully updated.
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/heat-meter-batches/{technicalId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: List of heat meter batches related to meter batch.
      description: Gets the list of meter batches related to meter batch.
      operationId: getRelatedMeterBatchesForHeatMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      responses:
        "200":
          description: List of heat meter batches related to meter batch.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/electricity-meter-batches:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: Creates the electricity meter batch
      description: Endpoint serving functionality of creating electricity meter batch
      operationId: createElectricityMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateElectricityMeterBatch.yaml"
      responses:
        "201":
          description: "Returns created electricity meter batch technical id"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "./schemas/DataTypes/Guid.yaml"
                description: Meter Batch Technical Id of created electricity meter batch
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/water-meter-batches:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: Creates the water meter batch
      description: Endpoint serving functionality of creating water meter batch
      operationId: createWaterMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateWaterMeterBatch.yaml"
      responses:
        "201":
          description: "Returns created water meter batch technical id"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "./schemas/DataTypes/Guid.yaml"
                description: Meter Batch Technical Id of created water meter batch
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/heat-meter-batches:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: Creates the heat meter batch
      description: Endpoint serving functionality of creating heat meter batch
      operationId: createHeatMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateHeatMeterBatch.yaml"
      responses:
        "201":
          description: "Returns created heat meter batch technical id"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "./schemas/DataTypes/Guid.yaml"
                description: Meter Batch Technical Id of created heat meter batch
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/meter-batches/commands/search:
    post:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatches
      summary: List of meter batches.
      description: Gets the list of meter batches matching search criteria.
      operationId: searchMeterBatches
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterBatchesBody.yaml"
        required: true
      responses:
        "200":
          description: List of meter matches matching search criteria.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterBatchesPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/meter-components/{meterComponentId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Get meter component.
      description: Get meter component data.
      operationId: getMeterComponent
      parameters:
        - $ref: "./parameters/MeterComponentId.yaml"
      responses:
        "200":
          description: Successfully fetched Meter Component.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterComponent.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Updates meter component.
      description: Updates meter component data.
      operationId: updateMeterComponent
      parameters:
        - $ref: "./parameters/MeterComponentId.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterComponent.yaml"
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Deletes meter component.
      description: Deletes meter component data.
      operationId: deleteMeterComponent
      parameters:
        - $ref: "./parameters/MeterComponentId.yaml"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-components/commands/batch-delete:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Batch delete of meter components.
      description: Batch delete of meter components.
      operationId: batchDeleteMeterComponents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/BatchDeleteMeterComponents.yaml"
      parameters:
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      responses:
        "202":
          description: Meter components ordered for deletion.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-components/commands/get-available:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Get available components for meter relations for specific meter.
      description: Get available components.
      operationId: getMeterRelationsAvailableComponents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/MeterRelationsAvailableComponentsFilter.yaml"
      responses:
        "200":
          description: Successfully fetched Meter components available for meter relations.
          content:
            application/json:
              schema:
                type: array
                maxItems: 10000
                description: Meter relations available components.
                items:
                  $ref: "./schemas/MeterComponentBase.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-components/{meterComponentId}/get-meter-relations:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Get relations to meters for specific meter component.
      description: Get relations to meters for specific meter component.
      operationId: getMeterComponentMeterRelations
      parameters:
        - $ref: "./parameters/MeterComponentId.yaml"
      responses:
        "200":
          description: Successfully fetched meter relations for current meter component.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterRelationsToMeterComponent.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/bulk-create-meters-manually-processes:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Schedule process to bulk create meters.
      description: Schedule process to bulk create meters.
      operationId: bulkCreateMetersManually
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/BulkCreateMeterProcess.yaml"
        required: true
      responses:
        "202":
          content:
            application/json:
              schema:
                type: integer
                format: int32
                minimum: 1
                maximum: 2147483647
                example: 1
                description: New created process id.
          description: -|
            Schedule bulk create meters process was accepted.
            Return identifier of process in process center.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/bulk-create-meters-manually-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Cancels bulk create meters manually process.
      description: Cancels bulk create meters manually process.
      operationId: cancelBulkCreateMetersManually
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/bulk-create-meters-manually-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Marks as handled the second step of bulk create meters manually process.
      description: -|
        Changes the state of the second step of bulk create meters manually process to Manually handled.
        Changes the process state to Completed.
      operationId: markAsHandledBulkCreateMetersManuallyProcessStep
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      responses:
        "204":
          description: Process step Marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/bulk-create-meters-manually-processes/{processId}/commands/generate-csv-report:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Triggers a job that generates a report with all meters.
      description: "-| Triggers a job that generates a report with all meters. CSV file includes such columns as Serial number, State and optional Error message"
      operationId: generateCsvReportForBulkCreateMetersManuallyProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "202":
          content:
            application/json:
              schema:
                $ref: "./schemas/CsvReportDetailsForBulkCreateMetersManuallyProcess.yaml"
          description: "-| Request was accepted."
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/bulk-create-meters-manually-processes/{processId}/commands/download-csv-report:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Downloads the report as a CSV file.
      description: Downloads the report as a CSV file.
      operationId: downloadCsvReportForBulkCreateMetersManuallyProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/bulk-create-meters-manually-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Gets specific bulk create meters manually process
      description: Gets specific bulk create meters manually process by identifier
      operationId: getBulkCreateMetersManuallyProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
      responses:
        "200":
          description: Found bulk create meters manually process.
          content:
            application/json:
              schema:
                $ref: "./schemas/BulkCreateMetersManuallyProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Update specific bulk create meters manually process
      description: Update specific bulk create meters manually process by identifier
      operationId: updateBulkCreateMetersManuallyProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateBulkCreateMetersManuallyProcess.yaml"
        required: true
      responses:
        "204":
          description: Process updated successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-manually-processes/{processId}/commands/update-additional-information:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Update additional information fields of a specific bulk create meters manually process
      description: Update additional information fields of a specific bulk create meters manually process by identifier
      operationId: updateBulkCreateMetersManuallyProcessAdditionalInformationById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateBulkCreateMetersManuallyProcessAdditionalInformation.yaml"
        required: true
      responses:
        "204":
          description: Additional information of the bulk create meters manually process updated successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-manually-processes/{processId}/commands/re-run:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Rerun bulk create meters manually process.
      description: Rerun bulk create meters manually process.
      operationId: rerunBulkCreateMetersManually
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      responses:
        "204":
          description: Process restarted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/bulk-create-meters-from-file-processes:
    post:
      description: |-
        ### Remarks
        - Create process for bulk creating Meters from file
      summary: Endpoint that allows to create process for bulk creating Meters from file
      tags:
        - BulkCreateMetersFromFile
      operationId: bulkCreateMetersFromFileProcess
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/BulkCreateMetersFromFileProcess.yaml"
            examples:
              ValidRequest:
                value:
                  asOfDate: "2023-10-04T10:27:33.0309131Z"
                  file:
                    - fileId: aa502c9b-2c74-4691-87d4-010ce8c45880
                    - fileName: "meters.csv"
                  supplyTypeCode: "Electricity"
                  configurationTemplateId: 12334e81-3fa0-4cff-bc37-3b836ff7b0cc
                  productionYear: "2020-12-31"
                  purchaseYear: "2022-05-31"
                  meterBatchId: 53139d84-836c-4dc0-95ce-063a6bd86c40
      responses:
        "202":
          content:
            application/json:
              schema:
                type: integer
                format: int32
                minimum: 1
                maximum: 2147483647
                example: 1
                description: Id of newly created process.
          description: |-
            Bulk create Meters from file process was created.
            Returns identifier of newly created process in process center.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-from-file-processes/template:
    get:
      tags:
        - BulkCreateMetersFromFile
      operationId: getBulkCreateMetersFromFileCsvTemplate
      description:
        Endpoint for retrieving csv template. User can download a file, |-
        which consists of empty table with predefined meter attributes names as headers. |-
        User fills the template on local device with data of meters to be created during bulk create from file process.
      summary: Endpoint for retrieving csv template for bulk creating meters from file.
      x-authorization: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      parameters:
        - $ref: "./parameters/SupplyTypeCodeQueryString.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: 200 Success
          content:
            application/octet-stream:
              schema:
                description: CSV file with results
                maxLength: 2147483647
                type: string
                format: binary
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
  /v2/bulk-create-meters-from-file-processes/commands/generate-upload-url:
    post:
      description: |-
        ### Remarks
        - Generate URL for upload file to the public storage
      summary: Endpoint that allows to generate URL for upload file to public storage
      tags:
        - BulkCreateMetersFromFile
      operationId: getUploadUrl
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      responses:
        "200":
          description: Model for request upload url.
          content:
            application/json:
              schema:
                $ref: "./schemas/FileUploadUrl.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-from-file-processes/{processId}/commands/download-csv-validation-report:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - BulkCreateMetersFromFile
      summary: Downloads the validation outcome as a CSV file.
      description: Downloads the validation outcome as a CSV file.
      operationId: downloadCsvValidationReportForBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/bulk-create-meters-from-file-processes/{processId}/commands/download-csv-creation-report:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - BulkCreateMetersFromFile
      summary: Downloads the creation outcome as a CSV file.
      description: Downloads the creation outcome as a CSV file.
      operationId: downloadCsvCreationReportForBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/bulk-create-meters-from-file-processes/{processId}/commands/start-meter-creation:
    post:
      description: |-
        ### Remarks
        - Starts creation of meters
      summary: Endpoint that allows to start creation of meters
      tags:
        - BulkCreateMetersFromFile
      operationId: startMeterCreation
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      responses:
        "202":
          description: "Meter creation started"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-from-file-processes/{processId}/commands/select-meter-template:
    post:
      description: |-
        ### Remarks
        - Select meter template for bulk creating Meters from file process
      summary: Endpoint that allows to select meter template for bulk creating Meters from file process
      tags:
        - BulkCreateMetersFromFile
      operationId: selectMeterTemplate
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SelectMeterTemplateForProcess.yaml"
            examples:
              ValidRequest:
                value:
                  meterTemplateId: 123
      responses:
        "204":
          description: Meter template selected successfully
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-from-file-processes/{processId}:
    get:
      description: |-
        ### Remarks
        - Get bulk create meters from file specific process by id
      summary: Endpoint that allows to get bulk create meters from file specific process by its identifier
      tags:
        - BulkCreateMetersFromFile
      operationId: getBulkCreateMetersFromFileProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      responses:
        "200":
          description: Found bulk create meters from file process.
          content:
            application/json:
              schema:
                $ref: "./schemas/BulkCreateMetersFromFileProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Update additional information fields of a specific bulk create meters from file process
      description: Update additional information fields of a specific bulk create meters from file process by identifier
      operationId: updateBulkCreateMetersFromFileProcessAdditionalInformationById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateBulkCreateMetersFromFileProcessAdditionalInformation.yaml"
      responses:
        "204":
          description: Additional information of the bulk create meters manually process updated successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-from-file-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Cancels bulk create meters from file process.
      description: Cancels bulk create meters from file process.
      operationId: cancelBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-from-file-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Marks as handled bulk create meters from file process.
      description: Changes the process state to Manually Handled.
      operationId: markAsHandledBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process Marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-from-file-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Rerun bulk create meters from file process.
      description: Rerun bulk create meters from file process.
      operationId: rerunBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process restarted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/bulk-create-meters-from-file-processes/{processId}/commands/reset-to-meter-template-selection-step:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - BulkCreateMetersFromFile
      summary: Reset drafts validation and go back to meter template selection step in bulk create meters from file process.
      description: Reset drafts validation and go back to meter template selection step in bulk create meters from file process.
      operationId: resetToMeterTemplateSelectionStepBulkCreateMetersFromFileProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process reset to meter template selection step.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"

  /v2/meter-command-groups/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Retrieves list of the meter command groups.
      description: Retrieves list of the meter command groups.
      operationId: searchMeterCommandGroup
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterCommandGroup.yaml"
      responses:
        "200":
          description: Successfully retrieved meter command group.
          content:
            application/json:
              schema:
                $ref: "./schemas/PagedMeterCommandGroups.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/meter-command-groups/commands/get-command-groups-by-command-id/{meterCommandId}:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Retrieves list of the meter command groups related to a given command.
      description: Retrieves list of the meter command groups related to a given command.
      operationId: getMeterCommandGroupsByCommandId
      parameters:
        - $ref: "./parameters/MeterCommandId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/GetMeterCommandGroupsRelatedToCommandQuery.yaml"
      responses:
        "200":
          description: Successfully retrieved meter command groups related to a given command.
          content:
            application/json:
              schema:
                $ref: "./schemas/PagedMeterCommandGroupsRelatedToCommand.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/meter-command-group-processes:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute and send a meter command group on single meter.
      description: Execute and send a meter command group on single meter.
      operationId: executeMeterCommandGroupProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandGroupProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "201":
          description: Execution of meter command group successfully started.
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Meter command group process center identifier.
                minimum: 1
                maximum: 2147483647
                example: 543535
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/meter-command-groups/{meterCommandGroupId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Retrieves meter command group by id.
      description: Retrieves meter command group by id.
      operationId: getMeterCommandGroup
      parameters:
        - $ref: "./parameters/MeterCommandGroupId.yaml"
      responses:
        "200":
          description: Successfully retrieved meter command group.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandGroup.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterCommands
      summary: Update details of a meter command group.
      description: Update details of a meter command group.
      operationId: updateMeterCommandGroup
      parameters:
        - $ref: "./parameters/MeterCommandGroupId.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterCommandGroup.yaml"
      responses:
        "204":
          description: Update of meter command group finished successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    delete:
      x-authorization: MeterCommand.Write
      tags:
        - MeterCommands
      summary: Delete a meter command group.
      description: Delete a meter command group.
      operationId: deleteMeterCommandGroup
      parameters:
        - $ref: "./parameters/MeterCommandGroupId.yaml"
      responses:
        "204":
          description: Delete of meter command group finished successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Read
      tags:
        - MeterCommandProcesses
      summary: Retrieve details of execution of meter command group with steps.
      description: Retrieve details of execution of meter command group with steps.
      operationId: getMeterCommandGroupProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
      responses:
        "200":
          description: Details of execution meter command group with steps.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandGroupProcessBody.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Updates meter command group process.
      description: Updates meter command group process.
      operationId: updateMeterCommandGroupProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterCommandGroupProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully returned the meter command group process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-processes/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Read
      tags:
        - MeterCommandProcesses
      summary: Search meter command group processes.
      description: Search meter commands group processes.
      operationId: searchMeterCommandGroupProcesses
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterCommandGroupProcesses.yaml"
      responses:
        "200":
          description: Details of execution meter command group with steps.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterCommandGroupProcessesPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Reruns failed meter command group process.
      description: Reruns failed meter command group process.
      operationId: rerunMeterCommandGroupProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully returned the meter command group process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Cancels meter command group process.
      description: Cancels meter command group process.
      operationId: cancelMeterCommandGroupProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully cancelled the meter command group process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Marks as handled meter command group process.
      description: Marks as handled meter command group process.
      operationId: markAsHandledMeterCommandGroupProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-processes/commands/execute-on-multiple-meters:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute meter command group processes with individual meter command group on multiple meters.
      description: Execute meter command group processes on multiple meters.
      operationId: executeMeterCommandGroupProcessesOnMultipleMeters
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandGroupProcessesOnMultipleMetersBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "202":
          description: Request to start meter command group processes for multiple meters successfully accepted.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-bulk-processes:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Execute a meter command group process on multiple meters.
      description: Execute a meter command group process on multiple meters.
      operationId: executeMeterCommandGroupBulkProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/ExecuteMeterCommandGroupBulkProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "201":
          description: Execution of meter command group successfully started.
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Meter command group process center identifier.
                minimum: 1
                maximum: 2147483647
                example: 543535
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-bulk-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Read
      tags:
        - MeterCommandProcesses
      summary: Retrieve details of bulk execution of meter command group.
      description: Retrieve details of bulk execution of meter command group.
      operationId: getMeterCommandGroupBulkProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
      responses:
        "200":
          description: Details of execution meter command group with steps.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterCommandGroupBulkProcessBody.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Updates meter command group bulk process.
      description: Updates meter command group bulk process.
      operationId: updateMeterCommandGroupBulkProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterCommandGroupProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully updated the meter command group bulk process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-bulk-processes/{processId}/commands/attach-meters:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Attach meters to already existing bulk meter command group execution.
      description: Attach meters to already existing bulk meter command group execution.
      operationId: attachMetersToMeterCommandGroupBulkProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/AttachMetersToMeterCommandGroupBulkProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully attached meters to already existing bulk meter command group execution.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-bulk-processes/{processId}/commands/detach-meters:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Detach meters from already existing bulk meter command group execution.
      description: Detach meters from already existing bulk meter command group execution.
      operationId: detachMetersFromMeterCommandGroupBulkProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/DetachMetersFromMeterCommandGroupBulkProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully detached meters from already existing bulk meter command group execution.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-bulk-processes/{processId}/commands/start:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Start meter command group bulk process.
      description: Start meter command group bulk process.
      operationId: startMeterCommandGroupBulkProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process started.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-bulk-processes/{processId}/commands/re-run-failed-process-creation:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Re-runs failed process creation.
      description: Re-runs failed process creation.
      operationId: reRunFailedMeterCommandGroupBulkProcessCreation
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "202":
          description: Operation was scheduled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-bulk-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Mark as handled failed process creation.
      description: Mark as handled failed process creation.
      operationId: markAsHandledMeterCommandGroupBulkProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-command-group-bulk-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      x-authorization-4: MeterCommandProcess.Write
      tags:
        - MeterCommandProcesses
      summary: Cancel the running process and attempt to cancel related subprocesses.
      description: Cancel the running process and attempt to cancel related subprocesses
      operationId: cancelMeterCommandGroupBulkProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/meter-command-groups:
    post:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterCommands
      summary: Create meter command group.
      description: Create meter command group.
      operationId: createMeterCommandGroup
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterCommandGroup.yaml"
      responses:
        "201":
          description: Successfully created meter command group.
          content:
            application/json:
              schema:
                $ref: "./schemas/DataTypes/Guid.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/export/assign-meters-to-batch-processes/{processId}/meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - MeterBatchesProcesses
      summary: Downloads the list of meters taking part in the process as a CSV file.
      description: Downloads the list of meters taking part in the process as a CSV file.
      operationId: getAssignMetersToBatchProcessMetersCsv
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            text/csv:
              schema:
                description: File content
                type: string
                maxLength: 2147483647
                format: binary
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/assign-meters-to-batch-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Retrieve details of assign meters to batch process.
      description: Retrieve details of assign meters to batch process.
      operationId: getAssignMetersToBatchProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Details of assign meters to batch process.
          content:
            application/json:
              schema:
                $ref: "./schemas/AssignMetersToBatchProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Updates assign meters to batch process.
      description: Updates assign meters to batch process.
      operationId: updateAssignMetersToBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully updated the assign meters to batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/assign-meters-to-batch-processes/{processId}/commands/search-suggested-meter-batches:
    post:
      x-authorization-1: MeterBatch.Read
      x-authorization-2: Meters.Read
      x-authorization-3: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Gets the list of meter batches suggested for the assign meters to batch process.
      description: Gets the list of meter batches suggested for the assign meters to batch process.
      operationId: assignMetersToBatchProcessSearchSuggestedMeterBatches
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ./schemas/AssignMetersToBatchProcessSearchSuggestedMeterBatchesBody.yaml
      responses:
        "200":
          description: List of meter batches suggested for the assign meters to batch process.
          content:
            application/json:
              schema:
                $ref: "./schemas/SearchMeterBatchesPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/assign-meters-to-batch-processes/{processId}/commands/select-meter-batch:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Selects meter batch in the assign meters to batch process.
      description: Selects meter batch in the assign meters to batch process.
      operationId: assignMetersToBatchProcessSelectMeterBatch
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AssignMetersToBatchProcessSelectMeterBatchBody.yaml"
      responses:
        "204":
          description: Successfully selected a meter batch to the assign meters to batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/assign-meters-to-batch-processes/{processId}/commands/select-max-size-exceeded-strategy:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Selects the strategy to use when the meter batch max size is exceeded.
      description: Selects the strategy to use when the meter batch max size is exceeded.
      operationId: assignMetersToBatchProcessSelectMaxSizeExceededStrategy
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AssignMetersToBatchProcessSelectMaxSizeExceededStrategy.yaml"
      responses:
        "204":
          description: Successfully selected max size exceeded strategy.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/assign-meters-to-batch-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Reruns assign meters to batch process.
      description: Reruns assign meters to batch process.
      operationId: rerunAssignMetersToBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully triggered the assign meters to batch process rerun.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/assign-meters-to-batch-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Cancels assign meters to batch process.
      description: Cancels assign meters to batch process.
      operationId: cancelAssignMetersToBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully cancelled the assign meters to batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/assign-meters-to-batch-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Marks as handled assign meters to batch process.
      description: Marks as handled assign meters to batch process.
      operationId: markAsHandledAssignMetersToBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/assign-meters-to-batch-processes:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Create assign meters to batch process.
      description: Create assign meters to batch process.
      operationId: createAssignMetersToBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/AssignMetersToBatchProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "201":
          description: Assign meters to batch process successfully created.
          content:
            application/json:
              schema:
                $ref: "./schemas/AssignMetersToBatchProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/detach-meters-from-batch-processes:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Create detach meter from batch process.
      description: Create detach meter from batch process.
      operationId: createDetachMetersFromBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateDetachMetersFromBatchProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeader.yaml"
      responses:
        "201":
          description: Detach meters from batch process created.
          content:
            application/json:
              schema:
                $ref: "./schemas/DetachMetersFromBatchProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/export/detach-meters-from-batch-processes/{processId}/meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - MeterBatchesProcesses
      summary: Downloads the list of meters taking part in the process as a CSV file.
      description: Downloads the list of meters taking part in the process as a CSV file.
      operationId: getDetachMetersFromBatchProcessMetersCsv
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            text/csv:
              schema:
                description: File content
                type: string
                maxLength: 2147483647
                format: binary
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/detach-meters-from-batch-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Gets specific detach meters from batch process
      description: Gets specific detach meters from batch process by identifier
      operationId: getDetachMetersFromBatchProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Found detach meters from batch process.
          content:
            application/json:
              schema:
                $ref: "./schemas/DetachMetersFromBatchProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Updates detach meters from batch process.
      description: Updates detach meters from batch process.
      operationId: updateDetachMetersFromBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./schemas/UpdateProcessBody.yaml
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully updated the detach meters from batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/detach-meters-from-batch-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Reruns failed detach meters from batch process.
      description: Reruns failed detach meters from batch process.
      operationId: rerunDetachMetersFromBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully returned the detach meters from batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/detach-meters-from-batch-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Cancels detach meters from batch process.
      description: Cancels detach meters from batch process.
      operationId: cancelDetachMetersFromBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully cancelled the detach meters from batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/detach-meters-from-batch-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Marks as handled detach meters from batch process.
      description: Marks as handled detach meters from batch process.
      operationId: markAsHandledDetachMetersFromBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/merge-batches-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Retrieve details of merge meter batches process.
      description: Retrieve details of merge meter batches process.
      operationId: getMergeBatchesProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Details of merge meter batches process.
          content:
            application/json:
              schema:
                $ref: "./schemas/MergeBatchesProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Updates merge batches process.
      description: Updates merge batches process.
      operationId: updateMergeBatchesProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./schemas/UpdateProcessBody.yaml
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully updated the merge batches process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/merge-batches-processes/{processId}/commands/create-batch:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Creates meter batch as a part of a merge batches process.
      description: Endpoint serving functionality of creating meter batch as a part of a merge batches process.
      operationId: createMeterBatchForMergeBatchesProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterBatchForMergeBatchesProcess.yaml"
      responses:
        "201":
          description: "Returns created meter batch technical id"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "./schemas/DataTypes/Guid.yaml"
                description: Meter Batch Technical Id of created meter batch
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-jobs:
    post:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Create meter job.
      description: Create meter job.
      operationId: createMeterJob
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterJob.yaml"
      responses:
        "201":
          description: Successfully created meter job.
          content:
            application/json:
              schema:
                $ref: "./schemas/DataTypes/Guid.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: [ ]
  /v2/meter-jobs/{meterJobId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      tags:
        - MeterJobs
      summary: Retrieves meter job by id.
      description: Retrieves meter job by id.
      operationId: getMeterJob
      parameters:
        - $ref: "./parameters/MeterJobId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Successfully retrieved meter job.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterJob.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: [ ]
    put:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Update details of a meter job.
      description: Update details of a meter job.
      operationId: updateMeterJob
      parameters:
        - $ref: "./parameters/MeterJobId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateMeterJob.yaml"
      responses:
        "204":
          description: Update of meter job finished successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: [ ]
    delete:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Delete a meter job.
      description: Delete a meter job.
      operationId: deleteMeterJob
      parameters:
        - $ref: "./parameters/MeterJobId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Delete of meter job finished successfully.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-jobs/{meterJobId}/commands/skip-next-execution:
    post:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Skip next meter job execution.
      description: Skip next meter job execution.
      operationId: skipNextMeterJobExecution
      parameters:
        - $ref: "./parameters/MeterJobId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Next execution skipped.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-jobs/{meterJobId}/commands/execute-now:
    post:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Execute meter job now
      description: Execute meter job now.
      operationId: executeMeterJobNow
      parameters:
        - $ref: "./parameters/MeterJobId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - in: query
          name: skipNextExecution
          required: true
          description: Skip next planned meter job execution.
          schema:
            type: boolean
          example: true
      responses:
        "201":
          description: Execution of bulk meter command group successfully created.
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Bulk meter command group process center identifier.
                minimum: 1
                maximum: 2147483647
                example: 543535
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meter-jobs/commands/search:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Retrieves list of meter jobs.
      description: Retrieves list of meter jobs.
      operationId: searchMeterJobs
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/SearchMeterJobs.yaml"
      responses:
        "200":
          description: Successfully retrieved meter jobs.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterJobsPagedResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: [ ]
  /v2/meter-jobs/commands/calculate-next-execution-date:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Calculate next execution date based on cron expression.
      description: Calculate next execution date based on cron expression.
      operationId: calculateNextExecutionDate
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CalculateNextExecutionDate.yaml"
      responses:
        "200":
          description: Successfully calculated next execution date.
          content:
            application/json:
              schema:
                $ref: "./schemas/DataTypes/DateTime.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: [ ]
  /v2/split-meter-batch-processes:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Creates two new meter batches as a part of a split meter batch process.
      description: Endpoint serving functionality of creating two meter batches and assigning meters evenly between them as a part of a split meter batch process.
      operationId: createSplitMeterBatchProcess
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateSplitMeterBatchProcess.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "201":
          description: Details of newly created split meter batch process.
          content:
            application/json:
              schema:
                $ref: "./schemas/SplitMeterBatchProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/split-meter-batch-processes/{processId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Retrieve details of split meter batch process.
      description: Retrieve details of split meter batch process.
      operationId: getSplitMeterBatchProcessById
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Details of split meter batches process.
          content:
            application/json:
              schema:
                $ref: "./schemas/SplitMeterBatchProcessDetails.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    put:
      x-authorization-1: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Updates split meter batch process.
      description: Updates split meter batch process.
      operationId: updateSplitMeterBatchProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./schemas/UpdateProcessBody.yaml
        required: true
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully updated the split meter batch process.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/export/split-meter-batch-processes/{processId}/attached-meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - MeterBatchesProcesses
      summary: Returns a csv file consisting of data from meters attached to meter batch.
      description: Returns a csv file consisting of data from meters attached to meter batch.
      operationId: getAttachedMetersForSplitMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        "200":
          description: Document as a file stream.
          content:
            text/csv:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/split-meter-batch-processes/{processId}/commands/create-meter-batches:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Creates new meter batches as a split meter batch process step.
      description: Creates new meter batches as a split meter batch process step.
      operationId: createMeterBatchesForSplitMeterBatchProcess
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateMeterBatchesForSplitMeterBatchProcess.yaml"
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "201":
          description: A collection of TechnicalId's
          content:
            application/json:
              schema:
                description: Technical Id of related meter batch.
                type: array
                minItems: 2
                maxItems: 2
                items:
                  $ref: ./schemas/DataTypes/Guid.yaml
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/split-meter-batch-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Marks as handled split meter batch process.
      description: Marks as handled split meter batch process.
      operationId: markAsHandledSplitMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/split-meter-batch-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Cancels split meter batch process.
      description: Cancels split meter batch process.
      operationId: cancelSplitMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/split-meter-batch-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Reruns split meter batch process.
      description: Reruns split meter batch process.
      operationId: rerunSplitMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "202":
          description: Process scheduled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/merge-batches-processes:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Create merge meter batches process.
      description: Create merge meter batches process.
      operationId: createMergeMeterBatchesProcess
      requestBody:
        content:
          application/json:
            schema:
                $ref: "./schemas/CreateMergeMeterBatchesProcessBody.yaml"
        required: true
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "201":
          description: Merge meter batches process successfully created.
          content:
            application/json:
              schema:
                $ref: "./schemas/MergeMeterBatchesProcessCreated.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/export/merge-batches-processes/{processId}/attached-meters:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-activity-log: DisableResponseBodyLogging
      tags:
        - MeterBatchesProcesses
      summary: Returns a csv file consisting of data from meters attached to both batches to merge.
      description: Returns a csv file consisting of data from meters attached to both batches to merge.
      operationId: getAttachedMetersCsvMergeBatchesProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
        - $ref: "./parameters/AcceptLanguageInHeader.yaml"
      responses:
        '200':
          description: Document as a file stream.
          content:
            text/csv:
              schema:
                type: string
                format: binary
                description: File stream.
                maxLength: 2147483647
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/merge-batches-processes/{processId}/commands/mark-as-handled:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Marks as handled merge meter batch process.
      description: Marks as handled merge meter batch process.
      operationId: markAsHandledMergeMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process marked as handled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/merge-batches-processes/{processId}/commands/cancel:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Cancels merge meter batch process.
      description: Cancels merge meter batch process.
      operationId: cancelMergeMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Process cancelled.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/merge-batches-processes/{processId}/commands/rerun:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatchesProcesses
      summary: Reruns merge meter batch process.
      description: Reruns merge meter batch process.
      operationId: rerunMergeMeterBatchProcess
      parameters:
        - $ref: "./parameters/ProcessId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Successfully triggered the merge batches process rerun.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "./responses/499.yaml"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
components:
  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
