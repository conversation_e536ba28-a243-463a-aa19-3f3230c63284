description: The characteristic of contracted capacity
nullable: true
type: object
additionalProperties: false
properties:
  maximumCurrentInAmperes:
    description: Is mapped to `MaximumCurrent`
    allOf:
      - $ref: '../DataTypes/PositiveDecimalNullable.yaml'
  maximumPowerInKiloWatts:
    description: Is mapped to `MaximumPower`
    allOf:
      - $ref: '../DataTypes/PositiveDecimalNullable.yaml'
