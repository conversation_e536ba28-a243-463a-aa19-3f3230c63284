type: object
description: |
  Extended metering point net settlement group.
additionalProperties: false
properties:
  netSettlementGroup:
    nullable: true
    description: Mapped from DH 'netSettlementGroup'.
    allOf:
      - $ref: '../DataTypes/LongStringNullable.yaml'
  powerPlantId:
    description: Is mapped to `PowerPlantDomainLocationId`
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
    nullable: true
  connectionType:
    nullable: true
    description: Mapped from DH 'connectionType'.
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
  capacity:
    description: Is mapped to `MPCapacity`
    allOf:
      - $ref: '../DataTypes/PositiveDecimal.yaml'
  assetType:
    description: Mapped from DH 'MeteringPoint' assetType.
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
