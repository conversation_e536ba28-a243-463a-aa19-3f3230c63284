type: object
description: Search criteria for searching meters.
required:
  - page
  - waterMeterSearchExpressionCollection
properties:
  page:
    allOf:
      - $ref: '../kmd-elements-meter-management.yaml#/components/schemas/Page'
    description: Page properties.
  waterMeterSearchExpressionCollection:
    description: Search expression collection.
    type: array
    items:
      type: object
      allOf:
        - $ref: "../kmd-elements-meter-management.yaml#/components/schemas/SearchExpression"
      description: Search filter for meters.
      required:
        - propertyName
      properties:
        propertyName:
          description: Pattern for property name for search expression.
          pattern: "^(Id|MeterNumber|MeterTypeId|AlternativeMeterNumber|BarCodeEanNumber|CommunicationMethodId|FirstCommissioningDate|HesSystemId|ProductionYear|PurchaseDate|RemoteDisconnectable|SerialNumber|ManufacturerId|FabricationNumber|FabricationTypeId|MeterControlTypeCode|MainControlMeterRoleCode|RemoteRegisterConfigurationId|RemoteDisplayConfigurationId|MeterStatusId|SecondaryStatusId|MeterConfigurationId|MeterBatchTechnicalId|MeterBatchId|AccuracyClassId|MeterRoleCode|MeterTagIds|StartTimeForMeterBatchCode|WaterMeterConnectionTypeId|WaterMeasuringPrincipleId|WaterMeterSizeId|DataOutputId)$"
          minLength: 1
          maxLength: 50
          type: string
          example: "Id"
      additionalProperties: false
    minItems: 0
    maxItems: 255
additionalProperties: false
