type: object
description: Model used to get Connection Point Default Value Set details.
additionalProperties: false
required:
  - supplyType
  - commonConnectionPointDefaultValueSet
properties:
  id:
    description: Connection Point Default Value Set identifier.
    allOf:
      - $ref: "../../DataTypes/GuidNullable.yaml"
  supplyType:
    $ref: '../../SupplyTypes.yaml'
    description: Supply types of the default value set related to Connection Point.
  commonConnectionPointDefaultValueSet:
    description: The base attributes of Connection Point Default Value Set.
    allOf:
      - $ref: './CommonConnectionPointDefaultValueSet.yaml'
  electricityConnectionPointDefaultValueSet:
    description: Electricity attributes of Connection Point Default Value Set.
    allOf:
      - $ref: './ElectricityConnectionPointDefaultValueSet.yaml'
    nullable: true
  heatingConnectionPointDefaultValueSet:
    description: Heating attributes of Connection Point Default Value Set.
    allOf:
      - $ref: './HeatingConnectionPointDefaultValueSet.yaml'
    nullable: true
  waterConnectionPointDefaultValueSet:
    description: Water attributes of Connection Point Default Value Set.
    allOf:
      - $ref: './WaterConnectionPointDefaultValueSet.yaml'
    nullable: true
  rowVersion:
    description: Row version.
    allOf:
      - $ref: '../../DataTypes/RowVersion.yaml'
    nullable: true
