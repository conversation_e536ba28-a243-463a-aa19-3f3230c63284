﻿title: MeterFramesListSearchQueryModel
type: object
additionalProperties: false
description: Meter Frames list search query model.
required:
  - filter
properties:
  page:
    nullable: true
    type: object
    description: Page object.
    additionalProperties: false
    oneOf:
      - $ref: '../../../_pageable/PageableRequest.yaml'
  sort:
    nullable: true
    type: object
    description: Sort object.
    additionalProperties: false
    oneOf:
      - $ref: '../../../_pageable/properties/SortOrderNullable.yaml'
  filter:
    description: Properties for filtration of Connection Points.
    type: object
    additionalProperties: false
    properties:
      supplyType:
        description: Filter by Type of supply as described by object Supply Types DTO.
        nullable: true
        allOf:
          - $ref: '../../../common/SupplyTypesModel.yaml'
      connectionPointId:
        description: Filter by Connection Point Identifier - must be valid GUID.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/GuidNullable.yaml'
        example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
      meterFrameId:
        description: Filter by Id of the Meter Frame.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/GuidNullable.yaml'
        example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
      connectionPointBusinessId:
        description: Filter by Connection point business identifier.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 1100000016
      meterFrameNumber:
        description: Filter by Meter frame number.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 1100000016-1
      supplyStatus:
        description: Filter by List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
        nullable: true
        allOf:
          - $ref: '../MeterFrameSupplyStatusModel.yaml'
        example: 1
      supplyDisconnectType:
        description: Filter by List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation", "Connected", "Unknown".
        nullable: true
        allOf:
          - $ref: '../MeterFrameSupplyDisconnectTypeModel.yaml'
        example: 1
      noMeter:
        description: Filter by No meter.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/BooleanNullable.yaml'
        example: true
      meterMissing:
        description: Filter by meter is missing.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/BooleanNullable.yaml'
        example: false
      meterReadingType:
        description: Filter by a list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/GuidNullable.yaml'
        example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
      addressLine1:
        description: Filter by Address Line 1 - startsWith (like) search query parameter.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/DescriptionStringNullable.yaml'
        example: 'Trælastgade 21'
      addressLine2:
        description: Filter by Address Line 2 - startsWith (like) search query parameter.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/DescriptionStringNullable.yaml'
        example: '8000 Aarhus C'
      addressStatus:
        description: Filter by Address Status - exact query parameter.
        nullable: true
        allOf:
          - $ref: '../addresses/AddressStatusCode.yaml'
        example: 1
      addressType:
        description: Filter by Address Type - exact query parameter.
        nullable: true
        allOf:
          - $ref: '../addresses/AddressTypeCode.yaml'
        example: 1
      lifeCycleStatus:
        description: Filter by LifeCycleStatus.
        nullable: true
        allOf:
          - $ref: '../LifeCycleStatusCode.yaml'
      darStatus:
        description: Filter by Address DAR Status - exact query parameter.
        nullable: true
        allOf:
          - $ref: '../DarStatusCode.yaml'
      cabinetNumber:
        description: Filter by Number of the cable box where the plug is connected. Ex. 123658.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
      cableNumber:
        description: Filter by Number on execution in station. Ex. 1, 2, 3 etc.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      stationNumber:
        description: Filter by The station from which the branch line is supplied. Ex. 98756.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      transformerNumber:
        description: Filter by Transformer number, for several transformers in a station. Ex. 1, 2, 3 etc.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      shinNumber:
        description: Number on the rail in the locker.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      electricityPurpose:
        description: Filter by 'Purpose' property value on electricity attributes (code list ID).
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/GuidNullable.yaml'
        example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
      heatingBranchLineNumber:
        description: Filter by Number on branch line. Ex. 246810.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      heatingPlantId:
        description: Filter by Heating plant Id - Heater "kID".
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      heatingPlantName:
        description: Filter by Heating plant name.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'plant'
      heatingPlantPipeName:
        description: Filter by Pipe name / outlet marking.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'heating plan name'
      heatingPlantPipeNumber:
        description: Filter by Outlet marking number.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      waterBranchLineNumber:
        description: Filter by Number on branch line. Ex. XF2500.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 111
      sectionId:
        description: Filter by an area (polygon) in which the water meter frame is located (for checking water balance).
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      sectionName:
        description: Filter by Section name.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'section name'
      waterPlantId:
        description: Filter by Water Plant Id.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      waterPlantName:
        description: Filter by Water Plant Name.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'water plant name'
      waterPlantPipeId:
        description: Filter by Water Plant Pipe Id.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/NonNegativeIntegerNullable.yaml'
        example: 111
      waterPlantPipeName:
        description: Filter by Water Plant Pipe name.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'water plant pipe name'
      mainZone:
        description: Filter by an area (higher level than section) = an operating area (polygon).
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'main zone'
      superZone:
        description: Filter by an area <Section> (higher level than MainZone) = e.g. a city (polygon).
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'super zone'
      hasMainBranchLine:
        description: Filter by Main Branch Line.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/BooleanNullable.yaml'
        example: true
      hasReserveBranchLine:
        description: Filter by Reserve Branch Line.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/BooleanNullable.yaml'
        example: true
      connectionStatus:
        description: Filter by Connection Status.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/GuidNullable.yaml'
        example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
      branchLineNumber:
        description: Filter by Branch Line Number.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: '1234a'
      branchLineType:
        description: Filter by Branch Line Type.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'type c'
      branchLineSize:
        description: Filter by Branch Line Size.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'branch line size'
      reserveBranchLineNumber:
        description: Filter by Reserve Branch Line Number.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: '1234a'
      reserveBranchLineType:
        description: Filter by Reserve Branch Line Type.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'branch line type'
      reserveBranchLineSize:
        description: Filter by Reserve Branch Line Size.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 'branch line size'
      connectedThroughOtherMeterFrame:
        description: Filter by Connected Through Other Meter Frame.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/BooleanNullable.yaml'
        example: true
      connectedThroughOtherMeterFrameNumber:
        description: Filter by Connected Through Other Meter Frame NUmber.
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
        example: 1100000016-1
      meterNumber:
        description: 'Filter by Meter Number.'
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/ShortStringNullable.yaml'
      registerRequirementValidation:
        description: 'Filter by Register Requirement Validation.'
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/BooleanNullable.yaml'
      attributeRequirementValidation:
        description: 'Filter by Attribute Requirement Validation.'
        nullable: true
        allOf:
          - $ref: '../../../_simple-type/BooleanNullable.yaml'
      commonReading:
        type: boolean
        nullable: true
        description: 'Common reading flag.'
      collectiveReading:
        type: boolean
        nullable: true
        description: 'Collective reading flag.'
