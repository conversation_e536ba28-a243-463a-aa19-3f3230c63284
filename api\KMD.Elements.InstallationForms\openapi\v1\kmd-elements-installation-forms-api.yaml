openapi: 3.0.3
info:
  title: KMD.Elements.InstallationForms.Api
  description: KMD Elements - Installation Forms API
  termsOfService: "https://www.kmd.net/terms-of-use"
  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>
  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"
  version: "1.80"
  x-maintainers: Team-MD-4

servers:
  - url: http://localhost:5001
    description: Localhost

security:
  - Jwt: []

tags:
  - name: AutomaticArchiving
    description: Methods related to the AutomaticArchiving Context.
  - name: AutomaticEmails
    description: Methods related to the AutomaticEmails Context.
  - name: AutomaticInstructionTexts
    description: Methods related to Instruction text rules Context.
  - name: AutomaticScreening
    description: Methods related to the AutomaticScreening Context.
  - name: BaseForms
    description: Methods related to the BaseForms Context.
  - name: ChangeBranchLine
    description: Methods related to the ChangeBranchLineForms Context.
  - name: ChangeMeter
    description: Methods related to the ChangeMeterForms Context.
  - name: ChatMessages
    description: Methods related to the chat context.
  - name: Configuration
    description: Methods related to form configuration
  - name: ConnectionRights
    description: Methods related to the Connection Rights context integration.
  - name: Emails
    description: Methods related to the Emails context integration.
  - name: EnergyProduction
    description: Methods related to the EnergyProductionForms Context.
  - name: Extension
    description: Methods related to the ExtensionForms Context.
  - name: FormProblems
    description: Methods related to the FormProblems Context.
  - name: InstructionTexts
    description: Methods related to Instruction texts Context.
  - name: InternalResource
    description: Methods related to the InternalResource Context.
  - name: Invoices
    description: Methods related to Invoices Context.
  - name: MasterData
    description: Methods related to the MasterData Context.
  - name: MoveMeter
    description: Methods related to the MoveMeterForms Context.
  - name: NewInstallation
    description: Methods related to the NewInstallationForms Context.
  - name: Notes
    description: Methods related to the notes Context.
  - name: PendingUpdates
    description: Methods related to polling for pending updates.
  - name: SealBreach
    description: Methods related to the SealBreachForms Context.
  - name: Tasks
    description: Methods related to the FormTasks Context.
  - name: Termination
    description: Methods related to the TerminationForms Context.
  - name: WorkOrders
    description: Methods related to the Work Orders context integration.

paths:
  /v1/installation-forms/queries/get-work-orders-configuration:
    get:
      tags:
        - Configuration
      x-authorization: InstallationForms.Read
      x-authorization-2: PCWorkOrders.Read
      operationId: getWorkOrdersConfiguration
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/SupplyType"
      summary: Get work orders configuration
      description: Get work orders configuration.
      responses:
        "200":
          description: Work orders configuration returned successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/WorkOrders/WorkOrdersConfiguration.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
      security:
        - Jwt: []

  /v1/installation-forms/queries/get-value-lists:
    get:
      tags:
        - Configuration
      x-authorization: InstallationForms.Read
      operationId: getValueLists
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/ValueListTypes"
      summary: Get all value list items related to Installation Forms domain.
      description: Get all value list items related to Installation Forms domain.
      responses:
        "200":
          description: All value lists with items related to Installation Forms returned successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/ValueLists/ValueListModels.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
      security:
        - Jwt: []

  /v1/installation-forms/queries/get-application-types-configuration:
    get:
      tags:
        - Configuration
      x-authorization: InstallationForms.Read
      operationId: getApplicationTypesConfiguration
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/SupplyType"
      description: Retrieves allowed application types configuration.
      summary: Retrieves allowed application types configuration
      responses:
        "200":
          description: Application types configuration retrieved
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/ApplicationTypes/ApplicationTypesConfiguration.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/installation-forms/{installationFormId}/tasks:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      tags:
        - Tasks
      summary: Gets tasks associated with the installation form
      description: Gets tasks associated with the installation form
      operationId: getTasks
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Form tasks list returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of form tasks
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/Tasks/TaskListEntry.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/tasks/{taskId}/commands/re-execute:
    post:
      tags:
        - Tasks
      description: Allows to rerun form task
      summary: Allows to rerun form task
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      operationId: reExecuteTask
      parameters:
        - $ref: "#/components/parameters/TaskId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Re-execute task data
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Tasks/ReExecuteFormTask.yaml"
      responses:
        "204":
          description: Successfully reexecuted task.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/tasks/{taskId}/commands/mark-as-completed:
    post:
      tags:
        - Tasks
      description: Allows to mark form task as completed
      summary: Allows to mark form task as completed
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      operationId: markTaskAsCompleted
      parameters:
        - $ref: "#/components/parameters/TaskId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Mark task as completed
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Tasks/MarkFormTaskAsCompleted.yaml"
      responses:
        "204":
          description: Successfully marked task as completed.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/form-problems/{formProblemId}:
    post:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      tags:
        - FormProblems
      summary: Mark form problem as acknowledged
      description: Mark form problem as acknowledged
      operationId: markFormProblemAsAcknowledged
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/FormProblemId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Successfully marked form problem as acknowledged.
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/Validation/FormProblem.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []


  /v1/installation-forms/{installationFormId}/work-orders:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      x-authorization-3: PCWorkOrders.Read
      tags:
        - WorkOrders
      summary: Gets work orders associated with the installation form
      description: Gets work orders associated with the installation form
      operationId: getWorkOrders
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Work order entries returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of work order entries
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/WorkOrders/WorkOrder.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/master-data-processes:
    post:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      x-authorization-3: PCMasterData.Write
      tags:
        - MasterData
      summary: Creates a Master Data process associated with the installation form
      description: Creates a Master Data process associated with the installation form
      operationId: createMasterDataProcess
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create Master Data request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/MasterData/CreateMasterDataProcess.yaml"
      responses:
        "204":
          description: Successfully created new master data process.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      x-authorization-3: PCMasterData.Read
      tags:
        - MasterData
      summary: Gets Mater Data processes associated with the installation form
      description: Gets Mater Data processes associated with the installation form
      operationId: getMasterDataProcesses
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Master data process entries returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of master data processes
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/MasterData/MasterDataProcess.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/chat-messages:
    post:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      tags:
        - ChatMessages
      summary: Creates a new chat message associated with the installation form
      description: Creates a new chat message associated with the installation form
      operationId: createNewChatMessage
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Create new chat message request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/ChatMessages/CreateChatMessage.yaml"
      responses:
        "200":
          description: Successfully created new chat message.
          content:
            application/json:
              schema:
                $ref: "./schemas/ChatMessages/ChatMessage.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      tags:
        - ChatMessages
      summary: Gets chat messages associated with the installation form
      description: Gets chat messages associated with the installation form
      operationId: getChatMessages
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Chat message entries returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List chat messages
                nullable: false
                maxItems: 1000
                items:
                  $ref: "./schemas/ChatMessages/ChatMessage.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/chat-messages/mark-all-as-read:
    post:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      tags:
        - ChatMessages
      summary: Mark all chat messages associated with the installation form as read
      description: Mark all chat messages associated with the installation form as read
      operationId: markAllMessagesAsRead
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      responses:
        "204":
          description: Successfully marked all messages as read.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/notes:
    post:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      tags:
        - Notes
      summary: Creates a new note associated with the installation form
      description: Creates a new note associated with the installation form
      operationId: createNewNote
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new note request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Notes/CreateOrUpdateNote.yaml"
      responses:
        "200":
          description: Successfully created new note.
          content:
            application/json:
              schema:
                $ref: "./schemas/Notes/Note.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      tags:
        - Notes
      summary: Gets notes associated with the installation form
      description: Gets notes associated with the installation form
      operationId: getNotes
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Note entries returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of notes
                nullable: false
                maxItems: 1000
                items:
                  $ref: "./schemas/Notes/Note.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/notes/{noteId}:
    patch:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      tags:
        - Notes
      summary: Modifies an existing note associated with the installation form
      description: Updates an existing note based on the provided data.
      operationId: modifyNote
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/NoteId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Update note request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Notes/CreateOrUpdateNote.yaml"
      responses:
        "200":
          description: Successfully updated the note.
          content:
            application/json:
              schema:
                $ref: "./schemas/Notes/Note.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      tags:
        - Notes
      summary: Deletes a specific note associated with the installation form
      description: Deletes the note identified by the noteId.
      operationId: deleteNote
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/NoteId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "204":
          description: Note successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/emails:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: InstallationFormsCommunication.Read
      tags:
        - Emails
      summary: Gets email entries associated with the installation form
      description: Gets email entries associated with the installation form
      operationId: getEmails
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Email entries returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of email entries
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/Emails/Email.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []


  /v1/installation-forms/{installationFormId}/invoices:
    post:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      x-authorization-3: InvoiceBases.Write
      tags:
        - Invoices
      summary: Creates an invoice associated with the installation form
      description: Invoice is created for connection rights created from the installation form
      operationId: createInvoice
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new invoice body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Invoices/InvoiceCreate.yaml"
      responses:
        "204":
          description: New invoice basis for connection rights successfully created.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: InvoiceBases.Read
      tags:
        - Invoices
      summary: Gets invoice entries associated with the installation form
      description: Gets invoice entries associated with the installation form
      operationId: getInvoices
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Invoice entries returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of invoice entries
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/Invoices/Invoice.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/invoices/automatic-rules:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Read
      tags:
        - Invoices
      summary: Gets automatic invoice rules
      description: Gets automatic invoice rules
      operationId: getAutomaticInvoicesRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Automatic invoice rules returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of automatic invoice rules
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/Invoices/AutomaticInvoiceRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - Invoices
      summary: Create automatic invoices rule
      description: Create automatic invoices rule
      operationId: createAutomaticInvoicesRule
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new automatic invoice rule request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Invoices/AutomaticInvoiceRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully created new automatic invoice rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/Invoices/AutomaticInvoiceRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/invoices/automatic-rules/{automaticRuleId}:
    put:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - Invoices
      summary: Update automatic invoices rule by id
      description: Update automatic invoices rule by id
      operationId: updateAutomaticInvoicesRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Single automatic invoice rule update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Invoices/AutomaticInvoiceRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully updated automatic invoice rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/Invoices/AutomaticInvoiceRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - Invoices
      summary: Allows to delete automatic invoices rule by id
      description: Deletes automatic invoices rule
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: deleteAutomaticInvoicesRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "204":
          description: Successfully deleted automatic invoice rule data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/invoices/automatic-rules/commands/reorder:
    post:
      tags:
        - Invoices
      summary: Allows to reorder automatic invoices rules list
      description: Reorders automatic invoices rules list
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: reorderAutomaticInvoicesRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Automatic invoice rules list new order
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Automations/ReorderModel.yaml"
      responses:
        "204":
          description: Successfully reordered automatic invoice rules data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/invoices/prices:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      tags:
        - Invoices
      summary: Gets invoicing prices
      description: Gets invoicing prices
      operationId: getInvoicingPrices
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "200":
          description: Invoicing prices returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of invoicing prices
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/Invoices/Price.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - Invoices
      summary: Add invoicing price
      description: Add invoicing price
      operationId: addInvoicingPrice
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Add invoicing price
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Invoices/PriceAddOrUpdate.yaml"
      responses:
        "200":
          description: Successfully added invoicing price.
          content:
            application/json:
              schema:
                $ref: "./schemas/Invoices/Price.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/invoices/prices/{priceId}:
    put:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - Invoices
      summary: Update invoicing price by id
      description: Update invoicing price by id
      operationId: updateInvoicingPriceById
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/PriceId"
      requestBody:
        description: Single invoicing price update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Invoices/PriceAddOrUpdate.yaml"
      responses:
        "200":
          description: Successfully updated invoicing price.
          content:
            application/json:
              schema:
                $ref: "./schemas/Invoices/Price.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - Invoices
      summary: Allows to delete invoicing price by id
      description: Deletes invoicing price
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: deleteInvoicingPriceById
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/PriceId"
      responses:
        "204":
          description: Successfully deleted invoicing price data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/master-data/automatic-rules:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Read
      tags:
        - MasterData
      summary: Gets automatic master data process rules
      description: Gets automatic master data process rules
      operationId: getAutomaticMasterDataRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Automatic master data process rules returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of automatic master data process rules
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/MasterData/AutomaticMasterDataProcessRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - MasterData
      summary: Create automatic master data process rule
      description: Create automatic master data process rule
      operationId: createAutomaticMasterDataRule
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new automatic master data process rule request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/MasterData/AutomaticMasterDataProcessRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully created new automatic master data process rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/MasterData/AutomaticMasterDataProcessRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/master-data/automatic-rules/{automaticRuleId}:
    put:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - MasterData
      summary: Update automatic master data process rule by id
      description: Update automatic master data process rule by id
      operationId: updateAutomaticMasterDataRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Single automatic master data process rule update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/MasterData/AutomaticMasterDataProcessRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully updated automatic master data process rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/MasterData/AutomaticMasterDataProcessRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - MasterData
      summary: Allows to delete automatic master data process rule by id
      description: Deletes automatic master data process rule
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: deleteAutomaticMasterDataRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "204":
          description: Successfully deleted automatic master data process rule data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/master-data/automatic-rules/commands/reorder:
    post:
      tags:
        - MasterData
      summary: Allows to reorder automatic master data process rules list
      description: Reorders automatic master data process rules list
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: reorderAutomaticMasterDataRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Automatic master data process rules list new order
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Automations/ReorderModel.yaml"
      responses:
        "204":
          description: Successfully reordered automatic master data process rules.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/installation-forms/master-data/compare/{installationFormId}:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: ConnectionPoints.Read
      tags:
        - MasterData
      summary: Gets data for master data compare for specific form
      description: Gets data for master data compare for specific form
      operationId: getMasterDataToCompare
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Master data to compare returned successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/MasterData/Compare/MasterDataCompareResult.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/work-orders/automatic-rules:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Read
      tags:
        - WorkOrders
      summary: Gets automatic work order rules
      description: Gets automatic work order rules
      operationId: getAutomaticWorkOrderRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Automatic work order rules returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of automatic work order rules
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/WorkOrders/AutomaticWorkOrderRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - WorkOrders
      summary: Create automatic work order rule
      description: Create automatic work order rule
      operationId: createAutomaticWorkOrderRule
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new automatic work order rule request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/WorkOrders/AutomaticWorkOrderRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully created new automatic work order rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/WorkOrders/AutomaticWorkOrderRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/work-orders/automatic-rules/{automaticRuleId}:
    put:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - WorkOrders
      summary: Update automatic work order rule by id
      description: Update automatic work order rule by id
      operationId: updateAutomaticWorkOrderRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Single automatic work order rule update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/WorkOrders/AutomaticWorkOrderRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully updated automatic work order rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/WorkOrders/AutomaticWorkOrderRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - WorkOrders
      summary: Allows to delete automatic work order rule by id
      description: Deletes automatic work order rule
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: deleteAutomaticWorkOrderRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "204":
          description: Successfully deleted automatic work order rule.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/work-orders/automatic-rules/commands/reorder:
    post:
      tags:
        - WorkOrders
      summary: Allows to reorder automatic work order rules list
      description: Reorders automatic work order rules list
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: reorderAutomaticWorkOrderRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Automatic work order rules list new order
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Automations/ReorderModel.yaml"
      responses:
        "204":
          description: Successfully reordered automatic work order rules.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/automatic-archiving/automatic-rules:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Read
      tags:
        - AutomaticArchiving
      summary: Gets automatic archiving rules
      description: Gets automatic archiving rules
      operationId: getAutomaticArchivingRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Automatic archiving rules returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of automatic archiving rules
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/AutomaticArchiving/AutomaticArchivingRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - AutomaticArchiving
      summary: Create automatic archiving rule
      description: Create automatic archiving rule
      operationId: createAutomaticArchivingRule
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new automatic archiving rule request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AutomaticArchiving/AutomaticArchivingRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully created new automatic archiving rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/AutomaticArchiving/AutomaticArchivingRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/automatic-archiving/automatic-rules/{automaticRuleId}:
    put:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - AutomaticArchiving
      summary: Update automatic archiving rule by id
      description: Update automatic archiving rule by id
      operationId: updateAutomaticArchivingRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Single automatic archiving rule update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AutomaticArchiving/AutomaticArchivingRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully updated automatic archiving rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/AutomaticArchiving/AutomaticArchivingRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - AutomaticArchiving
      summary: Allows to delete automatic archiving rule by id
      description: Deletes automatic archiving rule
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: deleteAutomaticArchivingRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "204":
          description: Successfully deleted automatic archiving rule.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/automatic-archiving/automatic-rules/commands/reorder:
    post:
      tags:
        - AutomaticArchiving
      summary: Allows to reorder automatic archiving rules list
      description: Reorders automatic archiving rules list
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: reorderAutomaticArchivingRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Automatic archiving rules list new order
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Automations/ReorderModel.yaml"
      responses:
        "204":
          description: Successfully reordered automatic archiving rules.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/automatic-emails/automatic-rules:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Read
      tags:
        - AutomaticEmails
      summary: Gets automatic emails rules
      description: Gets automatic emails rules
      operationId: getAutomaticEmailsRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Automatic emails rules returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of automatic emails rules
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/AutomaticEmails/AutomaticEmailsRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - AutomaticEmails
      summary: Create automatic emails rule
      description: Create automatic emails rule
      operationId: createAutomaticEmailsRule
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new automatic emails rule request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AutomaticEmails/AutomaticEmailsRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully created new emails rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/AutomaticEmails/AutomaticEmailsRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/automatic-emails/automatic-rules/{automaticRuleId}:
    put:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - AutomaticEmails
      summary: Update emails rule by id
      description: Update emails rule by id
      operationId: updateAutomaticEmailsRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Single emails rule update
        required: true
        content:
          application/json:
            schema:
                  $ref: "./schemas/AutomaticEmails/AutomaticEmailsRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully updated emails rule.
          content:
            application/json:
              schema:
                  $ref: "./schemas/AutomaticEmails/AutomaticEmailsRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - AutomaticEmails
      summary: Allows to delete emails rule by id
      description: Deletes emails rule
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: deleteAutomaticEmailsRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "204":
          description: Successfully deleted emails rule.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/automatic-emails/automatic-rules/commands/reorder:
    post:
      tags:
        - AutomaticEmails
      summary: Allows to reorder emails rules list
      description: Reorders emails rules list
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: reorderAutomaticEmailsRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Emails rule list new order
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Automations/ReorderModel.yaml"
      responses:
        "204":
          description: Successfully reordered emails rule.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"


  /v1/automatic-instructions/automatic-rules:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Read
      tags:
        - AutomaticInstructionTexts
      summary: Gets instruction text rules
      description: Gets instruction text rules
      operationId: getAutomaticInstructionTextRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Automatic instruction text rules returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of automatic archiving rules
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/InstructionTexts/AutomaticInstructionTextRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - AutomaticInstructionTexts
      summary: Create instruction text rule
      description: Create instruction text rule
      operationId: createAutomaticInstructionTextRule
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new instruction text rule request body
        required: true
        content:
          application/json:
            schema:
                  $ref: "./schemas/InstructionTexts/AutomaticInstructionTextRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully created new instruction text rule.
          content:
            application/json:
              schema:
                  $ref: "./schemas/InstructionTexts/AutomaticInstructionTextRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/automatic-instructions/automatic-rules/{automaticRuleId}:
    put:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - AutomaticInstructionTexts
      summary: Update instruction text rule by id
      description: Update instruction text rule by id
      operationId: updateAutomaticInstructionTextRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Single instruction text rule update
        required: true
        content:
          application/json:
            schema:
                  $ref: "./schemas/InstructionTexts/AutomaticInstructionTextRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully updated instruction text rule.
          content:
            application/json:
              schema:
                  $ref: "./schemas/InstructionTexts/AutomaticInstructionTextRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - AutomaticInstructionTexts
      summary: Allows to delete instruction text rule by id
      description: Deletes instruction text rule
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: deleteAutomaticInstructionTextRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "204":
          description: Successfully deleted instruction text rule.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/automatic-instructions/automatic-rules/commands/reorder:
    post:
      tags:
        - AutomaticInstructionTexts
      summary: Allows to reorder instruction text rules list
      description: Reorders instruction text rules list
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: reorderAutomaticInstructionTextRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Instruction text rule list new order
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Automations/ReorderModel.yaml"
      responses:
        "204":
          description: Successfully reordered instruction text rule.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/installation-forms/automatic-screening/automatic-rules:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Read
      tags:
        - AutomaticScreening
      summary: Gets automatic screening rules
      description: Gets automatic screening rules
      operationId: getAutomaticScreeningRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Automatic screening rules returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of automatic screening rules
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/AutomaticScreening/AutomaticScreeningRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - AutomaticScreening
      summary: Create automatic screening rule
      description: Create automatic screening rule
      operationId: createAutomaticScreeningRule
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new automatic screening rule request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AutomaticScreening/AutomaticScreeningRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully created new automatic screening rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/AutomaticScreening/AutomaticScreeningRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/automatic-screening/automatic-rules/{automaticRuleId}:
    put:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - AutomaticScreening
      summary: Update automatic screening rule by id
      description: Update automatic screening rule by id
      operationId: updateAutomaticScreeningRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Single automatic screening rule update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/AutomaticScreening/AutomaticScreeningRuleCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully updated automatic screening rule.
          content:
            application/json:
              schema:
                $ref: "./schemas/AutomaticScreening/AutomaticScreeningRule.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - AutomaticScreening
      summary: Allows to delete automatic screening rule by id
      description: Deletes automatic screening rule
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: deleteAutomaticScreeningRuleById
      parameters:
        - $ref: "#/components/parameters/AutomaticRuleId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "204":
          description: Successfully deleted automatic screening rule.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/automatic-screening/automatic-rules/commands/reorder:
    post:
      tags:
        - AutomaticScreening
      summary: Allows to reorder automatic screening rules list
      description: Reorders automatic screening rules list
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: reorderAutomaticScreeningRules
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Automatic screening rules list new order
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Automations/ReorderModel.yaml"
      responses:
        "204":
          description: Successfully reordered automatic screening rules.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/installation-forms:
    post:
      tags:
        - BaseForms
      description: Returns paged result of installation forms
      summary: Returns paged result of installation forms
      x-authorization: InstallationForms.Read
      operationId: searchInstallationForms
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Search filters
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/Search/InstallationFormsSearchFilter.yaml"
      responses:
        "200":
          description: Installation forms returned successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/Search/InstallationFormsPagedResult.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    get:
      tags:
        - BaseForms
      description: Returns filtered result of installation forms
      summary: Returns filtered result of installation forms
      x-authorization: InstallationForms.Read
      operationId: filterInstallationForms
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/FormType"
        - $ref: "#/components/parameters/FormState"
        - $ref: "#/components/parameters/FormNumber"
        - $ref: "#/components/parameters/PageStart"
        - $ref: "#/components/parameters/PageSize"
        - $ref: "#/components/parameters/InstallationFormIdToExclude"
      responses:
        "200":
          description: Installation forms returned successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/Search/InstallationFormsSimpleListPagedResult.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
  /v1/installation-forms/{installationFormId}/case-worker:
    post:
      tags:
        - BaseForms
      description: Allows to update installation form case worker
      summary: Allows to update installation form case worker
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      operationId: updateInstallationFormCaseWorkerAssignment
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Case worker
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/CaseWorker/CaseWorkerUpdate.yaml"
      responses:
        "204":
          description: Successfully updated case worker.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - BaseForms
      summary: Allows to delete installation form case worker assignment
      description: Deletes case worker assignment
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      operationId: deleteInstallationFormCaseWorkerAssignment
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/installation-forms/flags/{installationFormId}:
    put:
      tags:
        - BaseForms
      description: Allows to update installation form flag values
      summary: Allows to update installation form flag values
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      operationId: updateInstallationFormFlagValues
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Flags value update
        required: true
        content:
          application/json:
            schema:
              type: array
              description: List of all flag values
              nullable: false
              items:
                $ref: "./schemas/Forms/Flags/FlagUpdate.yaml"
              maxItems: 10
      responses:
        "204":
          description: Successfully updated flags value.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/commands/send-email:
    post:
      tags:
        - Emails
      description: Allows to send installation form email
      summary: Allows to send installation form email
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: CommunicationTemplates.Read
      x-authorization-4: CommunicationTemplates.Write
      x-authorization-5: CommunicationChannelEmail.Write
      x-authorization-6: InstallationFormsCommunication.Read
      x-authorization-7: InstallationFormsCommunication.Write
      operationId: sendInstallationFormEmail
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Email
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Emails/SendEmail.yaml"
      responses:
        "204":
          description: Successfully sent an email
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/commands/update-state:
    post:
      tags:
        - BaseForms
      description: Allows to update installation form state
      summary: Allows to update installation form state
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      operationId: updateInstallationFormState
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: State change
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/FormState/FormStateUpdate.yaml"
      responses:
        "204":
          description: Successfully updated state.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/commands/update-screening-status:
    post:
      tags:
        - BaseForms
      description: Allows to update installation form screening status
      summary: Allows to update installation form screening status
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      operationId: updateInstallationFormScreeningStatus
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Screening status change
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/Screening/FormScreeningStatusUpdate.yaml"
      responses:
        "204":
          description: Successfully updated screening status.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
  /v1/installation-forms/{installationFormId}/commands/update-payment-details:
    post:
      tags:
        - BaseForms
      summary: Allows to update payment details after instruction
      description: Allows to update payer and contact person in instructed and completed states
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      operationId: updateFormPaymentDetails
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Payer and contact person data update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/PaymentDetailsUpdate.yaml"
      responses:
        "204":
          description: Successfully updated payer and contact person data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/add-connection-rights:
    post:
      tags:
        - ConnectionRights
      description: Adds connection rights for the form
      summary: Adds connection rights for the form
      x-authorization: ConnectionPoints.Write
      x-authorization-2: Electricity.All
      operationId: addConnectionRights
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Adds connection rights data
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/ConnectionRights/AddConnectionRights.yaml"
      responses:
        "204":
          description: Successfully started the adding connection rights flow
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/new-installation/{installationFormId}:
    get:
      tags:
        - NewInstallation
      description: Gets new-installation form data by id
      summary: Gets new-installation form data by id
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      operationId: getNewInstallationById
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: New-installation form
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/FormTypes/NewInstallation/NewInstallation.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    put:
      tags:
        - NewInstallation
      description: Allows to update new installation form data
      summary: Allows to update new installation form data
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      operationId: updateNewInstallationFormData
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: New installation form data update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/FormTypes/NewInstallation/NewInstallationUpdate.yaml"
      responses:
        "204":
          description: Successfully updated new installation form data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
  /v1/installation-forms/seal-breach/{installationFormId}:
    get:
      tags:
        - SealBreach
      description: Gets seal-breach form data by id
      summary: Gets seal-breach form data by id
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      operationId: getSealBreachById
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Seal-breach form
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/FormTypes/SealBreach/SealBreach.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    put:
      tags:
        - SealBreach
      description: Allows to update seal breach form data
      summary: Allows to update seal breach form data
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      operationId: updateSealBreachFormData
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Seal breach form data update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/FormTypes/SealBreach/SealBreachUpdate.yaml"
      responses:
        "204":
          description: Successfully updated seal breach form data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
  /v1/installation-forms/change-meter/{installationFormId}:
    get:
      tags:
        - ChangeMeter
      description: Gets change-meter form data by id
      summary: Gets change-meter form data by id
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      operationId: changeMeterById
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Change-meter form
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/FormTypes/ChangeMeter/ChangeMeter.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    put:
      tags:
        - ChangeMeter
      description: Allows to update change meter form data
      summary: Allows to update change meter form data
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      operationId: updateChangeMeterFormData
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Seal breach form data update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/FormTypes/ChangeMeter/ChangeMeterUpdate.yaml"
      responses:
        "204":
          description: Successfully updated change meter form data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
  /v1/installation-forms/termination/{installationFormId}:
    get:
      tags:
        - Termination
      description: Gets termination form data by id
      summary: Gets termination form data by id
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      operationId: getTerminationById
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Termination form
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/FormTypes/Termination/Termination.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    put:
      tags:
        - Termination
      description: Allows to update termination form data
      summary: Allows to update termination form data
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      operationId: updateTerminationFormData
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Termination form data update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/FormTypes/Termination/TerminationUpdate.yaml"
      responses:
        "204":
          description: Successfully updated termination form data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
  /v1/installation-forms/move-meter/{installationFormId}:
    get:
      tags:
        - MoveMeter
      description: Gets move-meter form data by id
      summary: Gets move-meter form data by id
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      operationId: getMoveMeterById
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Move-meter form
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/FormTypes/MoveMeter/MoveMeter.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    put:
      tags:
        - MoveMeter
      description: Allows to update move meter form data
      summary: Allows to update move meter form data
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      operationId: updateMoveMeterFormData
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Move meter form data update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/FormTypes/MoveMeter/MoveMeterUpdate.yaml"
      responses:
        "204":
          description: Successfully updated move meter form data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
  /v1/installation-forms/change-branch-line/{installationFormId}:
    get:
      tags:
        - ChangeBranchLine
      description: Gets change-branch-line form data by id
      summary: Gets change-branch-line form data by id
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      operationId: getChangeBranchLineById
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Change-branch-line form
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/FormTypes/ChangeBranchLine/ChangeBranchLine.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    put:
      tags:
        - ChangeBranchLine
      description: Allows to update change of branch line form data
      summary: Allows to update change of branch line form data
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      operationId: updateChangeBranchLineFormData
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Change of branch line form data update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/FormTypes/ChangeBranchLine/ChangeBranchLineUpdate.yaml"
      responses:
        "204":
          description: Successfully updated change of branch line form data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/extension/{installationFormId}:
    get:
      tags:
        - Extension
      description: Gets extension form data by id.
      summary: Gets extension form data by id.
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      operationId: getExtensionById
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Extension form
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/FormTypes/Extension/Extension.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    put:
      tags:
        - Extension
      description: Allows to update extension form data.
      summary: Allows to update extension form data.
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      operationId: updateExtensionFormData
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Extension form data update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/FormTypes/Extension/ExtensionUpdate.yaml"
      responses:
        "204":
          description: Successfully updated extension form data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/energy-production/{installationFormId}:
    get:
      tags:
        - EnergyProduction
      description: Gets energy-production form data by id
      summary: Gets energy-production form data by id
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      operationId: getEnergyProductionById
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Energy production form.
          content:
            application/json:
              schema:
                $ref: "./schemas/Forms/FormTypes/EnergyProduction/EnergyProduction.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    put:
      tags:
        - EnergyProduction
      description: Allows to update energy production form data
      summary: Allows to update energy production form data
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All
      operationId: updateEnergyProductionFormData
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/RowVersion"
      requestBody:
        description: Energy production form data update.
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Forms/FormTypes/EnergyProduction/EnergyProductionUpdate.yaml"
      responses:
        "204":
          description: Successfully updated energy production form data.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/invoices/{invoiceEntryId}/commands/update-is-required-flag:
    post:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: InvoiceBases.Write
      tags:
        - Invoices
      summary: Updates invoice is required flag
      description: Updates invoice is required flag
      operationId: updateInvoiceIsRequiredFlag
      parameters:
        - $ref: "#/components/parameters/InvoiceEntryId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: State change
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Invoices/InvoiceIsRequiredFlagUpdate.yaml"
      responses:
        "204":
          description: Successfully updated state.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/invoices/{invoiceEntryId}/commands/transfer-to-erp:
    post:
      x-authorization: InstallationForms.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: InvoiceBases.Write
      tags:
        - Invoices
      summary: Transfers invoice to ERP
      description: Transfers invoice to ERP
      operationId: transferInvoiceToErp
      parameters:
        - $ref: "#/components/parameters/InvoiceEntryId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Transfer to ERP request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Invoices/TransferToErp.yaml"
      responses:
        "204":
          description: Successfully updated state.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/installation-forms/{installationFormId}/pending-updates:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All
      tags:
        - PendingUpdates
      summary: Gets a list of functional areas with pending updates for a specific installation form
      description: Gets a list of functional areas with pending updates for a specific installation form
      operationId: getPendingUpdates
      parameters:
        - $ref: "#/components/parameters/InstallationFormId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/LatestChangeTimestamp"
      responses:
        "200":
          description: Pending form update list returned successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/PendingUpdates/PendingUpdatesResult.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/default-instruction-texts:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Read
      tags:
        - InstructionTexts
      summary: Gets instruction texts
      description: Gets instruction texts
      operationId: getInstructionTexts
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Instruction text returned successfully.
          content:
            application/json:
              schema:
                type: array
                description: List of instruction texts
                nullable: false
                maxItems: 200
                items:
                  $ref: "./schemas/InstructionTexts/InstructionText.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - InstructionTexts
      summary: Create instruction text
      description: Create instruction text
      operationId: createInstructionText
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Create new instruction text body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/InstructionTexts/InstructionTextCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully created new instruction text.
          content:
            application/json:
              schema:
                $ref: "./schemas/InstructionTexts/InstructionText.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/default-instruction-texts/{instructionTextId}:
    put:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - InstructionTexts
      summary: Update instruction text by id
      description: Update instruction text by id
      operationId: updateInstructionTextById
      parameters:
        - $ref: "#/components/parameters/InstructionTextId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Single instruction text update
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/InstructionTexts/InstructionTextCreateOrUpdate.yaml"
      responses:
        "200":
          description: Successfully updated instruction text.
          content:
            application/json:
              schema:
                $ref: "./schemas/InstructionTexts/InstructionText.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    delete:
      tags:
        - InstructionTexts
      summary: Deletes instruction text by id
      description: Deletes instruction text by id
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: deleteInstructionTextById
      parameters:
        - $ref: "#/components/parameters/InstructionTextId"
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "204":
          description: Successfully deleted instruction text.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/default-instruction-texts/commands/reorder:
    post:
      tags:
        - InstructionTexts
      summary: Allows to reorder instruction texts
      description: Reorders instruction texts list
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      operationId: reorderInstructionTexts
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Instruction texts list new order
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/Automations/ReorderModel.yaml"
      responses:
        "204":
          description: Successfully reordered instruction texts.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"

  /v1/internal-resource/settings:
    get:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Read
      tags:
        - InternalResource
      summary: Gets internal resource settings
      description: Gets internal resource settings
      operationId: getInternalResourceSettings
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      responses:
        "200":
          description: Internal resource settings returned successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/InternalResource/InternalResourceSettings.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      x-authorization-3: FormConfiguration.Write
      tags:
        - InternalResource
      summary: Update internal resource settings
      description: Update internal resource settings
      operationId: updateInternalResourceSettings
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
      requestBody:
        description: Update internal resource settings request body
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/InternalResource/InternalResourceSettings.yaml"
      responses:
        "200":
          description: Successfully updated internal resource settings.
          content:
            application/json:
              schema:
                $ref: "./schemas/InternalResource/InternalResourceSettings.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

components:
  parameters:
    EsMessageId:
      $ref: "./parameters/EsMessageId.yaml"
    EsCorrelationId:
      $ref: "./parameters/EsCorrelationId.yaml"
    RowVersion:
      $ref: "./parameters/RowVersion.yaml"
    InstallationFormId:
      $ref: "./parameters/InstallationFormId.yaml"
    InstallationFormIdToExclude:
      $ref: "./parameters/InstallationFormIdToExclude.yaml"
    InvoiceEntryId:
      $ref: "./parameters/InvoiceEntryId.yaml"
    InstructionTextId:
      $ref: "./parameters/InstructionTextId.yaml"
    AutomaticRuleId:
      $ref: "./parameters/AutomaticRuleId.yaml"
    NoteId:
      $ref: "./parameters/NoteId.yaml"
    FormNumber:
      $ref: "./parameters/FormNumber.yaml"
    FormState:
      $ref: "./parameters/FormState.yaml"
    FormType:
      $ref: "./parameters/FormType.yaml"
    PageStart:
      $ref: "./parameters/PageStart.yaml"
    PageSize:
      $ref: "./parameters/PageSize.yaml"
    PriceId:
      $ref: "./parameters/PriceId.yaml"
    FormProblemId:
      $ref: "./parameters/FormProblemId.yaml"
    SupplyType:
      $ref: "./parameters/SupplyType.yaml"
    TaskId:
      $ref: "./parameters/TaskId.yaml"
    ValueListTypes:
      $ref: "./parameters/ValueListTypes.yaml"
    LatestChangeTimestamp:
      $ref: "./parameters/LatestChangeTimestamp.yaml"

  responses:
    "400":
      $ref: "./responses/400.yaml"
    "401":
      $ref: "./responses/401.yaml"
    "403":
      $ref: "./responses/403.yaml"
    "404":
      $ref: "./responses/404.yaml"
    "409":
      $ref: "./responses/409.yaml"
    "422":
      $ref: "./responses/422.yaml"
    "429":
      $ref: "./responses/429.yaml"
    "500":
      $ref: "./responses/500.yaml"
  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
      type: http
      scheme: bearer
      bearerFormat: JWT
