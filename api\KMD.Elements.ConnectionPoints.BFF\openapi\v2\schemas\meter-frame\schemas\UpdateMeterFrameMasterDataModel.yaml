title: UpdateMeterFrameMasterDataModel
type: object
additionalProperties: false
description: Meter Frame master data update model.
required:
  - connectionPointId
  - meterMissing
  - meterSealed
  - meterWorkConsumerBilled
  - supplyStatus
  - tagAssignments
  - placementCode
  - commonReading
  - noMeter
  - collectiveReading
  - rowVersion
properties:
  created:
    description: 'Meter Frame created date.'
    allOf:
      - $ref: '../../_simple-type/DateTimeNullable.yaml'
  placementCode:
    description: 'Placement code CodeList value id.'
    allOf:
      - $ref: '../../_simple-type/Guid.yaml'
  connectionPointId:
    description: 'Connection point identifier.'
    allOf:
      - $ref: '../../_simple-type/ShortStringNullable.yaml'
  meterMissing:
    description: 'DK: MålerVæk.'
    allOf:
      - $ref: '../../_simple-type/Boolean.yaml'
  placementSpecification:
    description: 'DK: Placeringsbeskrivelse.'
    allOf:
      - $ref: '../../_simple-type/ShortStringNullable.yaml'
  tagAssignments:
    type: array
    description: Tags.
    maxItems: 1000000
    items:
      $ref: './AddTagAssignmentModel.yaml'
  meterSealDate:
    description: 'DK: Målerplomberingsdato.
      Indicates the date when the meter was sealed.'
    allOf:
      - $ref: '../../_simple-type/DateTimeNullable.yaml'
  meterSealed:
    description: 'DK: MålerPlomberet.
      Indicates whether the meter is sealed.'
    allOf:
      - $ref: '../../_simple-type/Boolean.yaml'
  meterWorkConsumerBilled:
    description: 'DK: MålerYdelseFaktureresEjer.
      Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.'
    allOf:
      - $ref: '../../_simple-type/Boolean.yaml'
  decommissioned:
    description: 'Decommissioned date.'
    allOf:
      - $ref: '../../_simple-type/DateTimeNullable.yaml'
  statusChanged:
    description: 'Latest status change date.'
    allOf:
      - $ref: '../../_simple-type/DateTimeNullable.yaml'
  supplyStatus:
    $ref: './MeterFrameSupplyStatusModel.yaml'
  meterReadingType:
    allOf:
      - $ref: '../../_simple-type/GuidNullable.yaml'
    description: 'A list of different reading methods a meter can have.'
  supplyDisconnectType:
    $ref: './MeterFrameSupplyDisconnectTypeModel.yaml'
  noMeter:
    description: 'DK: Målerfri.'
    allOf:
      - $ref: '../../_simple-type/Boolean.yaml'
  commonReading:
    description: 'Common reading flag.'
    allOf:
      - $ref: '../../_simple-type/Boolean.yaml'
  collectiveReading:
    description: 'Collective reading flag.'
    allOf:
      - $ref: '../../_simple-type/Boolean.yaml'
  supplyDisconnectedComment:
    description: 'Supply Disconnected Comment.'
    allOf:
      - $ref: '../../_simple-type/ShortStringNullable.yaml'
  electricityAttributes:
    description: ElectricityAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameElectricityAttributesModel.yaml'
  heatingAttributes:
    description: HeatingAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameHeatingAttributesModel.yaml'
  waterAttributes:
    description: MeterFrameWaterAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameWaterAttributesModel.yaml'
  rowVersion:
    $ref: '../../_simple-type/RowVersion.yaml'
