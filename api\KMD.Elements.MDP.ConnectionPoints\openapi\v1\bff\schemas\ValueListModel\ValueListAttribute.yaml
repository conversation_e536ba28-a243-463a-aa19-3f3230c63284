type: object
description: Value list attribute model.
additionalProperties: false
required:
  - id
  - valueListHeaderId
  - valueListValueId
  - value
properties:
  id:
    $ref: '../DataTypes/Guid.yaml'
  valueListHeaderId:
    description: Value List Header id.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  valueListValueId:
    description: Value List Value id.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  value:
    description: Value List Attribute value.
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'