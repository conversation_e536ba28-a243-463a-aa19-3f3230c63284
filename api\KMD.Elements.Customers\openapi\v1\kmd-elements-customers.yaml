openapi: 3.0.3
info:
  title: KMD.Elements.Customers
  x-maintainers: Team-SE-2
  description: KMD Elements - Customers Api
  termsOfService: https://www.kmd.net/terms-of-use
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: License
    url: https://www.kmd.net/terms-of-use
  version: '1.2'

servers:
  - url: https://localhost:9278
    description: Local environment

security:
  - JWT: []

paths:
  /api/customers/search:
    post:
      x-authorization: Customers.Read
      tags:
        - Customers
      summary: Fetches list of customers.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of customers according to passed criteria.
      operationId: searchCustomers
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchListQueryModel"
        required: true
      responses:
        "200":
          description: Customers returned successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PagedResultOfCustomersSearchModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /api/customers:
    post:
      x-authorization: Customers.Write
      tags:
        - Customers
      summary: Allows to create a customer.
      description: |-
        Remarks
        passed model has to meet validation requirements described below.
        Result
        Returns *CustomerId* for newly created customer."
      operationId: createCustomer
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: "#/components/schemas/CreateCustomerModel"
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCustomerModel"
          text/json:
            schema:
              $ref: "#/components/schemas/CreateCustomerModel"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CreateCustomerModel"
        required: true
      responses:
        "201":
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IdModel"
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
  /api/customers/{id}:
    put:
      x-authorization: Customers.Write
      tags:
        - Customers
      summary: Allows to update customer.
      description: |
        Remarks
        *CustomerId* has to point to valid customer in the database, passed model has to meet validation requirements described below.
      operationId: updateCustomer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerModel"
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerModel"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerModel"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerModel"
        required: true
      responses:
        "204":
          description: Customer updated successfully.
        '400':
          $ref: '#/components/responses/400'
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
    get:
      x-authorization: Customers.Read
      tags:
        - Customers
      summary: Fetches single customer by given *CustomerId*.
      operationId: getCustomer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Customer returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerDetailsModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

    delete:
      x-authorization: Customers.Write
      tags:
        - Customers
      summary: Allows to delete customer.
      description: |
        Remarks
        *Id* has to point to valid customer in the database.
      operationId: deleteCustomer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "204":
          description: Customer deleted successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "422":
          $ref: "#/components/responses/422"

  /api/customers/{customerId}/obfuscated:
    get:
      x-authorization: Customers.Read
      tags:
        - Customers
      summary: Fetches single customer by given *CustomerId* with obfuscated data.
      description: |
        Result
        Currently this method returns the same data as *GetCustomer()* method.
        Init should be refactored in the future."
      operationId: getObfuscatedCustomer
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Obfuscated customer returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ObfuscatedCustomerDetailsModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Obfuscated customer not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers/dropdown-data:
    get:
      x-authorization: Customers.Read
      tags:
        - Customers
      summary: Fetches all customers filtered by query mapped to CustomerDropDownDataQueryResult (id, name, number).
      operationId: getCustomersDropdownData
      parameters:
        - name: nameSearchPhrase
          in: query
          schema:
            type: string
            nullable: true
      responses:
        "200":
          description: List customer's drop down items filtered by query returned successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CustomerDropDownDataQueryResult"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers/dk-industry-codes:
    get:
      x-authorization: Customers.Read
      tags:
        - Customers
      summary: Fetches dk industry codes filtered by query mapped to DkIndustryCodeModel (id, name, value).
      operationId: getDkBranchCodes
      parameters:
        - name: nameOrCodeValue
          in: query
          schema:
            type: string
            nullable: true
        - name: parentCodeValue
          in: query
          schema:
            type: string
            nullable: true
        - name: dkIndustryCodeLevel
          in: query
          schema:
            $ref: "#/components/schemas/DkIndustryCodeLevel"
      responses:
        "200":
          description: List of available DK industry codes.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/DkIndustryCodeModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers/external-systems:
    get:
      x-authorization: Customers.Read
      tags:
        - Customers
      summary: Fetches all external systems.
      operationId: getExternalSystems
      responses:
        "200":
          description: External systems returned successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CustomerExternalSystemModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers/{customerId}/cpr:
    get:
      x-authorization: Cpr.Read
      tags:
        - Customers
      summary: Gets cpr numbers by given *CustomerId*.
      operationId: getCustomerCprNumbers
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Cpr numbers for given customer id returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerCprModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
    put:
      x-authorization: Cpr.Write
      tags:
        - Customers
      summary: Updates customer cpr numbers.
      operationId: updateCustomerCprNumbers
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerCprNumbersModel"
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerCprNumbersModel"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerCprNumbersModel"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerCprNumbersModel"
        required: true
      responses:
        "204":
          description: Customer cpr numbers updated successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "422":
          $ref: "#/components/responses/422"

  /api/customers/{customerId}/additional-addresses:
    get:
      x-authorization: Customers.Read
      tags:
        - CustomersAdditionalAddresses
      summary: Fetches all additional addresses for given customer.
      description: |-
        Remarks
        *CustomerId* has to point to valid customer in the database.
      operationId: getAdditionalCustomerAddresses
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Customer's additional addresses returned successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AdditionalAddressModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
    post:
      x-authorization: Customers.Write
      tags:
        - CustomersAdditionalAddresses
      summary: Allows to add additional address to customer.
      description: |-
        Remarks:
        *CustomerId* has to point to valid customer in the database, passed model has to meet validation requirements described below."
      operationId: addCustomersAdditionalAddresses
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: "#/components/schemas/AddCustomerAdditionalAddressesModel"
          application/json:
            schema:
              $ref: "#/components/schemas/AddCustomerAdditionalAddressesModel"
          text/json:
            schema:
              $ref: "#/components/schemas/AddCustomerAdditionalAddressesModel"
          application/*+json:
            schema:
              $ref: "#/components/schemas/AddCustomerAdditionalAddressesModel"
        required: true
      responses:
        "201":
          description: Customer's additional addresses created successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers/{customerId}/additional-addresses/{additionalAddressId}:
    put:
      x-authorization: Customers.Write
      tags:
        - CustomersAdditionalAddresses
      summary: Allows to modify customer additional address.
      description: |-
        Remarks
        - *CustomerId* has to point to valid customer in the database,
        - passed model has to meet validation requirements described below.
      operationId: updateCustomerAdditionalAddress
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: additionalAddressId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerAdditionalAddressModel"
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerAdditionalAddressModel"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerAdditionalAddressModel"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerAdditionalAddressModel"
        required: true
      responses:
        "204":
          description: Customer's additional address updated successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer's additional address not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
    delete:
      x-authorization: Customers.Write
      tags:
        - CustomersAdditionalAddresses
      summary: Allows to delete customer additional address.
      description: |-
        Remarks
        - both *CustomerId* and *AdditionalAddressId* have to be valid ids.
        - *CustomerRowVersion* - customer concurrency token.
      operationId: deleteCustomerAdditionalAddress
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: additionalAddressId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: customerRowVersion
          in: query
          schema:
            type: string
            format: byte
      responses:
        "204":
          description: Customer's additional address deleted successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer's additional address not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers/{customerId}/company-contact-persons:
    post:
      x-authorization: Customers.Write
      tags:
        - CustomersCompanyContactPersons
      summary: Allows to add customer contact person.
      description: |-
        Remarks
        - *CustomerId* has to point to valid customer in the database,
        - passed model has to meet validation requirements described below.
        - customer has to be company."
      operationId: addCompanyContactPerson
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: "#/components/schemas/AddCustomerCompanyContactPersonModel"
          application/json:
            schema:
              $ref: "#/components/schemas/AddCustomerCompanyContactPersonModel"
          text/json:
            schema:
              $ref: "#/components/schemas/AddCustomerCompanyContactPersonModel"
          application/*+json:
            schema:
              $ref: "#/components/schemas/AddCustomerCompanyContactPersonModel"
        required: true
      responses:
        "201":
          description: Customer's company contact person created successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IdModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
    get:
      x-authorization: Customers.Read
      tags:
        - CustomersCompanyContactPersons
      summary: Fetches all contact person for given customer.
      description: |-
        Remarks
        - *CustomerId* has to point to valid customer in the database.
      operationId: getCompanyContactPersons
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Customer's company contact persons returned successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CompanyContactPersonModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer's company contact person not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers/{customerId}/company-contact-persons/{contactPersonId}:
    get:
      x-authorization: Customers.Read
      tags:
        - CustomersCompanyContactPersons
      summary: Fetches all contact person for given customer.
      description: |-
        Remarks
        - *CustomerId* has to point to valid customer in the database.
      operationId: getCompanyContactPerson
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: contactPersonId
          in: path
          required: true
          schema:
            type: string
        - name: companyContactPersonId
          in: query
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Customer's company contact person returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompanyContactPersonModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer's company contact person not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

    delete:
      x-authorization: Customers.Write
      tags:
        - CustomersCompanyContactPersons
      summary: Allows to delete customer contact person.
      description: |-
        Remarks
        Both *CustomerId* and *ContactPersonId* have to be valid ids.
        *CustomerRowVersion* - customer concurrency token."
      operationId: deleteCompanyContactPerson
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: contactPersonId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: customerRowVersion
          in: query
          schema:
            type: string
            format: byte
            nullable: true
      responses:
        "204":
          description: Customer's company contact person deleted successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer's company contact person not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
    put:
      x-authorization: Customers.Write
      tags:
        - CustomersCompanyContactPersons
      summary: Allows to update customer contact person.
      description: |-
        Remarks
        *CustomerId* has to point to valid customer in the database,
        - passed model has to meet validation requirements described below.
        - customer has to be company.
      operationId: updateCompanyContactPerson
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: contactPersonId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerCompanyContactPersonModel"
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerCompanyContactPersonModel"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerCompanyContactPersonModel"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerCompanyContactPersonModel"
        required: true
      responses:
        "204":
          description: Customer's company contact person updated successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Customer's company contact person not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers/{customerId}/external-identifiers:
    post:
      x-authorization: Customers.Write
      tags:
        - CustomersExternalIdentifiers
      summary: Allows to insert collection of external identifiers to given customer.
      description: |-
        Remarks
        - *CustomerId* has to point to valid customer in the database,
        - All passed ExternalIdentifierModels have to meet validation requirements described below.
      operationId: addCustomerExternalSystemIdentifiers
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: "#/components/schemas/AddCustomerExternalIdentifierModel"
          application/json:
            schema:
              $ref: "#/components/schemas/AddCustomerExternalIdentifierModel"
          text/json:
            schema:
              $ref: "#/components/schemas/AddCustomerExternalIdentifierModel"
          application/*+json:
            schema:
              $ref: "#/components/schemas/AddCustomerExternalIdentifierModel"
        required: true
      responses:
        "201":
          description: External identifiers created successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: External identifier not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
    get:
      x-authorization: Customers.Read
      tags:
        - CustomersExternalIdentifiers
      summary: Fetches all external identifiers for given customer.
      description: |-
        Remarks
        - *CustomerId* has to point to valid customer in the database.
      operationId: getCustomerExternalSystemIdentifiers
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: External identifiers returned successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CustomerExternalSystemIdentifierModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: External identifier not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers/{customerId}/external-identifiers/{externalSystemId}:
    get:
      x-authorization: Customers.Read
      tags:
        - CustomersExternalIdentifiers
      summary: Fetches specific identifier for given customer.
      description: |-
        Remarks
        - both *CustomerId* and *ExternalSystemId* have to be valid ids.
      operationId: getCustomerExternalSystemIdentifier
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: externalSystemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: External identifier returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerExternalSystemIdentifierModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: External identifier not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
    put:
      x-authorization: Customers.Write
      tags:
        - CustomersExternalIdentifiers
      summary: Allows to update customer external identifier.
      description: |-
        Remarks
        -both *CustomerId* and *ExternalSystemId* have to be valid ids,
        - model has to meet validation requirements described below.
      operationId: updateExternalIdentifier
      parameters:
        - name: externalSystemId
          in: path
          description: "Has to point to external system in the database."
          required: true
          schema:
            type: string
            description: "Has to point to external system in the database."
            format: uuid
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerExternalIdentifierModel"
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerExternalIdentifierModel"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerExternalIdentifierModel"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerExternalIdentifierModel"
        required: true
      responses:
        "204":
          description: External identifier updated successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: External identifier not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
    delete:
      x-authorization: Customers.Write
      tags:
        - CustomersExternalIdentifiers
      summary: Allows to delete customer external identifier.
      description: |-
        Remarks
        - both *CustomerId* and *ExternalSystemId* have to be valid ids.
        - *CustomerRowVersion* - customer concurrency token.
      operationId: deleteExternalIdentifier
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: externalSystemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: customerRowVersion
          in: query
          schema:
            type: string
            format: byte
            nullable: true
      responses:
        "204":
          description: External identifier deleted successfully.
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: External identifier not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers-integration/country-companies-registries/{isoCountryCode}/companies/{companyRegistryId}:
    get:
      x-authorization: Customers.Read
      tags:
        - CustomersIntegration
      summary: Provides information about company by it's companyRegistryId and country registration.
      description: |-
        Remarks
        - *isoCountryCode* has to specify country for company external verification registry.
      operationId: getCompanyIntegration
      parameters:
        - name: isoCountryCode
          in: path
          required: true
          schema:
            type: string
            minLength: 1
            maxLength: 2
            pattern: "^[a-zA-Z]+$"
            nullable: false
        - name: companyRegistryId
          in: path
          required: true
          schema:
            type: string
            nullable: true
      responses:
        "200":
          description: Company integration query result model.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerCompanyIntegrationModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Company not found in registry.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "422":
          description: Provided country code is not supported.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers-integration/country-persons-registries/{isoCountryCode}/persons/{personRegistryId}:
    get:
      x-authorization: Customers.Read
      tags:
        - CustomersIntegration
      summary: Provides information about customer by it's customerRegistryId and country registration.
      description: |-
        Remarks
        - *isoCountryCode* has to specify country for customer external verification registry.
      operationId: getPersonIntegration
      parameters:
        - name: isoCountryCode
          in: path
          required: true
          schema:
            type: string
            minLength: 1
            maxLength: 2
            pattern: "^[a-zA-Z]+$"
            nullable: false
        - name: personRegistryId
          in: path
          required: true
          schema:
            type: string
            nullable: true
      responses:
        "200":
          description: Person integration query result model.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerPersonIntegrationModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Person not found in registry.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "422":
          description: Provided country code is not supported.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
  /api/customers-integration/country-persons-registries/{isoCountryCode}/persons-customerid/{customerId}:
    get:
      x-authorization: Customers.Write
      tags:
        - CustomersIntegration
      summary: Provides information about customer by it's customerId and country registration.
      description: |-
        Remarks
        - *isoCountryCode* has to specify country for customer external verification registry.
      operationId: getPersonIntegrationByCustomerId
      parameters:
        - name: isoCountryCode
          in: path
          required: true
          example: 'DK'
          description: CountryCode for customer external verification registry
          schema:
            type: string
            minLength: 1
            maxLength: 2
            pattern: "^[a-zA-Z]+$"
            nullable: false
        - name: customerId
          in: path
          required: true
          example: 6a6018e8-98a7-4f41-a3ee-127fd34eeb37
          description: Customer identifier
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Person integration query result model.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerPersonIntegrationModel"
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "404":
          description: Person not found in registry.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "422":
          description: 422 Unprocessable.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ErrorDescription'
              examples:
                CustomValidationsIncorrect:
                  value:
                    - errorCode: "Translatable error code."
                      defaultMessage: "Default error description in english."
                    - errorCode: "Other translatable error code."
                      defaultMessage: "Other error description in english."
        "429":
          description: 429 Too Many Requests
          headers:
            Retry-After:
              description: Number of seconds until you should try again.
              schema:
                type: integer
                format: int32
                example: 360
                minimum: 0
                maximum: 2147483647
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
              examples:
                TooFastTooQuicklyTooSoon:
                  value:
                    type: "https://errors.kmdelements.com/429"
                    title: Too Many Requests
                    status: 360
                    detail: Rate limit is exceeded.
                    instance: /resources-path/1
        "499":
          description: 499 Client Closed Request.
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
              examples:
                NotFoundExample:
                  value:
                    type: "https://errors.kmdelements.com/499"
                    title: Client Closed Request
                    status: 499
                    detail: Client Closed Request
                    instance: /resources-path/1
        "500":
          description: 500 Internal Server Error.
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
              examples:
                This-should-not-happen:
                  value:
                    type: "https://errors.kmdelements.com/500"
                    title: Internal Server Error
                    status: 500
                    detail: "body.0.age: Value `Not Int` does not match format `int32`"
                    instance: /resources-path/1
        "503":
          description: 503 Service Unavailable.
          headers:
            Retry-After:
              description: Number of seconds until you should try again.
              schema:
                type: integer
                example: 360
                format: int32
                minimum: 0
                maximum: 2147483647
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
              examples:
                CannotDoMore:
                  value:
                    type: "https://errors.kmdelements.com/503"
                    title: Service Unavailable
                    status: 503
                    detail: The server is currently unable to receive requests. Please retry your request.
                    instance: /resources-path/1
        "504":
          description: 504 Gateway Timeout.
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
              examples:
                NotLivedToSee:
                  value:
                    type: "https://errors.kmdelements.com/504"
                    title: Gateway Timeout
                    status: 504
                    detail: The server response not received in time.
                    instance: /resources-path/1
  /api/customers-integration/country-companies-registries/supported-countries:
    get:
      x-authorization: Customers.Read
      tags:
        - CustomersIntegration
      summary: Method indicating supported countries for company integration.
      operationId: getCompanyIntegrationSupportedCountries
      responses:
        "200":
          description: ISO 2-letters Country codes for supported country-company-registries.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"

  /api/customers-integration/country-persons-registries/supported-countries:
    get:
      x-authorization: Customers.Read
      tags:
        - CustomersIntegration
      summary: Method indicating supported countries for customer integration.
      operationId: getPersonIntegrationSupportedCountries
      responses:
        "200":
          description: ISO 2-letters Country codes for supported country-persons-registries.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
        "401":
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
        "403":
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProblemDetails"
  /api/addresses-integration/{isoCountryCode}/{addressQuery}:
    get:
      x-authorization: Addresses.Read
      tags:
        - AddressIntegration
      summary: Returns addresses from public API.
      description: "### Remarks\r\n- *isoCountryCode* has to specify country for address lookup."
      operationId: GetAddresses
      parameters:
        - name: isoCountryCode
          in: path
          required: true
          schema:
            type: string
            minLength: 1
            maxLength: 2
            pattern: "^[a-zA-Z]+$"
            nullable: false
        - name: addressQuery
          in: path
          required: true
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: List of autocomplete IntegrationAddressQueryAutocompleteResult.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AddressIntegrationQueryAutocompleteModel"
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'

  /api/address-types:
    get:
      x-authorization: Customers.Read
      tags:
        - AddressTypes
      summary: Fetches all address types.
      operationId: getAddressTypes
      responses:
        '200':
          description: Address types returned successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AddressTypeModel'
        '401':
          description: Lacks valid authentication credentials for the target resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '403':
          description: Lacks valid permissions for the target resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  /api/countries:
    get:
      tags:
        - Country
      summary: Gets all Countries.
      operationId: GetCountries
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        403:
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        200:
          description: Found Address.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetCountriesModel'

  /api/tenant-configuration:
    get:
      tags:
        - TenantConfigurations
      summary: Gets product configuration tenant configuration
      operationId: GetTenantConfiguration
      responses:
        '200':
          description: Product configuration - tenant configuration returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TenantConfigurationModel"
        '401':
          $ref: '#/components/responses/401'
components:
  schemas:
    AddressTypeModel:
      type: object
      properties:
        id:
          type: integer
          format: int32
        systemName:
          type: string
          nullable: true
        languageId:
          type: string
          format: uuid
      additionalProperties: false
    GetCountriesModel:
      type: object
      additionalProperties: false
      required:
        - id
        - systemName
        - code
      properties:
        id:
          type: integer
          format: int32
        systemName:
          type: string
        code:
          type: string
    PagedResultOfCustomersSearchModel:
      allOf:
        - $ref: "#/components/schemas/PagedResultBase"
        - type: object
          additionalProperties: false
          properties:
            results:
              type: array
              nullable: true
              items:
                $ref: "#/components/schemas/GetCustomersSearchModel"

    GetCustomersSearchModel:
      type: object
      required:
        - countryCode
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          nullable: true
        additionalName:
          type: string
          nullable: true
        protectedName:
          type: boolean
        attention:
          type: string
          nullable: true
        vat:
          type: boolean
        customerNumber:
          type: string
          nullable: true
        personCountryCode:
          type: string
          nullable: true
        dateOfBirth:
          type: string
          format: date-time
          nullable: true
        cprNumber:
          type: string
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true
        companyCountryCode:
          type: string
          nullable: true
        cvrNumber:
          type: string
          nullable: true
        maintainAutomatically:
          type: boolean
          nullable: true
        comment:
          type: string
          nullable: true
        additionalCvrNumber:
          type: string
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        customerAddressId:
          type: string
          format: uuid
          nullable: true
        protectedAddress:
          type: boolean
          nullable: true
        contactName:
          type: string
          nullable: true
        contactName2:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        mobile:
          type: string
          nullable: true
        addressId:
          type: string
          format: uuid
          nullable: true
        streetName:
          type: string
          nullable: true
        streetCode:
          type: integer
          format: int32
          nullable: true
        houseNumber:
          type: string
          nullable: true
        floor:
          type: string
          nullable: true
        room:
          type: string
          nullable: true
        citySubDivision:
          type: string
          nullable: true
        postalCode:
          type: string
          nullable: true
        postOfficeBox:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: integer
          format: int32
          nullable: true
        countryCode:
          type: string
          nullable: false
        addressComment:
          type: string
          nullable: true
        addressType:
          type: integer
          format: int32
          nullable: true
        address:
          type: string
          nullable: true
          readOnly: true
        addressSort:
          type: string
          nullable: true
        addressPublicRegistryId:
          type: string
          nullable: true
        houseNumberId:
          type: string
          format: uuid
          nullable: true
        companyStartDate:
          type: string
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          format: date-time
          nullable: true
        companyStatus:
          type: string
          nullable: true
        companyIndustryCodeName:
          type: string
          nullable: true
        companyIndustryCodeValue:
          type: string
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          nullable: true
        registrationNumber:
          type: string
          nullable: true
        isPerson:
          type: boolean
          readOnly: true
        externalIdentifiers:
          type: string
          nullable: true
        rowVersion:
          type: string
          format: byte
          nullable: true
        addressRowVersion:
          type: string
          format: byte
          nullable: true
        changedByUserId:
          type: string
          format: uuid
        changeReason:
          type: string
          nullable: true
        changeDate:
          type: string
          format: date-time
      additionalProperties: false

    CustomerDetailsModel:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          nullable: true
        customerNumber:
          type: string
          nullable: true
        additionalName:
          type: string
          nullable: true
        attention:
          type: string
          nullable: true
        protectedName:
          type: boolean
        vat:
          type: boolean
        dateOfBirth:
          type: string
          format: date-time
          nullable: true
        personCountryCode:
          type: string
          nullable: true
        cprNumber:
          type: string
          nullable: true
        companyCountryCode:
          type: string
          nullable: true
        companyIndustryCodeName:
          type: string
          nullable: true
        companyIndustryCodeValue:
          type: string
          nullable: true
        cvrNumber:
          type: string
          nullable: true
        maintainAutomatically:
          type: boolean
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true
        additionalCvrNumber:
          type: string
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        comment:
          type: string
          nullable: true
        isPerson:
          type: boolean
          readOnly: true
        companyStartDate:
          type: string
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          format: date-time
          nullable: true
        companyStatus:
          type: string
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          nullable: true
        rowVersion:
          type: string
          format: byte
          nullable: true
        primaryAddress:
          $ref: "#/components/schemas/CustomerAddressModel"
        additionalAddresses:
          type: array
          items:
            $ref: "#/components/schemas/CustomerAddressModel"
          nullable: true
        companyContactPersons:
          type: array
          items:
            $ref: "#/components/schemas/CompanyContactPersonModel"
          description: "Must be empty when Cvr Number is also empty."
          nullable: true
        externalIdentifiers:
          type: array
          items:
            $ref: "#/components/schemas/CustomerExternalIdentifierModel"
          nullable: true
        changedByUserId:
          type: string
          format: uuid
        changeReason:
          type: string
          nullable: true
        changeDate:
          type: string
          format: date-time

    CreateCustomerModel:
      type: object
      additionalProperties: false
      properties:
        name:
          type: string
          description: |-
            Remarks:
            - Max 132 characters.
            Can't be empty.
          nullable: true
          maxLength: 132
        additionalName:
          type: string
          description: |-
            Remarks:
            - Max 132 characters.
            Can't be empty.
          nullable: true
          maxLength: 132
        attention:
          type: string
          description: |-
            Remarks:
            Optional.
            Max 40 characters.
          nullable: true
          maxLength: 40
        protectedName:
          type: boolean
          description: |-
            Remarks:
            Optional.
            Default set to false.
        vat:
          type: boolean
          description: |-
            Remarks:
            - Required."
        dateOfBirth:
          type: string
          description: |-
            Remarks:
            - Must be valid date when set
            - Can be empty if CprNumber is set, but only if date matches one in CprNumber's first 6 digits
            - Must be empty if CvrNumber is set.
          format: date-time
          nullable: true
        personCountryCode:
          type: string
          description: |-
            Remarks:
            - Max 2 characters.
            - Must be empty when CvrNumber is set.
            - Has to be valid country code.
          nullable: true
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
        cprNumber:
          type: string
          description: |-
            Remarks:
            - Can't be empty if CvrNumber is also empty.
            - Must be empty if CvrNumber is not empty.
            - If not empty, it has to be valid Cpr Number (10 characters or 11 characters with hyphen).
          nullable: true
        additionalCprNumber:
          type: string
          description: |-
            Remarks:
            - If not empty, it has to be valid CprNumber (10 characters or 11 characters with hyphen).
          nullable: true
        companyCountryCode:
          type: string
          description: |-
            Remarks:
            - Max 2 characters.
            - Must be empty when CprNumber is set.
            - Has to be valid country code.
          nullable: true
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
        cvrNumber:
          type: string
          description: |-
            Remarks:
            - Can't be empty if CprNumber is also empty.
            - Must be empty if CprNumber is not empty.
            - If not empty, it has to be valid Cvr Number - exact 8 characters.
          nullable: true
        additionalCvrNumber:
          type: string
          description: |-
            Remarks:
            - If not empty, it has to be valid CvrNumber - exact 8 characters.
            - Must be empty if CvrNumber is also empty.
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        comment:
          type: string
          nullable: true
          maxLength: 500
        companyStartDate:
          type: string
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          format: date-time
          nullable: true
        companyStatus:
          type: string
          nullable: true
        companyIndustryCodeName:
          type: string
          nullable: true
        companyIndustryCodeValue:
          type: string
          nullable: true
        maintainAutomatically:
          type: boolean
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          nullable: true
        primaryAddress:
          $ref: "#/components/schemas/CreateCustomerAddressModel"
        companyContactPersons:
          type: array
          items:
            $ref: "#/components/schemas/CompanyContactPersonModel"
          description: "Must be empty when Cvr Number is also empty."
          nullable: true
        additionalAddresses:
          type: array
          items:
            $ref: "#/components/schemas/CreateCustomerAddressModel"
          nullable: true
        externalIdentifiers:
          type: array
          items:
            $ref: "#/components/schemas/CustomerExternalIdentifierModel"
          nullable: true
        changeReason:
          type: string
          nullable: true

    UpdateCustomerModel:
      type: object
      additionalProperties: false
      properties:
        name:
          type: string
          description: |
            Remarks:
            - Max 132 characters.
            - Can't be empty.
          nullable: true
          maxLength: 132
        additionalName:
          type: string
          description: |
            Remarks:
            - Max 132 characters.
            - Can't be empty.
          nullable: true
          maxLength: 132
        attention:
          type: string
          description: |
            - Optional.
            - Max 40 characters.
          nullable: true
          maxLength: 40
        protectedName:
          type: boolean
          nullable: true
        vat:
          type: boolean
          description: "Required."
        dateOfBirth:
          type: string
          description: |
            Remarks:
            - Must be valid date when set
            - Can be empty if CprNumber is set, but only if date matches one in CprNumber's first 6 digits
            - Must be empty if CvrNumber is set.
          format: date-time
          nullable: true
        cprNumber:
          type: string
          maxLength: 11
          minLength: 10
          description: |-
            Remarks:
            - Can't be empty if CvrNumber is also empty.
            - Must be empty if CvrNumber is not empty.
            - If not empty, it has to be valid Cpr Number (10 characters or 11 characters with hyphen).
          nullable: true
        additionalCprNumber:
          type: string
          maxLength: 11
          minLength: 10
          description: |-
            Remarks:
            - If not empty, it has to be valid CprNumber (10 characters or 11 characters with hyphen).
          nullable: true
        cvrNumber:
          type: string
          description: |
            Remarks:
            - Can't be empty if CprNumber is also empty.
            - Must be empty if CprNumber is not empty.
            - If not empty, it has to be valid Cvr Number - exact 8 characters.
          nullable: true
        maintainAutomatically:
          type: boolean
          description: "Required when CVR number is set."
          nullable: true
        additionalCvrNumber:
          type: string
          description: |
            Remarks:
            - If not empty, it has to be valid CvrNumber - exact 8 characters.
            - Must be empty if CvrNumber is also empty.
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        personCountryCode:
          type: string
          description: |
            Remarks:
            - Max 2 characters.
            - Can't be empty when CPR or BirthDate is set.
            - Has to be valid country
          nullable: true
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
        companyCountryCode:
          type: string
          description: |
            Remarks:
            - Max 2 characters.
            - Can't be empty when CVR is set.
            - Has to be valid country code.
          nullable: true
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
        comment:
          type: string
          description: |
            Remarks:
            - Max 500 characters.
          nullable: true
          maxLength: 500
        companyStartDate:
          type: string
          description: |
            Remarks:
            - Optional.
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          description: |
            Remarks:
            - Optional.
          format: date-time
          nullable: true
        companyStatus:
          type: string
          description: |
            Remarks:
            - Optional.
          nullable: true
        companyIndustryCodeName:
          type: string
          description: |
            Remarks:
            - Optional.
          nullable: true
        companyIndustryCodeValue:
          type: string
          description: |
            Remarks:
            - Optional.
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          description: |
            Remarks:
            - Optional.
          nullable: true
        primaryAddress:
          $ref: "#/components/schemas/UpdateCustomerAddressModel"
        additionalAddresses:
          type: array
          items:
            $ref: "#/components/schemas/UpdateCustomerAddressModel"
          nullable: true
        companyContactPersons:
          type: array
          items:
            $ref: "#/components/schemas/CompanyContactPersonModel"
          description: "Must be empty when Cvr Number is also empty."
          nullable: true
        externalIdentifiers:
          type: array
          items:
            $ref: "#/components/schemas/CustomerExternalIdentifierModel"
          nullable: true
        rowVersion:
          type: string
          description: |
            Remarks:
            - The concurrency token.
          format: byte
          nullable: true
        changeReason:
          type: string
          description: |
            Remarks:
            - Optional.
          nullable: true

    ObfuscatedCustomerDetailsModel:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          nullable: true
        customerNumber:
          type: string
          nullable: true
        additionalName:
          type: string
          nullable: true
        attention:
          type: string
          nullable: true
        protectedName:
          type: boolean
        vat:
          type: boolean
        cprNumber:
          type: string
          nullable: true
        cvrNumber:
          type: string
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true
        additionalCvrNumber:
          type: string
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        comment:
          type: string
          nullable: true
        isPerson:
          type: boolean
        companyStartDate:
          type: string
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          format: date-time
          nullable: true
        companyStatus:
          type: string
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          nullable: true
        rowVersion:
          type: string
          format: byte
          nullable: true
        primaryAddress:
          $ref: "#/components/schemas/CustomerAddressModel"
        customerAddresses:
          type: array
          items:
            $ref: "#/components/schemas/CustomerAddressModel"
          nullable: true

    CustomerDropDownDataQueryResult:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          nullable: true
        customerNumber:
          type: string
          nullable: true

    AdditionalAddressModel:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: uuid
        addressTypeId:
          type: integer
          description: "Must be positive address type id integer."
          format: int32
          minimum: 0
          maximum: 2147483647
        contactName:
          type: string
          nullable: true
          maxLength: 132
        contactName2:
          type: string
          nullable: true
          maxLength: 132
        email:
          type: string
          format: email
          description: "If not empty, it has to be valid email address."
          nullable: true
          maxLength: 60
        phone:
          type: string
          nullable: true
          maxLength: 20
        mobile:
          type: string
          nullable: true
          maxLength: 20
        comment:
          type: string
          nullable: true
          maxLength: 500
        address:
          $ref: "#/components/schemas/Address"

    CustomerAddressModel:
      type: object
      additionalProperties: false
      properties:
        addressTypeId:
          type: integer
          description: "Must be positive address type id integer."
          format: int32
          minimum: 0
          maximum: 2147483647
        contactName:
          type: string
          nullable: true
          maxLength: 132
        contactName2:
          type: string
          nullable: true
          maxLength: 132
        email:
          type: string
          format: email
          description: "If not empty, it has to be valid email address."
          nullable: true
          maxLength: 60
        phone:
          type: string
          nullable: true
          maxLength: 20
        mobile:
          type: string
          nullable: true
          maxLength: 20
        comment:
          type: string
          nullable: true
          maxLength: 500
        address:
          $ref: "#/components/schemas/Address"

    CreateCustomerAddressModel:
      type: object
      additionalProperties: false
      properties:
        addressTypeId:
          type: integer
          description: "Must be positive address type id integer."
          format: int32
          minimum: 0
          maximum: 2147483647
        contactName:
          type: string
          nullable: true
          maxLength: 132
        contactName2:
          type: string
          nullable: true
          maxLength: 132
        email:
          type: string
          format: email
          description: "If not empty, it has to be valid email address."
          nullable: true
          maxLength: 60
        phone:
          type: string
          nullable: true
          maxLength: 20
        mobile:
          type: string
          nullable: true
          maxLength: 20
        comment:
          type: string
          nullable: true
          maxLength: 500
        address:
          $ref: "#/components/schemas/CreateAddressModel"

    UpdateCustomerAddressModel:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: uuid
          nullable: true
        addressTypeId:
          type: integer
          description: "Must be positive address type id integer."
          format: int32
          minimum: 0
          maximum: 2147483647
        contactName:
          type: string
          nullable: true
          maxLength: 132
        contactName2:
          type: string
          nullable: true
          maxLength: 132
        email:
          type: string
          format: email
          description: "If not empty, it has to be valid email address."
          nullable: true
          maxLength: 60
        phone:
          type: string
          nullable: true
          maxLength: 20
        mobile:
          type: string
          nullable: true
          maxLength: 20
        comment:
          type: string
          nullable: true
          maxLength: 500
        address:
          $ref: "#/components/schemas/UpdateAddressModel"
        customerRowVersion:
          type: string
          description: "The concurrency token."
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true

    UpdateCustomerAdditionalAddressModel:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: uuid
        addressTypeId:
          type: integer
          description: "Must be positive address type id integer."
          format: int32
          minimum: 0
          maximum: 2147483647
        contactName:
          type: string
          nullable: true
          maxLength: 132
        contactName2:
          type: string
          nullable: true
          maxLength: 132
        email:
          type: string
          format: email
          description: "If not empty, it has to be valid email address."
          nullable: true
          maxLength: 60
        phone:
          type: string
          nullable: true
          maxLength: 20
        mobile:
          type: string
          nullable: true
          maxLength: 20
        comment:
          type: string
          nullable: true
          maxLength: 500
        address:
          $ref: "#/components/schemas/UpdateAddressModel"
        customerRowVersion:
          type: string
          description: "The concurrency token."
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true

    CustomerExternalIdentifierModel:
      type: object
      properties:
        externalSystemId:
          type: string
          description: "Has to point to external system in the database."
          format: uuid
        externalCustomerId:
          type: string
          nullable: true
      additionalProperties: false

    Address:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        id:
          type: string
          format: uuid
        protectedAddress:
          type: boolean
          nullable: true
        streetName:
          type: string
          nullable: true
        streetCode:
          type: integer
          format: int32
          minimum: 1
          maximum: 9999
          nullable: true
        houseNumber:
          type: string
          nullable: true
        houseNumberId:
          type: string
          format: uuid
          nullable: true
        floor:
          type: string
          nullable: true
        room:
          type: string
          nullable: true
        citySubDivision:
          type: string
          nullable: true
        postalCode:
          type: string
          nullable: true
        postOfficeBox:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: integer
          format: int32
          nullable: true
          minimum: -2147483648
          maximum: 2147483647
        countryCode:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: false
        comment:
          type: string
          nullable: true
        publicRegistryId:
          type: string
          nullable: true
        rowVersion:
          type: string
          format: byte
          nullable: true
          description: The concurrency token.
        changeReason:
          type: string
          nullable: true
        concatenatedAddress:
          type: string
          nullable: true
          readOnly: true

    CreateAddressModel:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        protectedAddress:
          type: boolean
          nullable: true
        streetName:
          type: string
          nullable: true
        streetCode:
          type: integer
          format: int32
          minimum: 1
          maximum: 9999
          nullable: true
        houseNumber:
          type: string
          nullable: true
        houseNumberId:
          type: string
          format: uuid
          nullable: true
        floor:
          type: string
          nullable: true
        room:
          type: string
          nullable: true
        citySubDivision:
          type: string
          nullable: true
        postalCode:
          type: string
          nullable: true
        postOfficeBox:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: integer
          format: int32
          nullable: true
          minimum: -2147483648
          maximum: 2147483647
          description: It should be null when countryCode is different than DK.
        countryCode:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: false
        comment:
          type: string
          nullable: true
        publicRegistryId:
          type: string
          nullable: true
        changeReason:
          type: string
          nullable: true

    UpdateAddressModel:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        id:
          type: string
          format: uuid
          nullable: true
        protectedAddress:
          type: boolean
          nullable: true
        streetName:
          type: string
          nullable: true
        streetCode:
          type: integer
          format: int32
          minimum: 1
          maximum: 9999
          nullable: true
        houseNumber:
          type: string
          nullable: true
        houseNumberId:
          type: string
          format: uuid
          nullable: true
        floor:
          type: string
          nullable: true
        room:
          type: string
          nullable: true
        citySubDivision:
          type: string
          nullable: true
        postalCode:
          type: string
          nullable: true
        postOfficeBox:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: integer
          format: int32
          nullable: true
          minimum: -2147483648
          maximum: 2147483647
        countryCode:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: false
        comment:
          type: string
          nullable: true
        publicRegistryId:
          type: string
          nullable: true
        rowVersion:
          type: string
          format: byte
          nullable: true
          description: The concurrency token.
        changeReason:
          type: string
          nullable: true

    DkIndustryCodeModel:
      type: object
      additionalProperties: false
      required:
        - id
        - name
        - value
        - dkIndustryCodeLevel
      properties:
        id:
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
        name:
          type: string
        value:
          type: string
        parentValue:
          type: string
          nullable: true
        dkIndustryCodeLevel:
          $ref: "#/components/schemas/DkIndustryCodeLevel"

    CustomerExternalSystemModel:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          nullable: true

    CustomerCprModel:
      type: object
      additionalProperties: false
      properties:
        cprNumber:
          type: string
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true

    UpdateCustomerCprNumbersModel:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        cprNumber:
          type: string
          nullable: true
        dateOfBirth:
          type: string
          format: date-time
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true
        countryCode:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: false
        rowVersion:
          type: string
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true

    AddCustomerAdditionalAddressesModel:
      type: object
      additionalProperties: false
      properties:
        additionalAddresses:
          type: array
          items:
            $ref: "#/components/schemas/CreateCustomerAddressModel"
          nullable: true
        customerRowVersion:
          type: string
          description: "The concurrency token."
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true
          maxLength: 100

    AddCustomerCompanyContactPersonModel:
      type: object
      additionalProperties: false
      properties:
        name:
          type: string
          nullable: false
          maxLength: 132
        role:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
          maxLength: 20
        mobile:
          type: string
          nullable: true
          maxLength: 20
        email:
          type: string
          description: "If not empty, it has to be valid email address."
          nullable: true
          maxLength: 60
        comment:
          type: string
          nullable: true
          maxLength: 500
        customerRowVersion:
          type: string
          description: "The concurrency token."
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true
          maxLength: 100

    UpdateCustomerCompanyContactPersonModel:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          nullable: false
          maxLength: 132
        role:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
          maxLength: 20
        mobile:
          type: string
          nullable: true
          maxLength: 20
        email:
          type: string
          description: "If not empty, it has to be valid email address."
          nullable: true
          maxLength: 60
        comment:
          type: string
          nullable: true
          maxLength: 500
        customerRowVersion:
          type: string
          description: "The concurrency token."
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true
          maxLength: 100

    CompanyContactPersonModel:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: uuid
          nullable: true
        name:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        mobile:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        role:
          type: string
          nullable: true
        comment:
          type: string
          nullable: true

    AddCustomerExternalIdentifierModel:
      type: object
      additionalProperties: false
      properties:
        externalIdentifiers:
          type: array
          items:
            $ref: "#/components/schemas/CustomerExternalIdentifierModel"
          nullable: true
        customerRowVersion:
          type: string
          description: "The concurrency token."
          format: byte
          nullable: true
        changeReason:
          type: string
          maxLength: 100
          nullable: true

    UpdateCustomerExternalIdentifierModel:
      type: object
      additionalProperties: false
      properties:
        externalSystemId:
          type: string
          description: "Has to point to external system in the database."
          format: uuid
        externalCustomerId:
          type: string
          nullable: true
        customerId:
          type: string
          format: uuid
        customerRowVersion:
          type: string
          description: "The concurrency token."
          format: byte
          nullable: true
        changeReason:
          type: string
          maxLength: 100
          nullable: true

    CustomerExternalSystemIdentifierModel:
      type: object
      properties:
        externalSystem:
          $ref: "#/components/schemas/CustomerExternalSystemModel"
        externalCustomerId:
          type: string
          nullable: true
        id:
          type: string
          format: uuid
      additionalProperties: false

    CustomerCompanyIntegrationModel:
      type: object
      properties:
        name:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        industryCodeName:
          type: string
          description: Customer company's integration industry code name.
          nullable: true
        industryCodeValue:
          type: string
          description: Customer company's integration industry code value.
          nullable: true
        address:
          $ref: "#/components/schemas/AddressIntegrationModel"
        company:
          $ref: "#/components/schemas/CustomerCompanyIntegrationCompanyModel"
      additionalProperties: false

    AddressIntegrationModel:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        streetName:
          type: string
          description: Street name.
          nullable: true
        streetCode:
          type: string
          description: Street code.
          nullable: true
        houseNumber:
          type: string
          description: House number.
          nullable: true
        floor:
          type: string
          description: Floor information.
          nullable: true
        room:
          type: string
          description: Room information.
          nullable: true
        citySubDivision:
          type: string
          description: City sub division.
          nullable: true
        postalCode:
          type: string
          description: Postal code.
          nullable: true
        city:
          type: string
          description: City.
          nullable: true
        municipalityCode:
          type: string
          description: Municipality / City code from external system.
          nullable: true
        countryCode:
          type: string
          description: ISO CountryCode.
          nullable: false
        publicRegistryId:
          type: string
          description: |-
            External API address identification number.
            In Denmark - DAR ID.
          nullable: true
        houseNumberId:
          type: string
          description: "External API house number Id."
          format: uuid
          nullable: true

    CustomerCompanyIntegrationCompanyModel:
      type: object
      additionalProperties: false
      properties:
        companyRegistryNumber:
          type: string
          description: |-
            Company's public registry identification number.
            In Denmark - CVR Number.
          nullable: true
        startDate:
          type: string
          description: Public registry information about company's start date.
          nullable: true
        endDate:
          type: string
          description: Public registry information about company's end date.
          nullable: true
        status:
          type: string
          description: Company's, provided by public registry.
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          description: Customer company's integration external API Id response.
          nullable: true

    CustomerPersonIntegrationModel:
      type: object
      additionalProperties: false
      properties:
        name:
          type: string
          nullable: true
        personRegistryNumber:
          type: string
          nullable: true
        nameAndAddressProtection:
          type: boolean
        address:
          $ref: "#/components/schemas/CustomerPersonAddressIntegrationModel"

    CustomerPersonAddressIntegrationModel:
      type: object
      additionalProperties: false
      properties:
        roadAddressingName:
          type: string
          nullable: true
        houseNo:
          type: string
          nullable: true
        floor:
          type: string
          nullable: true
        doorNo:
          type: string
          nullable: true
        postCode:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: string
          nullable: true

    AddressIntegrationQueryAutocompleteModel:
      type: object
      properties:
        text:
          type: string
          description: Text for autocomplete lookup (formatted address).
          nullable: true
        address:
          allOf:
            - $ref: "#/components/schemas/AddressIntegrationQueryAutocompleteResultModel"
          description: Address details.
          nullable: true
      additionalProperties: false

    AddressIntegrationQueryAutocompleteResultModel:
      type: object
      required:
        - countryCode
      properties:
        streetName:
          type: string
          description: Street name.
          nullable: true
        streetCode:
          type: string
          description: Street code.
          nullable: true
        houseNumberId:
          type: string
          description: House number identifier.
          format: uuid
          nullable: true
        houseNumber:
          type: string
          description: House number.
          nullable: true
        floor:
          type: string
          description: Floor information.
          nullable: true
        room:
          type: string
          description: Room information.
          nullable: true
        citySubDivision:
          type: string
          description: City sub division.
          nullable: true
        postalCode:
          type: string
          description: Postal code.
          nullable: true
        city:
          type: string
          description: City.
          nullable: true
        municipalityCode:
          type: string
          description: Municipality / City code from external system.
          nullable: true
        countryCode:
          type: string
          description: ISO CountryCode.
          nullable: false
        publicRegistryId:
          type: string
          description: "External API address identification number.\r\nIn Denmark - DAR ID."
          nullable: true
      additionalProperties: false

    TenantConfigurationModel:
      type: object
      properties:
        addressIntegration:
          allOf:
            - $ref: "#/components/schemas/AddressIntegrationSettingsModel"
          nullable: true
        companyIntegration:
          allOf:
            - $ref: "#/components/schemas/CompanyIntegrationSettingsModel"
          nullable: true
        personIntegration:
          allOf:
            - $ref: "#/components/schemas/PersonIntegrationSettingsModel"
          nullable: true
        customerSettings:
          allOf:
            - $ref: "#/components/schemas/CustomerSettingsModel"
          nullable: true
      additionalProperties: false
    CompanyIntegrationSettingsModel:
      type: object
      properties:
        dkLogicCvr:
          allOf:
            - $ref: "#/components/schemas/DkLogicCvrSettingsModel"
          nullable: true
      additionalProperties: false

    AddressIntegrationSettingsModel:
      type: object
      properties:
        dkDawa:
          allOf:
            - $ref: "#/components/schemas/DKDawaSettingsQueryResult"
          nullable: false
      additionalProperties: false

    DKDawaSettingsQueryResult:
      type: object
      properties:
        apiUrl:
          type: string
          nullable: false
        clientType:
          allOf:
            - $ref: "#/components/schemas/DawaClientType"
          nullable: true
      additionalProperties: false

    DawaClientType:
      enum:
        - HttpWithoutProxy
        - Stub
      type: string
      description: >-
        0 - HttpWithoutProxy, 1 - Stub

    DkLogicCvrSettingsModel:
      type: object
      properties:
        cvrConfigurationId:
          type: string
          nullable: true
        subscriptionId:
          type: string
          nullable: true
        subscribedToCvrEvents:
          type: boolean
      additionalProperties: false

    PersonIntegrationSettingsModel:
      type: object
      properties:
        dkLogicCpr:
          allOf:
            - $ref: "#/components/schemas/DkLogicCprSettingsModel"
          nullable: true
      additionalProperties: false

    DkLogicCprSettingsModel:
      type: object
      properties:
        cprConfigurationId:
          type: string
          nullable: true
        subscriptionId:
          type: string
          nullable: true
      additionalProperties: false

    CustomerSettingsModel:
      type: object
      properties:
        vatEnabledByDefault:
          type: boolean
        enableCheckVatStatus:
          type: boolean
        enableDuplicateCprNumbers:
          type: boolean
        enableDuplicateCvrNumbers:
          type: boolean
        enableCprLookup:
          type: boolean
          description: 'Determines whether CprLookup button should be visible'
          example: false
          nullable: true
        dataHubActorType:
          allOf:
            - $ref: "#/components/schemas/DataHubActorType"
          nullable: true
      additionalProperties: false

    DataHubActorType:
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
      type: integer
      description: >-
        0 - BalanceResponsible, 1 - BalanceSupplier, 2 - GridCompany, 3 -
        SystemOwner, 4 - DanishEnergyAgency
      format: int32

    DkIndustryCodeLevel:
      type: string
      description: |-
        Industry code levels
        | Code name | Description |
        | --------- | ----------- |
        | Section   |             |
        | MainGroup |             |
        | Group     |             |
        | SubGroup  |             |
        | Industry  |             |
      enum:
        - Section
        - MainGroup
        - Group
        - SubGroup
        - Industry
    SearchListQueryModel:
      type: object
      additionalProperties: false
      properties:
        page:
          nullable: true
          type: object
          oneOf:
            - $ref: "#/components/schemas/PageBase"
        sort:
          nullable: true
          type: object
          oneOf:
            - $ref: "#/components/schemas/Sort"
        filter:
          $ref: "#/components/schemas/Filter"

    PageBase:
      type: object
      additionalProperties: false
      required:
        - number
        - size
      properties:
        number:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
        size:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647

    Sort:
      allOf:
        - $ref: "#/components/schemas/SortAbstraction"
        - type: object
          additionalProperties: false
          properties:
            direction:
              type: string
              nullable: true

    SortAbstraction:
      type: object
      additionalProperties: false
      properties:
        propertyName:
          type: string
          nullable: true
        nestedProperty:
          nullable: true
          type: object
          oneOf:
            - $ref: "#/components/schemas/NestedPropertySort"

    NestedPropertySort:
      allOf:
        - $ref: "#/components/schemas/SortAbstraction"
        - type: object
          additionalProperties: false
    Filter:
      allOf:
        - $ref: "#/components/schemas/FilterAbstraction"
        - type: object
          additionalProperties: false

    FilterAbstraction:
      type: object
      x-abstract: true
      additionalProperties: false
      properties:
        properties:
          type: array
          nullable: true
          items:
            $ref: "#/components/schemas/PropertyFilter"
        propertiesForNestedCollections:
          type: array
          nullable: true
          items:
            $ref: "#/components/schemas/NestedCollectionItemFilter"

    PropertyFilter:
      type: object
      additionalProperties: false
      required:
        - condition
      properties:
        name:
          type: string
          nullable: true
        condition:
          $ref: "#/components/schemas/FilterCondition"
        value:
          nullable: true
          type: object

    FilterCondition:
      type: string
      description: |-
        Industry code levels
        | Code name          | Description |
        | ------------------ | ----------- |
        | Like               |             |
        | In                 |             |
        | LessThan           |             |
        | LessThanOrEqual    |             |
        | GreaterThan        |             |
        | GreaterThanOrEqual |             |
        | StartsWith         |             |
      enum:
        - Like
        - Equal
        - In
        - LessThan
        - LessThanOrEqual
        - GreaterThan
        - GreaterThanOrEqual
        - StartsWith

    NestedCollectionItemFilter:
      allOf:
        - $ref: "#/components/schemas/FilterAbstraction"
        - type: object
          additionalProperties: false
          properties:
            name:
              type: string
              nullable: true

    PagedResultBase:
      type: object
      additionalProperties: false
      required:
        - currentPage
        - pageCount
        - pageSize
        - rowCount
      properties:
        currentPage:
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
        pageCount:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
        pageSize:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
        rowCount:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
        maxCount:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
          nullable: true

    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          nullable: true
          example: "https://errors.kmdelements.com/500"
        title:
          description: "A short, human-readable summary of the problem type."
          type: string
          nullable: true
          example: Error short description
        status:
          description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          nullable: true
          example: Description what exactly happened
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if de-referenced.
          type: string
          nullable: true
          example: /resources-path/1
      additionalProperties: true

    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              additionalProperties:
                type: array
                description: Array of validation error messages.
                items:
                  type: string
              nullable: false

    ErrorDescription:
      type: object
      additionalProperties: false
      required:
        - errorCode
        - defaultMessage
      properties:
        errorCode:
          type: string
        defaultMessage:
          type: string
        args:
          type: array
          description: Array of arguments for error messages.
          items:
            type: object
          nullable: true

    IdModel:
      type: object
      properties:
        id:
          type: string
          description: Id of created resource.
          format: uuid
      additionalProperties: false
  responses:
    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ValidationProblemDetails"
          examples:
            ItIsABadRequest:
              value:
                type: "https://errors.kmdelements.com/400"
                title: Bad Request
                status: 400
                detail: "Invalid request"
                instance: /resources-path/1
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            YouShallNotPass:
              value:
                type: "https://errors.kmdelements.com/401"
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources-path/1
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            CannotTouchThis:
              value:
                type: "https://errors.kmdelements.com/403"
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources-path/1
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ItWasHere:
              value:
                type: "https://errors.kmdelements.com/404"
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources-path/1
    "409":
      description: Conflict - entity updated concurrently and/or incorrect row version passed and/or resource is conflicting with unique constraint.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ItsConflictingOrSomethingElseChangedIt:
              value:
                type: "https://errors.kmdelements.com/409"
                title: Conflict
                status: 409
                detail: Conflict
                instance: /resources-path/1
    "422":
      description: 422 Unprocessable.
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/ErrorDescription'
          examples:
            CustomValidationsIncorrect:
              value:
                - errorCode: "Translatable error code."
                  defaultMessage: "Default error description in english."
                - errorCode: "Other translatable error code."
                  defaultMessage: "Other error description in english."
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            type: integer
            format: int32
            example: 360
            minimum: 0
            maximum: 2147483647
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            TooFastTooQuicklyTooSoon:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 360
                detail: Rate limit is exceeded.
                instance: /resources-path/1
    "499":
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/499"
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: /resources-path/1
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            This-should-not-happen:
              value:
                type: "https://errors.kmdelements.com/500"
                title: Internal Server Error
                status: 500
                detail: "body.0.age: Value `Not Int` does not match format `int32`"
                instance: /resources-path/1
    "503":
      description: 503 Service Unavailable.
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            type: integer
            example: 360
            format: int32
            minimum: 0
            maximum: 2147483647
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            CannotDoMore:
              value:
                type: "https://errors.kmdelements.com/503"
                title: Service Unavailable
                status: 503
                detail: The server is currently unable to receive requests. Please retry your request.
                instance: /resources-path/1
    "504":
      description: 504 Gateway Timeout.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotLivedToSee:
              value:
                type: "https://errors.kmdelements.com/504"
                title: Gateway Timeout
                status: 504
                detail: The server response not received in time.
                instance: /resources-path/1

  securitySchemes:
    JWT:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
tags:
  - name: Customers
    description: API for Customer management.
  - name: CustomersAdditionalAddresses
    description: API for management of customer additional addresses.
  - name: CustomersCompanyContactPersons
    description: API for management of company customer contact person.
  - name: CustomersExternalIdentifiers
    description: API for management customers external identifiers.
  - name: CustomersIntegration
    description: API for management customers integration.
  - name: AddressIntegration
    description: API for management address integration.
  - name: TenantConfigurations
    description: API supporting customer specific tenant's configuration management.
