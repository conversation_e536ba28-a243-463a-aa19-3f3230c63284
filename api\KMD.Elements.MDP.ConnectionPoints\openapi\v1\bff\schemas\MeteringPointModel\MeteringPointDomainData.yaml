type: object
nullable: true
description: Data used internally by metering point domain.
additionalProperties: false
properties:
  connectionPointVirtualId:
    description: Connection point object private identifier.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    nullable: true
  carId:
    description: Central address registry identifier.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  formulaVersion:
    description: |
      The formula that describes how data for the measuring point's time series is calculated using data
      from the individual formula parameters. The formula is sent to MDM, and all the parameters' data
      must be found in MDM in order for MDM to calculate the result profile.
    nullable: true
    allOf:
      - $ref: './FormulaVersionNullable.yaml'
