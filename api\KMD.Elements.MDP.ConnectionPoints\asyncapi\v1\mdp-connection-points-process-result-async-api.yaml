asyncapi: 2.6.0
id: https://dev.kmdelements.com/api/mdp-connection-points-api/
info:
  title: MDP.ConnectionPoints.API
  version: 1.2.0
  contact:
    name: Team-MD-3
    url: https://dev.azure.com/kmddk/COMBAS/_wiki/wikis/COMBAS.wiki/6375/Development-Team-MD-3
    email: <EMAIL>
  license:
    name: License
    url: 'https://www.kmd.net/terms-of-use'
  description: |
    Master Data Processes Connection Points API allows you to start processes that upsert connection point data.
  x-maintainers: Team-MD-3

tags:
  - name: Team-MD-3
    description: maintained by

servers:
  local:
    url: localhost:5001
    description: Local server
    protocol: kafka
    protocolVersion: 2.6.0

  development:
    url: 60000-kafkaBrokers
    description: Strimzi server which is stored under 60000-kafkaBrokers secret in KeyVault
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.event.mdp-connection-points.connection-point-process-result.v1:
    description: The topic on which master data connection point process result are sent.
     Event have process result information.
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    subscribe:
      description: Consume connection point process command.
      operationId: onUpsertProcess
      message:
        $ref: '#/components/messages/ConnectionPointProcessResultEvent'
    publish:
      description: Push draft connection point process command to the topic
      operationId: emitUpsertMeteringPointProcessCommand
      message:
        $ref: '#/components/messages/ConnectionPointProcessResultEvent'

components:
  messages:
    ConnectionPointProcessResultEvent:
      name: ConnectionPointProcessResultEvent
      title: Connection point process result event.
      contentType: application/json
      headers:
        $ref: "#/components/schemas/MessageHeaders"
      payload:
        $ref: "#/components/schemas/ConnectionPointProcessResultEventPayload"

  schemas:
    MessageHeaders:
      $ref: "./schemas/MessageHeaders.yaml"
    ConnectionPointProcessResultEventPayload:
      $ref: "./schemas/ConnectionPointProcessResultEventPayload.yaml"

  parameters:
    TenantId:
      $ref: "./parameters/TenantId.yaml"
