type: object
description: Value list header model.
additionalProperties: false
required:
  - id
  - headerText
  - headerOrder
properties:
  id:
    $ref: '../DataTypes/Guid.yaml'
  headerText:
    description: Header display text.
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
  headerOrder:
    allOf:
      - $ref: '../DataTypes/PositiveInteger.yaml'
    nullable: false
    description: Column order - lowest should come first.