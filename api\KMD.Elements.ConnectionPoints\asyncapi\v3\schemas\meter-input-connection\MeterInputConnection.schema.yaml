MeterInputConnection:
  type: object
  additionalProperties: false
  description: Model of Meter Input Connection.
  required:
    - entityId
    - meterFrameId
    - meterId
    - meterInputNumber
    - meterInputType
    - meterNumber
    - meterType
    - fulfillsRequirements
    - validFrom
  properties:
    # - REQUIRED FOR BUSINESS EVENTS
    entityId:
      description: Meter Input Connection Entity Id.
      type: string
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
    # - END OF REQUIRED FOR BUSINESS EVENTS
    # - ROOT ENTITY FIELDS
    meterFrameId:
      description: Technical Id of the Meter Frame
      type: string
      format: uuid
      example: "8902FA98-E40C-4434-ADFF-AA85A80F0FC0"
      x-reference:
        entityFamily: "MeterFrame"
        entityType: "MeterFrame"
    meterId:
      description: The technical key that uniquely identifies the Meter
      type: integer
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 111
      x-reference:
        entityFamily: "MeterManagement"
        entityType: "Meter"
    meterInputNumber:
      description: This field is part of the MeterConfiguration in the Meter domain.
      type: integer
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 111
    meterInputType:
      description: Shared list of enums between Meter Frame and Meter domain
      type: string
      pattern: "^(Internal|External|WiredMbus|MBus|Pulse)$"
      example: "Internal"
    meterNumber:
      description: |-
        This is the meter number that is communicated to the DataHub.
        The meter number can deviate from the meter number that can be seen on the physical meter,
        though according to guidelines they should match.
      type: string
      pattern: "^.*$"
      minLength: 1
      maxLength: 25
      example: "8975439870435"
    meterType:
      description: Values comes from the Meter entity in the Meter domain. The field defines the type of Meter assigned.
      type: string
      pattern: "^.*$"
      minLength: 1
      maxLength: 50
      example: "meter type"
    fulfillsRequirements:
      description: Boolean updated by validation method that indicates whether Meter fulfils the register requirements.
      type: boolean
      example: true
    connectionTypeValidationPassed:
      description: Boolean updated by validation method that indicates whether Meter fulfils the connection type requirements.
      type: [ 'boolean', 'null' ]
      example: true
    ctValidationPassed:
      description: Boolean updated by validation method that indicates whether Meter fulfils the CT validation requirements.
      type: [ 'boolean', 'null' ]
      example: true
    vtValidationPassed:
      description: Boolean updated by validation method that indicates whether Meter fulfils the VT validation requirements.
      type: [ 'boolean', 'null' ]
      example: true
    meterAttributeRequirementsValidationPassed:
      description: Boolean updated by validation method that indicates whether Meter fulfils the attributes requirements.
      type: boolean
      example: true
    meterAttributeRequirementsLastValidationDate:
      description: Indicates time of the last meter attributes validation check.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2024-06-03T22:00:00.000Z"
    lastChecked:
      description: Indicates time of the last validation check.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2024-06-03T22:00:00.000Z"
    validationStatus:
      description: Status text on the latest validation check.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
    validFrom:
      description: Defines the validity period, when the meter is assigned to the meter frame.
      type: string
      format: date-time
      example: "2024-06-03T22:00:00.000Z"
    validUntil:
      description: Defines the validity period, when the meter is assigned to the meter frame.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2024-06-03T22:00:00.000Z"
    displayConfiguration:
      description: 'Display configuration.'
      type: [ 'string', 'null' ]
      format: uuid
      example: '79f3ec96-3bd4-41f8-bbc2-aa73846c86a7'
    registerConfiguration:
      description: 'Register configuration.'
      type: [ 'string', 'null' ]
      format: uuid
      example: 'f0322843-8742-41ea-91e1-970634c84bb1'
    meterComponentRequirementsValidationPassed:
      description: Boolean updated by validation method that indicates whether Meter fulfils the component requirements.
      type: [ 'boolean', 'null' ]
      example: true
    meterComponentRequirementsLastValidationDate:
      description: Indicates time of the last meter component validation check.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2024-06-03T22:00:00.000Z"
    meterComponentInvalidRequirements:
      description: Collection of meter components which do not fulfill meter component requirements.
      type: array
      minItems: 0
      maxItems: 100
      items:
        $ref: "./sub-schemas/MeterComponentInvalidRequirement.schema.yaml#/MeterComponentInvalidRequirement"
