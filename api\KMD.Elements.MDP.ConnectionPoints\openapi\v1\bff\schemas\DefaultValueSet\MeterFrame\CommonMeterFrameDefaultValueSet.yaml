type: object
description: Model containing the base attributes of Metering Frame Default Value Set.
required:
  - name
properties:
  name:
    description: Name of Meter Frame Default Value Set.
    allOf:
      - $ref: '../../DataTypes/ShortString.yaml'
  tagAssignments:
    type: array
    description: Tags.
    maxItems: 100
    items:
      $ref: '../../TagAssignmentModel.yaml'
    nullable: true
  commonReading:
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
    description: 'Common Reading.'
  meterReadingType:
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
    description: 'A list of different reading methods a meter can have.'
  placementCode:
    description: 'Placement code'
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  placementSpecification:
    description: 'DK: Placeringsbeskrivelse.'
    allOf:
      - $ref: '../../DataTypes/ShortStringNullable.yaml'
