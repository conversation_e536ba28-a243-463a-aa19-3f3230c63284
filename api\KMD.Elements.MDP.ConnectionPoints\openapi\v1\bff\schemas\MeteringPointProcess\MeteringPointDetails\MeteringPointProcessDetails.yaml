type: object
description: Process details.
required:
  - status
  - startedDate
properties:
  processId:
    description: Process ID.
    allOf:
      - $ref: "../../DataTypes/Guid.yaml"
  supplyType:
    description: Process supply type.
    allOf:
      - $ref: "../../SupplyType.yaml"
  processType:
    description: Process type.
    allOf:
      - $ref: "./ProcessType.yaml"
  assignedTo:
    description: Name of user that is assigned to process.
    allOf:
      - $ref: "../../DataTypes/MediumStringNullable.yaml"
  completedDate:
    description: Date and time of process completion.
    nullable: true
    allOf:
      - $ref: "../../DataTypes/DateTime.yaml"
  status:
    $ref: "../../ProcessModel/OverallState.yaml"
  startedBy:
    description: Started by user is used for identifying who started the process.
    type: string
    maxLength: 100
    pattern: ^.*$
  startedDate:
    description: Date and time of start process
    allOf:
      - $ref: "../../DataTypes/DateTime.yaml"
  comment:
    type: string
    maxLength: 512
    pattern: ^.*$
    nullable: true
    description: The process comment.
  errorDetails:
    type: string
    maxLength: 512
    pattern: ^.*$
    nullable: true
    description: The process error details.
  onBehalfOf:
    description: On behalf of is used for identifying on behalf who the process was started.
    allOf:
      - $ref: "../../DataTypes/MediumStringNullable.yaml"
  steps:
    type: array
    description: Array containing steps of the process.
    maxItems: 100
    items:
      $ref: "./Step.yaml"
  relatedObjects:
    type: array
    description: Array of related objects connected with process.
    maxItems: 100
    items:
      $ref: "../../ProcessModel/RelatedObject.yaml"
  parentProcessInfo:
    nullable: true
    description: Contain basic parent process data.
    oneOf:
      - $ref: '../../ProcessModel/ParentProcess.yaml'
additionalProperties: false
