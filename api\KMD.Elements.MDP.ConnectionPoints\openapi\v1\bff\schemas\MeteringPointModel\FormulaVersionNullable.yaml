type: object
nullable: true
description: |
  Price element association that connects a price element to a metering point in a given time period.
additionalProperties: false
required:
  - isSimpleFormula
properties:
  name:
    description: The formula short name.
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  isSimpleFormula:
    description: |
      Indicates whether a formula is calculated on the basis of a formula expression or calculated on the basis
      of a meter reading on a specific meter frame register requirement (simple formula).
      When it is a "simple formula" the formula will not have a reference to formulaType.
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
  formulaType:
    nullable: true
    description: |
      A reference to a FormulaType that indicates which formula template to use.
      If isSimpleFormula is False then FormulaType is Mandatory.
    allOf:
      - $ref: './FormulaType.yaml'
  formula:
    description: |
      The formula that describes how data for the measuring point's time series is calculated using data
      from the individual formula parameters. The formula is sent to MDM, and all the parameters' data
      must be found in MDM in order for MDM to calculate the result profile.
    allOf:
      - $ref: '../DataTypes/DescriptionEmptyStringNullable.yaml'
  formulaParameters:
    description: Metering Point Formula Parameters collection.
    nullable: true
    type: array
    minItems: 0
    maxItems: 10000
    items:
      $ref: './FormulaParameter.yaml'
  description:
    description: A description of what resulting data the formula generates.
    allOf:
      - $ref: '../DataTypes/DescriptionStringNullable.yaml'
  meterFrameVirtualId:
    description: Internal Virtual Meter Frame id.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  registerRequirementVirtualId:
    description: Internal Virtual Register Requirement id.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
