title: MeterFrameGisPropertiesElectricityModel
description: |-
  GIS properties for electricity supply type in Meter Frame.
type: object
additionalProperties: false
properties:
  gisId:
    type: string
    format: uuid
    nullable: true
    description: Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.
  gidEquipmentContainerId:
    type: string
    format: uuid
    nullable: true
    description: Top level (Equipment container) topology item identifier. Top level parent of topology node specified in gisId.
  branchLineFuseAmps:
    description: "Indicates the size of, or setting of the maximum circuit breaker in front of the branch line on the network company's side. Ex. 125 Amperes."
    type: integer
    nullable: true
    format: int32
    minimum: -2147483648
    maximum: 2147483647
    example: 111
  branchLineFuseType:
    description: 'Indicates the type of branch line fuse present. Ex. Fuse, Maximum switch, HSP fuse.'
    pattern: "^.*$"
    type: string
    nullable: true
    minLength: 1
    maxLength: 50
