type: object
description: Modify Connection Point process details.
properties:
  processId:
    description: Modify Connection Point process ID.
    allOf:
      - $ref: "../DataTypes/Guid.yaml"
  connectionPointVirtualId:
    description: Virtual Connection Point Id.
    allOf:
      - $ref: "../DataTypes/Guid.yaml"
  supplyTypes:
    type: array
    description: Array of related with connection point supply types.
    maxItems: 100
    items:
      $ref: "../DataTypes/OneWordString.yaml"
  processType:
    description: Type of process
    allOf:
      - $ref: "../ProcessModel/ProcessType.yaml"
  assignedTo:
    description: Name of user that is assigned to process.
    allOf:
      - $ref: "../DataTypes/MediumStringNullable.yaml"
  completedDate:
    type: string
    description: Datetime when the process finished.
    format: date-time
    nullable: true
  status:
    $ref: "./OverallState.yaml"
  startedBy:
    description: Started by user is used for identifying who started the process.
    type: string
    maxLength: 100
    pattern: ^.*$
  startedDate:
    type: string
    description: Datetime when the process started sending messages.
    format: date-time
  comment:
    type: string
    maxLength: 512
    pattern: ^.*$
    nullable: true
    description: The process comment.
  onBehalfOf:
    description: On behalf of is used for identifying on behalf who the process was started.
    allOf:
      - $ref: "../DataTypes/MediumStringNullable.yaml"
  targetDate:
    type: string
    description: Datetime when the process should finished.
    format: date-time
    nullable: true
  steps:
    type: array
    description: Array containing steps of the process.
    maxItems: 100
    items:
      $ref: "./Step.yaml"
  connectionPointNumber:
    allOf:
      - $ref: "../DataTypes/ShortStringObsolete.yaml"
    description: Number of the ConnectionPoint.
    example: "20000001"
  connectionPointId:
    allOf:
      - $ref: "../DataTypes/GuidNullable.yaml"
    description: ConnectionPoint Id.
    example: "8902FA98-E40C-4434-ADFF-AA85A80F0FC0"
  errorMessages:
    type: array
    maxItems: 100
    description: Error message.
    items:
      $ref: "./ErrorMessage.yaml"
    nullable: true
  additionalInformation:
    description: Additional information.
    allOf:
      - $ref: "./AdditionalInformation.yaml"
    nullable: true
  template:
    description: Information about template usage.
    allOf:
      - $ref: "./TemplateDetails.yaml"
    nullable: true
  relatedObjects:
    type: array
    description: Array of related objects connected with process.
    maxItems: 100
    items:
      $ref: "./RelatedObject.yaml"
  parentProcessInfo:
    nullable: true
    type: object
    description: Contain basic parent process data.
    oneOf:
      - $ref: './ParentProcess.yaml'
additionalProperties: false
