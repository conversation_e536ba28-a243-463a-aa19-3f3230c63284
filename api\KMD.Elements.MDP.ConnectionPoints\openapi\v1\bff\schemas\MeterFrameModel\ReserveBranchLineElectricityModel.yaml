title: ReserveBranchLineElectricityModel
description: ReserveBranchLineElectricityModel
type: object
additionalProperties: false
required:
  - branchLineNumber
properties:
  branchLineNumber:
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    description: 'Number on branch line. Ex. XF2500.'
    example: 'XF2500'
  branchLineType:
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
    description: 'Branch line type.'
    example: 'ShortStringWithoutSpaces'
  materialCode:
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
    description: 'Material code.'
    example: 'Material code'
  neutralSquare:
    allOf:
      - $ref: '../DataTypes/DecimalNullable.yaml'
    description: 'Neutral square.'
    example: 1
  numberOfCables:
    allOf:
      - $ref: '../DataTypes/IntegerNullable.yaml'
    description: 'Number of cables.'
    example: 1
  numberOfConductors:
    allOf:
      - $ref: '../DataTypes/IntegerNullable.yaml'
    description: 'Number of conductors.'
    example: 1
  systemGrounding:
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
    description: 'System Grounding.'
    example: 'SystemGrounding'
