openapi: 3.0.3

info:
  title: KMD.Elements.ConnectionPoints.BFC
  x-maintainers: Team-MD-2
  description: KMD Elements - Connection Points BFC. Service provides a endpoints for external clients to manage Connection Points.
  termsOfService: https://www.kmd.net/terms-of-use
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: License
    url: https://www.kmd.net/terms-of-use
  version: '1.64'

servers:
  - url: "/bfc"
  - url: "/connection-points-bfc"
  - url: "/api/connection-points/bfc"

security:
  - Jwt: [ ]

paths:
  /v1/meter-frames/commands/get-meter-frame-by-number:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: getMeterFrameModelByNumber
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFrameNumberInSchema'
        required: true
      responses:
        '200':
          description: Found Meter Frame.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMeterFrameModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/commands/by-business-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Gets specific by Connection Point Number.
      description: Gets specific by Connection Point Number.
      operationId: getConnectionPointByConnectionPointNumber
      parameters:
        - $ref: '#/components/parameters/ConnectionPointNumberInQueryString'
      responses:
        '200':
          description: Found Connection Point.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionPointModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/register-requirements:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirement
      summary: Gets paged Register Requirements for passed query parameters.
      description: Gets paged Register Requirements for passed query parameters.
      operationId: getPagedRegisterRequirements
      parameters:
        - $ref: '#/components/parameters/MeterFrameIdInQueryGuid'
        - $ref: '#/components/parameters/RegisterRequirementIdStartsWithInQueryString'
        - $ref: '#/components/parameters/MeterFrameNumberInQueryString'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: Register Requirements collection.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRegisterRequirementResponseBody'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
  /v1/meter-frames/{meterFrameId}/meter-input-connections:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Gets all Meter Input Connections assigned to Meter Frame.
      description: Gets a list of "Meter Input Connection" objects on a Meter Frame.
      operationId: getMeterInputConnectionsByMeterFrameId
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Meter Input Connections.
          content:
            application/json:
              schema:
                description: Meter Input Connections.
                type: array
                maxItems: 1000
                items:
                  $ref: '#/components/schemas/MeterInputConnectionModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-input-connections/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Fetches list of Meter Input Connections.
      description: |-
        ### Result
        Returns filtered and paged list of Meter Input Connections according to passed criteria.
      operationId: searchMeterInputConnections
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterInputConnectionsListSearchQueryModel'
        required: true
      responses:
        '200':
          description: Meter Input Connections returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetMeterInputConnectionsSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

components:
  parameters:
    MeterFrameId:
      name: meterFrameId
      in: path
      description: Id of the Meter Frame.
      required: true
      schema:
        description: Id of the Meter Frame.
        type: string
        format: uuid
      example: 4d14817c-b622-493b-a515-284c0872d15d
    MeterFrameIdInQueryGuid:
      name: meterFrameId
      in: query
      description: Id of the Meter Frame.
      schema:
        description: Id of the Meter Frame.
        type: string
        format: uuid
        nullable: true
      example: 4d14817c-b622-493b-a515-284c0872d15d
    ConnectionPointNumberInQueryString:
      name: connectionPointNumber
      in: query
      description: Connection Point Number.
      required: true
      schema:
        description: Connection Point Number.
        pattern: "^.*$"
        minLength: 1
        maxLength: 128
        type: string
      example: "20000001"
    PageNumber:
      name: pageNumber
      in: query
      description: Page number for pagination - defaults to 1.
      required: true
      schema:
        description: Page number for pagination - defaults to 1.
        type: integer
        nullable: true
        format: int32
        minimum: -2147483648
        maximum: 2147483647
      example: 1
    PageSize:
      name: pageSize
      in: query
      description: Page size for pagination - limited by application if not passed.
      required: true
      schema:
        description: Page size for pagination - limited by application if not passed.
        type: integer
        minimum: 0
        maximum: 10000
        nullable: true
        format: int32
      example: 1
    RegisterRequirementIdStartsWithInQueryString:
      name: registerRequirementIdStartsWithInQueryString
      in: query
      description: Starts with technical identifier.
      schema:
        description: Starts with technical identifier.
        type: integer
        format: int32
        minimum: -2147483648
        maximum: 2147483647
        nullable: true
      example: 1234
    MeterFrameNumberInQueryString:
      name: meterFrameNumber
      in: query
      description: Meter Frame number.
      schema:
        type: string
        description: Number on the Meter Frame.
        pattern: "^.*$"
        minLength: 1
        maxLength: 50
        example: '4268761_01'
      example: '4268761_01'
  schemas:
    MeterFrameNumberInSchema:
      title: MeterFrameNumberInSchema
      type: object
      description: |-
        Meter Frame Number property.
      additionalProperties: false
      required:
        - meterFrameNumber
      properties:
        meterFrameNumber:
          type: string
          description: Meter Frame number.
          pattern: "^.*$"
          minLength: 1
          maxLength: 50
          nullable: false
          example: 'aa123'

    MeterFrameGisProperties:
      title: MeterFrameGisProperties
      type: object
      description: 'Common properties for GIS in MeterFrame.'
      additionalProperties: false
      required:
        - gisId
        - gisDescription
      properties:
        gisId:
          type: string
          description: 'Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.'
          format: uuid
        gisDescription:
          type: string
          pattern: "^.*$"
          minLength: 1
          maxLength: 1000
          description: 'The name of the connection point in the physical topology. The name is set when the gis id is selected from the GIS system.'
          example: 'GisDescription is any 1000 characters long string with spaces allowed.'
          nullable: false

    MeterFrameGisPropertiesElectricityModel:
      title: MeterFrameGisPropertiesElectricityModel
      description: |-
        GIS properties for electricity supply type in Meter Frame.
      additionalProperties: false
      allOf:
        - $ref: '#/components/schemas/MeterFrameGisProperties'
        - type: object
          additionalProperties: false
          properties:
            branchLineFuseAmps:
              type: integer
              description: "Indicates the size of, or setting of the maximum circuit breaker in front of the branch line on the network company's side. Ex. 125 Amperes."
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              example: 125
              nullable: true
            branchLineFuseType:
              type: string
              pattern: "^.*$"
              maxLength: 50
              description: 'Indicates the type of branch line fuse present. Ex. Fuse, Maximum switch, HSP fuse.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            cabinetNumber:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              description: 'Number of the cable box where the plug is connected. Ex. 123658.'
              example: 123658
              nullable: true
            cableNumber:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              description: 'Number on execution in station. Ex. 1, 2, 3 etc.'
              example: 3
              nullable: true
            connectionPointLevel:
              type: string
              pattern: "^.*$"
              maxLength: 50
              description: 'Indicates at which level the branch line is connected in the network. Ex. C, B1, B2, A1, A2 and A0.'
              example: 'B1'
              nullable: true
            shinNumber:
              type: integer
              description: 'Number on the rail in the locker.'
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              example: 15
            stationNumber:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              description: 'The station from which the branch line is supplied. Ex. 98756.'
              example: 98756
            transformerNumber:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              description: 'Transformer number, for several transformers in a station. Ex. 1, 2, 3 etc.'
              example: 2

    MeterFrameGisPropertiesHeatingModel:
      title: MeterFrameGisPropertiesHeatingModel
      description: |-
        GIS properties for heating supply type in Meter Frame.
      required:
        - branchLineSize
      allOf:
        - $ref: '#/components/schemas/MeterFrameGisProperties'
        - type: object
          additionalProperties: false
          properties:
            branchLineSize:
              type: string
              pattern: "^.*$"
              minLength: 1
              maxLength: 25
              description: 'Indicates how large a branch line is laid in the ground from the main line to the meter.'
              example: 'SomeStringWithoutSpaces'
              nullable: false
            branchLineNumber:
              type: integer
              description: 'Number on branch line. Ex. 246810.'
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              example: 246810
            expectedForwardFlowTemp:
              type: integer
              description: 'Calculated value of forward-flow temperature.'
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              example: 2
            expectedPressure:
              type: string
              pattern: "^.*$"
              maxLength: 50
              description: 'Calculated value of expected pressure - max/min/delta.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            heatingPlantId:
              type: integer
              description: 'Heating plant Id - Heater "kID".'
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              example: 3
            heatingPlantName:
              description: 'Heating plant name.'
              type: string
              nullable: true
              pattern: "^.*$"
              minLength: 0
              maxLength: 50
              example: 'Max. 50 characters long string with spaces.'
            heatingPlantPipeName:
              description: 'Pipe name / outlet marking.'
              type: string
              nullable: true
              pattern: "^.*$"
              minLength: 0
              maxLength: 50
              example: 'Max. 50 characters long string with spaces.'
            heatingPlantPipeNumber:
              type: integer
              description: 'Outlet marking number.'
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              example: 4
            hydraulicZone:
              description: 'Indicates an area, e.g. a city (polygon).'
              type: string
              nullable: true
              pattern: "^.*$"
              maxLength: 50
              example: 'Max. 50 characters long string with spaces.'

    MeterFrameGisPropertiesWaterModel:
      title: MeterFrameGisPropertiesWaterModel
      description: |-
        GIS properties for water supply type in Meter Frame.
      allOf:
        - $ref: '#/components/schemas/MeterFrameGisProperties'
        - type: object
          additionalProperties: false
          properties:
            branchLineSizeSquare:
              description: 'Indicates how large a branch line is laid in the ground from Main line to Area.'
              type: string
              nullable: true
              pattern: "^.*$"
              maxLength: 25
              example: 'ShortStringWithoutSpaces'
            branchLineNumber:
              type: string
              maxLength: 50
              pattern: "^.*$"
              description: 'Number on branch line. Ex. XF2500.'
              example: 'XF2500'
              nullable: true
            hardness:
              type: integer
              description: 'Hardness in dH (german unit for water hardness).'
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              example: 1
            mainZone:
              type: string
              maxLength: 50
              pattern: "^.*$"
              description: 'Indicates an area (higher level than section) = an operating area (polygon).'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            pressure:
              type: string
              maxLength: 50
              pattern: "^.*$"
              description: 'Section/elevation/conduction loss (calculated value).'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            sectionId:
              type: integer
              description: 'Indicates an area (polygon) in which the water meter frame is located (for checking water balance).'
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              example: 2
            sectionName:
              type: string
              maxLength: 50
              pattern: "^.*$"
              description: 'Section name.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            superZone:
              type: string
              maxLength: 50
              pattern: "^.*$"
              description: 'Indicates an area <Section> (higher level than MainZone) = e.g. a city (polygon).'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            waterPlantId:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              description: 'Water Plant Id.'
              nullable: true
              example: 3
            waterPlantName:
              type: string
              maxLength: 50
              pattern: "^.*$"
              description: 'Water Plant Name.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            waterPlantPipeId:
              type: integer
              description: 'Water Plant Pipe Id.'
              format: int32
              minimum: -2147483648
              maximum: 2147483647
              nullable: true
              example: 4
            waterPlantPipeName:
              type: string
              maxLength: 50
              pattern: "^.*$"
              description: 'Water Plant Pipe name.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true

    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
          example: "https://errors.kmdelements.com/500"
        title:
          description: "A short, human-readable summary of the problem type."
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
          example: Error short description
        status:
          description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
          type: integer
          format: int32
          minimum: 400
          maximum: 599
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          maxLength: 2048
          pattern: "^.*$"
          nullable: true
          example: Description what exactly happened
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          maxLength: 32779
          pattern: "^.*$"
          nullable: true
          example: /resources/1

    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1000
              additionalProperties:
                type: array
                description: Array of validation error messages.
                maxItems: 1000
                items:
                  type: string
                  maxLength: 2048
                  pattern: "^.*$"
              nullable: true

    FilterAbstraction:
      type: object
      additionalProperties: false
      description: Filter abstraction.
      properties:
        properties:
          type: array
          nullable: true
          description: Array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/PropertyFilter'
        propertiesForNestedCollections:
          type: array
          nullable: true
          description: Properties for nested collections.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/NestedCollectionItemFilter'
    PropertyFilter:
      type: object
      additionalProperties: false
      description: Property filter.
      required:
        - condition
      properties:
        name:
          type: string
          nullable: true
          maxLength: 1000
          pattern: "^.*$"
          description: Property name.
        condition:
          $ref: '#/components/schemas/FilterCondition'
        value:
          nullable: true
          type: object
          description: Property value.
    FilterCondition:
      type: integer
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: Filter condition.
      x-enumNames:
        - Like
        - Equal
        - In
        - LessThan
        - LessThanOrEqual
        - GreaterThan
        - GreaterThanOrEqual
        - StartsWith
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
    NestedCollectionItemFilter:
      description: Nested collection item filter.
      allOf:
        - $ref: '#/components/schemas/FilterAbstraction'
        - type: object
          additionalProperties: false
          properties:
            name:
              type: string
              maxLength: 1000
              pattern: "^.*$"
              nullable: true
              description: Name.
    ErrorDescription:
      type: object
      additionalProperties: false
      description: Error description.
      required:
        - errorCode
        - defaultMessage
      properties:
        errorCode:
          type: string
          maxLength: 1000
          pattern: "^.*$"
          description: Error code.
        defaultMessage:
          type: string
          maxLength: 10000
          pattern: "^.*$"
          description: Default message.
    SupplyTypesModel:
      type: integer
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: List of possible supply types "Electricity", "Heating", "Water"
      x-enumFlags: true
      x-enumNames:
        - Electricity
        - Heating
        - Water
      enum:
        - 1
        - 2
        - 4
    AddressStatus:
      type: integer
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: List of possible statuses that a master data address can have "Active", "Inactive".
      x-enumNames:
        - Active
        - Inactive
      enum:
        - 1
        - 2
    AddressType:
      type: integer
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: List of possible address types that a MasterDataAddressDetails object can hold. Eg. "Address", "AccessAddress".
      x-enumNames:
        - Primary
        - Temporary
        - AccessAddress
      enum:
        - 1
        - 2
        - 3
    MeterFrameSupplyStatusModel:
      type: integer
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
      x-enumNames:
        - Connected
        - Disconnected
      enum:
        - 1
        - 2
    MeterFrameSupplyDisconnectTypeModel:
      type: integer
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation".
      x-enumNames:
        - MeterNoVoltage
        - DisconnectedWithBreakerInMeter
        - DisconnectedBeforeMeter
        - DisconnectedInKabinet
        - DisconnectedAfterMeter
        - DisconnectedStation
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
    MeterFrameId:
      type: string
      format: uuid
      description: Technical Id of the Meter Frame.
      example: 4d14817c-b622-493b-a515-284c0872d15d
    GetMeterFrameModel:
      type: object
      additionalProperties: false
      description: Get meter frame model.
      required:
        - id
        - changedByUserId
        - created
        - meterFrameNumber
        - supplyType
        - commonReading
        - meterMissing
        - meterSealed
        - meterWorkConsumerBilled
        - rowVersion
        - connectionPointId
        - supplyStatus
        - tagAssignments
        - placementCode
        - accessInformation
        - connectionStatus
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        changedByUserId:
          type: string
          description: Last change user id.
          format: uuid
        created:
          type: string
          description: |-
            DK: Oprettet.
            Creation time stamp of the Meter Frame.
          format: date-time
        meterFrameNumber:
          description: |-
            DK: Målerrammenummer.
            Number on the Meter Frame.
          allOf:
            - $ref: '#/components/schemas/ShortString'
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        commonReading:
          allOf:
            - $ref: '#/components/schemas/GuidField'
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
        meterMissing:
          type: boolean
          description: 'DK: MålerVæk.'
        placementSpecification:
          description: 'DK: Placeringsbeskrivelse.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        meterSealDate:
          type: string
          description: |-
            DK: Målerplomberingsdato.
            Indicates the date when the meter was sealed.
          format: date-time
          nullable: true
        meterSealed:
          type: boolean
          description: |-
            DK: MålerPlomberet.
            Indicates whether the meter is sealed.
        meterWorkConsumerBilled:
          type: boolean
          description: |-
            DK: MålerYdelseFaktureresEjer.
            Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
        connectionStatus:
          type: string
          description: 'DK: Tilslutningsstatus.'
          format: uuid
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
        statusChanged:
          type: string
          description: Latest status change date.
          format: date-time
          nullable: true
        electricityAttributes:
          description: ElectricityAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameElectricityAttributesModel'
        heatingAttributes:
          description: HeatingAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameHeatingAttributesModel'
        waterAttributes:
          description: MeterFrameWaterAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameWaterAttributesModel'
        geographicalLocation:
          description: Set of geographical location properties - describing Meter Frame geo location.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/GeographicalLocationModel'
        gisPropertiesElectricity:
          description: MeterFrameGisPropertiesElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesElectricityModel'
        gisPropertiesHeating:
          description: MeterFrameGisPropertiesHeating
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesHeatingModel'
        gisPropertiesWater:
          description: MeterFrameGisPropertiesWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesWaterModel'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        connectionPointId:
          type: string
          description: ConnectionPointId.
          format: uuid
        addressId:
          type: string
          description: An UUID reference to a master data address.
          format: uuid
          nullable: true
        addressName:
          description: AddressName.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine1:
          description: AddressLine1.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine2:
          description: AddressLine2.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressStatus:
          $ref: '#/components/schemas/AddressStatus'
        addressType:
          $ref: '#/components/schemas/AddressType'
        darStatus:
          $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          $ref: '#/components/schemas/LifeCycleStatus'
        supplyStatus:
          $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/TagAssignmentModel'
        meterReadingType:
          $ref: '#/components/schemas/GuidFieldNullable'
        supplyDisconnectType:
          $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        placementCode:
          type: string
          description: 'Placement code CodeList value id.'
          format: uuid
          nullable: false
        noMeter:
          type: boolean
          description: 'DK: Målerfri.'
          nullable: false
        accessInformation:
          type: array
          description: Access information.
          maxItems: 1000
          items:
            $ref: '#/components/schemas/AccessInformationModel'
    MeterFrameElectricityAttributesModel:
      type: object
      additionalProperties: false
      description: Meter frame electricity attributes model.
      required:
        - connectionType
        - breakerBeforeMeter
      properties:
        connectionType:
          type: string
          description: 'DK: Tilslutningstype.'
          format: uuid
        breakerBeforeMeter:
          type: boolean
          description: 'DK: AfbryderFørMåler.'
        powerLimitA:
          type: number
          description: 'DK: EffektgrænseA.'
          format: decimal
          nullable: true
        powerLimitKw:
          type: number
          description: 'DK: EffektgrænseKW.'
          format: decimal
          nullable: true
        productionCapacity:
          type: number
          description: 'DK: Anlægskapacitet.'
          format: decimal
          nullable: true
        purpose:
          type: string
          description: 'DK: Formål.'
          format: uuid
          nullable: true
        ratioCt:
          type: string
          maxLength: 1000
          description: 'DK: OmsætningsforholdCT.'
          pattern: "^.*$"
          nullable: true
        ratioVt:
          type: string
          maxLength: 1000
          description: 'DK: OmsætningsforholdVT.'
          pattern: "^.*$"
          nullable: true
        tarifConnectionPoint:
          type: string
          description: 'DK: TarifTilslutningspunkt.'
          format: uuid
          nullable: true
        flexAttributeObject:
          type: string
          description: |-
            DK: FleksAttributObjekt.
            An UUID reference to a Settlement Object that is used to register flexible attributes about the Meter Frame.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical Meter Frame.
          format: uuid
          nullable: true
        lossFactor:
          type: number
          description: |-
            DK: NettabsFaktor.
            Nettabsfaktor som bruges på målerrammen i MDM. Vi skal lige gennemgå den fra MDM siden. Skal indgå både på fuldttidserie niveau så faktoren ganges på både 15/60 forbrug samt
            tællerstande.
            Mains loss factor used on the measuring frame in MDM. We just need to review it from the MDM page. Must be included both at full-time series level so the factor is multiplied by
            both 15/60 consumption and meter readings.
          format: decimal
          nullable: true
        meterFrameFuse:
          type: integer
          description: |-
            DK: MålerRammeForsikring.
            Forsikring (tarifsikring, T-ret sikring etc) som ikke er stikledningssikringen eller tilslutningsrettigheden. Kunden kan selv bestemme størrelsen på denne.
            Hvis kunden ikke har oplyst en forsikring, så er stikledningssikringen kundens forsikring.
            Kunden kan selv sikrer op eller ned (stikledningssikringen begrænser ham selvfølgelig).
            Vi har den kun i systemet for at kunne rumme data fra blanketten.
            EN:
            Insurance (tariff fuse, T-right fuse etc) which is not the branch line fuse or the connection right. The customer can decide the size of this.
            If the customer has not stated an insurance, then the branch line insurance is the customer's insurance.
            The customer can secure up or down himself (the branch line protection limits him of course).
            We only have it in the system to be able to hold data from the form.
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          nullable: true
        meterDisplaySettings:
          type: string
          description: |-
            DK: VisningMåler.
            En bruger skal kunne stille krav til visning af alle målerer som installeres i målerrammen.
            Skal medfører målerrammekrav på målerammen som gennemtvinger specifik display visning.
            EN:
            A user must be able to set requirements for displaying all meters that are installed in the meter frame.
            Must entail meter frame requirements on the meter frame which enforces specific display.
          format: uuid
          nullable: true
        connectionRemark:
          type: string
          description: |-
            DK: TilslutningsBemærkning.
            Specielle forhold omkring tilslutning, f.eks. adapter, kvadrat, klemmetype etc.
            EN:
            Special conditions regarding connection, e.g. adapter, square, terminal type etc.
          format: uuid
          nullable: true
    MeterFrameHeatingAttributesModel:
      type: object
      additionalProperties: false
      description: Meter frame heating attributes model (MFPropertySetHeat).
      required:
        - calculateCooling
      properties:
        calculateCooling:
          type: boolean
          description: 'DK: BeregnAfkøling.'
        coolingLimit:
          type: number
          description: 'DK: Afkølingsgrænse.'
          format: decimal
          nullable: true
        criticalCustomer:
          type: string
          description: 'DK: KritiskKundekategori.'
          format: uuid
          nullable: true
        planEffect:
          description: 'DK: Anlægsydelse.'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        plantType:
          description: 'DK: Anlægstype.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        counterPlacement:
          description: Counter Placement.
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        heatMeterConnectionType:
          description: Heat Meter Connection Type.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        heatMeterDisplaySettings:
          description: Heat Meter Display Settings.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        returnHeatingConnected:
          type: boolean
          description: 'DK: Returvarme.'
          nullable: true
        totalAllowedAreaM2:
          description: 'Allowed total area m2'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        allowedBusinessAreaM2:
          description: 'Allowed business area m2'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        allowedStorageAreaM2:
          description: 'Allowed storage area m2'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
    MeterFrameWaterAttributesModel:
      type: object
      additionalProperties: false
      description: Meter frame water attributes model.
      required:
        - directSprinkling
        - driveBy
      properties:
        directSprinkling:
          type: boolean
          description: DK:DirekteSprinkling.
        driveBy:
          type: string
          description: DK:DriveBy.
          format: uuid
        criticalCustomer:
          type: string
          description: DK:KritiskKundekategori.
          format: uuid
          nullable: true
        mediumCategory:
          type: string
          description: DK:MediumKategori.
          format: uuid
          nullable: true
        ownPump:
          type: boolean
          description: DK:EgenPumpe.
          nullable: true
        pressureEnhancer:
          type: boolean
          description: DK:TrykForøger.
          nullable: true
        qvkSensor:
          type: boolean
          description: DK:QvkSensor.
          nullable: true
        waterMeterDisplaySettings:
          description: Water Meter Display Settings.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        waterFlowLimit:
          type: string
          description: Flow limit
          minLength: 1
          maxLength: 3
          pattern: "^(2,5|6|10|15|25|40|100|150)$"
        flexAttributeObject:
          type: string
          description: DK:FleksAttributObjekt.
          format: uuid
          nullable: true
    GeographicalLocationModel:
      description: This object contains information about a point on the globe, including information about the projection, source accuracy, etc.
      type: object
      additionalProperties: false
      required:
        - accuracy
        - name
        - source
        - technicalStandard
        - longitude
        - latitude
        - elevation
      properties:
        accuracy:
          type: string
          pattern: "^.*$"
          minLength: 1
          maxLength: 25
          description: The accuracy class of the point.
        name:
          type: string
          pattern: "^.*$"
          minLength: 1
          maxLength: 25
          description: The name of the point, e.g. Waypoint, Access Point.
          nullable: false
        source:
          type: string
          pattern: "^.*$"
          minLength: 1
          maxLength: 50
          description: Source of the geographical location point.
          nullable: false
        technicalStandard:
          type: string
          pattern: "^.*$"
          minLength: 1
          maxLength: 25
          description: Technical standard.
          nullable: false
        longitude:
          type: number
          description: Longitude - X in most spatial file formats.
          format: decimal
          nullable: false
        latitude:
          type: number
          description: Latitude - Y in most spatial file formats.
          format: decimal
          nullable: false
        elevation:
          type: number
          description: Z - elevation in some spatial formats - number of meters above the surface of the water.
          format: decimal
          nullable: false
    DarStatus:
      type: integer
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated".
      x-enumNames:
        - 'Yes'
        - 'No'
        - Temporary
      enum:
        - 1
        - 2
        - 3
    LifeCycleStatus:
      type: integer
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: List of possible life cycle states that the MDR system can put the address into.
      x-enumNames:
        - ToBeDeleted
        - Valid
        - UnderInvestigation
      enum:
        - 1
        - 2
        - 3
    GetConnectionPointModel:
      type: object
      additionalProperties: false
      description: Get connection point model.
      required:
        - id
        - connectionPointNumber
        - created
        - rowVersion
        - changedByUserId
        - priceGroupExcluded
        - supplyType
        - tagAssignments
        - createdWithoutInstallationForm
      properties:
        id:
          type: string
          description: Connection point object private identifier.
          format: uuid
        connectionPointNumber:
          type: string
          maxLength: 128
          pattern: "^.*$"
          description: Number of the ConnectionPoint.
          example: "20000001"
        created:
          type: string
          description: Creation time stamp of the ConnectionPoint.
          format: date-time
        description:
          type: string
          maxLength: 1024
          pattern: "^.*$"
          description: Custom description.
          nullable: true
        rowVersion:
          type: string
          description: Row version.
          format: byte
        changedByUserId:
          type: string
          description: ID of a user who changed the entity last time.
          format: uuid
        address:
          type: string
          description: An UUID reference to a master data address.
          format: uuid
          nullable: true
        addressName:
          type: string
          maxLength: 50
          pattern: "^.*$"
          description: AddressName from CAR.
          nullable: true
        addressLine1:
          type: string
          maxLength: 128
          pattern: "^.*$"
          description: AddressLine1 from CAR.
          nullable: true
        addressLine2:
          type: string
          maxLength: 128
          pattern: "^.*$"
          description: AddressLine2 from CAR.
          nullable: true
        addressStatus:
          description: AddressStatus from CAR.
          nullable: true
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          oneOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          description: AddressType from CAR.
          nullable: true
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          oneOf:
            - $ref: '#/components/schemas/AddressType'
        darStatus:
          description: DarStatus from CAR.
          nullable: true
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          oneOf:
            - $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          description: LifeCycleStatus from CAR.
          nullable: true
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          oneOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        alternativeInstallationNumber:
          type: string
          maxLength: 256
          pattern: "^.*$"
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        installationNumber:
          type: string
          nullable: true
          maxLength: 256
          pattern: "^.*$"
          description: Installation number.
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        supplyType:
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          description: 'Defines usage of Connection Point: “Electricity”, ”Heating”, “Water”.'
          oneOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/TagAssignmentModel'
        electricityAttributes:
          nullable: true
          type: object
          description: Electricity attributes.
          oneOf:
            - $ref: '#/components/schemas/ElectricityAttributesModel'
        heatingAttributes:
          nullable: true
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          description: Heating attributes.
          oneOf:
            - $ref: '#/components/schemas/HeatingAttributesModel'
        waterAttributes:
          nullable: true
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          description: Water attributes.
          oneOf:
            - $ref: '#/components/schemas/WaterAttributesModel'
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form.
          type: boolean
          nullable: false
    TagAssignmentModel:
      type: object
      additionalProperties: false
      description: Tag assignment model.
      required:
        - id
        - assigneeId
        - tagCodeListValueId
        - tagCodeListValue
      properties:
        id:
          type: string
          format: uuid
          description: Id.
        assigneeId:
          type: string
          format: uuid
          description: Assignee id.
        tagCodeListValueId:
          type: string
          format: uuid
          description: Tag code list value id.
        tagCodeListValue:
          type: string
          maxLength: 1024
          pattern: "^.*$"
          description: Tag code list value.
    ElectricityAttributesModel:
      type: object
      additionalProperties: false
      description: Electricity attributes model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - connectionStatus
        - temporary
      properties:
        connectionPointCategoryValue:
          type: string
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
          format: uuid
        installationTypeValue:
          type: string
          description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
          format: uuid
        connectionStatus:
          type: string
          description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
          format: uuid
        consumerCategory:
          type: string
          description: Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.
          format: uuid
          nullable: true
        deMasterDataForms:
          type: integer
          description: DEMasterDataForm that comes from the settlement calculation.
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          nullable: true
        installationDescription:
          type: string
          maxLength: 256
          pattern: "^.*$"
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        netSettlementGroup:
          type: string
          description: This field register the net settlement group, which is also used in the market communication (DataHub).
          format: uuid
          nullable: true
        gridAreaId:
          description: Grid area id.
          allOf:
            - $ref: '#/components/schemas/OneWordStringNullable'
        temporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
        temporaryUntil:
          type: string
          description: |-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
            grid company.
          format: date-time
          nullable: true
        flexAttributeObject:
          type: string
          description: |-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: uuid
          nullable: true
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    HeatingAttributesModel:
      type: object
      additionalProperties: false
      description: Heating attributes model.
      required:
        - connectionPointCategoryValue
        - connectionStatus
        - installationTypeValue
        - heatWaterHeater
      properties:
        connectionPointCategoryValue:
          type: string
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific codelist.
          format: uuid
        connectionStatus:
          type: string
          description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
          format: uuid
        installationTypeValue:
          type: string
          description: Defines type of installation type. Eg. For apartments, single households, Industrial, Agricultural.
          format: uuid
        installationDescription:
          type: string
          maxLength: 256
          pattern: "^.*$"
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        numberOfWaterHeater:
          type: integer
          description: The number of Water heaters.
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          nullable: true
        heatWaterHeater:
          type: string
          description: List of different hot water heating controls that can be installed.
          format: uuid
        waterHeaterType:
          type: string
          description: List of different water heating types that can be installed.
          format: uuid
          nullable: true
        heatPlantType:
          type: string
          description: Lists the different plant types.
          format: uuid
          nullable: true
        heatExchange:
          type: string
          description: List of different heat exchanger options.
          format: uuid
          nullable: true
        flexAttributeObject:
          type: string
          description: |-
            An UUID reference to a settlement object that is used to register flexible attributes about the connection point.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: uuid
          nullable: true
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    WaterAttributesModel:
      type: object
      additionalProperties: false
      description: Water attributes model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - connectionStatus
        - temporary
      properties:
        connectionPointCategoryValue:
          type: string
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
          format: uuid
        installationTypeValue:
          type: string
          description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
          format: uuid
        connectionStatus:
          type: string
          description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
          format: uuid
        temporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
        temporaryUntil:
          type: string
          description: |-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
            grid company.
          format: date-time
          nullable: true
        installationDescription:
          type: string
          maxLength: 256
          pattern: "^.*$"
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        flexAttributeObject:
          type: string
          description: |-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: uuid
          nullable: true
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    GetRegisterRequirementResponseBody:
      type: object
      additionalProperties: false
      description: Paged result of register requirement collection.
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          description: Current page number.
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
        pageSize:
          description: Page size.
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
        totalPages:
          description: Number of pages.
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
        totalRows:
          description: Number of total rows.
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
        results:
          description: Array with result models.
          type: array
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/RegisterRequirementModel'
    RegisterRequirementModel:
      type: object
      additionalProperties: false
      description: Register Requirement model.
      properties:
        id:
          description: Register Requirement internal identifier.
          example: 1234
          type: integer
          format: int32
          nullable: false
          minimum: 0
          maximum: 2147483647
        meterFrameId:
          description: Meter Frame id.
          type: string
          format: uuid
          nullable: false
        registerRequirementType:
          $ref: '#/components/schemas/RegisterRequirementTypeModel'
        name:
          description: |-
            DK: Navn.
            Name of requirement (typically from MeteringComponent value list).
          type: string
          maxLength: 128
          pattern: "^.*$"
          nullable: false
        meteringComponentId:
          description: |-
            DK: MålingsKomponentId.
            Shared value list with Meter domain.
            The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
            It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
          type: string
          format: uuid
          nullable: false
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          nullable: true
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        inputNumber:
          description: Input number
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 1
        mainMeterFrameId:
          description: Reference to the main Meter Frame
          allOf:
            - $ref: "#/components/schemas/GuidFieldNullable"
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        mainMeterFrameNumber:
          description: Extended reference to the main Meter Frame - Meter Frame Number
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: MF-001122
        mainMeterFrameRegisterRequirementId:
          description: |-
            DK: HovedMalerrammeRegisterKravId.
            Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          nullable: true
        mainMeterFrameRegisterRequirementName:
          description: Used to refer to the main meter register requirement.
          type: string
          maxLength: 128
          pattern: "^.*$"
          nullable: true
        outsourcedToMeterFrameRegisterRequirementId:
          description: |-
            DK: HjemtagesAfMalerrammeRegisterKravId.
            Can refer to another meter frame which is actually the one that receives data through an external input.
          type: integer
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          nullable: true
        outsourcedToMeterFrameRegisterRequirementName:
          description: Used to refer to another meter frame which is actually the one that receives data through an external input.
          pattern: "^.*$"
          maxLength: 128
          type: string
          nullable: true
        futureRequirement:
          description: |-
            DK: FremtidigtRegisterKra
            Indicates whether the register requirement should only be a future requirement. Default false.
            If set to true Validity period will be ignored.
          type: boolean
          nullable: false
        meterReadingRequired:
          description: |-
            DK: AflæsningPåkrævet
            Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
          type: boolean
          nullable: false
        requiredFrom:
          description: |-
            DK: GyldigFra.
            Indicates when the meter must meet the requirement.
            format: date-time
          type: string
          format: date-time
          nullable: true
        requiredUntil:
          description: |-
            DK: GyldigTil.
            Indicates from when the meter does not have to meet the requirement.
          type: string
          format: date-time
          nullable: true
        rowVersion:
          description: Row version.
          type: string
          format: byte
    RegisterRequirementTypeModel:
      nullable: false
      description: List of possible register requirement "ControlRequirement", "MarketRequirement".
      type: integer
      format: int32
      minimum: 1
      maximum: 2
      x-enumFlags: false
      x-enumNames:
        - ControlRequirement
        - MarketRequirement
      enum:
        - 1
        - 2
    MeterInputConnectionModel:
      type: object
      additionalProperties: false
      description: Model of Meter Input Connection.
      required:
        - meterFrameId
        - meterId
        - meterInputNumber
        - meterInputType
        - meterNumber
        - meterType
        - fulfillsRequirements
        - validFrom
      properties:
        id:
          description: |-
            Meter Input Connection id.
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
          type: string
          format: uuid
          nullable: false
        meterFrameId:
          $ref: '#/components/schemas/MeterFrameId'
        meterId:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
          example: 360
          description: The technical key that uniquely identifies the Meter
        meterInputNumber:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
          example: 360
          description: This field is part of the MeterConfiguration in the Meter domain.
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        meterNumber:
          type: string
          description: |-
            This is the meter number that is communicated to the DataHub.
            The meter number can deviate from the meter number that can be seen on the physical meter,
            though according to guidelines they should match.
          maxLength: 25
          pattern: ""
          example: "8975439870435"
        meterType:
          type: string
          maxLength: 50
          pattern: "^.*$"
          example: "meter type"
          description: Values comes from the Meter entity in the Meter domain. The field defines the type of Meter assigned.
        fulfillsRequirements:
          type: boolean
          example: true
          description: Boolean updated by validation method that indicates whether Meter fulfils the register requirements.
        lastChecked:
          type: string
          format: date-time
          nullable: true
          example: "2022-09-07T09:50:30.870Z"
          description: Indicates time of the last validation check.
        validationStatus:
          type: string
          maxLength: 50
          pattern: "^.*$"
          nullable: true
          description: Status text on the latest validation check.
        validFrom:
          type: string
          format: date-time
          nullable: false
          example: "2022-09-07T09:50:30.870Z"
          description: Defines the validity period, when the meter is assigned to the meter frame.
        validUntil:
          type: string
          format: date-time
          nullable: true
          example: "2022-09-07T09:50:30.870Z"
          description: Defines the validity period, when the meter is assigned to the meter frame.
        displayConfiguration:
          description: |-
            Display configuration.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        registerConfiguration:
          description: |-
            Display configuration.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
    MeterInputType:
      type: string
      enum:
        - Internal
        - External
        - WiredBus
        - MBus
        - Pulse
      description: Shared list of enums between Meter Frame and Meter domain
    MeterInputConnectionsListSearchQueryModel:
      type: object
      additionalProperties: false
      description: Meter Input Connections list search query model.
      required:
        - filter
      properties:
        page:
          nullable: true
          type: object
          description: Page object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Page'
        filter:
          description: Filter object.
          type: object
          nullable: false
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/MeterInputConnectionsListSearchFilterModel'
    PagedResultOfGetMeterInputConnectionsSearchModel:
      description: Paged result of get meter input connections search model.
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          additionalProperties: false
          properties:
            results:
              type: array
              nullable: true
              description: Results.
              maxItems: 1000000
              items:
                $ref: '#/components/schemas/GetMeterInputConnectionsSearchModel'
    AccessInformationModel:
      type: object
      description: Indicates the registered access information that the utility has regarding the address, including contact information, key information, codes, etc.
      additionalProperties: false
      required:
        - type
      properties:
        type:
          allOf:
            - $ref: '#/components/schemas/ShortString'
          description: Indicates where the information is accessed. Ex. "Locked basement door".
          example: 'type'
        contactPerson:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Indicates the name of the contact person who can be contacted to gain access.
          example: 'contact person'
        keyNumber:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: Indicates a possible key number when there are multiple keys.
          example: 'key number'
        keyStorageLocation:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Describes storage location, e.g. '60 / 10 SKB.
          example: 60 / 10 SKB
        keyPlacement:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Describes the location of the key, e.g. key cabinet.
          example: key cabinet
        keyComment:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Various remarks.
          example: 'key comment'
        doorCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: Indicates any code for the door.
          example: 'door code'
        doorCodeComment:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Various remarks on the door code.
          example: 'door code comment'
    FlexAttributeObject:
      description: |-
        An UUID reference to a Settlement Object that is used to register flexible attributes about the entity.
        In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an entity.
      nullable: true
      type: string
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
    GetMeterInputConnectionsSearchModel:
      description: Get connection points search model.
      allOf:
        - $ref: '#/components/schemas/MeterInputConnectionModel'
        - type: object
          additionalProperties: false
          properties:
            connectionPointId:
              $ref: '#/components/schemas/ConnectionPointId'
            connectionPointNumber:
              allOf:
                - $ref: '#/components/schemas/ShortStringObsolete'
              description: Number of the ConnectionPoint.
              example: "20000001"
            meterFrameNumber:
              allOf:
                - $ref: '#/components/schemas/ShortString'
              description: Meter Frame number.
              nullable: false
              example: 'aa123'
    MeterInputConnectionsListSearchFilterModel:
      description: Properties for filtration of Meter Input Connections.
      type: object
      additionalProperties: false
      properties:
        meterId:
          description: Filter by Meter Id - integer value.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MeterId'
        supplyType:
          description: Filter by Supply Type Enum Flag - integer value.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
          example: 1
    ConnectionPointId:
      description: Connection Point technical identifier.
      type: string
      format: uuid
      example: 4d14817c-b622-493b-a515-284c0872d15e
    MeterId:
      allOf:
        - $ref: '#/components/schemas/PositiveInteger'
      example: 360
      description: The technical key that uniquely identifies the Meter
    Page:
      type: object
      additionalProperties: false
      description: Page.
      required:
        - number
        - size
      properties:
        number:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Page number.
        size:
          type: integer
          format: int32
          minimum: 0
          maximum: 10000
          description: Page size.
    PagedResultBase:
      type: object
      additionalProperties: false
      description: Paged result.
      required:
        - currentPage
        - pageCount
        - pageSize
        - rowCount
      properties:
        currentPage:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Current page.
        pageCount:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Page count.
        pageSize:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Page size.
        rowCount:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Row count.
        maxCount:
          allOf:
            - $ref: '#/components/schemas/NonNegativeIntegerNullable'
          description: Max count.
    # --------------------------------------------------------- COMMON DATA TYPES --------------------------------------------------------------------------
    GuidId:
      description: UUID Id of the Resource.
      allOf:
        - $ref: '#/components/schemas/GuidField'

    GuidField:
      type: string
      description: GUID field.
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8

    GuidFieldNullable:
      nullable: true
      type: string
      description: GUID field.
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8

    BooleanField:
      type: boolean
      description: Boolean field.
      example: true

    IntegerNullable:
      type: integer
      nullable: true
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Integer type in <-2147483648,2147483647> range.'
      example: 111

    PositiveInteger:
      type: integer
      nullable: false
      format: int32
      minimum: 1
      maximum: 2147483647
      description: 'Integer type in <1,2147483647> range.'
      example: 111

    NonNegativeInteger:
      type: integer
      nullable: false
      format: int32
      minimum: 0
      maximum: 2147483647
      description: 'Integer type in <0,2147483647> range.'
      example: 111

    NonNegativeIntegerNullable:
      type: integer
      nullable: true
      format: int32
      minimum: 0
      maximum: 2147483647
      description: 'Integer type in <0,2147483647> range.'
      example: 111

    OneWordStringNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 0
      maxLength: 25
      description: 'Max. 25 characters long string with spaces.'
      example: 'Max. 25 chars. w. spaces.'

    ShortStringObsolete:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 128
      description: 'Max. 128 characters long string with spaces.'
      example: 'Max. 128 characters long string with spaces.'

    ShortString:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 50
      description: 'Max. 50 characters long string with spaces.'
      example: 'Max. 50 characters long string with spaces.'

    ShortStringNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 0
      maxLength: 50
      description: 'Max. 50 characters long string with spaces.'
      example: 'Max. 50 characters long string with spaces.'

    MediumStringNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 0
      maxLength: 100
      description: 'Max. 100 characters long string with spaces.'
      example: 'Max. 100 characters long string with spaces.'

    RowVersion:
      type: string
      description: RowVersion.
      format: byte
      example: "AAAAAAAAB+I="

  headers:
    RetryAfter:
        description: Number of seconds until you should try again.
        schema:
          type: integer
          format: int32
          minimum: 1
          maximum: 2678400 # 31 days
        required: true
        example: 3600 # 1 hour

  responses:
    '400':
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ValidationProblemDetails'
          examples:
            ItIsABadRequest:
              value:
                type: 'https://errors.kmdelements.com/400'
                title: Bad Request
                status: 400
                detail: 'Invalid request'
                instance: /resources-path/1
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    '401':
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            YouShallNotPass:
              value:
                type: 'https://errors.kmdelements.com/401'
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources-path/1
    '403':
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            CannotTouchThis:
              value:
                type: 'https://errors.kmdelements.com/403'
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources-path/1
    '404':
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ItWasHere:
              value:
                type: 'https://errors.kmdelements.com/404'
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources-path/1
    '422':
      description: 422 Unprocessable.
      content:
        application/problem+json:
          schema:
            description: 422 Unprocessable.
            type: array
            maxItems: 1000000
            items:
              $ref: '#/components/schemas/ErrorDescription'
          examples:
            CustomValidationsIncorrect:
              value:
                - errorCode: 'Translatable error code.'
                  defaultMessage: 'Default error description in english.'
                - errorCode: 'Other translatable error code.'
                  defaultMessage: 'Other error description in english.'
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          $ref: "#/components/headers/RetryAfter"
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            TooManyRequestsExample:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 429
                detail: Rate limit is exceeded.
                instance: "/resources/1"
    "499":
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ClientClosedRequestExample:
              value:
                type: "https://errors.kmdelements.com/499"
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: "/resources/1"
    '500':
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            Thisshouldnothappen:
              value:
                type: 'https://errors.kmdelements.com/500'
                title: Internal Server Error
                status: 500
                detail: 'body.0.age: Value `Not Int` does not match format `int32`'
                instance: /resources-path/1
    '502':
      description: 502 Bad Gateway.
    '503':
      description: 503 Service Unavailable.
    '504':
      description: 504 Gateway Timeout.

  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
tags:
  - name: ConnectionPoint
    description: API for Connection Points management.
  - name: MeterFrame
    description: API for Meter Frames management.
  - name: MeterInputConnections
    description: API for managing information about information about assigning Meter to a Meter Frame in certain period of time.
  - name: RegisterRequirement
    description: API for Register Requirement management.
