title: ModifyConnectionPointProcessCommand
type: object
required:
  - processId
properties:
  processId:
    type: string
    format: uuid
    description: Id of Modify Connection Point process to create.
    nullable: false
  templateId:
    type: string
    format: uuid
    description: Master data process template Id
    nullable: true
  installationFormReference:
    type: string
    description: Reference number from the installation form (form number)
    nullable: true
    minLength: 1
    maxLength: 50
  meterFrame:
    $ref: './../MeterFrame.yaml'
  connectionPoint:
    $ref: './../ConnectionPoint.yaml'
  meterNumber:
    description: Number identifying the meter
    type: string
    maxLength: 100
    nullable: true
  consumptionMeteringPointId:
    description: Unique number for the consumption metering point (18 characters)
    type: string
    nullable: true
    minLength: 18
    maxLength: 18
    pattern: '^\d{18}$'
  installationAddressCarId:
    description: Installation address Car Id
    type: string
    format: uuid
    nullable: true
  parentProcess:
    $ref: './../ParentProcess.yaml'
additionalProperties: false
