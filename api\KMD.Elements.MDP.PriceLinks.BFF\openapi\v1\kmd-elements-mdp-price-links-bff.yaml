openapi: 3.0.3
info:
  title: KMD.Elements.MDP.PriceLinks.BFF
  description: |-
    ## Master Data Processes Price Links BFF
    Stability level: PREVIEW<br/>

    ## Capabilities
    The API allows to:
    - Manage Price Link processes

  termsOfService: 'https://www.kmd.net/terms-of-use'

  contact:
    name: KMD Elements
    url: 'https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements'
    email: <EMAIL>

  license:
    name: License
    url: 'https://www.kmd.net/terms-of-use'

  version: '0.17'
  x-maintainers: Team-SE-1

servers:
  - url: http://localhost:5740
  - url: https://localhost:5011

security:
  - Bearer: []
  - OAuth2: []

tags:
  - name: ConnectionPoints
    description: Methods related to fetching Connection Point data.
  - name: MarketParticipants
    description: Methods related to fetching Market Participants data.
  - name: MassPriceLinkProcesses
    description: Methods related to the Master Data Procesess - Mass Price Link Process.
  - name: PriceGroupProcesses
    description: Methods related to the Master Data Processes - Price Group Process.
  - name: ConditionalPriceLinkProcesses
    description: Methods related to the Master Data Processes - Conditional Price Link Process.
  - name: PriceLinkProcesses
    description: Methods related to the Master Data Processes - Price Link Process.
  - name: PriceManagement
    description: Methods related to fetching Price data.
  - name: Users
    description: Methods related to fetching Users data.

paths:
  /v1/price-link-processes/{id}:
    get:
      tags:
        - PriceLinkProcesses
      summary: Get the Price Link Process.
      description: Get the Price Link Process.
      operationId: getPriceLinkProcess
      x-authorization: ProcessPriceLinks.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/Id"
      responses:
        '200':
          description: Price Link Process returned successfully.
          content:
              application/json:
                schema:
                  $ref: '#/components/schemas/GetPriceLinkProcessResponse'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    patch:
      tags:
        - PriceLinkProcesses
      summary: Update Price Link Process.
      description: Update Price Link Process.
      operationId: patchPriceLinkProcesses
      x-authorization: ProcessPriceLinks.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/Id"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePriceLinkProcessRequest'
        required: true
      responses:
        '204':
          description: Price Link Process updated successfully.
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        '500':
          $ref: "#/components/responses/500"
        '502':
          $ref: "#/components/responses/502"
        '503':
          $ref: "#/components/responses/503"
        '504':
          $ref: "#/components/responses/504"
  /v1/price-link-processes/{processId}/actions:
    get:
      tags:
        -  PriceLinkProcesses
      summary: Get the Price Link Process Action history.
      description: Get the Price Link Process Action history.
      operationId: getPriceLinkProcessActions
      x-authorization: ProcessPriceLinks.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - name: processId
          in: path
          description: The Price Link Process identifier
          required: true
          schema:
            type: string
            format: uuid
          example: 6a6018e8-98a7-4f41-a3ee-127fd34eeb37
      responses:
        '200':
          description: Price Link Process Action history returned successfully.
          content:
              application/json:
                schema:
                  $ref: '#/components/schemas/GetPriceLinkProcessActionsPagedResponse'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/price-link-processes:
    post:
      tags:
        - PriceLinkProcesses
      summary: Create Price Link Process.
      description: Create Price Link Process.
      operationId: postPriceLinkProcess
      x-authorization: ProcessPriceLinks.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePriceLinkProcessRequest'
        required: true
      responses:
        '201':
          description: Price Link Process created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Id'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/users:
    get:
      tags:
        - Users
      summary: Returns paged list of users.
      description: Returns paged list of users.
      operationId: getUsers
      x-authorization: ProcessPriceLinks.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - in: query
          name: name
          schema:
            type: string
            maxLength: 100
            pattern: ^.*$
          description: Name of the user.
          example: 'Bob'
        - $ref: "#/components/parameters/PageNumber"
        - $ref: "#/components/parameters/PageSize"
      responses:
        '200':
          description: Users found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUsersPagedResponse'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/mass-price-link-processes/{id}:
    get:
      tags:
        - MassPriceLinkProcesses
      summary: Get the Mass Price Link Process.
      description: Get the Mass Price Link Process.
      operationId: getMassPriceLinkProcess
      x-authorization: ProcessPriceLinks.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/Id"
      responses:
        '200':
          description: Mass Price Link Processes returned successfully.
          content:
              application/json:
                schema:
                  $ref: '#/components/schemas/GetMassPriceLinkProcessResponse'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

    patch:
      tags:
        - MassPriceLinkProcesses
      summary: Update Mass Price Link Process.
      description: Update Mass Price Link Process.
      operationId: patchMassPriceLinkProcess
      x-authorization: ProcessPriceLinks.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/Id"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMassPriceLinkProcessRequest'
        required: true
      responses:
        '204':
          description: Mass Price Link Process updated successfully.
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/mass-price-link-processes:
    post:
      tags:
        - MassPriceLinkProcesses
      summary: Create Mass Price Link Process.
      description: Create Mass Price Link Process.
      operationId: postMassPriceLinkProcess
      x-authorization: ProcessPriceLinks.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMassPriceLinkProcessRequest'
        required: true
      responses:
        '201':
          description: Mass Price Link Process created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Id'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '409':
          $ref: "#/components/responses/409"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/mass-price-link-processes/{processId}/price-link-processes:
    get:
      tags:
        - MassPriceLinkProcesses
      summary: Get the Price Link Processes of the Mass Price Link Process.
      description: Get the Price Link Processes.
      operationId: getMassPriceLinkProcessPriceLinkProcesses
      x-authorization: ProcessPriceLinks.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - name: processId
          in: path
          description: The Mass Price Link Process identifier
          required: true
          schema:
            type: string
            format: uuid
          example: 6a6018e8-98a7-4f41-a3ee-127fd34eeb37
      responses:
        '200':
          description: Price Link Process returned successfully.
          content:
              application/json:
                schema:
                  $ref: '#/components/schemas/GetPriceLinkProcessesPagedResponse'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/mass-price-link-process-files/{id}:
    get:
      tags:
        - MassPriceLinkProcesses
      summary: Get the Mass Price Link Process file.
      description: Get the Mass Price Link Process file.
      operationId: getMassPriceLinkProcessFile
      x-authorization: ProcessPriceLinks.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/Id"
      responses:
        '200':
          $ref: "#/components/responses/MassPriceLinkFileResponse"
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/price-links/{id}:
    get:
      tags:
        - PriceManagement
      summary: Get the Price Link.
      description: Get the Price Link by Id.
      operationId: getPriceLink
      x-authorization: MeteringPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/PriceLinkId"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        '200':
          description: Price Link returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPriceLinkResponse'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/price-elements:
    get:
      tags:
        - PriceManagement
      summary: Returns paged list of Price Elements.
      description: Returns paged list of Price Elements.
      operationId: getPriceElements
      x-authorization: ProcessPriceLinks.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      x-authorization-2: PriceManagement.Read
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/OwnerGln"
        - $ref: "#/components/parameters/SupplyType"
        - $ref: "#/components/parameters/PriceTypeCode"
        - in: query
          name: priceId
          schema:
            type: string
            maxLength: 36
            pattern: ^.*$
          description: The id of the Price Elements.
          example: '8ff91da4-8c22'
        - in: query
          name: name
          schema:
            type: string
            maxLength: 100
            pattern: ^.*$
          description: Name of the price.
          example: 'price name'
        - $ref: "#/components/parameters/ValidityDate"
        - $ref: "#/components/parameters/PageNumber"
        - $ref: "#/components/parameters/PageSize"
      responses:
        '200':
          description: Price Elements found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPriceElementsPagedResponse'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/connection-points/{connectionPointId}:
    get:
      tags:
        - ConnectionPoints
      summary: Gets the specified Connection Point.
      description: Gets the specified Connection Point.
      operationId: getConnectionPoint
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/ConnectionPointId"
      responses:
        '200':
          description: ConnectionPoint not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionPointResponse'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/connection-points/{connectionPointId}/metering-points:
    get:
      tags:
        - ConnectionPoints
      summary: Gets Metering Points connected to the specified Connection Point.
      description: Gets Metering Points connected to the specified Connection Point.
      operationId: getPagedMeteringPoints
      x-authorization: ProcessPriceLinks.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      x-authorization-2: MeteringPoints.Read
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - in: path
          name: connectionPointId
          schema:
            type: string
            format: uuid
          description: The Connection Point identifier
          example: 6a6018e8-98a7-4f41-a3ee-127fd34eeb37
          required: true
        - $ref: "#/components/parameters/SupplyType"
        - $ref: "#/components/parameters/PageNumber"
        - $ref: "#/components/parameters/PageSize"
      responses:
        '200':
          description: Price Elements found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMeteringPointsPagedResponse'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/price-group-processes/{priceGroupProcessId}/reports/export-summary:
    get:
      tags:
        - PriceGroupProcesses
      summary: Get the Price Group Process file.
      description: Get the Price Group Process file.
      operationId: getPriceGroupProcessFile
      x-authorization: PriceGroup.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/PriceGroupProcessId"
      responses:
        '200':
          $ref: "#/components/responses/PriceGroupProcessFileResponse"
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/price-group-processes/{priceGroupProcessId}:
    get:
      tags:
        - PriceGroupProcesses
      summary: Get Price Group Process.
      description: Get Price Group Process.
      operationId: getPriceGroupProcess
      x-authorization: PriceGroup.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/PriceGroupProcessId"
      responses:
        '200':
          description: Price Group Process returned successfully.
          content:
              application/json:
                schema:
                  $ref: "#/components/schemas/GetPriceGroupProcessResponse"
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    patch:
      tags:
        -  PriceGroupProcesses
      summary: Update Price Group Process.
      description: Update Price Group Process.
      operationId: patchPriceGroupProcess
      x-authorization: PriceGroup.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/PriceGroupProcessId"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePriceGroupProcessRequest'
        required: true
      responses:
        '204':
          description: Price Group Process updated successfully.
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/conditional-price-link-processes/{conditionalPriceLinkProcessId}/reports/export-summary:
    get:
      tags:
        - ConditionalPriceLinkProcesses
      summary: Get the Conditional Price Link Process file.
      description: Get the Conditional Price Link Process file.
      operationId: getConditionalPriceLinkProcessFile
      x-authorization: ConditionalPriceLinks.Read
      x-authorization-1: Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/ConditionalPriceLinkProcessId"
      responses:
        '200':
          $ref: "#/components/responses/ConditionalPriceLinkProcessFileResponse"
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/conditional-price-link-processes/{conditionalPriceLinkProcessId}:
    get:
      tags:
        - ConditionalPriceLinkProcesses
      summary: Get Conditional Price Link Process.
      description: Get Conditional Price Link Process.
      operationId: getConditionalPriceLinkProcess
      x-authorization: ConditionalPriceLinks.Read
      x-authorization-1: Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/ConditionalPriceLinkProcessId"
      responses:
        '200':
          description: Conditional Price Link Process returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetConditionalPriceLinkProcessResponse"
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    patch:
      tags:
        - ConditionalPriceLinkProcesses
      summary: Update Conditional Price Link Process.
      description: Update Conditional Price Link Process.
      operationId: patchConditionalPriceLinkProcess
      x-authorization: ConditionalPriceLinks.Write
      x-authorization-1: Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
        - $ref: "#/components/parameters/ConditionalPriceLinkProcessId"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConditionalPriceLinkProcessRequest'
        required: true
      responses:
        '204':
          description: Conditional Price Link Process updated successfully.
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/market-participants/glns:
    get:
      tags:
        - MarketParticipants
      summary: Gets GLN numbers from the market participant.
      description: Gets GLN numbers related to the market participant.
      operationId: getGlns
      x-authorization: ProcessPriceLinks.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/CorrelationId"
        - $ref: "#/components/parameters/MessageId"
      responses:
        '200':
          description: Returns paged list of GLN numbers.
          content:
            application/json:
              schema:
                $ref: './schemas/GlnNumbersPaged.yaml'
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '409':
          $ref: "#/components/responses/409"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

components:
  parameters:
    CorrelationId:
      $ref: "./parameters/CorrelationId.yaml"
    MessageId:
      $ref: "./parameters/MessageId.yaml"
    Id:
      $ref: "./parameters/Id.yaml"
    PriceLinkId:
      $ref: "./parameters/PriceLinkId.yaml"
    PageNumber:
      $ref: "./parameters/PageNumber.yaml"
    PageSize:
      $ref: "./parameters/PageSize.yaml"
    PriceGroupProcessId:
      $ref: "./parameters/PriceGroupProcessId.yaml"
    PriceTypeCode:
      $ref: "./parameters/PriceTypeCode.yaml"
    SupplyType:
      $ref: "./parameters/SupplyType.yaml"
    ValidityDate:
      $ref: "./parameters/ValidityDate.yaml"
    OwnerGln:
      $ref: './parameters/GlnNumber.yaml'
    ConnectionPointId:
      $ref: "./parameters/ConnectionPointId.yaml"
    ConditionalPriceLinkProcessId:
      $ref: "./parameters/ConditionalPriceLinkProcessId.yaml"

  schemas:
    Id:
      $ref: "./schemas/Id.yaml"
    GetConnectionPointResponse:
      $ref: "./responses/GetConnectionPointResponse.yaml"
    GetPriceLinkProcessResponse:
      $ref: "./responses/GetPriceLinkProcessResponse.yaml"
    GetPriceLinkProcessesPagedResponse:
      $ref: "./responses/GetPriceLinkProcessesPagedResponse.yaml"
    GetPriceLinkProcessActionsPagedResponse:
      $ref: "./responses/GetPriceLinkProcessActionsPagedResponse.yaml"
    CreatePriceLinkProcessRequest:
      $ref: "./schemas/requests/CreatePriceLinkProcessRequest.yaml"
    UpdatePriceLinkProcessRequest:
      $ref: "./schemas/requests/UpdatePriceLinkProcessRequest.yaml"
    GetMassPriceLinkProcessResponse:
      $ref: "./responses/GetMassPriceLinkProcessResponse.yaml"
    CreateMassPriceLinkProcessRequest:
      $ref: "./schemas/requests/CreateMassPriceLinkProcessRequest.yaml"
    UpdateMassPriceLinkProcessRequest:
      $ref: "./schemas/requests/UpdateMassPriceLinkProcessRequest.yaml"
    GetUsersPagedResponse:
      $ref: "./responses/GetUsersPagedResponse.yaml"
    GetPriceLinkResponse:
      $ref: "./responses/GetPriceLinkResponse.yaml"
    GetPriceElementsPagedResponse:
      $ref: "./responses/GetPriceElementsPagedResponse.yaml"
    GetMeteringPointsPagedResponse:
      $ref: "./responses/GetMeteringPointsPagedResponse.yaml"
    UpdatePriceGroupProcessRequest:
      $ref: "./schemas/requests/UpdatePriceGroupProcessRequest.yaml"
    GetPriceGroupProcessResponse:
      $ref: "./responses/GetPriceGroupProcessResponse.yaml"
    GetConditionalPriceLinkProcessResponse:
      $ref: "./responses/GetConditionalPriceLinkProcessResponse.yaml"
    UpdateConditionalPriceLinkProcessRequest:
      $ref: "./schemas/requests/UpdateConditionalPriceLinkProcessRequest.yaml"

  responses:
    "PriceGroupProcessFileResponse":
      $ref: "./responses/PriceGroupProcessFileResponse.yaml"
    "MassPriceLinkFileResponse":
      $ref: "./responses/MassPriceLinkFileResponse.yaml"
    ConditionalPriceLinkProcessFileResponse:
      $ref: "./responses/ConditionalPriceLinkProcessFileResponse.yaml"
    "400":
      $ref: "./responses/common/400.yaml"
    "401":
      $ref: "./responses/common/401.yaml"
    "403":
      $ref: "./responses/common/403.yaml"
    "404":
      $ref: "./responses/common/404.yaml"
    "409":
      $ref: "./responses/common/409.yaml"
    "429":
      $ref: "./responses/common/429.yaml"
    "499":
      $ref: "./responses/common/499.yaml"
    "500":
      $ref: "./responses/common/500.yaml"
    "502":
      $ref: "./responses/common/502.yaml"
    "503":
      $ref: "./responses/common/503.yaml"
    "504":
      $ref: "./responses/common/504.yaml"
  securitySchemes:
    Bearer:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
      type: http
      scheme: bearer
      bearerFormat: JWT
    OAuth2:
      description: |-
        This API uses OAuth 2.
        For more information, see https://developers.getbase.com/docs/rest/articles/oauth2/requests
      type: oauth2
      flows:
         authorizationCode:
           authorizationUrl: https://logicidentityprod.b2clogin.com/aa0d6f3b-8fb2-477d-ab88-bb2e664620e8/b2c_1a_signup_signin/oauth2/v2.0/authorize
           tokenUrl: https://logicidentityprod.b2clogin.com/aa0d6f3b-8fb2-477d-ab88-bb2e664620e8/b2c_1a_signup_signin/oauth2/v2.0/token
           scopes:
            https://logicidentityprod.onmicrosoft.com/0f25787d-014e-4482-903f-b3c331416587/KMDElements.Default: Default
