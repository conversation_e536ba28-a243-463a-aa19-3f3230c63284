MeterFrame:
  title: MeterFrame
  type: object
  additionalProperties: false
  description: Meter frame model.
  required:
    - id
    - changedByUserId
    - created
    - meterFrameNumber
    - supplyType
    - commonReading
    - collectiveReading
    - meterMissing
    - meterSealed
    - meterWorkConsumerBilled
    - connectionPointId
    - supplyStatus
    - tagAssignments
    - placementCode
    - connectionStatus
  properties:
    id:
      description: Meter Frame Entity Id.
      type: string
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
    changedByUserId:
      type: string
      description: Last change user id.
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    created:
      description: |-
        DK: Oprettet.
        Creation time stamp of the Meter Frame.
      type: string
      format: date-time
      example: "2022-09-07T09:50:30.870Z"
    meterFrameNumber:
      description: |-
        DK: Målerrammenummer.
        Number on the Meter Frame.
      type: string
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: "1234"
    supplyType:
      description: List of possible supply types "Electricity", "Heating", "Water"
      type: string
      pattern: "^(Electricity|Heating|Water)$"
      example: "Heating"
    commonReading:
      description: 'Common Reading flag.'
      type: boolean
      example: true
    collectiveReading:
      description: 'collective Reading flag.'
      type: boolean
      example: true
    supplyDisconnectedComment:
      description: 'collective Reading flag.'
      type: [ 'string', 'null' ]
      patter: "^.*$"
      minLength: 0
      maxLength: 100
    meterMissing:
      description: 'DK: MålerVæk.'
      type: boolean
      example: true
    noMeter:
      description: 'No meter.'
      type: boolean
      example: true
    placementSpecification:
      description: 'DK: Placeringsbeskrivelse.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: "placement specification"
    meterSealDate:
      description: |-
        DK: Målerplomberingsdato.
        Indicates the date when the meter was sealed.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2022-09-07T09:50:30.870Z"
    meterSealed:
      description: |-
        DK: MålerPlomberet.
        Indicates whether the meter is sealed.
      type: boolean
      example: true
    meterWorkConsumerBilled:
      description: |-
        DK: MålerYdelseFaktureresEjer.
        Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
      type: boolean
      example: true
    connectionStatus:
      description: 'DK: Tilslutningsstatus.'
      type: string
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    decommissioned:
      description: Decommissioned date.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2022-09-07T09:50:30.870Z"
    statusChanged:
      description: Latest status change date.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2022-09-07T09:50:30.870Z"
    electricityAttributes:
      description: ElectricityAttributes.
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/MeterFrameElectricityAttributesModel.schema.yaml#/MeterFrameElectricityAttributesModel'
    heatingAttributes:
      description: HeatingAttributes.
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/MeterFrameHeatingAttributesModel.schema.yaml#/MeterFrameHeatingAttributesModel'
    waterAttributes:
      description: MeterFrameWaterAttributes.
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/MeterFrameWaterAttributesModel.schema.yaml#/MeterFrameWaterAttributesModel'
    geographicalLocation:
      description: Set of geographical location properties - describing Meter Frame geo location.
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/MeterFrameGeographicalLocationModel.schema.yaml#/MeterFrameGeographicalLocationModel'
    gisPropertiesElectricity:
      description: MeterFrameGisPropertiesElectricity
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/MeterFrameGisPropertiesElectricityModel.schema.yaml#/MeterFrameGisPropertiesElectricityModel'
    gisPropertiesHeating:
      description: MeterFrameGisPropertiesHeating
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/MeterFrameGisPropertiesHeatingModel.schema.yaml#/MeterFrameGisPropertiesHeatingModel'
    gisPropertiesWater:
      description: MeterFrameGisPropertiesWater
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/MeterFrameGisPropertiesWaterModel.schema.yaml#/MeterFrameGisPropertiesWaterModel'
    connectionPointId:
      description: ConnectionPointId.
      type: string
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    addressId:
      description: An UUID reference to a master data address.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    addressName:
      description: AddressName from CAR.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: "address name"
    addressLine1:
      description: AddressLine1 from CAR.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 100
      example: "address line 1"
    addressLine2:
      description: AddressLine2 from CAR.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 100
      example: "address line 2"
    addressStatus:
      description: List of possible statuses that a master data address can have "Active", "Inactive". Statuses from Central Address Registry.
      type: [ 'string', 'null' ]
      pattern: "^(Active|Inactive)$"
      example: Active
    addressType:
      description: List of possible address types that a MasterDataAddressDetails object can hold. Eg. "Address", "AccessAddress".
      type: [ 'string', 'null' ]
      pattern: "^(Primary|Temporary|AccessAddress)$"
      example: Primary
    darStatus:
      description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "ApproximateDAR=Not DAR but expected DAR", "No=Permanently not DAR validated".
      type: [ 'string', 'null' ]
      pattern: "^(Yes|No|ApproximateDAR)$"
      example: Yes
    lifeCycleStatus:
      description: List of possible life cycle states that the MDR system can put the address into.
      type: [ 'string', 'null' ]
      pattern: "^(ToBeDeleted|Valid|UnderInvestigation)$"
      example: UnderInvestigation
    supplyStatus:
      description: List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
      type: string
      pattern: "^(Connected|Disconnected)$"
      example: Connected
    tagAssignments:
      description: Tags.
      type: array
      maxItems: 1000000
      items:
        $ref: "./sub-schemas/TagAssignmentModel.schema.yaml#/TagAssignmentModel"
    meterReadingType:
      description: A list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".
      type: string
      format: uuid
      example: 'd988b796-225b-4eef-8ec4-191ee69a3a80'
    supplyDisconnectType:
      description: |-
        List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation".
        Indicates whether the supply is disconnected before the meter, disconnected in the cable cabinet, disconnected at the meter outlet, or in the mains station.
      type: [ 'string', 'null' ]
      pattern: "^(MeterNoVoltage|DisconnectedWithBreakerInMeter|DisconnectedBeforeMeter|DisconnectedInKabinet|DisconnectedAfterMeter|DisconnectedStation|Connected|Unknown)$"
      example: Connected
    placementCode:
      description: 'Placement code CodeList value id.'
      type: string
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    mainBranchLineElectricity:
      type: [ 'object', 'null' ]
      description: Main branch line electricity.
      oneOf:
        - $ref: './sub-schemas/MeterFrameMainBranchLineElectricityModel.schema.yaml#/MeterFrameMainBranchLineElectricityModel'
    reserveBranchLineElectricity:
      type: [ 'object', 'null' ]
      description: Reserve branch line electricity.
      oneOf:
        - $ref: './sub-schemas/MeterFrameReserveBranchLineElectricityModel.schema.yaml#/MeterFrameReserveBranchLineElectricityModel'
    mainBranchLineWater:
      type: [ 'object', 'null' ]
      description: Main branch line water.
      oneOf:
        - $ref: './sub-schemas/MeterFrameMainBranchLineWaterModel.schema.yaml#/MeterFrameMainBranchLineWaterModel'
    reserveBranchLineWater:
      type: [ 'object', 'null' ]
      description: Reserve branch line water.
      oneOf:
        - $ref: './sub-schemas/MeterFrameReserveBranchLineWaterModel.schema.yaml#/MeterFrameReserveBranchLineWaterModel'
