type: object
description: |
  Extended metering point version.
additionalProperties: false
properties:
  id:
    nullable: true
    description: MP identifier'.
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  parentMeteringPointId:
    nullable: true
    description: Parent MP identifier'.
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  virtualId:
    nullable: true
    description: Internal MP MDP identifier'.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  meteringPointVersionId:
    description: Id of Metering Point Version. Metering Point Version Id contains of {GSRN}_{DATE}. so <MeteringPointId>_<Occurrence>
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    example: 317575025980776095_2022-01-02
  typeOfMeteringPoint:
    description: Mapped from DH 'typeOfMeteringPoint'.
    allOf:
      - $ref: '../MeteringPointModel/DataTypes/TypeOfMeteringPoint.yaml'
  addressId:
    description: CAR Address reference.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  addressWashInstruction:
    nullable: true
    description: Mapped from DH 'LocationAddress.washable' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  assetType:
    description: Mapped from DH 'MeteringPoint' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
  connectionStatus:
    nullable: true
    description: Mapped from DH 'connectionStatus' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  connectionType:
    nullable: true
    description: Mapped from DH 'connectionType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  disconnectionType:
    nullable: true
    description: Mapped from DH 'disconnectionType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  fromGrid:
    nullable: true
    description: Mapped from DH 'fromNet' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/OneWordString.yaml'
  locationDescription:
    nullable: true
    description: Mapped from DH 'locationDescription' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/LongString.yaml'
  maximumCurrent:
    nullable: true
    description: Mapped from DH 'maximumCurrent' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/PositiveDecimalNullable.yaml'
  maximumPower:
    nullable: true
    description: Mapped from DH 'maximumPower' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/PositiveDecimalNullable.yaml'
  meteringGridAreaUsed:
    nullable: true
    description: Mapped from DH 'meteringGridAreaId' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/OneWordString.yaml'
  meterReadingOccurrence:
    nullable: true
    description: Mapped from DH 'meterReadingOccurrence' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  netSettlementGroup:
    nullable: true
    description: Mapped from DH 'netSettlementGroup' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  powerSupplierGsrn:
    nullable: true
    description: Mapped from DH 'powerSupplierGsrn' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/OneWordString.yaml'
  productId:
    nullable: true
    description: Mapped from DH 'productId' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  productionCapacityInKiloWatts:
    description: Mapped from DH 'capacityInKiloWatts' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/PositiveDecimalNullable.yaml'
  productionObligation:
    nullable: true
    description: Mapped from DH 'productionObligation' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
  remoteReadable:
    nullable: true
    description: Mapped from DH 'remoteReadable' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  scheduledMeterReadingDates:
    nullable: false
    description: Mapped from DH 'scheduledMeterReadingDate' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    type: array
    minItems: 0
    maxItems: 10000
    items:
      $ref: '../DataTypes/ShortString.yaml'
  settlementMethod:
    nullable: true
    description: Mapped from DH 'settlementMethod' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  subTypeOfMeteringPoint:
    nullable: true
    description: Mapped from DH 'subTypeOfMeteringPoint' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  supplierStatus:
    nullable: true
    description: Mapped from DH 'supplierStatus' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
  supplyStart:
    nullable: true
    description: Mapped from DH 'supplyStart' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  toGrid:
    nullable: true
    description: Mapped from DH 'toNet' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/OneWordString.yaml'
  unitType:
    nullable: true
    description: Mapped from DH 'unitType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  occurrence:
    nullable: false
    description: Mapped from DH 'occurrence' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  formulaVersion:
    description: |
      The formula that describes how data for the measuring point's time series is calculated using data
      from the individual formula parameters. The formula is sent to MDM, and all the parameters' data
      must be found in MDM in order for MDM to calculate the result profile.
    allOf:
      - $ref: './FormulaVersionNullable.yaml'
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
  needAttention:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity need attention.
