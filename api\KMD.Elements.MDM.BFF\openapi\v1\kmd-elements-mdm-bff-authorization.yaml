openapi: 3.0.3
info:
  title: KMD.Elements.Authorization.BFF
  x-maintainers: Team-UI-2
  description: |-
    # KMD Elements Authorization BFF
    <br/>
    <br/>
    The **KMD Elements Authorization BFF** is part of the KMD Element product.
    <br/>
    This api acts as a proxy and depends on TEAM-SE-2 endpoints

    ## Capabilities
    The API allows to:
    - get user permissions
    - authorize and get user
    ---
  termsOfService: https://www.kmd.net/terms-of-use
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: License
    url: https://www.kmd.net/terms-of-use
  version: '1.1'
servers:
  - url: /bff
security:
  - Jwt: []
tags:
  - name: OAuth
    description: Authorization API.
  - name: Users
    description: Application Users API.
paths:
  /v1/me:
    get:
      tags:
        - Users
      summary: Get authenticated user details.
      operationId: getAuthenticatedUserDetails
      x-authorization: null
      description: Gets authenticated user details.
      parameters:
        - $ref: '#/components/parameters/EsCorrelationId'
        - $ref: '#/components/parameters/EsMessageId'
      responses:
        '200':
          description: Authenticated user datails.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticatedUserDetails'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '500':
          $ref: '#/components/responses/500'

  /v1/oauth/tenant-configuration/{tenantId}:
    get:
      tags:
        - OAuth
      summary: Get tenant auth configuration
      operationId: getTenantAuthConfiguration
      parameters:
        - $ref: '#/components/parameters/TenantId'
        - $ref: '#/components/parameters/EsMessageId'
        - $ref: '#/components/parameters/EsCorrelationId'
      description: Get tenant auth configuration.
      responses:
        '200':
          description: Tenant auth configuration.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantAuthConfiguration'
        '400':
          $ref: '#/components/responses/400'
        '500':
          $ref: '#/components/responses/500'
      security:
        - Jwt: []
components:
  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Guid:
      description: A globally unique identifier UUID.
      type: string
      format: uuid
      nullable: false
      example: 8902FA98-E40C-4434-ADFF-AA85A80F0FC0
    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          maxLength: 256
          pattern: ^.*$
          nullable: true
          example: https://errors.kmdelements.com/500
        title:
          description: A short, human-readable summary of the problem type.
          type: string
          maxLength: 256
          pattern: ^.*$
          nullable: true
          example: Error short description
        status:
          description: The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem.
          type: integer
          format: int32
          minimum: 400
          maximum: 599
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          maxLength: 2048
          pattern: ^.*$
          nullable: true
          example: Description what exactly happened
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          maxLength: 32779
          pattern: ^.*$
          nullable: true
          example: /resources/1
    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1000
              additionalProperties:
                type: array
                description: Array of validation error messages.
                maxItems: 1000
                items:
                  type: string
                  description: Error message item.
                  maxLength: 2048
                  pattern: ^.*$
              nullable: true
    MediumString:
      pattern: ^.*$
      type: string
      minLength: 1
      maxLength: 100
      description: Max. 100 characters long string with spaces.
      example: Max. 100 characters long string with spaces.
    NonNegativeInteger:
      type: integer
      format: int32
      minimum: 0
      maximum: 2147483647
      description: Integer type in <0-2147483647> range.
      example: 111
    Permission:
      type: object
      additionalProperties: false
      description: Permission.
      required:
        - id
        - name
      properties:
        id:
          description: Permission identifier.
          allOf:
            - $ref: '#/components/schemas/Guid'
        name:
          description: Permission name.
          allOf:
            - $ref: '#/components/schemas/MediumString'
    Boolean:
      type: boolean
      description: Boolean field.
      nullable: false
      example: true
    RoleItem:
      type: object
      description: Role list item.
      additionalProperties: false
      required:
        - id
        - name
        - type
        - permissions
        - hasBeenDeleted
      properties:
        id:
          description: Role id.
          allOf:
            - $ref: '#/components/schemas/Guid'
        name:
          description: Role name.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        type:
          type: string
          description: System or User defined.
          pattern: ^(SystemDefined|UserDefined)$
          maxLength: 50
        permissions:
          type: array
          description: List of permissions.
          nullable: false
          maxItems: 100
          items:
            $ref: '#/components/schemas/Permission'
        hasBeenDeleted:
          description: Information whether returned object is showing historical deleted entry.
          allOf:
            - $ref: '#/components/schemas/Boolean'
    UserStatus:
      type: string
      nullable: false
      description: Status of a user.
      minLength: 8
      maxLength: 11
      pattern: ^(NotApproved|Approved|Deactivated)$

    AuthenticatedUserDetails:
      type: object
      description: User details.
      additionalProperties: false
      required:
        - userId
        - userName
        - externalId
        - email
        - userStatus
        - authenticationSource
        - roles
        - accessRequests
      properties:
        userId:
          description: User identifier.
          allOf:
            - $ref: '#/components/schemas/Guid'
        userName:
          description: User name.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        externalId:
          description: User email.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        email:
          description: User email.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        userStatus:
          description: User status.
          allOf:
            - $ref: '#/components/schemas/UserStatus'
        authenticationSource:
          description: Authentication source.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        roles:
          type: array
          description: Current roles.
          nullable: false
          minItems: 0
          maxItems: 1000
          items:
            $ref: '#/components/schemas/RoleItem'
        accessRequests:
          type: array
          description: Access requests.
          items:
            $ref: '#/components/schemas/UserAccessRequest'
          minItems: 0
          maxItems: 1000
    UserAccessRequest:
      type: object
      description: User access request model
      additionalProperties: false
      required:
        - id
        - status
        - requestDate
      properties:
        id:
          description: Access Request identifier.
          allOf:
            - $ref: '#/components/schemas/Guid'
        status:
          description: Status of Access Request.
          allOf:
            - $ref: '#/components/schemas/AccessRequestStatus'
        requestDate:
          description: Request date.
          allOf:
            - $ref: '#/components/schemas/DateTime'
        approvalDate:
          description: Approval date.
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        approverName:
          description: User email.
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
    AccessRequestStatus:
      type: string
      pattern: ^(Created|Approved|Rejected|Assigned|Error)
      maxLength: 10
      description: Access request status
    MediumStringNullable:
      pattern: ^.*$
      type: string
      nullable: true
      minLength: 0
      maxLength: 100
      description: Max. 100 characters long string with spaces.
      example: Max. 100 characters long string with spaces.
    DateTimeNullable:
      type: string
      description: DateTime Nullable.
      format: date-time
      nullable: true
      example: '2023-07-01T22:00:00Z'
    DateTime:
      type: string
      description: DateTime.
      format: date-time
      nullable: false
      example: '2023-07-01T22:00:00Z'
    TenantAuthConfiguration:
      type: object
      description: Tenant auth configuration.
      additionalProperties: false
      required:
        - tenantId
        - clientId
        - domainHint
        - scope
      properties:
        tenantId:
          description: Tenant Id.
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
        clientId:
          description: Client id.
          allOf:
            - $ref: '#/components/schemas/Guid'
        domainHint:
          description: Domain hint (Identity provider).
          allOf:
            - $ref: '#/components/schemas/MediumString'
        scope:
          description: Scope.
          allOf:
            - $ref: '#/components/schemas/MediumString'
  parameters:
    EsCorrelationId:
      name: es-correlation-id
      description: |
        This is used to "link" messages together. This can be supplied on a request, so
        that the client can correlate a corresponding reply message.
        The server will place the incoming es-correlation-id value as the es-correlation-id
        on the outgoing reply. If not supplied on the request, the es-correlation-id of the
        reply should be set to the value of the es-message-id that was used on the request, if present.
        Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
      in: header
      schema:
        $ref: '#/components/schemas/Guid'
      required: true
      example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d
    EsMessageId:
      name: es-message-id
      description: Unique message ID. The same message id is used when resending the message.
      in: header
      schema:
        $ref: '#/components/schemas/Guid'
      example: 3773907e-45a2-11ee-be56-0242ac120003
      required: true
    TenantId:
      name: tenantId
      in: path
      description: Tenant identifier.
      required: true
      example: da85baa6-a66a-11ea-bb37-0242ac130002
      schema:
        $ref: '#/components/schemas/NonNegativeInteger'
  responses:
    '400':
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ValidationProblemDetails'
          examples:
            BadRequestExample:
              value:
                type: https://errors.kmdelements.com/400
                title: Bad Request
                status: 400
                detail: Invalid request
                instance: /resources/1
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    '401':
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            UnauthorizedExample:
              value:
                type: https://errors.kmdelements.com/401
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources/1
    '403':
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ForbiddenExample:
              value:
                type: https://errors.kmdelements.com/403
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources/1
    '404':
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            NotFoundExample:
              value:
                type: https://errors.kmdelements.com/404
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources/1
    '500':
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            InternalServerErrorExample:
              value:
                type: https://errors.kmdelements.com/500
                title: Internal Server Error
                status: 500
                detail: 'body.0.age: Value `Not Int` does not match format `int32`'
                instance: /resources/1
