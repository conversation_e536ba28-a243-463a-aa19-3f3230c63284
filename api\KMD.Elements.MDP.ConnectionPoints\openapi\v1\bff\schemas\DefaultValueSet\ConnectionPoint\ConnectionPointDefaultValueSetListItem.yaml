type: object
description: Base details of a connection point default value set.
additionalProperties: false
required:
  - id
  - name
  - supplyTypes
properties:
  id:
    allOf:
      - $ref: '../../DataTypes/Guid.yaml'
    description: Default Value Set ID.
  name:
    allOf:
      - $ref: '../../DataTypes/ShortString.yaml'
    description: Default Value Set name.
  supplyTypes:
    $ref: '../../SupplyTypes.yaml'
    description: Supply types of the default value set related to connection point.
  rowVersion:
    description: Row version.
    allOf:
      - $ref: '../../DataTypes/RowVersion.yaml'
    nullable: true
