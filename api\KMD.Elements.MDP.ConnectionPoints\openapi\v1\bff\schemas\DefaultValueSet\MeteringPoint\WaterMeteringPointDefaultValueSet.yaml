type: object
description: Model containing water attributes of Metering Point Default Value Set.
properties:
    supplInfoBilling:
      description: Suppl. billing info for the Metering Point.
      type: [ 'string', 'null' ]
      pattern: '^.*$'
      minLength: 1
      maxLength: 100
      example: A01
    disconnectKind:
      description: |-
        Must be specified for consumption and production measurement points. Indicates how the measuring point can be interrupted by the grid company.
        | CodeListName                                 | DisplayName        | Code | Name               | Translation           |
        |----------------------------------------------|--------------------|------|--------------------|-----------------------|
        | System-WaterMeteringPoint-DisconnectKind     | D01                | D01  | Fjern afbrydelig   | Remote disconnection  |
        | System-WaterMeteringPoint-DisconnectKind     | D02                | D02  | Manual afbrydelig  | Manual disconnection  |
      type: [ 'string', 'null' ]
      minLength: 3
      maxLength: 3
      pattern: "^(D01|D02)$"
      example: D01
    unitType:
      description: |-
        Type of Heat Metering Point Unit.

        | CodeListName                        | DisplayName | Code    | Name           | Translation    |
        |-------------------------------------|-------------|---------|----------------|----------------|
        | System-HeatMeteringPoint-UnitType   | MWH         | MWH     | Megawatt timer | Megawatt hours |
        | System-HeatMeteringPoint-UnitType   | M3          | M3      | Kubikmeter     | Cubic Meter    |
        | System-HeatMeteringPoint-UnitType   | Celsius     | Celsius | Celsius        | Celsius        |
      type: string
      nullable: true
      minLength: 2
      maxLength: 7
      pattern: "^(MWH|M3|Celsius)$"
      example: "MWH"
