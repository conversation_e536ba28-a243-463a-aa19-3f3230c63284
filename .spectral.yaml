#
# KMD Elements OpenApi & AsyncApi Spectral Rulesets
#
# Refernces
# - spectral:oas:      https://github.com/stoplightio/spectral/blob/develop/docs/reference/openapi-rules.md
# - spectral:asyncapi: https://github.com/stoplightio/spectral/blob/develop/docs/reference/asyncapi-rules.md
# - formats:           https://github.com/stoplightio/spectral/blob/develop/docs/guides/4-custom-rulesets.md#formats
# - functions:         https://github.com/stoplightio/spectral/blob/develop/docs/reference/functions.md
#
# Samples:
# - https://github.com/Azure/azure-api-style-guide/blob/main/spectral.yaml
# - https://github.com/redhat-developer/app-services-api-guidelines/blob/main/spectral/ruleset.yaml
#
# Notes:
# - The Spectral VSCode extension will not display "hint" messages, so use "info" rather than "hint".
#

extends:
  - ./.spectral.oas.yaml
  - ./.spectral.oas.owasp.yaml
  - ./.spectral.aas.yaml

rules:

  # General rules...

  kmd-elements-info-x-maintainers-exists-and-valid:
    description: The x-maintainers property in the info section has an invalid value.
    message: |-
      The x-maintainers property in the info section doesn't exists or it has an invalid value.
      Allowed values:
        - Team-01
        - Team-02
        - Team-03
        - Team-04
        - Team-05
        - Team-06
        - Team-07
        - Team-08
        - Team-09
        - Team-11
        - Team-12
        - Team-14
        - Team-ENS
        - Integration-Team
        - QA-Team
        - ETL-Team
        - Architects-Team
        - Platform-Team
        - Shell-Team
        - Team-SE-1
        - Team-SE-2
        - Team-MC-1
        - Team-AU-1
        - Team-AU-2
        - Team-UI-1
        - Team-UI-2
        - Team-MO-1
        - Team-MO-2
        - Team-MD-1
        - Team-MD-2
        - Team-MD-3
        - Team-MD-4
        - Team-DP-1
        - Team-DP-2
      https://dev.azure.com/kmddk/COMBAS/_wiki/wikis/COMBAS.wiki/11933/Rules-for-OpenAPI?anchor=oapi-ca-002%3A-use-the-**x-maintainers**-attribute-to-specify-the-api-spec-maintainers
    severity: error
    recommended: true
    given: $.info
    then:
      - function: truthy
        field: x-maintainers
      - function: enumeration
        field: x-maintainers
        functionOptions:
          values:
            - Team-01
            - Team-02
            - Team-03
            - Team-04
            - Team-05
            - Team-06
            - Team-07
            - Team-08
            - Team-09
            - Team-11
            - Team-12
            - Team-14
            - Team-ENS
            - Integration-Team
            - QA-Team
            - ETL-Team
            - Architects-Team
            - Platform-Team
            - Shell-Team
            - Team-SE-1
            - Team-SE-2
            - Team-MC-1
            - Team-AU-1
            - Team-AU-2
            - Team-UI-1
            - Team-UI-2
            - Team-MO-1
            - Team-MO-2
            - Team-MD-1
            - Team-MD-2
            - Team-MD-3
            - Team-MD-4
            - Team-DP-1
            - Team-DP-2

  kmd-elements-avoid-x-custom-attributes:
    description: Avoid using unapproved custom attributes.
    message: |-
      {{error}}
      Only approved custom attributes are allowed.
      https://dev.azure.com/kmddk/COMBAS/_wiki/wikis/COMBAS.wiki/11933/Rules-for-OpenAPI?_a=edit&anchor=oapi-ca-001%3A-use-only-approved-custom-attributes
    severity: error
    recommended: true
    resolved: false
    given:
      - $..x-name
      - $..x-abstract
      - $..x-position
      - $..x-nullable
      - $..x-displayName
      - $..x-tagGroups
    then:
      function: undefined

overrides:
  - files:
    - "api/KMD.Elements.Authorization/openapi/bff/v1/*.yaml#/paths/~1v1~1oauth~1tenant-configuration~1%7BtenantId%7D/get"
    rules:
      kmd-elements-oas-define-x-authorization: "off"
      kmd-elements-oas-security-min-length: "off"
  - files:
    - "api/KMD.Elements.MDM.MasterData/openapi/andel/v1/*.yaml"
    rules:
      kmd-elements-oas-request-mime-types: "off"
  - files:
    - "api/KMD.Elements.Connectors.SendGrid/openapi/public/v1/*.yaml"
    rules:
      kmd-elements-oas-define-x-authorization: "off"
      kmd-elements-oas-operation-message-id-required-parameters: "off"
      kmd-elements-oas-operation-correlation-id-required-parameters: "off"
