type: object
description: Details of given message.
additionalProperties: false
required:
  - errorCode
  - errorMessage
properties:
  propertyNavigation:
    allOf:
      - $ref: ./DataTypes/DescriptionString.yaml
    nullable: true
    description: Property of error.
  errorCode:
    allOf:
      - $ref: ./DataTypes/DescriptionString.yaml
    description: Error code.
  errorMessageTemplate:
    allOf:
      - $ref: ./DataTypes/DescriptionString.yaml
    description: Template of default message of given error.
    nullable: true
  errorMessage:
    allOf:
      - $ref: ./DataTypes/DescriptionString.yaml
    description: Default message of given error.
    nullable: true
  parameters:
    type: object
    description: Parameters of error.
    nullable: true
    additionalProperties: false
