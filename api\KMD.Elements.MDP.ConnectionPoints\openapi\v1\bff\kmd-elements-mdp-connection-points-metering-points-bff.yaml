openapi: 3.0.3
info:
  title: KMD.Elements.MDP.ConnectionPoints.MeteringPoints.BFF
  description: |-
    ## Master Data Processes Connection Points Metering Points Processes API
    Stability level: PREVIEW<br/>

    ## Capabilities
    The API allows to:
    - Create a process for managing metering points.

  termsOfService: "https://www.kmd.net/terms-of-use"

  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>

  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"

  version: "1.22"
  x-maintainers: Team-MD-3

servers:
  - url: http://localhost:5730

security:
  - Bearer: []

tags:
  - name: ChangeFormulaProcessBff
    description: Methods specific for change formula process.
  - name: ChangeNetSettlementGroupAttributesProcessBff
    description: Methods specific for change net settlement group attributes process.
  - name: CreateMeteringPointProcessBff
    description: Methods specific for create metering point process.
  - name: MeteringPointIdsBff
    description: Methods for generating metering point ids reservation and release.
  - name: MeteringPointProcessBff
    description: Common methods using for managing metering point process.
  - name: UpdateMeteringPointProcessBff
    description: Methods specific for update metering point process.

paths:
  /bff/v1/metering-point-processes/change-formula/{processId}:
    put:
      tags:
        - ChangeFormulaProcessBff
      summary: Resend failed subprocesses.
      description: Updates payload of existing change formula metering point process and retries it.
      operationId: upsertChangeFormulaProcess
      x-authorization-1: MeteringPoints.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      requestBody:
        description: Payload for change formula metering point process with process information.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangeFormulaPayload"
        required: true
      responses:
        "202":
          description: Process resend successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /bff/v1/metering-points/{processId}/change-formula-prefill-updated:
    get:
      tags:
        - MeteringPointProcessBff
      summary: Gets metering point formula updated.
      description: Gets metering point formula updated.
      operationId: getUpdateMeteringPointChangeFormulaUpdated
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Metering point change formula updated returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChangeFormulaPrefillData"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/metering-point-processes/create-metering-point:
    post:
      tags:
        - CreateMeteringPointProcessBff
      summary: Create a new process
      description: Create a new process if there is no existing one for the given connection point
      operationId: createNewProcess
      x-authorization-1: ProcessCreateMeteringPoint.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      requestBody:
        description: Payload for string create metering point process with process information and metering point data.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MeteringPointPayload"
        required: true
      responses:
        "202":
          description: Process created successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/DataTypes/Guid.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /bff/v1/metering-point-processes/create-metering-point/{processId}:
    put:
      tags:
        - CreateMeteringPointProcessBff
      summary: Resend create electricity metering point with new payload
      description: Updates payload of existing create electricity metering point process and retries it.
      operationId: upsertProcess
      x-authorization-1: ProcessCreateMeteringPoint.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      requestBody:
        description: Payload for create metering point process with process information and metering point data.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MeteringPointPayload"
        required: true
      responses:
        "202":
          description: Process created successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /bff/v1/metering-point-processes/change-formula:
    post:
      tags:
        - ChangeFormulaProcessBff
      summary: Create a new process
      description: Create a new process
      operationId: createNewChangeFormulaProcess
      x-authorization-1: MeteringPoints.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      requestBody:
        description: Payload for starting change formula process with proper data.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangeFormulaPayload"
        required: true
      responses:
        "202":
          description: Process created successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/DataTypes/Guid.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /bff/v1/metering-point-processes/create-metering-point/{processId}/metering-point-payload:
    get:
      tags:
        - CreateMeteringPointProcessBff
      summary: Returns metering point payload for specific process id.
      description: Returns metering point payload for specific process id.
      operationId: getMeteringPointPayloadByProcessId
      x-authorization-1: ProcessCreateMeteringPoint.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Metering point payload returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeteringPointPayload"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/metering-point-processes/change-net-settlement-group-attributes-processes:
    post:
      tags:
        - ChangeNetSettlementGroupAttributesProcessBff
      summary: Starts new change net settlement group attributes process
      description: Starts new change net settlement group attributes process
      operationId: startChangeNetSettlementGroupAttributesProcessCommand
      x-authorization-1: ProcessChangeNetSettlementGroup.Write
      x-authorization-2: Electricity.All
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      requestBody:
        description: Payload for starting change net settlement group attributes with process information and metering point data.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangeNetSettlementGroupAttributesProcessCommandPayload"
        required: true
      responses:
        "202":
          description: Process created successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/DataTypes/Guid.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /bff/v1/metering-point-processes/update-metering-point:
    post:
      tags:
        - UpdateMeteringPointProcessBff
      summary: Starts new update metering point process
      description: Starts new update metering point process
      operationId: startUpdateMeteringPoint
      x-authorization-1: ProcessUpdateMeteringPoint.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      requestBody:
        description: Payload for starting update metering point process with process information and metering point data.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeteringPointPayload"
        required: true
      responses:
        "202":
          description: Process created successfully.
          content:
            application/json:
              schema:
                $ref: "./schemas/DataTypes/Guid.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /bff/v1/metering-point-processes/{processId}:
    get:
      tags:
        - MeteringPointProcessBff
      summary: Get process details
      description: Get process details
      operationId: getProcessDetails
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Process details found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessDetailsResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /bff/v1/metering-point-processes/{processId}/update-main-data:
    post:
      tags:
        - MeteringPointProcessBff
      summary: Update main data on process manually by user.
      description: Update main data on process manually by user.
      operationId: updateManuallyMainDataForProcess
      x-authorization-1: ProcessCreateMeteringPoint.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateManuallyMainDataProcessRequestBody"
        required: true
      responses:
        "204":
          description: Data changed successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/metering-point-processes/{processId}/restart:
    post:
      tags:
        - MeteringPointProcessBff
      summary: Restart process.
      description: Restart process.
      operationId: restartProcess
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "202":
          description: "Process restarted"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /bff/v1/metering-point-processes/{processId}/cancel:
    post:
      tags:
        - MeteringPointProcessBff
      summary: Cancel process
      description: Cancel process
      operationId: cancelProcess
      x-authorization-1: ProcessCreateMeteringPoint.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "204":
          description: "Process canceled"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
  /bff/v1/metering-point-processes/{processId}/manually-handle:
    post:
      tags:
        - MeteringPointProcessBff
      summary: Mark process as manually handled
      description: Mark process as manually handled
      operationId: markProcessAsManuallyHandled
      x-authorization-1: ProcessCreateMeteringPoint.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "204":
          description: "Process marked as manually handled"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /bff/v1/connection-points/{connectionPointId}/meter-frames:
    get:
      tags:
        - MeteringPointProcessBff
      summary: Returns list of meter frames.
      description: Returns list of meter frames.
      operationId: getMeterFramesList
      x-authorization-1: ProcessCreateMeteringPoint.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - name: supplyType
          in: query
          required: true
          description: Supply type of the metering point.
          schema:
            $ref: "./schemas/SupplyType.yaml"
          example: "Electricity"
        - $ref: "#/components/parameters/ConnectionPointId"
        - $ref: "#/components/parameters/MeteringPointConnectionStatus"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Meter frame list returned successfully.
          content:
            application/json:
              schema:
                description: Meter frame list.
                type: array
                maxItems: 100
                items:
                  $ref: "#/components/schemas/MeterFrameIdentifiers"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/meter-frames/{meterFrameId}/register-requirements:
    get:
      tags:
        - MeteringPointProcessBff
      summary: Returns list of Register requirements.
      description: Returns list of Register requirements.
      operationId: getRegisterRequirementsList
      x-authorization-1: ProcessCreateMeteringPoint.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/MeterFrameId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Register requirements list returned successfully.
          content:
            application/json:
              schema:
                description: Register requirements list.
                type: array
                maxItems: 100
                items:
                  $ref: "#/components/schemas/RegisterRequirementIdentifiers"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/meter-frames/{meterFrameId}/metering-point-prefill:
    get:
      tags:
        - MeteringPointProcessBff
      summary: Returns metering point payload for specific meter frame id.
      description: Returns metering point payload for specific process meter frame id.
      operationId: getMeteringPointPayloadByMeterFrameId
      x-authorization-1: ProcessCreateMeteringPoint.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/MeterFrameId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Metering point payload returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeteringPointPrefilledDataFromMeterFrame"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/connection-points/{connectionPointId}/metering-point-prefill:
    get:
      tags:
        - MeteringPointProcessBff
      summary: Returns metering point payload for specific connection point id.
      description: Returns metering point payload for specific process connection point id.
      operationId: getMeteringPointPayloadByConnectionPointId
      x-authorization-1: ConnectionPoints.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ConnectionPointId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Metering point payload returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeteringPointPrefilledDataFromConnectionPoint"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/metering-points/{connectionPointId}/net-settlement-group-attributes-prefill:
    get:
      tags:
        - MeteringPointProcessBff
      summary: Get net settlement group attributes details
      description: Get net settlement group attributes details
      operationId: getNetSettlementGroupAttributesDetails
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ConnectionPointId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Get net settlement group attributes details successfully fetched for prefill
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NetSettlementGroupPrefillData"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/metering-points/{supplyType}/{meteringPointId}:
    get:
      tags:
        - MeteringPointProcessBff
      summary: Get details about given metering point
      description: Get details about given metering point
      operationId: getMeteringPointDetails
      x-authorization: ProcessUpdateMeteringPoint.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - name: supplyType
          in: path
          required: true
          description: Supply type of the metering point.
          schema:
            $ref: "./schemas/SupplyType.yaml"
          example: 1
        - $ref: "#/components/parameters/MeteringPointId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Metering point details returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateMeteringPointPrefillData"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/metering-points/{meteringPointId}/change-formula-prefill:
    get:
      tags:
        - MeteringPointProcessBff
      summary: Gets metering point formula.
      description: Gets metering point formula.
      operationId: getUpdateMeteringPointChangeFormula
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/MeteringPointId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Metering point change formula returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChangeFormulaPrefillData"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/metering-point-ids/configurations:
    post:
      tags:
        - MeteringPointIdsBff
      summary: Adds new configuration for metering point id generator.
      description: Adds new configuration for metering point id generator.
      operationId: addMeteringPointIdConfiguration
      x-authorization: Migrations.Write, ProcessCreateMeteringPoint.Write
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      requestBody:
        description: Configuration.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MeteringPointIdGenerationConfiguration"
        required: true
      responses:
        "204":
          description: Configuration has been created successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/v1/metering-point-ids/{meteringPointId}/release:
    post:
      tags:
        - MeteringPointIdsBff
      summary: Released reserved metering point id.
      description: Released reserved metering point id.
      operationId: releaseMeteringPointId
      x-authorization: ProcessCreateMeteringPoint.Write
      parameters:
        - $ref: "#/components/parameters/MeteringPointId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "204":
          description: Metering point id has been requested to release.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/v1/metering-point-ids/reserve:
    post:
      tags:
        - MeteringPointIdsBff
      summary: Reserve metering point id.
      description: Reserve metering point id.
      operationId: reserveMeteringPointId
      x-authorization: ProcessCreateMeteringPoint.Write
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      requestBody:
        description: Configuration.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ReserveMeteringPointIdPayload"
        required: true
      responses:
        "201":
          description: Metering point id has been reserved.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeteringPointId"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
components:
  schemas:
    ProcessDetailsResult:
      $ref: "./schemas/MeteringPointProcess/MeteringPointDetails/MeteringPointProcessDetails.yaml"
    UpdateManuallyMainDataProcessRequestBody:
      $ref: "./schemas/MeteringPointProcess/ProcessModel/UpdateManuallyMainDataProcess.yaml"
    ChangeFormulaPayload:
      $ref: "./schemas/MeteringPointProcess/ChangeFormula/ChangeFormulaPayload.yaml"
    MeteringPointPayload:
      $ref: "./schemas/MeteringPointProcess/CreateMeteringPoint/MeteringPoint.yaml"
    ReserveMeteringPointIdPayload:
      $ref: "./schemas/MeteringPointProcess/MeteringPointIdGeneration/ReserveMeteringPointIdPayload.yaml"
    MeteringPointPrefilledDataFromConnectionPoint:
      $ref: "./schemas/MeteringPointProcess/CreateMeteringPoint/MeteringPointPrefilledDataFromConnectionPoint.yaml"
    MeteringPointPrefilledDataFromMeterFrame:
      $ref: "./schemas/MeteringPointProcess/CreateMeteringPoint/MeteringPointPrefilledDataFromMeterFrame.yaml"
    MeteringPointId:
      $ref: "./schemas/MeteringPointModel/DataTypes/MeteringPointId.yaml"
    MeteringPointIdGenerationConfiguration:
      $ref: "./schemas/MeteringPointProcess/MeteringPointIdGeneration/MeteringPointIdGenerationConfiguration.yaml"
    MeterFrameIdentifiers:
      $ref: "./schemas/MeteringPointProcess/MeterFrameIdentifiers.yaml"
    RegisterRequirementIdentifiers:
      $ref: "./schemas/MeteringPointProcess/RegisterRequirementIdentifiers.yaml"
    UpdateMeteringPointPayload:
      $ref: "./schemas/MeteringPointProcess/UpdateMeteringPoint/UpdateMeteringPointPayload.yaml"
    UpdateMeteringPointPrefillData:
      $ref: "./schemas/MeteringPointProcess/UpdateMeteringPoint/UpdateMeteringPointPrefillData.yaml"
    ChangeFormulaPrefillData:
      $ref: "./schemas/MeteringPointProcess/ChangeFormula/ChangeFormulaPrefillData.yaml"
    ChangeNetSettlementGroupAttributesProcessCommandPayload:
      $ref: "./schemas/MeteringPointProcess/ChangeNetSettlementGroupAttributes/ChangeNetSettlementGroupAttributesProcessCommandPayload.yaml"
    NetSettlementGroupPrefillData:
      $ref: "./schemas/MeteringPointProcess/ChangeNetSettlementGroupAttributes/NetSettlementGroupPrefillData.yaml"

  parameters:
    ConnectionPointId:
      $ref: "./parameters/ConnectionPointId.yaml"
    MeterFrameId:
      $ref: "./parameters/MeterFrameId.yaml"
    EsMessageIdInHeader:
      $ref: "./parameters/EsMessageIdInHeader.yaml"
    EsCorrelationIdInHeaderNullable:
      $ref: "./parameters/EsCorrelationIdInHeaderNullable.yaml"
    ProcessId:
      $ref: "./parameters/ProcessId.yaml"
    MeteringPointId:
      $ref: "./parameters/MeteringPointId.yaml"
    MeteringPointConnectionStatus:
      $ref: "./parameters/MeteringPointConnectionStatus.yaml"

  responses:
    "400":
      $ref: "./responses/400.yaml"
    "401":
      $ref: "./responses/401.yaml"
    "403":
      $ref: "./responses/403.yaml"
    "409":
      $ref: "./responses/409.yaml"
    "500":
      $ref: "./responses/500.yaml"
    "503":
      $ref: "./responses/503.yaml"
    "504":
      $ref: "./responses/504.yaml"

  securitySchemes:
    Bearer:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
      type: http
      scheme: bearer
      bearerFormat: JWT
