type: object
description: Metering point basic data.
additionalProperties: false
required: 
  - meteringPointId
  - meteringPointType
properties:
  meteringPointId:
    description: Id of Metering Point.
    nullable: false
    allOf:
      - $ref: './DataTypes/MeteringPointId.yaml'
  meteringPointType:
    description: Type of metering point
    nullable: false
    allOf:
      - $ref: './DataTypes/TypeOfMeteringPoint.yaml'