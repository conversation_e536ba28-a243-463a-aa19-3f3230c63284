type: object
description: Meter object of Metering Point version.
additionalProperties: false
properties:
  conversionFactor:
    description: Mapped from DH 'conversionFactor' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    nullable: true
    type: number
    format: decimal
  meterNumber:
    description: Mapped from DH 'meterNumber' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    nullable: true
    allOf:
      - $ref: '../DataTypes/OneWordString.yaml'
  meterReadingType:
    description: Mapped from DH 'meterReadingType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    nullable: true
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  numberOfDigits:
    description: Mapped from DH 'numberOfDigits' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    nullable: true
    allOf:
      - $ref: '../DataTypes/OneWordString.yaml'
  unitType:
    description: Mapped from DH 'unitType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    nullable: true
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'