type: object
description: Model containing heating attributes of Metering Point Default Value Set.
properties:
    supplInfoBilling:
      description: Suppl. billing info for the Metering Point.
      type: [ 'string', 'null' ]
      pattern: '^.*$'
      minLength: 1
      maxLength: 100
      example: A01
    disconnectKind:
      description: |-
        Type of Heat Metering Point Disconnect Kind.
        | CodeListName                            | DisplayName            | Code | Name              | Translation         |
        |-----------------------------------------|------------------------|------|-------------------|---------------------|
        | System-HeatMeteringPoint-DisconnectKind | D01 – fjernafbrydelig  | D01  | Fjern afbrydelig  | Remote disconnection|
        | System-HeatMeteringPoint-DisconnectKind | D02 – Manual afbrydelig| D02  | Manual afbrydelig | Manual disconnection|
      type: [ 'string', 'null' ]
      minLength: 3
      maxLength: 3
      pattern: "^(D01|D02)$"
      example: D01
    unitType:
      description: |-
        Type of Heat Metering Point Unit.
        | CodeListName                        | DisplayName | Code    | Name           | Translation    |
        |-------------------------------------|-------------|---------|----------------|----------------|
        | System-HeatMeteringPoint-UnitType   | MWH         | MWH     | Megawatt timer | Megawatt hours |
        | System-HeatMeteringPoint-UnitType   | M3          | M3      | Kubikmeter     | Cubic Meter    |
        | System-HeatMeteringPoint-UnitType   | Celsius     | Celsius | Celsius        | Celsius        |
      type: string
      nullable: true
      minLength: 2
      maxLength: 7
      pattern: "^(MWH|M3|Celsius)$"
      example: "MWH"
