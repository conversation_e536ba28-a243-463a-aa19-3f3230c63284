type: object
description: Metering point payload for any supply type
additionalProperties: false
properties:
  meteringPointId:
    nullable: false
    description: MP identifier.
    allOf:
      - $ref: '../../MeteringPointModel/DataTypes/MeteringPointId.yaml'
  supplyType:
    nullable: false
    description: MP supply type.
    allOf:
      - $ref: '../../SupplyType.yaml'
  parentMeteringPointId:
    nullable: true
    description: Parent MP identifier'.
    allOf:
      - $ref: '../../MeteringPointModel/DataTypes/MeteringPointId.yaml'
  typeOfMeteringPoint:
    nullable: false
    description: Type of metering point.
    allOf:
      - $ref: '../../MeteringPointModel/DataTypes/TypeOfMeteringPoint.yaml'
  addressId:
    description: CAR Address reference.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  disconnectionType:
    description: Mapped from DH 'disconnectionType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: './DisconnectionTypeNullable.yaml'
  connectionPointId:
    nullable: false
    description: Connection point, which new metering point will belong to.
    allOf:
      - $ref: '../../DataTypes/Guid.yaml'
  connectionPointNumber:
    nullable: false
    description: Connection point, which new metering point will belong to.
    allOf:
      - $ref: '../../DataTypes/MediumString.yaml'
  locationDescription:
    nullable: true
    description: Mapped from DH 'locationDescription' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/LongString.yaml'
  validFrom:
    nullable: false
    description: Date from which this metering point applies .
    allOf:
      - $ref: '../../DataTypes/DateTime.yaml'
  subTypeOfMeteringPoint:
    description: Subtype of metering point (Physical or Virtual).
    allOf:
      - $ref: './SubTypeOfMeteringPoint.yaml'
  gridAreaId:
    nullable: false
    description: Grid area identifier that metering point belongs to.
    minLength: 3
    maxLength: 3
    allOf:
      - $ref: '../../DataTypes/OneWordString.yaml'
  meterReadingOccurrence:
    description: Duration in ISO8601
    allOf:
      - $ref: './MeterReadingOccurrenceNullable.yaml'
  unitType:
    description: Unit type of a metering point
    allOf:
      - $ref: './UnitTypeNullable.yaml'
  formula:
    nullable: false
    description: Formula for metering point.
    oneOf:
      - $ref: '../Formula.yaml'
  meter:
    type: object
    additionalProperties: false
    nullable: true
    description: DataHub Metering Point Version address.
    oneOf:
      - $ref: '../../MeteringPointModel/DataHubMeter.yaml'
  electricityAttributes:
    nullable: true
    description: If supply type is electricity these are specific attributes for this supply type.
    allOf:
      - $ref: './ElectricityAttributes.yaml'
  waterAttributes:
    nullable: true
    description: If supply type is water these are specific attributes for this supply type.
    allOf:
      - $ref: './WaterAttributes.yaml'
  heatingAttributes:
    nullable: true
    description: If supply type is heating these are specific attributes for this supply type.
    allOf:
      - $ref: './HeatingAttributes.yaml'
