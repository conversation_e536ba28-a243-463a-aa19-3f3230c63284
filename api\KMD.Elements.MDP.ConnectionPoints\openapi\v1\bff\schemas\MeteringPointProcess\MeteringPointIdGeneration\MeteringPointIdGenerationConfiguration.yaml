title: MeteringPointIdGenerationConfiguration
description: |
  Details required to create new configuration for generating metering point ids
properties:
  supplyType:
    $ref: '../../SupplyType.yaml'
  minimumSequentialNumber:
    description: Minimum sequential number for generating metering point id.
    type: integer
    format: int32
    minimum: 0
    maximum: 9999999
  maximumSequentialNumber:
    description: Maximum sequential number for generating metering point id.
    type: integer
    format: int32
    minimum: 0
    maximum: 9999999
additionalProperties: false
