asyncapi: 2.6.0
id: https://dev.kmdelements.com/api/mdp-connection-points-api/
info:
  title: MDP.ConnectionPoints.Modify.API
  version: 1.1.0
  contact:
    name: Team-MD-3
    url: https://dev.azure.com/kmddk/COMBAS/_wiki/wikis/COMBAS.wiki/6375/Development-Team-MD-3
    email: <EMAIL>
  license:
    name: License
    url: 'https://www.kmd.net/terms-of-use'
  description: |
    Master Data Processes Connection Points API allows you to start processes that modify connection point data.
  x-maintainers: Team-MD-3

tags:
  - name: MasterDataProcessesConnectionPoints
    description: "API that is responsible for starting master data connection point modify processes initiated from installation forms"
  - name: Team-MD-3
    description: maintained by

servers:
  local:
    url: localhost:5001
    description: Local server
    protocol: kafka
    protocolVersion: 2.6.0

  development:
    url: 60000-kafkaBrokers
    description: Strimzi server which is stored under 60000-kafkaBrokers secret in KeyVault
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.command.installation-forms-mdp.modify-connection-point-process.v1:
    description: The topic on which master data connection point process modify commands from installation forms are sent.
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    subscribe:
      description: Consume modify connection point process command.
      operationId: onModifyProcess
      traits:
        - $ref: '#/components/operationTraits/kafka'
      message:
        $ref: '#/components/messages/ModifyConnectionPointProcessCommand'
    publish:
      description: Push modify connection point process command to the topic
      operationId: emitModifyConnectionPointProcessCommand
      message:
        $ref: '#/components/messages/ModifyConnectionPointProcessCommand'

components:
  messages:
    ModifyConnectionPointProcessCommand:
      name: ModifyConnectionPointProcessCommand
      title: Modify connection point process command.
      contentType: application/json
      headers:
        $ref: "#/components/schemas/MessageHeaders"
      payload:
        $ref: "#/components/schemas/ModifyConnectionPointProcessCommand"

  schemas:
    MessageHeaders:
      $ref: "./schemas/MessageHeaders.yaml"
    ModifyConnectionPointProcessCommand:
      $ref: "./schemas/InstallationFormsProcesses/ModifyConnectionPointProcessCommand.yaml"

  parameters:
    TenantId:
      $ref: "./parameters/TenantId.yaml"

  operationTraits:
    kafka:
      bindings:
        kafka:
          groupId:
            type: string
            enum: ['mdp-metering-points-group']
          key:
            type: string
            description: Message key is a process Id.
          bindingVersion: 1.0.0
