title: ParentProcess
type: object
additionalProperties: false
required:
  - source
properties:
  source:
    $ref: "#/ParentProcessSource"
  type:
    $ref: "#/ParentProcessType"
  id:
    $ref: '../DataTypes/MediumStringNullable.yaml'

ParentProcessSource:
  pattern: "^(Manual|InstallationForms)$"
  type: [ 'string', 'null' ]
  minLength: 1
  maxLength: 20
  description: |-
    | Code | Description                            |
    | ---- | -------------------------------------- |
    | InstallationForms | Process started by Installation Forms |
    | Manual | Process started manually in MDP.ConnectionPoint|
  example: InstallationForms

ParentProcessType:
  pattern: "^.*$"
  type: [ 'string', 'null' ]
  minLength: 1
  maxLength: 200
  description: |
    Action key from PC
  example: EnergyProduction
