type: object
description: Model containing the base attributes of Connection Point Default Value Set.
required:
  - name
properties:
  name:
    description: Name of Connection Point Default Value Set.
    allOf:
      - $ref: '../../DataTypes/ShortString.yaml'
  tagAssignments:
    type: array
    description: Tags.
    maxItems: 100
    items:
      $ref: '../../TagAssignmentModel.yaml'
  description:
    allOf:
      - $ref: '../../DataTypes/LongStringNullable.yaml'
    description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.