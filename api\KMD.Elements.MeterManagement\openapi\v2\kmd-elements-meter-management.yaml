openapi: 3.0.3
info:
  title: KMD.Elements.MeterManagement
  x-maintainers: Team-MO-2
  description: Meter management api service.
  termsOfService: "https://www.kmd.net/terms-of-use"
  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>
  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"
  version: "2.101-preview"
servers:
  - url: https://localhost:5001
security:
  - Jwt: []
tags:
  - name: ElectricityMeters
    description: |-
      API supporting electricity meters management functionality.
  - name: WaterMeters
    description: |-
      API supporting water meters management functionality.
  - name: HeatMeters
    description: |-
      API supporting heat meters management functionality.
  - name: MeterCommands
    description: |-
      API supporting common meter command functionality.
  - name: MeterConfigurations
    description: |-
      Api supporting meter configurations management functionality.
  - name: MeterInputs
    description: |-
      API supporting meter inputs management functionality.
  - name: MeterRegisters
    description: |-
      API supporting meter registers management functionality.
  - name: MeterManagementValueLists
    description: |-
      API supporting value lists functionality used in meter management process.
  - name: Meters
    description: |-
      API supporting common meter management functionality for different kinds of meters (electricity, heat, water).
  - name: MeterTemplates
    description: |-
      Api allows to create, get, update, delete and search templates for meter of chosen supply type.
  - name: MeterComponents
    description: |-
      Api allows to create, get, update, delete and search components for meter of chosen supply type
  - name: ElectricityMeterBatches
    description: |-
      API supporting electricity meter batches management functionality.
  - name: WaterMeterBatches
    description: |-
      API supporting water meter batches management functionality.
  - name: HeatMeterBatches
    description: |-
      API supporting heat meter batches management functionality.
  - name: MeterBatches
    description: |-
      API supporting common meter batches management functionality for different kinds of supply types electricity, heat, water.
  - name: MeterJobs
    description: |-
      API supporting meter jobs management functionality for different kinds of supply types electricity, heat, water.

paths:
  /v2/value-lists:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterManagementValueLists
      summary: Gets value lists used in meter management process.
      description: Endpoint supporting value lists functionality used in meter management process.
      operationId: getAllMeterManagementValueLists
      responses:
        "200":
          description: Value lists successfully returned.
          content:
            application/json:
              schema:
                description: Meter management value list items
                type: array
                maxItems: 1000
                items:
                  $ref: "#/components/schemas/MeterManagementValueList"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/dictionaries/supply-types:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets supply types values.
      description: Endpoint supporting supply types dictionary values functionality used in meter management process.
      operationId: getAllSupplyTypes
      responses:
        "200":
          $ref: '#/components/responses/DictonarySupplyTypes'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-commands:
    post:
      x-authorization: MeterCommand.Write
      tags:
        - MeterCommands
      summary: Creates a new meter command.
      description: Creates a new meter command.
      operationId: createMeterCommand
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterCommandData"
      responses:
        "201":
          $ref: "#/components/responses/CreateMeterCommandResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-commands/commands/get-tenant-configuration:
    post:
      x-authorization: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Gets configuration for meter commands for current tenant.
      description: Gets details of meter commands' configuration.
      operationId: getMeterCommandsConfiguration
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Configuration of meter commands.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterCommandsConfiguration"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-commands/{meterCommandId}:
    get:
      x-authorization: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Gets details of a meter command.
      description: Gets details of a meter command.
      operationId: getMeterCommandDetails
      parameters:
        - $ref: "#/components/parameters/MeterCommandId"
      responses:
        "200":
          description: Details of meter command successfully returned.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterCommandDetails"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    put:
      x-authorization: MeterCommand.Write
      tags:
        - MeterCommands
      summary: Update details of a meter command.
      description: Update details of a meter command.
      operationId: updateMeterCommandMasterData
      parameters:
        - $ref: "#/components/parameters/MeterCommandId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MeterCommandUpdate"
      responses:
        "204":
          description: Update of meter command finished successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    delete:
      x-authorization: MeterCommand.Write
      tags:
        - MeterCommands
      summary: Delete a meter command.
      description: Delete a meter command.
      operationId: deleteMeterCommandMasterData
      parameters:
        - $ref: "#/components/parameters/MeterCommandId"
      responses:
        "204":
          description: Delete of meter command finished successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-commands/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterCommands
      summary: Search meter commands.
      description: Search meter commands. Used during creating meter
      operationId: searchMeterCommands
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterCommands"
      responses:
        "200":
          description: Returns paged list of searched meter commands.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterCommandPagedCollection"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-configurations:
    post:
      x-authorization-1: MeterConfigurationTemplate.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Create meter configuration template.
      description: Create meter configuration template.
      operationId: createMeterConfiguration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterConfiguration"
      responses:
        "201":
          description: Successfully created.
          content:
            application/json:
              schema:
                description: Identifier of newly created meter configuration
                type: string
                format: uuid
              example: "FE1FEA7B-78F1-44DC-B88D-39BBB8E12B12"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-configurations/{meterConfigurationId}:
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Get meter configuration template or meter configuration instance master data.
      description: |-
        Get meter configuration template or meter configuration instance master data.
        In order to get meter configuration template master data, MeterConfigurationTemplate.Read permission is required.
        In order to get meter configuration instance master data, Meters.Read permission is required.
      operationId: getMeterConfigurationBaseData
      parameters:
        - $ref: "#/components/parameters/MeterConfigurationId"
      responses:
        "200":
          description: Returns meter configuration base data.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterConfigurationBaseData"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Update meter configuration via instance or template.
      description: |-
        Update meter configuration via instance or template.
        In order to update meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to update meter configuration instance, Meters.Write permission is required.
      operationId: updateMeterConfiguration
      parameters:
        - $ref: "#/components/parameters/MeterConfigurationId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterConfiguration"
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    delete:
      x-authorization-1: MeterConfigurationTemplate.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Delete meter configuration template.
      description: Delete meter configuration template.
      operationId: deleteMeterConfigurationTemplate
      parameters:
        - $ref: "#/components/parameters/MeterConfigurationId"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-configurations/{meterConfigurationId}/commands/retrieve-attached-commands:
    post:
      x-authorization: MeterCommand.Read
      tags:
        - MeterConfigurations
      summary: Retrieve list of meter commands attached to a specific meter configuration
      description: Retrieve list of meter commands attached to a specific meter configuration.
      operationId: retrieveMeterCommandsAttachedToMeterConfiguration
      parameters:
        - $ref: "#/components/parameters/MeterConfigurationId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RetrieveSortedPagedData"
      responses:
        "200":
          description: Returns paged list of searched meter commands.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterCommandPagedCollection"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-configurations/{meterConfigurationId}/meter-inputs:
    post:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Create meter input for meter configuration template or instance.
      description: |-
        Create meter input for meter configuration template or instance.
        In order to create meter input for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to create meter input for meter configuration instance, Meters.Write permission is required
      operationId: createMeterInput
      parameters:
        - $ref: "#/components/parameters/MeterConfigurationId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterInput"
      responses:
        "201":
          description: Successfully created.
          content:
            application/json:
              schema:
                description: Identifier of newly created meter input
                type: string
                format: uuid
              example: "FE1FEA7B-78F1-44DC-B88D-39BBB8E12B12"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterConfigurations
      summary: Returns list of all meter inputs.
      description: |-
        Returns list of all meter inputs related with meter configuration template or meter configuration instance.
        In order to return list all inputs for meter configuration template, MeterConfigurationTemplate.Read permission is required.
        In order to return list all meter inputs for meter configuration template instance, Meters.Read permission is required
      operationId: getMeterInputsForMeterConfiguration
      parameters:
        - $ref: "#/components/parameters/MeterConfigurationId"
      responses:
        "200":
          description: List of all meter inputs related with meter configuration.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterInputsForMeterConfiguration"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/dictionaries/meter-command-types:
    get:
      x-authorization: MeterCommand.Read
      tags:
        - Meters
      summary: All meter command types.
      description: Endpoint supporting meter command types dictionary values functionality used in meter command process.
      operationId: getAllMeterCommandTypes
      responses:
        "200":
          $ref: "#/components/responses/MeterCommandTypesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/dictionaries/meter-command-sub-types:
    get:
      x-authorization: MeterCommand.Read
      tags:
        - Meters
      summary: All meter command sub types.
      description: Endpoint supporting meter command sub types dictionary values functionality used in meter command process.
      operationId: getAllMeterCommandSubTypes
      responses:
        "200":
          $ref: "#/components/responses/MeterCommandSubTypesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/attach-meter-commands:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Meters.Write
      tags:
        - MeterConfigurations
      summary: Attach meter commands to a meter configuration template/instance.
      description: Attach meter commands to a meter configuration template/instance.
      operationId: attachMeterCommandsToMeterConfiguration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AttachMeterCommandsToMeterConfiguration"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully attached meter commands to a meter configuration template/instance.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/detach-meter-commands:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Meters.Write
      tags:
        - MeterConfigurations
      summary: Detach meter commands from a meter configuration template/instance.
      description: Detach meter commands from a meter configuration template/instance.
      operationId: detachMeterCommandsFromMeterConfiguration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DetachMeterCommandsFromMeterConfiguration"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "204":
          description: Successfully detached meter commands from a meter configuration template/instance.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/batch-attach:
    post:
      x-authorization: Meters.Write
      tags:
        - MeterConfigurations
      summary: Attach meter configuration template to collection of meters by strategy.
      description: Attach meter configuration template to collection of meters by strategy.
      operationId: batchAttachMeterConfigurationTemplateByStrategy
      parameters:
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchAttachMeterConfigurationTemplateByStrategy"
      responses:
        "202":
          description: Attach request accepted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/batch-delete:
    post:
      x-authorization: MeterConfigurationTemplate.Write
      tags:
        - MeterConfigurations
      summary: Batch delete meter configuration templates.
      description: Batch delete meter configuration templates.
      operationId: batchDeleteMeterConfigurationTemplates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchDeleteMeterConfigurationTemplates"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-configurations/commands/search:
    post:
      x-authorization: MeterConfigurationTemplate.Read,Meters.Read
      tags:
        - MeterConfigurations
      summary: Search meter configurations templates or instances.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of meter configurations templates according to passed filters.
        In order to search meter configuration templates, MeterConfigurationTemplate.Read permission is required.
        In order to search meter configuration instances, Meters.Read permission is required
      operationId: searchMeterConfigurations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterConfigurations"
      responses:
        "200":
          description: Meter configurations returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMeterConfigurationsPagedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v2/meters/dictionaries/meter-control-types:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets meter control type values.
      description: Endpoint supporting meter control type values functionality used in meter management process.
      operationId: getAllMeterControlTypes
      responses:
        "200":
          description: Meter control type values successfully returned.
          content:
            application/json:
              schema:
                description: Collection of meter control types
                type: array
                maxItems: 32767
                items:
                  $ref: "#/components/schemas/DictionaryItem"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/dictionaries/meter-roles:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets meter roles values.
      description: Endpoint supporting meter roles values functionality used in meter management process.
      operationId: getAllMeterRoles
      responses:
        "200":
          description: Meter roles values successfully returned.
          content:
            application/json:
              schema:
                description: Collection of meter roles
                type: array
                maxItems: 32767
                items:
                  $ref: "#/components/schemas/DictionaryItem"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/dictionaries/meter-approved-after-types:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets meter approved after values.
      description: Endpoint supporting meter approved after values functionality used in meter management.
      operationId: getAllMeterApprovedAfterTypes
      responses:
        "200":
          description: Meter approved after values successfully returned.
          content:
            application/json:
              schema:
                description: Collection of meter approved after values
                type: array
                maxItems: 32767
                items:
                  $ref: "#/components/schemas/DictionaryItem"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/dictionaries/start-time-for-meter-batch-types:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets start time for meter batch values.
      description: Endpoint supporting start time for meter batch values functionality used in meter management.
      operationId: getAllStartTimeForMeterBatchTypes
      responses:
        "200":
          description: Start time for meter batch values successfully returned.
          content:
            application/json:
              schema:
                description: Collection of start time for meter batch values
                type: array
                maxItems: 32767
                items:
                  $ref: "#/components/schemas/DictionaryItem"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/dictionaries/meter-input-types:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets meter input types values.
      description: Endpoint supporting meter input types dictionary values functionality used in meter management process.
      operationId: getAllMeterInputTypes
      responses:
        "200":
          description: Meter input types values successfully returned.
          content:
            application/json:
              schema:
                description: Collection of meter input types
                type: array
                maxItems: 32767
                items:
                  $ref: "#/components/schemas/DictionaryItem"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/dictionaries/meter-reading-types:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets meter reading types values.
      description: Endpoint supporting meter reading types dictionary values functionality used in meter management process.
      operationId: getAllMeterReadingTypes
      responses:
        "200":
          description: Meter reading types values successfully returned.
          content:
            application/json:
              schema:
                description: Collection of meter reading types
                type: array
                maxItems: 32767
                items:
                  $ref: "#/components/schemas/DictionaryItem"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/dictionaries/measuring-units:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Gets measuring units values.
      description: Endpoint supporting measuring units dictionary values functionality used in meter management process.
      operationId: getAllMeasuringUnits
      responses:
        "200":
          description: Measuring units values successfully returned.
          content:
            application/json:
              schema:
                description: Collection of measuring units
                type: array
                maxItems: 32767
                items:
                  $ref: "#/components/schemas/DictionaryItem"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-inputs/{meterInputId}:
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Get meter input base data.
      description: |-
        Get meter input master data for meter configuration template or meter configuration instance.
        In order to get meter input master data for meter configuration template, MeterConfigurationTemplate.Read permission is required.
        In order to get meter input master data for meter configuration instance, Meters.Read permission is required.
      operationId: getMeterInputBaseData
      parameters:
        - $ref: "#/components/parameters/MeterInputId"
      responses:
        "200":
          description: Returns meter input base data.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterInputBaseData"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Update meter input via instance or template.
      description: |-
        Update meter input via instance or template.
        In order to update  meter input for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to update  meter input for meter configuration instance, Meters.Write permission is required
      operationId: updateMeterInput
      parameters:
        - $ref: "#/components/parameters/MeterInputId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterInput"
        required: true
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    delete:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Delete meter input via instance or template.
      description: |-
        Delete meter input via instance or template.
        In order to delete meter input for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to delete meter input for meter configuration instance, Meters.Write permission is required.
      operationId: deleteMeterInput
      parameters:
        - $ref: "#/components/parameters/MeterInputId"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-inputs/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Returns paged list of meter inputs.
      description: Returns paged list of meter inputs related with meter.
      operationId: searchMeterInputs
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterInputs"
      responses:
        "200":
          description: Paged list of meter inputs related with meter.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMeterInputsPagedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-inputs/{meterInputId}/meter-registers:
    post:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Create meter register for meter input.
      description: |-
        Create meter register for meter input.
        In order to create meter register for configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to create meter register for meter configuration instance, Meters.Write permission is required
      operationId: createMeterRegister
      parameters:
        - $ref: "#/components/parameters/MeterInputId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterRegister"
      responses:
        "201":
          description: Successfully created.
          content:
            application/json:
              schema:
                description: Identifier of newly created meter register
                type: string
                format: uuid
              example: "FE1FEA7B-78F1-44DC-B88D-39BBB8E12B12"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterInputs
      summary: Returns list of all meter registers.
      description: |-
        Returns list of all meter registers related with meter input.
        In order to return list of all meter registers for configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to return list of all meter registers for configuration instance, Meters.Write permission is required
      operationId: getMeterRegistersByMeterInputId
      parameters:
        - $ref: "#/components/parameters/MeterInputId"
      responses:
        "200":
          description: List of all meter registers related with meter input.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterRegistersForMeterInput"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-registers/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterRegisters
      summary: Returns paged list of meter registers.
      description: Returns paged list of meter registers related with meter.
      operationId: searchMeterRegisters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterRegisters"
      responses:
        "200":
          description: Paged list of meter registers related with meter.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMeterRegistersPagedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-registers/{meterRegisterId}:
    get:
      x-authorization-1: MeterConfigurationTemplate.Read,Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterRegisters
      summary: Get meter register base data.
      description: |-
        Retrieve meter register related with meter register id.
        In order to get meter register for meter configuration template, MeterConfigurationTemplate.Read permission is required.
        In order to get meter register for meter configuration instance, Meters.Read permission is required
      operationId: getMeterRegisterBaseData
      parameters:
        - $ref: "#/components/parameters/MeterRegisterId"
      responses:
        "200":
          description: Returns meter register data related with given id.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterRegisterBaseData"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterRegisters
      summary: Update meter register via instance or configuration template.
      description: |-
        Update meter register via instance or configuration template.
        In order to update meter register for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to update meter register for meter configuration instance, Meters.Write permission is required.
      operationId: updateMeterRegister
      parameters:
        - $ref: "#/components/parameters/MeterRegisterId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterRegister"
        required: true
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    delete:
      x-authorization-1: MeterConfigurationTemplate.Write,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterRegisters
      summary: Delete meter register (instance or via configuration template).
      description: |-
        Delete meter register (instance or via configuration template).
        In order to delete meter register for meter configuration template, MeterConfigurationTemplate.Write permission is required.
        In order to delete meter register for meter configuration instance, Meters.Write permission is required
      operationId: deleteMeterRegister
      parameters:
        - $ref: "#/components/parameters/MeterRegisterId"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/commands/batch-attach-meter-commands:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Meters.Write
      tags:
        - Meters
      summary: Batch attach meter commands to meters.
      description: Batch attach meter commands to a list of selected meters.
      operationId: batchAttachMeterCommandsToMeters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchAttachMeterCommandsToMeters"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: Request of attaching meter commands to list of meters is accepted and scheduled.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/commands/batch-detach-meter-commands:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Meters.Write
      tags:
        - Meters
      summary: Batch detach meter commands from meters.
      description: Batch detach meter commands from list of selected meters.
      operationId: batchDetachMeterCommandsFromMeters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchDetachMeterCommandsFromMeters"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: Request of detaching meter commands from list of meters is accepted and scheduled.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/commands/batch-delete:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Search of meters list
      description: Allows to batch delete meters by Identifiers or SearchQuery strategy.
      operationId: batchDelete
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchDeleteMetersByStrategy"
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "202":
          description: Delete meters request accepted.
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/{meterId}/configurations/attach/{meterConfigurationId}:
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Attach meter configuration template to existing meter.
      description: Attach meter configuration template to existing meter.
      operationId: attachMeterConfiguration
      parameters:
        - $ref: "#/components/parameters/MeterId"
        - $ref: "#/components/parameters/MeterConfigurationId"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
      responses:
        "201":
          description: Successfully attached.
          content:
            application/json:
              schema:
                description: Identifier of attached meter configuration instance.
                type: string
                format: uuid
              example: "FE1FEA7B-78F1-44DC-B88D-39BBB8E12B12"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/commands/advance-search:
    post:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: List of meters.
      description: Gets the list of meters matching advance search criteria. Meters are fetched from Elasticsearcg storage, so that there might be latency issues between CUD operation and reading meters.
      operationId: advanceSearchMeters
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PagedSearchExpressionMeter"
        required: true
      responses:
        "200":
          description: List of meters matching search criteria.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMetersPagedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/commands/update-meter-status:
    post:
      x-authorization: Meters.Write
      tags:
        - Meters
      summary: Allows to change meter status.
      description: Allows to change meter status.
      operationId: updateMeterStatus
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterStatus"
        required: true
      responses:
        "204":
          description: Meter status successfully updated.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/next-available-statuses/{meterStatusId}:
    get:
      x-authorization: Meters.Read
      tags:
        - Meters
      summary: Get list of available statuses that provided status can be switched to.
      description: Get list of available statuses that provided status can be switched to.
      operationId: getNextAvailableMeterStatuses
      parameters:
        - $ref: "./parameters/MeterStatusId.yaml"
      responses:
        "200":
          description: List of available meter statuses.
          content:
            application/json:
              schema:
                $ref: "./schemas/MeterStatusesIdsCollection.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "429":
          $ref: "./responses/429.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/meters/commands/attach-meter-batch:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Attaches multiple meters to a meter batch.
      description: Attaches multiple meters to a meter batch or detaches them from the current meter batch and attaches it to a new meter batch.
      operationId: attachMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AttachMeterBatch"
      parameters:
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: Request to attach meters to the selected meter batch is accepted and scheduled.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meters/commands/detach-meter-batch:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - Meters
      summary: Detach multiple meters to a meter batch.
      description: Detach multiple meters to a meter batch.
      operationId: detachMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DetachMeterBatch"
      parameters:
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: Request to detach selected meters from the meter batch is accepted and scheduled.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/electricity-meters/by-master-resource-id/{masterResourceId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: Gets the electricity meter by *master resource id*.
      description: Endpoint serving functionality of getting electricity meter by master resource id.
      operationId: getElectricityMeterByMasterResourceId
      parameters:
        - $ref: "./parameters/MasterResourceId.yaml"
      responses:
        "200":
          $ref: "#/components/responses/GetElectricityMasterData"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/electricity-meters/{meterId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: Gets the electricity meter by *id*.
      description: Endpoint serving functionality of getting electricity meter by id.
      operationId: getElectricityMeter
      parameters:
        - $ref: "#/components/parameters/MeterId"
      responses:
        "200":
          $ref: "#/components/responses/GetElectricityMasterData"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    delete:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: Deletes the electricity meter by *id*.
      description: Endpoint serving functionality of deleting electricity meter by id.
      operationId: deleteElectricityMeter
      parameters:
        - $ref: "#/components/parameters/MeterId"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    patch:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: Updates the electricity meter.
      description: Endpoint serving functionality of updating electricity meter.
      operationId: updateElectricityMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateElectricityMeter"
      parameters:
        - $ref: "#/components/parameters/MeterId"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/electricity-meters:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: Creates the electricity meter.
      description: Endpoint serving functionality of creating electricity meter.
      operationId: createElectricityMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/CreateElectricityMeter"
              description: Id of created electricity meter batch
      responses:
        "201":
          description: "Returns created electricity meter id"
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Id of created electricity meter.
                minimum: 0
                maximum: 2147483647
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Electricity.All
      tags:
        - ElectricityMeters
      summary: Allows to retrieve meter, configuration, inputs and registers
      description: Endpoint serving functionality of retrieving meter, configuration, inputs and registers based on either meterId or manufacturer and meterNumber
      operationId: getElectricityMeterDetails
      parameters:
        - $ref: "#/components/parameters/MeterIdInQueryString"
        - $ref: "#/components/parameters/MeterTypeIdInQueryString"
        - $ref: "#/components/parameters/MeterNumberInQueryString"
      responses:
        "200":
          description: Electricity meters successfully retrieved.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ElectricityMeterDetails"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/electricity-meters/{meterId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: List of electricity meter batches related to meter.
      description: Gets the list of meter batches related to meter.
      operationId: getRelatedMeterBatchesForElectricityMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "200":
          description: List of electricity meter batches related to meter.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesForMeterResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/electricity-meters/commands/advance-search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeters
      summary: List of electricity meters.
      description: Gets the list of electricity meters matching advance search criteria. Meters are fetched from SQL storage, so that there is not latency issues between CUD operation and reading meters.
      operationId: advanceSearchElectricityMeters
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/PagedSearchExpressionElectricityMeter.yaml"
        required: true
      responses:
        "200":
          description: List of meters matching search criteria.
          content:
            application/json:
              schema:
                $ref: "./schemas/PagedResultOfElectricityMeters.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/water-meters/by-master-resource-id/{masterResourceId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: Gets the water meter by *master resource id*.
      description: Endpoint serving functionality of getting water meter by master resource id.
      operationId: getWaterMeterByMasterResourceId
      parameters:
        - $ref: "./parameters/MasterResourceId.yaml"
      responses:
        "200":
          $ref: "#/components/responses/GetWaterMasterData"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/water-meters/{meterId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: Gets the water meter by *id*.
      description: Endpoint serving functionality of getting water meter by id.
      operationId: getWaterMeter
      parameters:
        - $ref: "#/components/parameters/MeterId"
      responses:
        "200":
          $ref: "#/components/responses/GetWaterMasterData"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    delete:
      x-authorization-1: Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: Deletes the water meter by *id*.
      description: Endpoint serving functionality of deleting water meter by id.
      operationId: deleteWaterMeter
      parameters:
        - $ref: "#/components/parameters/MeterId"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    patch:
      x-authorization-1: Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: Updates the water meter.
      description: Endpoint serving functionality of updating water meter.
      operationId: updateWaterMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateWaterMeterData"
      parameters:
        - $ref: "#/components/parameters/MeterId"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/water-meters:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: Creates the water meter.
      description: Endpoint serving functionality of creating water meter.
      operationId: createWaterMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateWaterMeter"
      responses:
        "201":
          description: "Returns created water meter id"
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Id of created electricity meter.
                minimum: 0
                maximum: 2147483647
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Water.All
      tags:
        - WaterMeters
      summary: Allows to retrieve meter, configuration, inputs and registers
      description: Endpoint serving functionality of retrieving meter, configuration, inputs and registers based on either meterId or manufacturer and meterNumber
      operationId: getWaterMeterDetails
      parameters:
        - $ref: "#/components/parameters/MeterIdInQueryString"
        - $ref: "#/components/parameters/MeterTypeIdInQueryString"
        - $ref: "#/components/parameters/MeterNumberInQueryString"
      responses:
        "200":
          description: Water meters successfully retrieved.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WaterMeterDetails"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/water-meters/{meterId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: List of water meter batches related to meter.
      description: Gets the list of meter batches related to meter.
      operationId: getRelatedMeterBatchesForWaterMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "200":
          description: List of water meter batches related to meter.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesForMeterResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/water-meters/commands/advance-search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Water.All
      tags:
        - WaterMeters
      summary: List of water meters.
      description: Gets the list of water meters matching advance search criteria. Meters are fetched from SQL storage, so that there is not latency issues between CUD operation and reading meters.
      operationId: advanceSearchWaterMeters
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/PagedSearchExpressionWaterMeter.yaml"
        required: true
      responses:
        "200":
          description: List of meters matching search criteria.
          content:
            application/json:
              schema:
                $ref: "./schemas/PagedResultOfWaterMeters.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/heat-meters/by-master-resource-id/{masterResourceId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: Gets the heat meter by *master resource id*.
      description: Endpoint serving functionality of getting heat meter by master resource id.
      operationId: getHeatMeterMeterByMasterResourceId
      parameters:
        - $ref: "./parameters/MasterResourceId.yaml"
      responses:
        "200":
          $ref: "#/components/responses/GetHeatMasterData"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/heat-meters/{meterId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: Gets the heat meter by *id*.
      description: Endpoint serving functionality of getting heat meter by id.
      operationId: getHeatMeter
      parameters:
        - $ref: "#/components/parameters/MeterId"
      responses:
        "200":
          $ref: "#/components/responses/GetHeatMasterData"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    delete:
      x-authorization-1: Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: Deletes the heat meter by *id*.
      description: Endpoint serving functionality of deleting heat meter by id.
      operationId: deleteHeatMeter
      parameters:
        - $ref: "#/components/parameters/MeterId"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    patch:
      x-authorization-1: Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: Updates the heat meter.
      description: Endpoint serving functionality of updating heat meter.
      operationId: updateHeatMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateHeatMeterData"
      parameters:
        - $ref: "#/components/parameters/MeterId"
      responses:
        "204":
          description: "No content."
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/heat-meters:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: Creates the heat meter.
      description: Endpoint serving functionality of creating heat meter.
      operationId: createHeatMeter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateHeatMeter"
      responses:
        "201":
          description: "Returns created heat meter id"
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Id of created electricity meter.
                minimum: 0
                maximum: 2147483647
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: MeterCommand.Read
      x-authorization-3: Heating.All
      tags:
        - HeatMeters
      summary: Allows to retrieve meter, configuration, inputs and registers
      description: Endpoint serving functionality of retrieving meter, configuration, inputs and registers based on either meterId or manufacturer and meterNumber
      operationId: getHeatMeterDetails
      parameters:
        - $ref: "#/components/parameters/MeterIdInQueryString"
        - $ref: "#/components/parameters/MeterTypeIdInQueryString"
        - $ref: "#/components/parameters/MeterNumberInQueryString"
      responses:
        "200":
          description: Heat meters successfully retrieved.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HeatMeterDetails"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/heat-meters/{meterId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: List of heat meter batches related to meter.
      description: Gets the list of meter batches related to meter.
      operationId: getRelatedMeterBatchesForHeatMeter
      parameters:
        - $ref: "./parameters/MeterId.yaml"
      responses:
        "200":
          description: List of heat meter batches related to meter.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesForMeterResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
  /v2/heat-meters/commands/advance-search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Heating.All
      tags:
        - HeatMeters
      summary: List of heat meters.
      description: Gets the list of heat meters matching advance search criteria. Meters are fetched from SQL storage, so that there is not latency issues between CUD operation and reading meters.
      operationId: advanceSearchHeatMeters
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./schemas/PagedSearchExpressionHeatMeter.yaml"
        required: true
      responses:
        "200":
          description: List of meters matching search criteria.
          content:
            application/json:
              schema:
                $ref: "./schemas/PagedResultOfHeatMeters.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-templates:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterTemplates
      summary: Creates meter template.
      description: Creates meter template data. Meter templates support easy and correct creation of meters. The meter template can provide default values for most of the attributes that can be set during creation of meter.
      operationId: createMeterTemplate
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterTemplateBody"
        required: true
      responses:
        "201":
          description: Successfully created Meter Template.
          content:
            application/json:
              schema:
                type: integer
                format: int32
                minimum: 1
                maximum: 2147483647
                description: Created Meter Template identifier.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-templates/{meterTemplateId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterTemplates
      summary: Gets specific Meter Template
      description: Gets specific Meter Template by identifier.
      operationId: getMeterTemplateById
      parameters:
        - $ref: "#/components/parameters/MeterTemplateId"
        - $ref: "#/components/parameters/AsOfDate"
      responses:
        "200":
          description: Found Meter Template.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterTemplate"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterTemplates
      summary: Updates specific Meter Template
      description: Updates specific Meter Template by identifier.
      operationId: updateMeterTemplate
      parameters:
        - $ref: "#/components/parameters/MeterTemplateId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterTemplateBody"
        required: true
      responses:
        "204":
          description: Updated meter template.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    delete:
        x-authorization-1: Meters.Write
        x-authorization-2: Electricity.All,Heating.All,Water.All
        tags:
          - MeterTemplates
        summary: Deletes specific Meter Template
        description: Deletes specific Meter Template by identifier.
        operationId: deleteMeterTemplateById
        parameters:
          - $ref: "#/components/parameters/MeterTemplateId"
        responses:
          "204":
            description: Deleted meter template.
          "400":
            $ref: "#/components/responses/400"
          "401":
            $ref: "#/components/responses/401"
          "403":
            $ref: "#/components/responses/403"
          "404":
            $ref: "#/components/responses/404"
          "422":
            $ref: "#/components/responses/422"
          "499":
            $ref: "#/components/responses/499"
          "500":
            $ref: "#/components/responses/500"
          "503":
            $ref: "#/components/responses/503"
          "504":
            $ref: "#/components/responses/504"
        security:
          - Jwt: []
  /v2/meter-templates/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterTemplates
      summary: Search Meter Templates by name.
      description: |-
        ### Result
        Returns filtered, paged list of Meter Templates according to passed name.
      operationId: searchMeterTemplates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterTemplates"
      responses:
        "200":
          description: Meter Templates returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMeterTemplatesPagedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v2/meter-components/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Search Components
      description: |-
        ### Result
        Returns filtered, paged list of Meter Components according to passed filters.
      operationId: searchMeterComponents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterComponentsBody"
      responses:
        "200":
          description: Filtered, paged list of Meter Components
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMeterComponentsPagedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-components/commands/update-meter-component-status:
    post:
      x-authorization: Meters.Write
      tags:
        - MeterComponents
      summary: Allows to change meter component status.
      description: Allows to change meter component status.
      operationId: updateMeterComponentStatus
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterComponentStatusRequest"
        required: true
      responses:
        "204":
          description: Meter component status successfully updated.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-components/next-available-statuses/{componentStatusId}:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterComponents
      summary: Get list of available statuses that provided status can be switched to.
      description: Get list of available statuses that provided status can be switched to.
      operationId: getNextAvailableStatuses
      parameters:
        - $ref: '#/components/parameters/ComponentStatusId'
      responses:
        "200":
          description: List of available statuses.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusesIdCollection"
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
      security:
        - Jwt: []

  /v2/electricity-meter-batches/{technicalId}:
    get:
      x-authorization-1: MeterBatch.Read
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: Gets the electricity meter batch by it's *technical id*.
      description: Endpoint serving functionality of getting electricity meter batch by meter batch technical id.
      operationId: getElectricityMeterBatch
      parameters:
        - $ref: "#/components/parameters/MeterBatchTechnicalId"
      responses:
        "200":
          $ref: "#/components/responses/GetElectricityMeterBatchDetails"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: Updates an electricity meter batch
      description: Endpoint for updating an electricity meter batch
      operationId: updateElectricityMeterBatch
      parameters:
        - $ref: "#/components/parameters/MeterBatchTechnicalId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateElectricityMeterBatch.yaml"
      responses:
        "204":
          description: Electricity meter batch successfully updated.
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v2/electricity-meter-batches/{technicalId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: List of electricity meter batches related to meter batch.
      description: Gets the list of meter batches related to meter batch.
      operationId: getRelatedMeterBatchesForElectricityMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      responses:
        "200":
          description: List of electricity meter batches related to meter batch.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: Creates new meter batch relation.
      description: Creates new meter batch relation for electricity meter batch.
      operationId: createMeterBatchRelationForElectricityMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterBatchRelationForMeterBatch"
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Relation for electricity meter batch successfully created.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/water-meter-batches/{technicalId}:
    get:
      x-authorization-1: MeterBatch.Read
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: Gets the water meter batch by it's *technical id*.
      description: Endpoint serving functionality of getting water meter batch by meter batch technical id.
      operationId: getWaterMeterBatch
      parameters:
        - $ref: "#/components/parameters/MeterBatchTechnicalId"
      responses:
        "200":
          description: Returns paged list of searched meter commands.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WaterMeterBatchDetails"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: Updates a water meter batch
      description: Endpoint for updating a water meter batch
      operationId: updateWaterMeterBatch
      parameters:
        - $ref: "#/components/parameters/MeterBatchTechnicalId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateWaterMeterBatch.yaml"
      responses:
        "204":
          description: Water meter batch successfully updated.
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/water-meter-batches/{technicalId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: List of water meter batches related to meter batch.
      description: Gets the list of meter batches related to meter batch.
      operationId: getRelatedMeterBatchesForWaterMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      responses:
        "200":
          description: List of water meter batches related to meter batch.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: Creates new meter batch relation.
      description: Creates new meter batch relation for water meter batch.
      operationId: createMeterBatchRelationForWaterMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterBatchRelationForMeterBatch"
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Relation for water meter batch successfully created.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/heat-meter-batches/{technicalId}:
    get:
      x-authorization-1: MeterBatch.Read
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: Gets the electricity meter batch by it's *technical id*.
      description: Endpoint serving functionality of getting heat meter batch by meter batch technical id.
      operationId: getHeatMeterBatch
      parameters:
        - $ref: "#/components/parameters/MeterBatchTechnicalId"
      responses:
        "200":
          $ref: "#/components/responses/GetHeatMeterBatchDetails"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    put:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: Updates a heat meter batch
      description: Endpoint for updating a heat meter batch
      operationId: updateHeatMeterBatch
      parameters:
        - $ref: "#/components/parameters/MeterBatchTechnicalId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateHeatMeterBatch.yaml"
      responses:
        "204":
          description: Heat meter batch successfully updated.
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v2/heat-meter-batches/{technicalId}/related-meter-batches:
    get:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: List of heat meter batches related to meter batch.
      description: Gets the list of meter batches related to meter batch.
      operationId: getRelatedMeterBatchesForHeatMeterBatch
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
      responses:
        "200":
          description: List of heat meter batches related to meter batch.
          content:
            application/json:
              schema:
                $ref: "./schemas/RelatedMeterBatchesResult.yaml"
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: Creates new meter batch relation.
      description: Creates new meter batch relation for heat meter batch.
      operationId: createMeterBatchRelationForHeatMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterBatchRelationForMeterBatch"
      parameters:
        - $ref: "./parameters/MeterBatchTechnicalId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Relation for heat meter batch successfully created.
        "400":
          $ref: "./responses/400.yaml"
        "401":
          $ref: "./responses/401.yaml"
        "403":
          $ref: "./responses/403.yaml"
        "404":
          $ref: "./responses/404.yaml"
        "422":
          $ref: "./responses/422.yaml"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "./responses/500.yaml"
        "503":
          $ref: "./responses/503.yaml"
        "504":
          $ref: "./responses/504.yaml"
      security:
        - Jwt: []

  /v2/electricity-meter-batches:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: Creates the electricity meter batch
      description: Endpoint serving functionality of creating electricity meter batch
      operationId: createElectricityMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateElectricityMeterBatch.yaml"
      responses:
        "201":
          description: "Returns created electricity meter batch technical id"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/Guid"
                description: Meter Batch Technical Id of created electricity meter batch
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/electricity-meter-batches/commands/create-electricity-meter-batches:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Electricity.All
      tags:
        - ElectricityMeterBatches
      summary: Creates electricity meter batches.
      description: Endpoint serving functionality of creating electricity meter batches.
      operationId: createElectricityMeterBatches
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateElectricityMeterBatches.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "201":
          description: A collection of TechnicalId's
          content:
            application/json:
              schema:
                description: Technical Id of related meter batch.
                type: array
                minItems: 1
                maxItems: 2
                items:
                  $ref: ./schemas/DataTypes/Guid.yaml
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/water-meter-batches:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: Creates the water meter batch
      description: Endpoint serving functionality of creating water meter batch
      operationId: createWaterMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateWaterMeterBatch.yaml"
      responses:
        "201":
          description: "Returns created water meter batch technical id"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/Guid"
                description: Meter Batch Technical Id of created water meter batch
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/water-meter-batches/commands/create-water-meter-batches:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Water.All
      tags:
        - WaterMeterBatches
      summary: Creates water meter batches.
      description: Endpoint serving functionality of creating water meter batches.
      operationId: createWaterMeterBatches
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateWaterMeterBatches.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "201":
          description: A collection of TechnicalId's
          content:
            application/json:
              schema:
                description: Technical Id of related meter batch.
                type: array
                minItems: 1
                maxItems: 2
                items:
                  $ref: ./schemas/DataTypes/Guid.yaml
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/heat-meter-batches:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: Creates the heat meter batch
      description: Endpoint serving functionality of creating heat meter batch
      operationId: createHeatMeterBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateHeatMeterBatch.yaml"
      responses:
        "201":
          description: "Returns created heat meter batch technical id"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/Guid"
                description: Meter Batch Technical Id of created heat meter batch
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/heat-meter-batches/commands/create-heat-meter-batches:
    post:
      x-authorization-1: MeterBatch.Write
      x-authorization-2: Heating.All
      tags:
        - HeatMeterBatches
      summary: Creates heat meter batches.
      description: Endpoint serving functionality of creating heat meter batches.
      operationId: createHeatMeterBatches
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CreateHeatMeterBatches.yaml"
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "201":
          description: A collection of TechnicalId's
          content:
            application/json:
              schema:
                description: Technical Id of related meter batch.
                type: array
                minItems: 1
                maxItems: 2
                items:
                  $ref: ./schemas/DataTypes/Guid.yaml
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v2/meter-batches/dictionaries/batch-statuses:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterBatches
      summary: Gets meter batch status values.
      description: Endpoint supporting meter batch status dictionary values functionality used in meter management process.
      operationId: getAllMeterBatchStatuses
      responses:
        "200":
          $ref: '#/components/responses/DictonaryMeterBatchStatuses'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-batches/dictionaries/inspection-level:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterBatches
      summary: Gets meter batch inspection level values.
      description: Endpoint supporting meter batch inspection level dictionary values functionality used in meter management process.
      operationId: getAllMeterBatchInspectionLevels
      responses:
        "200":
          $ref: '#/components/responses/DictonaryMeterBatchInspectionLevels'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-batches/dictionaries/meter-batch-unit:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterBatches
      summary: Gets meter batch unit values.
      description: Endpoint supporting meter batch unit dictionary values functionality used in meter management process.
      operationId: getAllMeterBatchUnits
      responses:
        "200":
          $ref: '#/components/responses/DictionaryMeterBatchUnits'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v2/meter-batches/commands/update-active-process:
    post:
      x-authorization: Meters.Write
      tags:
        - MeterBatches
      parameters:
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      summary: Unlocks / locks meter batches from being disabled for users' manual edition by setting value for active process on a batch.
      description: Unlocks / locks meter batches from being disabled for users' manual edition by setting value for active process on a batch.
      operationId: updateActiveProcessOnBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/UpdateActiveProcessOnBatch.yaml"
      responses:
        "204":
          description: "Successfully updated active process on a meter batch"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v2/meter-batches/commands/search:
    post:
      x-authorization-1: MeterBatch.Read,Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterBatches
      summary: List of meter batches.
      description: Gets the list of meter batches matching search criteria.
      operationId: searchMeterBatches
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterBatchesBody"
        required: true
      responses:
        "200":
          description: List of meter matches matching search criteria.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMeterBatchesPagedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v2/meter-components:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Get components related with related meter id.
      description: Get meter relations data.
      operationId: getMeterRelations
      parameters:
        - in: query
          name: meterId
          required: true
          description: Meter identifier.
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 21474
          example: 1
        -  $ref: "#/components/parameters/SupplyTypeCodeQueryString"
      responses:
        "200":
          description: Successfully fetched Meter components.
          content:
            application/json:
              schema:
                description: Meter components data.
                type: array
                items:
                  $ref: "#/components/schemas/MeterComponentPayload"
                minItems: 0
                maxItems: 255

        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Creates meter component.
      description: Creates meter component data. Meter component represents a component that is installed with or in a physical meter.
      operationId: createMeterComponent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterComponent"
      responses:
        "201":
          description: Successfully created Meter Component.
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Id of created electricity meter.
                minimum: 1
                maximum: 2147483647
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-components/{meterComponentId}:
    get:
      x-authorization: Meters.Read
      tags:
        - MeterComponents
      summary: Get meter component.
      description: Get meter component data.
      operationId: getMeterComponent
      parameters:
        - $ref: "#/components/parameters/MeterComponentId"
      responses:
        "200":
          description: Successfully fetched Meter Component.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterComponent"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Updates meter component.
      description: Updates meter component data.
      operationId: updateMeterComponent
      parameters:
        - $ref: "#/components/parameters/MeterComponentId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterComponent"
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
    delete:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Deletes meter component.
      description: Deletes meter component data.
      operationId: deleteMeterComponent
      parameters:
        - $ref: "#/components/parameters/MeterComponentId"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-components/meter-relations/{meterComponentId}:
    put:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Create or update meter relation.
      description: Create or update meter relation.
      operationId: upsertMeterRelation
      parameters:
        - $ref: "#/components/parameters/MeterComponentId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpsertMeterRelation"
      responses:
        "204":
          description: Successfully created or updated.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-components/commands/batch-delete:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Batch delete of meter components.
      description: Batch delete of meter components.
      operationId: batchDeleteMeterComponents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchDeleteMeterComponents"
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "202":
          description: Meter components ordered for deletion.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-components/commands/get-available:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Get available components for meter relations for specific meter.
      description: Get available components.
      operationId: getMeterRelationsAvailableComponents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MeterRelationsAvailableComponentsFilter"
      responses:
        "200":
          description: Successfully fetched Meter components available for meter relations.
          content:
            application/json:
              schema:
                type: array
                maxItems: 10000
                description: Meter relations available components.
                items:
                  $ref: "#/components/schemas/MeterComponentBase"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-components/{meterComponentId}/get-meter-relations:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterComponents
      summary: Get relations to meters for specific meter component.
      description: Get relations to meters for specific meter component.
      operationId: getMeterComponentMeterRelations
      parameters:
        - $ref: "#/components/parameters/MeterComponentId"
      responses:
        "200":
          description: Successfully fetched meter relations for current meter component.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterRelationsToMeterComponent"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

  /v2/meters/commands/set-first-commissioning-date:
    post:
      x-authorization-1: Meters.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterFirstCommissioningDate.Write
      tags:
        - Meters
      summary: Set new value for first commissioning date in meter entity.
      description: Set new value for first commissioning date in meter entity.
      operationId: setMeterFirstCommissioningDate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MeterFirstCommissioningDateUpdate"
      responses:
        "204":
          description: Successfully updated first commissioning date.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]

  /v2/meter-command-groups/commands/search:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Retrieves list of the meter command groups.
      description: Retrieves list of the meter command groups.
      operationId: searchMeterCommandGroup
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterCommandGroup"
      responses:
        "200":
          description: Successfully retrieved meter command group.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PagedMeterCommandGroups"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]

  /v2/meter-command-groups/commands/get-command-groups-by-command-id/{meterCommandId}:
    post:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Retrieves list of the meter command groups related to a given command.
      description: Retrieves list of the meter command groups related to a given command.
      operationId: getMeterCommandGroupsByCommandId
      parameters:
        - $ref: "#/components/parameters/MeterCommandId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetMeterCommandGroupsRelatedToCommandQuery"
      responses:
        "200":
          description: Successfully retrieved meter command groups related to a given command.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PagedMeterCommandGroupsRelatedToCommand"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]

  /v2/meter-command-groups/{meterCommandGroupId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      tags:
        - MeterCommands
      summary: Retrieves meter command group by id.
      description: Retrieves meter command group by id.
      operationId: getMeterCommandGroup
      parameters:
        - $ref: "#/components/parameters/MeterCommandGroupId"
      responses:
        "200":
          description: Successfully retrieved meter command group.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterCommandGroup"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]
    put:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterCommands
      summary: Update details of a meter command group.
      description: Update details of a meter command group.
      operationId: updateMeterCommandGroup
      parameters:
        - $ref: "#/components/parameters/MeterCommandGroupId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterCommandGroup"
      responses:
        "204":
          description: Update of meter command group finished successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]
    delete:
      x-authorization: MeterCommand.Write
      tags:
        - MeterCommands
      summary: Delete a meter command group.
      description: Delete a meter command group.
      operationId: deleteMeterCommandGroup
      parameters:
        - $ref: "#/components/parameters/MeterCommandGroupId"
      responses:
        "204":
          description: Delete of meter command group finished successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-command-groups:
    post:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterCommands
      summary: Create meter command group.
      description: Create meter command group.
      operationId: createMeterCommandGroup
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterCommandGroup"
      responses:
        "201":
          description: Successfully created meter command group.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Guid"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "./responses/422.yaml"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]
  /v2/meter-jobs:
    post:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Create meter job.
      description: Create meter job.
      operationId: createMeterJob
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterJob"
      responses:
        "201":
          description: Successfully created meter job.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Guid"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]
  /v2/meter-jobs/{meterJobId}:
    get:
      x-authorization-1: Meters.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      x-authorization-3: MeterCommand.Read
      tags:
        - MeterJobs
      summary: Retrieves meter job by id.
      description: Retrieves meter job by id.
      operationId: getMeterJob
      parameters:
        - $ref: "#/components/parameters/MeterJobId"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Successfully retrieved meter job.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterJob"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]
    put:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Update details of a meter job.
      description: Update details of a meter job.
      operationId: updateMeterJob
      parameters:
        - $ref: "#/components/parameters/MeterJobId"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterJob"
      responses:
        "204":
          description: Update of meter job finished successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]
    delete:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Delete a meter job.
      description: Delete a meter job.
      operationId: deleteMeterJob
      parameters:
        - $ref: "#/components/parameters/MeterJobId"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Delete of meter job finished successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-jobs/{meterJobId}/commands/skip-next-execution:
    post:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Skip next meter job execution.
      description: Skip next meter job execution.
      operationId: skipNextMeterJobExecution
      parameters:
        - $ref: "./parameters/MeterJobId.yaml"
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "204":
          description: Next execution skipped.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-jobs/{meterJobId}/commands/execute-now:
    post:
      x-authorization-1: MeterCommand.Write
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Execute meter job now
      description: Execute meter job now.
      operationId: executeMeterJobNow
      parameters:
        - $ref: "#/components/parameters/MeterJobId"
        - in: query
          name: skipNextExecution
          required: true
          description: Skip next planned meter job execution.
          schema:
            type: boolean
          example: true
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "201":
          description: Execution of bulk meter command group successfully created.
          content:
            application/json:
              schema:
                type: integer
                format: int32
                description: Bulk meter command group process center identifier.
                minimum: 1
                maximum: 2147483647
                example: 543535
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-jobs/commands/search:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Retrieves list of meter jobs.
      description: Retrieves list of meter jobs.
      operationId: searchMeterJobs
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterJobs"
      responses:
        "200":
          description: Successfully retrieved meter jobs.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterJobsPagedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]
  /v2/meter-jobs/commands/calculate-next-execution-date:
    post:
      x-authorization-1: MeterCommand.Read
      x-authorization-2: Electricity.All,Heating.All,Water.All
      tags:
        - MeterJobs
      summary: Calculate next execution date based on cron expression.
      description: Calculate next execution date based on cron expression.
      operationId: calculateNextExecutionDate
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas/CalculateNextExecutionDate.yaml"
      responses:
        "200":
          description: Successfully calculated next execution date.
          content:
            application/json:
              schema:
                $ref: "./schemas/DataTypes/DateTime.yaml"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: [ ]
  /v2/meter-jobs/dictionaries/meter-job-statuses:
    get:
      x-authorization: MeterCommand.Read
      tags:
        - MeterJobs
      summary: Gets meter job statuses.
      description: Gets meter job statuses.
      operationId: getAllMeterJobStatuses
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Meter job statuses successfully returned.
          content:
            application/json:
              schema:
                description: Collection of meter statuses
                type: array
                maxItems: 32767
                items:
                  $ref: "#/components/schemas/DictionaryItem"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
  /v2/meter-jobs/dictionaries/meter-job-types:
    get:
      x-authorization: MeterCommand.Read
      tags:
        - MeterJobs
      summary: Gets meter job types.
      description: Gets meter job types.
      operationId: getAllMeterJobTypes
      parameters:
        - $ref: "./parameters/EsCorrelationIdInHeaderRequired.yaml"
        - $ref: "./parameters/EsMessageIdInHeader.yaml"
      responses:
        "200":
          description: Meter job types successfully returned.
          content:
            application/json:
              schema:
                description: Collection of meter jobs
                type: array
                maxItems: 32767
                items:
                  $ref: "#/components/schemas/DictionaryItem"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []
components:
  parameters:
    "MeterCommandId":
      $ref: "./parameters/MeterCommandId.yaml"
    "AsOfDate":
      $ref: "./parameters/AsOfDate.yaml"
    "MeterConfigurationId":
      $ref: "./parameters/MeterConfigurationId.yaml"
    "EsCorrelationIdInHeader":
      $ref: "./parameters/EsCorrelationIdInHeader.yaml"
    "EsMessageIdInHeader":
      $ref: "./parameters/EsMessageIdInHeader.yaml"
    "MeterId":
      $ref: "./parameters/MeterId.yaml"
    "MeterIdInQueryString":
      $ref: "./parameters/MeterIdInQueryString.yaml"
    "MeterNumberInQueryString":
      $ref: "./parameters/MeterNumberInQueryString.yaml"
    "MeterTypeIdInQueryString":
      $ref: "./parameters/MeterTypeIdInQueryString.yaml"
    "MeterTemplateId":
      $ref: "./parameters/MeterTemplateId.yaml"
    "ComponentStatusId":
      $ref: "./parameters/ComponentStatusId.yaml"
    "MeterBatchTechnicalId":
      $ref: "./parameters/MeterBatchTechnicalId.yaml"
    "MeterComponentId":
      $ref: "./parameters/MeterComponentId.yaml"
    "SupplyTypeCodeQueryString":
      $ref: "./parameters/SupplyTypeCodeQueryString.yaml"
    "MeterInputId":
      $ref: "./parameters/MeterInputId.yaml"
    "MeterCommandGroupId":
      $ref: "./parameters/MeterCommandGroupId.yaml"
    "MeterRegisterId":
      $ref: "./parameters/MeterRegisterId.yaml"
    "MeterJobId":
      $ref: "./parameters/MeterJobId.yaml"
  responses:
    "400":
      $ref: "./responses/400.yaml"
    "401":
      $ref: "./responses/401.yaml"
    "403":
      $ref: "./responses/403.yaml"
    "404":
      $ref: "./responses/404.yaml"
    "422":
      $ref: "./responses/422.yaml"
    "429":
      $ref: "./responses/429.yaml"
    "499":
      $ref: "./responses/499.yaml"
    "500":
      $ref: "./responses/500.yaml"
    "503":
      $ref: "./responses/503.yaml"
    "504":
      $ref: "./responses/504.yaml"
    "DictonarySupplyTypes":
      $ref: "./responses/DictonarySupplyTypes.yaml"
    "DictonaryMeterBatchStatuses":
      $ref: "./responses/DictonaryMeterBatchStatuses.yaml"
    "DictonaryMeterBatchInspectionLevels":
      $ref: "./responses/DictonaryMeterBatchInspectionLevels.yaml"
    "DictionaryMeterBatchUnits":
      $ref: "./responses/DictionaryMeterBatchUnits.yaml"
    "CreateMeterCommandResponse":
      $ref: "./responses/CreateMeterCommandResponse.yaml"
    "MeterCommandTypesResponse":
      $ref: "./responses/MeterCommandTypesResponse.yaml"
    "MeterCommandSubTypesResponse":
      $ref: "./responses/MeterCommandSubTypesResponse.yaml"
    "GetElectricityMasterData":
      $ref: "./responses/GetElectricityMasterData.yaml"
    "GetWaterMasterData":
      $ref: "./responses/GetWaterMasterData.yaml"
    "GetHeatMasterData":
      $ref: "./responses/GetHeatMasterData.yaml"
    "GetElectricityMeterBatchDetails":
      $ref: "./responses/GetElectricityMeterBatchDetails.yaml"
    "GetHeatMeterBatchDetails":
      $ref: "./responses/GetHeatMeterBatchDetails.yaml"
  schemas:
    RowVersion:
      $ref: ./schemas/DataTypes/RowVersion.yaml
    OneWordString:
      $ref: ./schemas/DataTypes/OneWordString.yaml
    ShortString:
      $ref: ./schemas/DataTypes/ShortString.yaml
    MediumString:
      $ref: ./schemas/DataTypes/MediumString.yaml
    DescriptionString:
      $ref: ./schemas/DataTypes/DescriptionString.yaml
    Guid:
      $ref: ./schemas/DataTypes/Guid.yaml
    Date:
      $ref: ./schemas/DataTypes/Date.yaml
    DateTime:
      $ref: ./schemas/DataTypes/DateTime.yaml
    DictionaryItem:
      $ref: "./schemas/DictionaryItem.yaml"
    MessageDetails:
      $ref: "./schemas/MessageDetails.yaml"
    Meter:
      $ref: "./schemas/Meter.yaml"
    MeterControlTypeCode:
      $ref: "./schemas/MeterControlTypeCode.yaml"
    Page:
      $ref: "./schemas/Page.yaml"
    PagedResult:
      $ref: "./schemas/PagedResult.yaml"
    ProblemDetails:
      $ref: "./schemas/ProblemDetails.yaml"
    ProblemDetailsWithErrors:
      $ref: "./schemas/ProblemDetailsWithErrors.yaml"
    RetrieveSortedPagedData:
      $ref: "./schemas/RetrieveSortedPagedData.yaml"
    Sort:
      $ref: "./schemas/Sort.yaml"
    SupplyType:
      $ref: "./schemas/SupplyType.yaml"
    MeterManagementValueList:
      $ref: "./schemas/MeterManagementValueList.yaml"
    CreateMeterCommandData:
      $ref: "./schemas/CreateMeterCommandData.yaml"
    MeterCommandTypeCode:
      $ref: "./schemas/MeterCommandTypeCode.yaml"
    MeterCommandSubTypeCode:
      $ref: "./schemas/MeterCommandSubTypeCode.yaml"
    SearchMeterCommands:
      $ref: "./schemas/SearchMeterCommands.yaml"
    MeterCommandDetails:
      $ref: "./schemas/MeterCommandDetails.yaml"
    MeterCommandsConfiguration:
      $ref: "./schemas/MeterCommandsConfiguration.yaml"
    MeterCommandPagedCollection:
      $ref: "./schemas/MeterCommandPagedCollection.yaml"
    AttachMeterCommandsToMeterConfiguration:
      $ref: "./schemas/AttachMeterCommandsToMeterConfiguration.yaml"
    DetachMeterCommandsFromMeterConfiguration:
      $ref: "./schemas/DetachMeterCommandsToMeterConfiguration.yaml"
    MeterCommandUpdate:
      $ref: "./schemas/MeterCommandUpdate.yaml"
    BatchAttachMeterCommandsToMeters:
      $ref: "./schemas/BatchAttachMeterCommandsToMeters.yaml"
    BatchDetachMeterCommandsFromMeters:
      $ref: "./schemas/BatchDetachMeterCommandsFromMeters.yaml"
    SearchMetersByIdentifiersStrategy:
      $ref: "./schemas/SearchMetersByIdentifiersStrategy.yaml"
    SearchMetersBySearchExpressionStrategy:
      $ref: "./schemas/SearchMetersBySearchExpressionStrategy.yaml"
    ElectricityMeterProperties:
      $ref: "./schemas/ElectricityMeterProperties.yaml"
    ElectricityMeterPropertiesNullable:
      $ref: "./schemas/ElectricityMeterPropertiesNullable.yaml"
    ElectricityMeter:
      $ref: "./schemas/ElectricityMeter.yaml"
    UpdateElectricityMeter:
      $ref: "./schemas/UpdateElectricityMeter.yaml"
    MeterBaseDataToUpdate:
      $ref: "./schemas/MeterBaseDataToUpdate.yaml"
    CreateMeterBaseData:
      $ref: "./schemas/CreateMeterBaseData.yaml"
    CreateElectricityMeter:
      $ref: "./schemas/CreateElectricityMeter.yaml"
    MeterInputType:
      $ref: "./schemas/MeterInputType.yaml"
    MeterReadingType:
      $ref: "./schemas/MeterReadingType.yaml"
    MeasuringUnit:
      $ref: "./schemas/MeasuringUnit.yaml"
    MeterRegister:
      $ref: "./schemas/MeterRegister.yaml"
    MeterInput:
      $ref: "./schemas/MeterInput.yaml"
    MeterConfiguration:
      $ref: "./schemas/MeterConfiguration.yaml"
    WaterMeter:
      $ref: "./schemas/WaterMeter.yaml"
    WaterMeterProperties:
      $ref: "./schemas/WaterMeterProperties.yaml"
    WaterMeterPropertiesNullable:
      $ref: "./schemas/WaterMeterPropertiesNullable.yaml"
    CreateWaterMeter:
      $ref: "./schemas/CreateWaterMeter.yaml"
    UpdateWaterMeterData:
      $ref: "./schemas/UpdateWaterMeter.yaml"
    MeterComponent:
      $ref: "./schemas/MeterComponent.yaml"
    HeatMeter:
      $ref: "./schemas/HeatMeter.yaml"
    HeatMeterProperties:
      $ref: "./schemas/HeatMeterProperties.yaml"
    HeatMeterPropertiesNullable:
      $ref: "./schemas/HeatMeterPropertiesNullable.yaml"
    CreateHeatMeter:
      $ref: "./schemas/CreateHeatMeter.yaml"
    UpdateHeatMeterData:
      $ref: "./schemas/UpdateHeatMeter.yaml"
    CommonMeterTemplateProperties:
      $ref: "./schemas/CommonMeterTemplateProperties.yaml"
    ElectricityMeterTemplateProperties:
      $ref: "./schemas/ElectricityMeterTemplateProperties.yaml"
    HeatingMeterTemplateProperties:
      $ref: "./schemas/HeatingMeterTemplateProperties.yaml"
    WaterMeterTemplateProperties:
      $ref: "./schemas/WaterMeterTemplateProperties.yaml"
    CreateMeterTemplateBody:
      $ref: "./schemas/CreateMeterTemplateBody.yaml"
    UpdateMeterTemplateBody:
      $ref: "./schemas/UpdateMeterTemplateBody.yaml"
    MeterTemplate:
      $ref: "./schemas/MeterTemplate.yaml"
    UpdateMeterComponentStatusRequest:
      $ref: "./schemas/UpdateMeterComponentStatusRequest.yaml"
    StatusesIdCollection:
      $ref: "./schemas/StatusesIdCollection.yaml"
    ElectricityMeterDetails:
      $ref: "./schemas/ElectricityMeterDetails.yaml"
    WaterMeterDetails:
      $ref: "./schemas/WaterMeterDetails.yaml"
    HeatMeterDetails:
      $ref: "./schemas/HeatMeterDetails.yaml"
    SearchMetersResult:
      $ref: "./schemas/SearchMetersResult.yaml"
    SearchMetersPagedResult:
      $ref: "./schemas/SearchMetersPagedResult.yaml"
    SearchMeterBatchesBody:
      $ref: "./schemas/SearchMeterBatchesBody.yaml"
    SearchMeterBatchesPage:
      $ref: "./schemas/SearchMeterBatchesPage.yaml"
    SearchMeterBatchesSort:
      $ref: "./schemas/SearchMeterBatchesSort.yaml"
    SearchMeterBatchesPagedResult:
      $ref: "./schemas/SearchMeterBatchesPagedResult.yaml"
    ElectricityMeterBatchDetails:
      $ref: "./schemas/ElectricityMeterBatchDetails.yaml"
    WaterMeterBatchDetails:
      $ref: "./schemas/WaterMeterBatchDetails.yaml"
    HeatMeterBatchDetails:
      $ref: "./schemas/HeatMeterBatchDetails.yaml"
    MeterComponentPayload:
      allOf:
        - $ref: "./schemas/MeterComponent.yaml"
      description: Meter component payload with meter id
      properties:
        relatedMeterId:
          type: integer
          format: int32
          description: Id of a meter.
          minimum: 1
          maximum: 2147483647
          example: 1
        installationDate:
          allOf:
            - $ref: ./schemas/DataTypes/DateTime.yaml
          description: Installation date time.
        removeDate:
          allOf:
            - $ref: ./schemas/DataTypes/DateTime.yaml
          description: Remove date time.
          nullable: true
      additionalProperties: false
    UpdateMeterStatus:
      $ref: "./schemas/UpdateMeterStatus.yaml"
    SearchMeterComponentsBody:
      $ref: "./schemas/SearchMeterComponentsBody.yaml"
    SearchMeterComponentsPagedResult:
      $ref: "./schemas/SearchMeterComponentsPagedResult.yaml"
    UpsertMeterRelation:
      type: object
      description: Meter relation for the specific meter.
      required:
        - supplyTypeCode
        - meterId
        - dateOfRelationChange
        - changeContext
      properties:
        supplyTypeCode:
          $ref: "./schemas/SupplyType.yaml"
        meterId:
          type: integer
          format: int32
          minimum: 1
          maximum: 2147483647
          description: Meter identifier.
        dateOfRelationChange:
          allOf:
            - $ref: ./schemas/DataTypes/DateTime.yaml
          description: DateTime of installation or removal.
          example: "2022-01-02T11:00:00Z"
        changeContext:
          type: string
          enum:
            - RemoveComponent
            - InstallComponent
          x-enumNames:
            - RemoveComponent
            - InstallComponent
          description: Type of action applied to a meter - component relation.
      additionalProperties: false
    Audit:
      $ref: "./schemas/Audit.yaml"
    AttachMeterBatch:
       $ref: "./schemas/AttachMeterBatch.yaml"
    DetachMeterBatch:
       $ref: "./schemas/DetachMeterBatch.yaml"
    MeterComponentBase:
      $ref: "./schemas/MeterComponentBase.yaml"
    CreateMeterComponent:
      $ref: "./schemas/CreateMeterComponent.yaml"
    UpdateMeterComponent:
      $ref: "./schemas/UpdateMeterComponent.yaml"
    MeterRelationsAvailableComponentsFilter:
      $ref: "./schemas/MeterRelationsAvailableComponentsFilter.yaml"
    CreateMeterConfiguration:
      $ref: "./schemas/CreateMeterConfiguration.yaml"
    CreateMeterInput:
      $ref: "./schemas/CreateMeterInput.yaml"
    CreateMeterRegister:
      $ref: "./schemas/CreateMeterRegister.yaml"
    ElectricityMeterRegisterProperties:
      $ref: "./schemas/ElectricityMeterRegisterProperties.yaml"
    HeatMeterRegisterProperties:
      $ref: "./schemas/HeatMeterRegisterProperties.yaml"
    WaterMeterRegisterProperties:
      $ref: "./schemas/WaterMeterRegisterProperties.yaml"
    MeterConfigurationBaseData:
      $ref: "./schemas/MeterConfigurationBaseData.yaml"
    UpdateMeterConfiguration:
      $ref: "./schemas/UpdateMeterConfiguration.yaml"
    MeterInputForMeterConfiguration:
      $ref: "./schemas/MeterInputForMeterConfiguration.yaml"
    MeterInputsForMeterConfiguration:
      $ref: "./schemas/MeterInputsForMeterConfiguration.yaml"
    BatchAttachMeterConfigurationTemplateByStrategy:
      $ref: "./schemas/BatchAttachMeterConfigurationTemplateByStrategy.yaml"
    BatchDeleteMeterConfigurationTemplates:
      $ref: "./schemas/BatchDeleteMeterConfigurationTemplates.yaml"
    MeterConfigurationBase:
      $ref: "./schemas/MeterConfigurationBase.yaml"
    SearchMeterConfigurations:
      $ref: "./schemas/SearchMeterConfigurations.yaml"
    SearchMeterConfigurationsPagedResult:
      $ref: "./schemas/SearchMeterConfigurationsPagedResult.yaml"
    MeterInputBaseData:
      $ref: "./schemas/MeterInputBaseData.yaml"
    UpdateMeterInput:
      $ref: "./schemas/UpdateMeterInput.yaml"
    MeterInputForMeter:
      $ref: "./schemas/MeterInputForMeter.yaml"
    SearchMeterInputsPagedResult:
      $ref: "./schemas/SearchMeterInputsPagedResult.yaml"
    MeterRegisterForMeterInput:
      $ref: "./schemas/MeterRegisterForMeterInput.yaml"
    MeterRegistersForMeterInput:
      $ref: "./schemas/MeterRegistersForMeterInput.yaml"
    SearchMeterInputs:
      $ref: "./schemas/SearchMeterInputs.yaml"
    SearchCompareOperator:
      $ref: "./schemas/SearchCompareOperator.yaml"
    SearchExpressionMeterPropertyAvailableValues:
      $ref: "./schemas/SearchExpressionMeterPropertyAvailableValues.yaml"
    SearchExpression:
      $ref: "./schemas/SearchExpression.yaml"
    PagedSearchExpressionMeter:
      $ref: "./schemas/PagedSearchExpressionMeter.yaml"
    SortRequired:
      $ref: "./schemas/SortRequired.yaml"
    MeterRegisterBaseData:
      $ref: "./schemas/MeterRegisterBaseData.yaml"
    SearchMeterRegisters:
      $ref: "./schemas/SearchMeterRegisters.yaml"
    SearchMeterRegistersPagedResult:
      $ref: "./schemas/SearchMeterRegistersPagedResult.yaml"
    UpdateMeterRegister:
      $ref: "./schemas/UpdateMeterRegister.yaml"
    MeterTemplateBaseData:
      $ref: "./schemas/MeterTemplateBaseData.yaml"
    SearchMeterTemplates:
      $ref: "./schemas/SearchMeterTemplates.yaml"
    SearchMeterTemplatesPagedResult:
      $ref: "./schemas/SearchMeterTemplatesPagedResult.yaml"
    BatchDeleteMetersByStrategy:
      $ref: "./schemas/BatchDeleteMetersByStrategy.yaml"
    MeterRow:
      $ref: "./schemas/MeterRow.yaml"
    SelectMetersToBatchDeleteBySearchExpressionStrategy:
      $ref: "./schemas/SelectMetersToBatchDeleteBySearchExpressionStrategy.yaml"
    MeterFirstCommissioningDateUpdate:
      $ref: "./schemas/MeterFirstCommissioningDateUpdate.yaml"
    MeterCommandErrorBehavior:
      $ref: "./schemas/MeterCommandErrorBehavior.yaml"
    MeterCommandTimeoutBehaviour:
      $ref: "./schemas/MeterCommandTimeoutBehaviour.yaml"
    MeterCommandReadProfileConfiguration:
      $ref: "./schemas/MeterCommandReadProfileConfiguration.yaml"
    MeterCommandReadPqiConfiguration:
      $ref: "./schemas/MeterCommandReadPqiConfiguration.yaml"
    MeterCommandGroupStep:
      $ref: "./schemas/MeterCommandGroupStep.yaml"
    MeterCommandGroup:
      $ref: "./schemas/MeterCommandGroup.yaml"
    SearchMeterCommandGroup:
      $ref: "./schemas/SearchMeterCommandGroup.yaml"
    MeterCommandGroupHeader:
      $ref: "./schemas/MeterCommandGroupHeader.yaml"
    PagedMeterCommandGroups:
      $ref: "./schemas/PagedMeterCommandGroups.yaml"
    MeterCommandPermissionTypeCode:
      $ref: "./schemas/MeterCommandPermissionTypeCode.yaml"
    MeterCommandPermission:
      $ref: "./schemas/MeterCommandPermission.yaml"
    SearchExpressionMeter:
      $ref: "./schemas/SearchExpressionMeter.yaml"
    SearchExpressionMeterCommandGroupPropertyAvailableValues:
      $ref: "./schemas/SearchExpressionMeterCommandGroupPropertyAvailableValues.yaml"
    SearchExpressionMeterCommandGroup:
      $ref: "./schemas/SearchExpressionMeterCommandGroup.yaml"
    SortInfoMeterCommandGroup:
      $ref: "./schemas/SortInfoMeterCommandGroup.yaml"
    CreateMeterCommandGroupStep:
      $ref: "./schemas/CreateMeterCommandGroupStep.yaml"
    CreateMeterCommandGroup:
      $ref: "./schemas/CreateMeterCommandGroup.yaml"
    UpdateMeterCommandGroup:
      $ref: "./schemas/UpdateMeterCommandGroup.yaml"
    UpdateMeterCommandGroupStep:
      $ref: "./schemas/UpdateMeterCommandGroupStep.yaml"
    BatchDeleteMeterComponents:
      $ref: "./schemas/BatchDeleteMeterComponents.yaml"
    GetMeterCommandGroupsRelatedToCommandQuery:
      $ref: "./schemas/GetMeterCommandGroupsRelatedToCommandQuery.yaml"
    MeterRelationsToMeterComponent:
      $ref: "./schemas/MeterRelationsToMeterComponent.yaml"
    "PagedMeterCommandGroupsRelatedToCommand":
      $ref: "./schemas/PagedMeterCommandGroupsRelatedToCommand.yaml"
    MeterApprovedAfter:
      $ref: "./schemas/MeterApprovedAfter.yaml"
    RelativeTime:
      $ref: "./schemas/RelativeTime.yaml"
    StartTimeForMeterBatch:
      $ref: "./schemas/StartTimeForMeterBatch.yaml"
    CreateMeterJob:
      $ref: "./schemas/CreateMeterJob.yaml"
    UpdateMeterJob:
      $ref: "./schemas/UpdateMeterJob.yaml"
    MeterJob:
      $ref: "./schemas/MeterJob.yaml"
    SearchMeterJobs:
      $ref: "./schemas/SearchMeterJobs.yaml"
    MeterJobsPagedResult:
      $ref: "./schemas/MeterJobsPagedResult.yaml"
    SearchExpressionMeterBatchesPropertyAvailableValues:
      $ref: "./schemas/SearchExpressionMeterBatchesPropertyAvailableValues.yaml"
    SearchExpressionMeterBatches:
      $ref: "./schemas/SearchExpressionMeterBatches.yaml"
    CreateMeterBatchRelationForMeterBatch:
      $ref: "./schemas/CreateMeterBatchRelationForMeterBatch.yaml"
    SearchExpressionMeterCommands:
      $ref: "./schemas/SearchExpressionMeterCommands.yaml"
    SearchExpressionMeterCommandsPropertyAvailableValues:
      $ref: "./schemas/SearchExpressionMeterCommandsPropertyAvailableValues.yaml"
    SearchExpressionMeterComponents:
      $ref: "./schemas/SearchExpressionMeterComponents.yaml"
    SearchExpressionMeterComponentsPropertyAvailableValues:
      $ref: "./schemas/SearchExpressionMeterComponentsPropertyAvailableValues.yaml"
    SearchExpressionMeterConfigurations:
      $ref: "./schemas/SearchExpressionMeterConfigurations.yaml"
    SearchExpressionMeterConfigurationsPropertyAvailableValues:
      $ref: "./schemas/SearchExpressionMeterConfigurationsPropertyAvailableValues.yaml"
    SearchMeterCommandsSort:
      $ref: "./schemas/SearchMeterCommandsSort.yaml"
    SearchMeterComponentsSort:
      $ref: "./schemas/SearchMeterComponentsSort.yaml"
    SearchMeterConfigurationsSort:
      $ref: "./schemas/SearchMeterConfigurationsSort.yaml"
    MeterJobsSortInfo:
      $ref: "./schemas/MeterJobsSortInfo.yaml"
    SearchExpressionMeterJobs:
      $ref: "./schemas/SearchExpressionMeterJobs.yaml"
    SearchExpressionMeterJobsPropertyAvailableValues:
      $ref: "./schemas/SearchExpressionMeterJobsPropertyAvailableValues.yaml"
    ElectricityMeterPropertiesCreateUpdate:
      $ref: "./schemas/ElectricityMeterPropertiesCreateUpdate.yaml"
    RelatedMeter:
      $ref: "./schemas/RelatedMeter.yaml"
  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
