title: SearchExpression
type: object
description: Represents a single filter condition used when searching installation forms.
additionalProperties: false
required:
  - propertyName
  - compareOperator
properties:
  propertyName:
    allOf:
      - $ref: "./SearchPropertyName.yaml"
    description: The name of the property to filter on.
  compareOperator:
    allOf:
      - $ref: "./SearchCompareOperator.yaml"
    description: The comparison operator to apply to the property.
  valueToCompare:
    type: object
    nullable: true
    description: >
      The value to compare against the property.
      Must be null when using a unary operator (e.g., 'Empty', 'NotEmpty').
