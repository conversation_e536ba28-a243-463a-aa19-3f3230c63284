title: CreateMeteringPointsTemplateRequest
type: object
description: Data required to create a new metering point template.
additionalProperties: false
required:
  - name
  - supplyType
properties:
  name:
    $ref: "../../DataTypes/ShortString.yaml"
  description:
    $ref: "../../DataTypes/LongStringNullable.yaml"
  supplyType:
    $ref: "../../SupplyType.yaml"
  meteringPointsParentChildRelations:
    type: array
    maxItems: 50
    items:
      $ref: "./MeteringPointsParentChildRelation.yaml"
    description: List of metering point parent-child relations.
