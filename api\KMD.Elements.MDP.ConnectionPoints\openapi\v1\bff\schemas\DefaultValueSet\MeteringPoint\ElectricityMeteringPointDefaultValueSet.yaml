type: object
description: Model containing electricity attributes of Metering Point Default Value Set.
properties:
  netSettlementGroup:
      description: Net settlement group.
      allOf:
        - $ref: '../../DataTypes/LongStringNullable.yaml'
  powerSupplierGsrn:
    description: Power plant.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  capacity:
    description: Metering Point capacity.
    allOf:
      - $ref: '../../DataTypes/PositiveDecimalNullable.yaml'
  connectionType:
    description: Metering Point connection type.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  assetType:
    description: Asset type.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  disconnectionType:
    description: Disconnection type.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  productId:
    description: ID of the product.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  unitType:
    description: |-
      Indicates unit Type.
      | Code  | Description              | DanishTranslation               |
      | ----  | ------------------------ | --------------------------------|
      | AMP   | Ampere                   | Ampere                          |
      | K3    | kVArh                    | KiloVolt-Ampere reactive hour   |
      | KWH   | kWh                      | Kilowatt-hour                   |
      | KWT   | kW                       | Kilowatt                        |
      | MAW   | MW                       | Megawatt                        |
      | MWH   | MWh                      | Megawatt-hour                   |
      | TNE   | Tonne                    | metric ton                      |
      | Z03   | MVAr                     | MegaVolt-Ampere reactive power  |
      | Z14   | Danish Tariff code       | KT Tarifkode                    |
      | H87   | STK                      | Antal styk                      |
    type: [ 'string', 'null' ]
    pattern: "^(AMP|K3|KWH|KWT|MAW|MWH|TNE|Z03|Z14|H87)$"
    minLength: 2
    maxLength: 3
    example: "AMP"
