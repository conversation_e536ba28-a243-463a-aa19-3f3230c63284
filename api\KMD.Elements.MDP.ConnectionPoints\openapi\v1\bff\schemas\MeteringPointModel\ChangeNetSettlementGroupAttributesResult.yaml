type: object
description: |
  Extended metering point version.
additionalProperties: false
properties:
  virtualId:
    nullable: true
    description: Internal MP MDP identifier'.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  typeOfMeteringPoint:
    description: Mapped from DH 'typeOfMeteringPoint'.
    allOf:
      - $ref: '../MeteringPointModel/DataTypes/TypeOfMeteringPoint.yaml'
  netSettlementGroup:
    description: Mapped from DH 'netSettlementGroup'.
    allOf:
      - $ref: '../DataTypes/LongStringNullable.yaml'
  powerPlantDomainLocationId:
    description: Is mapped to `PowerPlantDomainLocationId`
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  connectionType:
    description: Mapped from DH 'connectionType'.
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  assetType:
    description: Mapped from DH 'assetType'.
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  mpCapacity:
    description: Is mapped to `MPCapacity`
    allOf:
      - $ref: '../DataTypes/PositiveDecimal.yaml'
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
