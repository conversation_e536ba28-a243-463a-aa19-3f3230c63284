title: ConnectionPointTemplateDetails
type: object
description: Connection point template details.
additionalProperties: false
required:
  - id
  - name
properties:
  id:
    $ref: "../../DataTypes/Guid.yaml"
  name:
    $ref: "../../DataTypes/ShortString.yaml"
  description:
    $ref: "../../DataTypes/LongStringNullable.yaml"
  supplyType:
    $ref: "../../SupplyType.yaml"
  connectionPointDefaultValueSet:
    $ref: "./ConnectionPointDefaultValueSetDetails.yaml"
  meterFrameDefaultValueSets:
    type: array
    maxItems: 50
    items:
      $ref: "../DefaultValueSet.yaml"
    description: List of meter frame default value sets.
  meterFrameTemplates:
    type: array
    maxItems: 50
    items:
      $ref: "../MeterFrameTemplate/MeterFrameTemplateDetails.yaml"
    description: List of meter frame default value sets.
  meteringPointsDefaultValueSets:
    type: array
    maxItems: 50
    items:
      $ref: "../MeteringPointsTemplate/MeteringPointDefaultValueSetDetails.yaml"
    description: List of metering points default value sets.
  meteringPointsTemplates:
    type: array
    maxItems: 50
    items:
      $ref: "../MeteringPointsTemplate/MeteringPointsTemplateDetails.yaml"
    description: List of meter frame default value sets.
  parentChildRelations:
    type: array
    maxItems: 50
    items:
      $ref: "./ConnectionPointParentChildRelationDetails.yaml"
    description: List of metering points parent-child relation objects.
