title: SortBy
type: object
description: Defines a sorting rule to order the results of a search query.
additionalProperties: false
required:
  - propertyName
  - sortingDirection
properties:
  propertyName:
    allOf:
      - $ref: "./SortPropertyName.yaml"
    nullable: false
    description: The name of the property to sort by.
  sortingDirection:
    allOf:
      - $ref: "./SortingDirection.yaml"
    nullable: false
    description: The direction in which to sort the results (e.g., ascending or descending).
