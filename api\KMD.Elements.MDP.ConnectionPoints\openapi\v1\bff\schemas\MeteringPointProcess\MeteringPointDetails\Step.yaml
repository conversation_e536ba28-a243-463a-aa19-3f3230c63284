type: object
description: Process step.
additionalProperties: false
properties:
  type:
    $ref: "./StepType.yaml"
  actionType:
    $ref: "./ActionType.yaml"
  timeStamp:
    allOf:
      - $ref: "../../DataTypes/DateTime.yaml"
    description: Date time when the process step was last updated.
    nullable: true
  status:
    $ref: "../../ProcessModel/StepStatus.yaml"
  errorMessage:
    allOf:
      - $ref: "../../DataTypes/LongStringNullable.yaml"
    description: Error message for subprocess.
    example: "Error message"
  requestAttributes:
    type: array
    description: Array of step attributes objects.
    items:
      $ref: "./RequestAttributes.yaml"
    maxItems: 256
  mpmProcessId:
    allOf:
      - $ref: "../../DataTypes/GuidNullable.yaml"
    description: Id of MPM process.
    nullable: true
