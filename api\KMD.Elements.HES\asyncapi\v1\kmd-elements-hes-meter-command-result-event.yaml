asyncapi: 2.6.0
id: https://async.api.kmdelements.com/hes-meter-command-result-event/
info:
  title: HES Meter Command Result Event
  x-maintainers: Team-DP-2
  version: "0.0.2-preview"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    Schema for HES Meter Commands results.
    The command results will be returned by Meter Data Reader component which is responsible for communicating with the meters.

tags:
  - name: Team-DP-2
    description: Maintained by Team-DP-2

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.event.hes.meter-command-result.v1:
    description: Topic with messages containing results of meter commands
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Meter Command result
      description: Contains result of a performed meter command
      operationId: meterCommandCommand
      message:
        $ref: "#/components/messages/MeterCommandResultEvent"
  kmd.elements.{tenantId}.event.hes.meter-command-failed-result.v1:
    description: Topic with messages containing errors of failed meter commands
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Meter Command failed result
      description: Contains result and error of a performed and failed meter command
      operationId: meterCommandFailedCommand
      message:
        $ref: "#/components/messages/MeterCommandFailedResultEvent"

components:
  messages:
    MeterCommandResultEvent:
      name: Meter Command Result
      title: Result of a performed meter command on a meter
      summary: Command result contains data like meter readings, meter instant values, status of performed connect/disconnect command etc.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/MeterCommandResultEventPayload"
      headers:
        $ref: "#/components/schemas/MessageHeaders"
    MeterCommandFailedResultEvent:
      name: Failed Meter Command result
      title: Result and error log of a performed and failed meter command on a meter
      summary: Command result contains data like, reason of fail, stacktrace etc.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/MeterCommandFailedResultEventPayload"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    MeterCommandResultEventPayload:
      title: Meter command result event payload
      name: Meter command result event payload
      type: object
      additionalProperties: false
      required:
        - meterId
        - commandType
      properties:
        meterId:
          type: integer
          format: int64
          description: Meter ID
        commandType:
          type: string
          description: Meter command type
        getMeterReadingsCommandResult:
          $ref: "#/components/schemas/GetMeterReadingsCommandResultEvent"
        getMeterInstantValuesCommandResult:
          $ref: "#/components/schemas/GetMeterInstantValuesCommandResultEvent"

    GetMeterReadingsCommandResultEvent:
      title: Get meter readings command result
      name: Get meter readings command result
      type: object
      additionalProperties: false
      required:
        - meterClockData
      properties:
        readings:
          type: array
          description: List of readings from the meter
          items:
            $ref: "#/components/schemas/Reading"
        meterClockData:
          $ref: "#/components/schemas/MeterClockData"

    GetMeterInstantValuesCommandResultEvent:
      title: Get meter instant values command result
      name: Get meter instant values command result
      type: object
      additionalProperties: false
      required:
        - meterClockData
      properties:
        instantValues:
          type: array
          description: List of instant values from the meter
          items:
            $ref: "#/components/schemas/InstantValue"
        meterClockData:
          $ref: "#/components/schemas/MeterClockData"

    MeterCommandFailedResultEventPayload:
      title: Failed meter command result event payload
      name: Failed meter command result event payload
      type: object
      additionalProperties: false
      required:
        - meterId
        - meterCommand
        - timestamp
        - errorMessage
      properties:
        meterId:
          type: integer
          format: int64
          description: Meter ID
        meterCommand:
          $ref: "#/components/schemas/MeterCommand"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the failed meter command
        errorMessage:
          type: string
          description: Error message of the failed meter command

    Reading:
      title: Reading
      type: object
      additionalProperties: false
      required:
        - timestamp
        - statusCode
      properties:
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the reading
        statusCode:
          type: integer
          format: int64
          description: Status code of the reading
        readingValues:
          type: array
          description: Values of the reading
          items:
            $ref: "#/components/schemas/ReadingValue"

    ReadingValue:
      title: ReadingValue
      type: object
      additionalProperties: false
      required:
        - obisLongName
        - value
        - scaler
        - unit
      properties:
        obisLongName:
          type: string
          description: OBIS long name of the reading register
        value:
          type: integer
          format: int64
          description: Value of the reading
        scaler:
          type: number
          format: double
          description: Scaler for the reading value
        unit:
          type: integer
          format: int32
          description: Unit of the reading value

    InstantValue:
      title: Instant value
      type: object
      additionalProperties: false
      required:
        - timestamp
        - obisLongName
      properties:
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the instant value
        obisLongName:
          type: string
          description: OBIS long name of the reading register
        value:
          type: integer
          format: int64
          description: Value of the reading
        scaler:
          type: number
          format: double
          description: Scaler of instant value
        unit:
          type: integer
          format: int32
          description: Unit of the reading value

    MeterCommand:
      title: MeterCommand
      name: MeterCommand
      type: object
      additionalProperties: false
      required:
        - commandType
      properties:
        commandType:
          type: string
          description: Meter command type
        getMeterReadingsCommand:
          $ref: "#/components/schemas/GetMeterReadingsCommand"
        getMeterInstantValuesCommand:
          $ref: "#/components/schemas/GetMeterInstantValuesCommand"

    GetMeterInstantValuesCommand:
      title: Get meter instant values command
      name: Get meter instant values command
      type: object
      additionalProperties: false
      required:
        - readingTypes
      properties:
        readingTypes:
          type: array
          description: Set of reading types (OBIS names of registers)
          items:
            type: string

    GetMeterReadingsCommand:
      title: Get meter readings command
      name: Get meter readings command
      type: object
      additionalProperties: false
      required:
        - readingTypes
        - timeSchedule
      properties:
        readingTypes:
          type: array
          description: Set of reading types (OBIS names of registers)
          items:
            type: string
        timeSchedule:
          $ref: "#/components/schemas/TimeSchedule"

    MeterClockData:
      title: Meter clock data
      type: object
      additionalProperties: false
      required:
        - clockOffset
        - clockOffsetOutOfTolerance
      properties:
        clockOffset:
          type: integer
          format: int64
          description: Meter clock offset in milliseconds
        clockOffsetOutOfTolerance:
          type: boolean
          description: Indicate if meter clock offset value is bigger than tolerance and meter clock should be corrected

    TimeSchedule:
      title: Time schedule
      name: Time schedule
      type: object
      additionalProperties: false
      required:
        - start
      properties:
        start:
          format: date-time
          type: string
        end:
          format: date-time
          type: string

  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
