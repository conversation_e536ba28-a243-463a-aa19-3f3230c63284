type: object
description: Register Requirement default value set.
additionalProperties: false
required:
  - id
  - name
properties:
  id:
    allOf:
      - $ref: "../../DataTypes/Guid.yaml"
    description: Default Value Set ID.
  name:
    allOf:
      - $ref: "../../DataTypes/ShortString.yaml"
    description: Default Value Set name.
  meteringComponentId:
    description: |-
      DK: MålingsKomponentId.
      Shared value list with Meter domain.
      The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
      It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
    allOf:
      - $ref: "../../DataTypes/GuidNullable.yaml"
  meteringComponentName:
    description: Metering component name from value list
    allOf:
      - $ref: "../../DataTypes/ShortStringNullable.yaml"
