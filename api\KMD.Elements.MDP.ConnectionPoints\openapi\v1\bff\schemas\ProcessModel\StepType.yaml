type: string
enum:
  - ConnectionPointCreate
  - MeteringPointCreate
  - MeteringPointUpdate
  - MeterFrameCreate
  - Meter<PERSON>rameUpdate
  - RegisterRequirementCreate
  - RegisterRequirementUpdate
  - MeteringPointChangeFormulaAndSubtype
  - MeteringPointConnect
  - MeteringPointChangeAddress
  - MeteringPointChangeSettlementMethod
  - MeteringPointCloseDown
  - MeteringPointChangeNetSettlementGroupAttributes
  - ConnectionPointUpdateMasterData
  - WorkOrderCreate
  - StopRegisterRequirement
  - MeteringPointRemoveParentRelation
  - ManuallyHandledProcess
  - CancelProcess
  - MeteringPointChangeFormula
description: The step type.
example: "MeteringPointCreate"
