type: object
description: Value list value model.
additionalProperties: false
required:
  - id
  - displayValue
  - attributes
properties:
  id:
    $ref: '../DataTypes/Guid.yaml'
  displayValue:
    description: Value List Value Display Value.
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
    nullable: false
  attributes:
    type: array
    maxItems: 1000000
    description: Attributes collection.
    nullable: false
    items:
      $ref: './ValueListAttribute.yaml'
