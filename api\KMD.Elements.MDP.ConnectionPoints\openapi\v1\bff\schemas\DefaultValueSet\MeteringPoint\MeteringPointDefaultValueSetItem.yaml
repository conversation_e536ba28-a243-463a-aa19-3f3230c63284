type: object
description: Metering Point default value set.
additionalProperties: false
required:
  - id
  - name
  - typeOfMeteringPoint
properties:
  id:
    allOf:
      - $ref: '../../DataTypes/Guid.yaml'
    description: Default Value Set ID.
  name:
    allOf:
      - $ref: '../../DataTypes/ShortString.yaml'
    description: Default Value Set name.
    nullable: false
  typeOfMeteringPoint:
    description: Type of Metering Point.
    allOf:
      - $ref: '../../MeteringPointModel/DataTypes/TypeOfMeteringPoint.yaml'
    nullable: false
  subTypeOfMeteringPoint:
    description: Sub type of Metering Point.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
