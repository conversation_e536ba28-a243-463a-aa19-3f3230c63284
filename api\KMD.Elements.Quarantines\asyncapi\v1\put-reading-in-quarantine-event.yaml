asyncapi: 2.6.0
id: https://async.api.kmdelements.com/put-reading-in-quarantine-event/
info:
  title: Put reading in quarantine
  x-maintainers: Team-AU-2
  version: "0.0.2"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    Schema for Put reading in quarantine.
    The event will be consumed by Quarantines component which is responsible for managing of quarantines.

tags:
  - name: Team-AU-2
    description: Maintained by Team-AU-2

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.event.quarantine.reading-put.v1:
    description: Topic with messages containing events for quarantines
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    subscribe:
      summary: Event for putting reading in quarantine
      description: Contains all data required to put reading in quarantine
      operationId: putReadingInQuarantine
      message:
        $ref: "#/components/messages/PutReadingInQuarantineEvent"

components:
  messages:
    PutReadingInQuarantineEvent:
      name: PutReadingInQuarantineEvent
      title: Event for putting reading in quarantine
      summary: Contains all data required to put reading in quarantine.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/PutReadingInQuarantinePayload"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    ReadingIdentifier:
      title: ReadingIdentifier
      type: object
      required:
        - meterNumber
        - timestamp
        - registerId
        - meterId
      properties:
        timestamp:
          type: string
          description: Point in time when readings was measured (for interval this is end of interval)
          format: date-time
        meterNumber:
          type: string
          description: Number of the meter.
        registerId:
          type: string
          description: ID of the register.
        meterId:
          type: string
          description: ID of the meter.

    PutReadingInQuarantinePayload:
      title: Put reading in quarantine payload
      type: object
      additionalProperties: false
      required:
        - readingId
        - supplyType
        - validationRuleType
        - validationRuleId
        - createdAt
      properties:
        readingId:
          $ref: '#/components/schemas/ReadingIdentifier'
        supplyType:
          $id: supplyType
          type: string
          description: Type of supply
          enum:
            - ELECTRICITY
        validationRuleType:
          $id: validationRuleType
          type: string
          description: Failed validation rule type.
          enum:
            - LESS_THAN_PREVIOUS
        validationRuleId:
          type: string
          description: Failed validation rule id.
        createdAt:
          type: string
          format: date-time
          description: Date and timestamp when the notification was created.
      examples:
        [
          {
            readingId: {
              "meterNumber": "1234567890",
              "timestamp": "2023-10-01T10:00:00Z",
              "registerId": "8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d",
              "meterId": "8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d"
            },
            "supplyType": "ELECTRICITY",
            "validationRuleType": "LESS_THAN_PREVIOUS",
            "validationRuleId": "8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d",
            "createdAt": "2023-10-01T10:00:00Z"
          }
        ]

  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
