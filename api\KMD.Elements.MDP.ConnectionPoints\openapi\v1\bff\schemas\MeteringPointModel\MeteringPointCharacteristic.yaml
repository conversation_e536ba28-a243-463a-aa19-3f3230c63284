type: object
description: The characteristic of the metering point
additionalProperties: false
properties:
  settlementMethod:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `SettlementMethod`
    pattern: ^(D01|E01|E02)$
  physicalStatusOfMeteringPoint:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `PhysicalStatusOfMeteringPoint`
    pattern: ^(D02|D03|E22|E23)$
  scheduledMeterReadingDate:
    nullable: true
    type: array
    maxItems: 9999
    description: Is mapped to `ScheduledMeterReadingDate`
    pattern: ^\d{4}$
    items:
      type: string
      maxLength: 4
      pattern: ""
  readingCharacteristics:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `MPReadingCharacteristics`
    pattern: ^(D01|D02)$
  typeOfMeteringPoint:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `TypeOfMeteringPoint`
    pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D21|D22|D23|D24|D25|D26|D27|D28|D29|D30|D99|E17|E18|E20)$
  subTypeOfMeteringPoint:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `SubTypeOfMeteringPoint`
    pattern: ^(D01|D02|D03)$
  meterReadingOccurrence:
    nullable: true
    type: string
    maxLength: 5
    description: Duration in ISO8601
    pattern: ^(-?)P(?=\d|T\d)(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)([DW]))?(?:T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?)?$
  hourlyTimeSeries:
    nullable: true
    type: boolean
    description: Is mapped to `HourlyTimeSeries`
  netSettlementGroup:
    nullable: true
    type: string
    maxLength: 256
    description: Is mapped to `NetSettlementGroup`
    pattern: ""
  limitationContractedCapacityCharacteristics:
    description: Is mapped to `LimitationContractedCapacityCharacteristics`
    $ref: "./ContractedCapacityCharacteristics.yaml"
    nullable: true
  meteringGridAreaUsedDomainLocationId:
    description: Is mapped to `MeteringGridAreaUsedDomainLocationId`
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    nullable: true
  toGridDomainLocationId:
    description: Is mapped to `ToGridDomainLocationId`
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    nullable: true
  fromGridDomainLocationId:
    description: Is mapped to `FromGridDomainLocationId`
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    nullable: true
  productionObligation:
    nullable: true
    type: boolean
    description: Is mapped to `ProductionObligation`
  powerPlantDomainLocationId:
    description: Is mapped to `PowerPlantDomainLocationId`
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    nullable: true
  locationDescription:
    nullable: true
    type: string
    description: Is mapped to `LocationDescription`
    maxLength: 60
    pattern: ""
  includedProductCharacteristic:
    description: Is mapped to `IncludedProductCharacteristic`
    $ref: "./ProductCharacteristic.yaml"
  disconnectionType:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `DisconnectionType`
    pattern: ^(D01|D02)$
  connectionType:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `MPConnectionType`
    pattern: ^(D01|D02)$
  capacityInKiloWatts:
    description: Is mapped to `MPCapacity`
    allOf:
      - $ref: '../DataTypes/PositiveDecimalNullable.yaml'
  assetType:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `AssetType`
    pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D99)$
