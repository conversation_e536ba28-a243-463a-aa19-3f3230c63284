type: object
description: |
  Electricity attributes for metering point.
additionalProperties: false
properties:
  assetType:
    description: Mapped from DH 'MeteringPoint' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  connectionStatus:
    description: Mapped from DH 'connectionStatus' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  connectionType:
    description: Mapped from DH 'connectionType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  fromGrid:
    description: Mapped from DH 'fromNet' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  maximumCurrent:
    description: Mapped from DH 'maximumCurrent' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/PositiveDecimalNullable.yaml'
  maximumPower:
    description: Mapped from DH 'maximumPower' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/PositiveDecimalNullable.yaml'
  netSettlementGroup:
    description: Mapped from DH 'netSettlementGroup' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  powerSupplierGsrn:
    description: Mapped from DH 'powerSupplierGsrn' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  productId:
    description: Mapped from DH 'productId' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  productionCapacityInKiloWatts:
    type: number
    nullable: true
    description: Mapped from DH 'capacityInKiloWatts' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    format: decimal
    minimum: 0
    maximum: 99999999.9
  productionObligation:
    description: Mapped from DH 'productionObligation' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    nullable: true
    allOf:
      - $ref: '../../DataTypes/Boolean.yaml'
  remoteReadable:
    description: Mapped from DH 'remoteReadable' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  scheduledMeterReadingDates:
    description: Mapped from DH 'scheduledMeterReadingDate' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    type: array
    minItems: 0
    maxItems: 10000
    items:
      $ref: '../../DataTypes/ShortString.yaml'
  settlementMethod:
    description: Mapped from DH 'settlementMethod' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  toGrid:
    description: Mapped from DH 'toNet' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
