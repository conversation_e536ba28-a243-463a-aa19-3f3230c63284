type: object
additionalProperties: false
description: Water attributes model.
required:
  - connectionPointCategoryValue
  - installationTypeValue
  - connectionStatus
  - temporary
properties:
  connectionPointCategoryValue:
    description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  installationTypeValue:
    description: Defines type of Installation. Eg. For apartments, single households, Industrial, Agricultural.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  connectionStatus:
    description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  temporary:
    type: boolean
    description: Set to true, if the Connection Point is temporary.
  temporaryUntil:
    description: When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual grid company.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  installationDescription:
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
    description: This information comes from the "Installationsblanket". It contains the installer's note from the installation form if applicable.
  flexAttributeObject:
    description: An UUID reference to a Settlement Object that is used to register flexible attributes about the entity.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  decommissioned:
    description: Decommissioned date.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
