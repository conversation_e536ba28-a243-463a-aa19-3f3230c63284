type: object
description: Technical Metering Point Version Formula Parameter.
additionalProperties: false
required:
  - name
  - formulaParameterType
properties:
  name:
    description: The name of the formula parameter.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  formulaParameterType:
    $ref: '../MeteringPoint/DefaultValueSetsFormulaParameterType.yaml'
  description:
    description: A description of what data the formula parameter represents.
    allOf:
      - $ref: '../../DataTypes/DescriptionString.yaml'
    nullable: true
  typeOfMeteringPoint:
    description: Type of metering point value list reference, can refer 3 different VLs depending on supply type.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
