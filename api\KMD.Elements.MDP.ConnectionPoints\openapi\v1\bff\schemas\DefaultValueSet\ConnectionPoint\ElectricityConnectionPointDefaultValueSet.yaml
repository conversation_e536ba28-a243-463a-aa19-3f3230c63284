type: object
description: Model containing electricity attributes of Connection Point Default Value Set.
properties:
  connectionPointCategoryValueId:
    description: Categorization of a ConnectionPoint.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  installationTypeValueId:
    description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  consumerCategoryValueId:
    description: Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  netSettlementGroupValueId:
    description: This field register the net settlement group, which is also used in the market communication (DataHub).
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  gridAreaId:
    description: Grid area id.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  installationDescription:
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
    description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
