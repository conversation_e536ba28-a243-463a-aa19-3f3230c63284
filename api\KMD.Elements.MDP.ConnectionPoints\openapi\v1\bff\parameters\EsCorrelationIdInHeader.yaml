name: es-correlation-id
description: |
  This is used to "link" messages together. This can be supplied on a request, so
  that the client can correlate a corresponding reply message.
  The server will place the incoming X-Correlation-ID value as the X-Correlation-ID
  on the outgoing reply. If not supplied on the request, the X-Correlation-ID of the
  reply should be set to the value of the X-Message-ID that was used on the request, if present.
  Given that the X-Correlation-ID is used to ‘link’ messages together,
  it may be reused on more than one message.
in: header
schema:
  type: string
  format: uuid
required: true
example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d
