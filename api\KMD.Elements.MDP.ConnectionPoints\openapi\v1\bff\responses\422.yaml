description: Unprocessable Entity
content:
  application/problem+json:
    schema:
      description: Unprocessable Entity
      allOf:
        - $ref: "../schemas/ProblemDetailsWithErrors.yaml"
    examples:
      ForbiddenExample:
        value:
          type: https://errors.kmdelements.com/422
          title: Unprocessable Entity
          status: 422
          detail: Requirements were not met, check Errors for details.
          instance: /resouces-path/1
          errors:
            - errorCode: "YouDoNotHavePermissionsToDeleteARegister"
              errorMessageTemplate: "You do not have permissions to delete a register."
              errorMessage: "You do not have permissions to delete a register."
