title: ConnectionPointParentChildRelation
type: object
description: Represents connection point template relations between metering points Ids and register requirement objects Ids.
additionalProperties: false
required:
  - relationId
properties:
  relationId:
    $ref: "../../DataTypes/Guid.yaml"
  parentMeteringPointDefaultValueSetId:
    $ref: "../../DataTypes/GuidNullable.yaml"
  childMeteringPointDefaultValueSetId:
    $ref: "../../DataTypes/GuidNullable.yaml"
  registerRequirementDefaultValueSetId:
    $ref: "../../DataTypes/GuidNullable.yaml"
  meterFrameTemplateId:
    $ref: "../../DataTypes/GuidNullable.yaml"
