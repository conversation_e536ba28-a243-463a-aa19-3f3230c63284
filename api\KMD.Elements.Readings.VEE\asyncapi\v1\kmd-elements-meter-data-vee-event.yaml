asyncapi: 2.6.0
id: https://async.api.kmdelements.com/meter-data-vee-event/
info:
  title: Meter data VEE event
  x-maintainers: Team-DP-1
  version: "0.1.2-preview"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    Async API for meter data event needed for VEE process

tags:
  - name: Team-DP-1
    description: Maintained by

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.event.meter-data.vee.v1:
    description: Topic with meter data required by VEE process, grouped by meter number
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Event with latest version of meter data elements
      description: Event are structured for VEE process, grouped by meter number key and all it's corresponding structures
      operationId: MeterDataVeeEvent
      message:
        $ref: "#/components/messages/VeeMeterEvent"

components:
  messages:
    VeeMeterEvent:
      name: VeeMeterEvent
      title: VeeMeterEvent
      summary: Event with meter data structure for VEE process
      contentType: application/json
      bindings:
        kafka:
          key:
            type: string
            description: meterId with compaction off
      payload:
        $ref: "#/components/schemas/VeeMeterEventPayload"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    VeeMeterEventPayload:
      title: VeeMeterEventPayload
      type: object
      additionalProperties: false
      required:
        - meterNumber
        - meterId
        - veeMeterVersions
      properties:
        meterNumber:
          type: string
          description: This is needed for spark internal processing. We partition data by meterNumber when processing.
        meterId:
          type: string
          description: guid
        veeMeterVersions:
          type: array
          items:
            $ref: "#/components/schemas/VeeMeterVersion"

    VeeMeterVersion:
      type: object
      additionalProperties: false
      description: |
        Unique for given timestamp, hesId and meterNumber version of meter for which readings were received (guaranteed by publisher). If any property listed in this file changes new version of VEEMeterVersion should be created.
      required:
        - applicableAtOrAfter
        - applicableBefore
        - connectionPoint
        - meterFrame
        - registers
      properties:
        applicableAtOrAfter:
          type: string
          format: date-time
          pattern: "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.\\d{3}Z$"
          examples:
            - 2020-01-11T00:00:00.000Z
        applicableBefore:
          type: string
          format: date-time
          pattern: "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.\\d{3}Z$"
          examples:
            - 9999-12-31T23:59:59.999Z
            - 2023-01-12T00:00:00.000Z
        hesId:
          type: string
          examples:
            - HES7
        meterType:
          type: string
          examples:
            - Type 123
        connectionPoint:
          type: object
          $ref: "#/components/schemas/ConnectionPoint"
        meterFrame:
          type: object
          $ref: "#/components/schemas/MeterFrame"
        registers:
          type: array
          description: |
            List of registers existing for this version of VEEMeter. Empty if there are no registers. When register is added or removed, new version of VEEMeter should be created.
          items:
            $ref: "#/components/schemas/Register"

    ConnectionPoint:
      type: object
      title: ConnectionPoint
      additionalProperties: false
      required:
        - id
      properties:
        id:
          type: string
          description: guid, for finding matching meteringpoints in 2nd spark application
          examples:
            - 304413e4-da8b-43bd-861d-ec2f894fc67f
        consumerCategory:
          type: string
          examples:
            - Chemical industry
        netSettlementGroup:
          type: string
          examples:
            - Group 6
        installationTypeValue:
          type: string
          examples:
            - Industry
        categoryValue:
          type: string
          examples:
            - Industry
    MeterFrame:
      type: object
      title: MeterFrame
      additionalProperties: false
      required:
        - id
      properties:
        id:
          type: string
          description: guid, for persisting readings
        purpose:
          type: string
          examples:
            - Measurement
        tariff:
          type: string
          examples:
            - A hoj >50 kV
    Register:
      type: object
      title: Register
      additionalProperties: false
      required:
        - id
        - meteringComponent
        - measuringUnit
        - meterReadingType
      properties:
        id:
          type: string
          description: guid, for persisting readings
        meteringComponent:
          type: string
          $id: RegisterType
          enum:
            - A_PLUS
            - A_MINUS
            - R_PLUS
            - R_MINUS
            - UNSUPPORTED_VALUE
        measuringUnit:
          type: string
          $id: Unit
          description: "Indicates the base unit of measurement, without any scale or prefix"
          enum:
            - Wh
            - VArh
            - UNSUPPORTED_UNIT
        meterReadingType:
          type: string
          $id: ReadingType
          enum:
            - ACCUMULATIVE
            - INTERVAL
            - INSTANT
  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
