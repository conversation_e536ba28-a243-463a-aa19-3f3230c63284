InternalEquipment:
  title: InternalEquipment
  type: object
  additionalProperties: false
  description: Internal Equipment model.
  required:
    - entityId
    - installationPossibility
    - equipmentNumber
    - equipmentType
    - vendor
    - equipmentNotes
  properties:
    # - REQUIRED FOR BUSINESS EVENTS
    entityId:
      description: Internal Equipment Entity Id.
      type: string
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
    # - END OF REQUIRED FOR BUSINESS EVENTS
    # - ROOT ENTITY FIELDS
    installationPossibility:
      description: Installation Possibility
      type: string
      pattern: "^(All|ConnectionPoint|MeterFrame)$"
      example: All
    equipmentNumber:
      description: Equipment number.
      type: string
      pattern: "^.*$"
      minLength: 1
      maxLength: 128
      example: 'Max. 128 characters long string with spaces.'
    equipmentType:
      description: Equipment type.
      type: string
      pattern: "^.*$"
      minLength: 1
      maxLength: 128
      example: 'Max. 128 characters long string with spaces.'
    vendor:
      description: Vendor.
      type: string
      pattern: "^.*$"
      minLength: 1
      maxLength: 128
      example: 'Max. 128 characters long string with spaces.'
    description:
      description: Description.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 128
      example: 'Max. 128 characters long string with spaces.'
    flexAttributeObject:
      description: Flex attribute object.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130002
    equipmentNotes:
      description: Internal Equipment Notes.
      type: array
      minItems: 0
      maxItems: 1000000
      items:
        $ref: "./sub-schemas/InternalEquipmentNote.schema.yaml#/InternalEquipmentNote"
