openapi: 3.0.0
info:
  title: KMD.Elements.Customers.BFF
  description: KMD Elements - Customers BFF - dedicated BFF for Customers part of KMD.Elements.Customers
  termsOfService: https://www.kmd.net/terms-of-use
  contact:
    name: KMD Elements
    url: https://www.kmd.net/solutions/kmd-elements
    email: <EMAIL>
  license:
    name: License
    url: https://www.kmd.net/terms-of-use
  version: '1.3'
  x-maintainers: Team-SE-2
servers:
- url: https://localhost:9277
  description: Local environment
tags:
  - name: VatValidation
    description: Related to VAT validation.
paths:
  /api/address-integration:
    get:
      x-authorization: Customers.Read
      tags:
      - AddressIntegration
      operationId: AddressIntegration_GetAddresses
      parameters:
      - name: isoCountryCode
        in: query
        required: true
        schema:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
      - name: addressQuery
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: 'Addresses autocomplete lookup list.'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AddressIntegrationQueryAutocompleteResult'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/address-types:
    get:
      x-authorization: Customers.Read
      tags:
      - AddressTypes
      operationId: AddressTypes_GetAddressTypes
      responses:
        200:
          description: 'Address types.'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AddressTypeModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customer-configuration:
    get:
      x-authorization: Customers.Read
      tags:
      - CustomerConfiguration
      operationId: CustomerConfiguration_GetCustomerConfiguration
      responses:
        200:
          description: 'Customer configuration.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerConfigurationModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers/search:
    post:
      x-authorization: Customers.Read
      tags:
      - Customers
      operationId: Customers_Search
      summary: Search for customer.
      requestBody:
        description: Search request body.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BffListQueryModelWithCsvConfiguration'
      responses:
        "200":
          description: 'Customers list.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomersSearchActionResult'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers/commands/export:
    post:
      x-authorization: Customers.Read
      tags:
      - Customers
      operationId: Customers_Export
      requestBody:
        description: Export request body.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BffListQueryModelWithCsvConfiguration'
      responses:
        200:
          description: 'Customers export csv file.'
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers/{customerId}:
    get:
      x-authorization: Customers.Read
      tags:
      - Customers
      operationId: Customers_GetCustomer
      parameters:
      - name: customerId
        in: path
        required: true
        schema:
          type: string
          format: guid
      responses:
        200:
          description: Details of requested customer.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDetailsModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
    delete:
      x-authorization: Customers.Write
      tags:
      - Customers
      operationId: Customers_Delete
      parameters:
      - name: customerId
        in: path
        required: true
        schema:
          type: string
          format: guid
      responses:
        "204":
          $ref: "#/components/responses/204"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
    put:
      x-authorization: Customers.Write
      tags:
      - Customers
      operationId: Customers_UpdateCustomer
      parameters:
      - name: customerId
        in: path
        required: true
        schema:
          type: string
          format: guid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerModel'
        required: true
      responses:
        "204":
          $ref: "#/components/responses/204"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers/{customerId}/obfuscated:
    get:
      x-authorization: Customers.Read
      tags:
      - Customers
      operationId: Customers_GetObfuscatedCustomer
      parameters:
      - name: customerId
        in: path
        required: true
        schema:
          type: string
          format: guid
      responses:
        200:
          description: 'Obfuscated details of requested customer.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ObfuscatedCustomerDetailsModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers:
    post:
      x-authorization: Customers.Write
      tags:
      - Customers
      operationId: Customers_CreateCustomer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerModel'
        required: true
      responses:
        "201":
          $ref: "#/components/responses/201"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers/{customerId}/cpr:
    get:
      x-authorization: Cpr.Read
      tags:
      - Customers
      operationId: Customers_GetCustomerCprNumbers
      parameters:
      - name: customerId
        in: path
        required: true
        schema:
          type: string
          format: guid
      responses:
        200:
          description: 'Customer CPR Model'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerCprModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
    put:
      x-authorization: Customers.Write
      tags:
      - Customers
      operationId: Customers_UpdateCustomerCprNumbers
      parameters:
      - name: customerId
        in: path
        required: true
        schema:
          type: string
          format: guid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerCprNumbersModel'
        required: true
      responses:
        "204":
          $ref: "#/components/responses/204"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers/external-systems:
    get:
      x-authorization: Customers.Read
      tags:
      - Customers
      operationId: Customers_GetExternalSystems
      responses:
        200:
          description: 'Returns customer external systems.'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CustomerExternalSystemModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers/dk-industry-codes:
    get:
      x-authorization: Customers.Read
      tags:
      - Customers
      operationId: Customers_GetDkBranchCodes
      parameters:
      - name: nameOrCodeValue
        in: query
        schema:
          type: string
          nullable: true
      - name: parentCodeValue
        in: query
        schema:
          type: string
          nullable: true
      - name: dkIndustryCodeLevel
        in: query
        schema:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/DkIndustryCodeLevel'
        x-position: 3
      responses:
        200:
          description: 'DkIndustryCodeModel'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DkIndustryCodeModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers-integration/country-companies-registries/{isoCountryCode}/companies/{companyRegistryId}:
    get:
      x-authorization: Customers.Read
      tags:
      - CustomersIntegration
      operationId: CustomersIntegration_GetCompanyIntegration
      parameters:
      - name: isoCountryCode
        in: path
        required: true
        schema:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
      - name: companyRegistryId
        in: path
        required: true
        schema:
          type: string
      responses:
        200:
          description: 'CustomerCompanyIntegrationModel'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerCompanyIntegrationModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers-integration/country-persons-registries/{isoCountryCode}/persons/{personRegistryId}:
    get:
      x-authorization: Customers.Read
      tags:
      - CustomersIntegration
      operationId: CustomersIntegration_GetPersonIntegration
      parameters:
      - name: isoCountryCode
        in: path
        required: true
        schema:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
      - name: personRegistryId
        in: path
        required: true
        schema:
          type: string
      responses:
        200:
          description: 'CustomerPersonIntegrationModel'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerPersonIntegrationModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers-integration/country-persons-registries/{isoCountryCode}/persons-customerid/{customerId}:
    get:
      x-authorization: Customers.Write
      tags:
      - CustomersIntegration
      operationId: CustomersIntegration_GetPersonIntegrationByCustomerId
      parameters:
      - name: isoCountryCode
        in: path
        required: true
        schema:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
      - name: customerId
        in: path
        required: true
        schema:
          type: string
          format: uuid
      responses:
        200:
          description: 'CustomerPersonIntegrationModel'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerPersonIntegrationModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers-integration/country-companies-registries/supported-countries:
    get:
      x-authorization: Customers.Read
      tags:
      - CustomersIntegration
      operationId: CustomersIntegration_GetCompanyIntegrationSupportedCountries
      responses:
        200:
          description: 'List of supported countries for company integration'
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers-integration/country-persons-registries/supported-countries:
    get:
      x-authorization: Customers.Read
      tags:
      - CustomersIntegration
      operationId: CustomersIntegration_GetPersonIntegrationSupportedCountries
      responses:
        200:
          description: 'List of supported countries for person integration'
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/settlement-ownership:
    get:
      x-authorization: Customers.Read
      tags:
      - SettlementObjectOwnership
      operationId: SettlementObjectOwnership_GetSettlementObjectOwnerships
      parameters:
      - name: customerId
        required: true
        in: query
        schema:
          type: string
          format: guid
      responses:
        200:
          description: 'Settlement object ownerships.'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SettlementObjectOwnershipQueryResult'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/tenant-configuration:
    get:
      x-authorization: Customers.Read
      tags:
      - TenantConfiguration
      operationId: TenantConfiguration_GetCustomerTenantConfiguration
      responses:
        200:
          description: 'Current tenant configuration.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantConfigurationModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/tenant-configuration/change-reason:
    get:
      x-authorization: Customers.Read
      tags:
      - TenantConfiguration
      operationId: TenantConfiguration_GetChangeReasonConfiguration
      responses:
        200:
          description: 'Change reason configuration'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChangeReasonConfigurationDto'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
  /api/user-details:
    get:
      x-authorization: Customers.Read
      tags:
      - UserDetails
      operationId: UserDetails_GetUserDetails
      parameters:
      - name: userId
        required: true
        in: query
        schema:
          type: string
          format: guid
      responses:
        200:
          description: 'User details'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetailsQueryResults'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
  /api/customers/{customerId}/vat-status:
    get:
      x-authorization: VatValidationStatus.Read
      tags:
      - Customers
      operationId: Customers_GetVatStatus
      summary: Gets the VAT number status for the given customer ID
      description: Gets the VAT number status for the given customer ID
      parameters:
      - name: customerId
        description: Identifier of customer in KMD Elements.
        in: path
        required: true
        schema:
          type: string
          format: guid
        example: "23751a83-cae9-44eb-b3ff-50ca1e15c1b8"
      responses:
        200:
          description: 'VAT status'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetVatStatusResponseModel'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
      security:
      - Bearer: []
components:
  headers:
    RetryAfter:
      description: Number of seconds until you should try again.
      schema:
        type: integer
        format: int32
        minimum: 1
        maximum: 2678400 # 31 days
      required: true
      example: 3600 # 1 hour

  responses:
    "201":
      description: '201 Created - returns Id of the created resource.'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/IdModel'
    "204":
      description: 204 No Content - operation performed successfully with no content result.
    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ValidationProblemDetails"
          examples:
            BadRequestExample:
              value:
                type: "https://errors.kmdelements.com/400"
                title: Bad Request
                status: 400
                detail: "Invalid request"
                instance: "/resources/1"
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            UnauthorizedExample:
              value:
                type: "https://errors.kmdelements.com/401"
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: "/resources/1"
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ForbiddenExample:
              value:
                type: "https://errors.kmdelements.com/403"
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: "/resources/1"
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/404"
                title: Not Found
                status: 404
                detail: Not Found
                instance: "/resources/1"
    "409":
      description: Conflict - entity updated concurrently and/or incorrect row version passed and/or resource is conflicting with unique constraint.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ItsConflictingOrSomethingElseChangedIt:
              value:
                type: "https://errors.kmdelements.com/409"
                title: Conflict
                status: 409
                detail: Conflict
                instance: /resources-path/1
    "422":
      description: 404 Unprocessable.
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/ErrorDescription'
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          $ref: "#/components/headers/RetryAfter"
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            TooManyRequestsExample:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 429
                detail: Rate limit is exceeded.
                instance: "/resources/1"
    "499":
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ClientClosedRequestExample:
              value:
                type: "https://errors.kmdelements.com/499"
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: "/resources/1"
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            InternalServerErrorExample:
              value:
                type: "https://errors.kmdelements.com/500"
                title: Internal Server Error
                status: 500
                detail: "body.0.age: Value `Not Int` does not match format `int32`"
                instance: "/resources/1"
  schemas:
    ProblemDetails:
      type: object
      additionalProperties:
        nullable: true
      required:
      - extensions
      properties:
        type:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        status:
          type: integer
          format: int32
          nullable: true
        detail:
          type: string
          nullable: true
        instance:
          type: string
          nullable: true
        extensions:
          type: object
          additionalProperties: {}
    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1000
              additionalProperties:
                type: array
                description: Array of validation error messages.
                maxItems: 1000
                items:
                  type: string
                  maxLength: 2048
                  pattern: "^.*$"
              nullable: true
    AddressIntegrationQueryAutocompleteResult:
      type: object
      additionalProperties: false
      properties:
        text:
          type: string
          description: Text for autocomplete lookup (formatted address).
          nullable: true
        address:
          description: Address details.
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/AddressIntegrationQueryResult'
    AddressIntegrationQueryResult:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        streetName:
          type: string
          description: Street name.
          nullable: true
        streetCode:
          type: string
          description: Street code.
          nullable: true
        houseNumberId:
          type: string
          description: House number identifier.
          format: guid
          nullable: true
        houseNumber:
          type: string
          description: House number.
          nullable: true
        floor:
          type: string
          description: Floor information.
          nullable: true
        room:
          type: string
          description: Room information.
          nullable: true
        citySubDivision:
          type: string
          description: City sub division.
          nullable: true
        postalCode:
          type: string
          description: Postal code.
          nullable: true
        city:
          type: string
          description: City.
          nullable: true
        municipalityCode:
          type: string
          description: Municipality / City code from external system.
          nullable: true
        countryCode:
          type: string
          description: ISO CountryCode.
          nullable: false
        publicRegistryId:
          type: string
          description: >-
            External API address identification number.

            In Denmark - DAR ID.
          nullable: true
    AddressTypeModel:
      type: object
      additionalProperties: false
      required:
      - id
      - languageId
      properties:
        id:
          type: integer
          format: int32
        systemName:
          type: string
          nullable: true
        languageId:
          type: string
          format: guid
    CustomerConfigurationModel:
      type: object
      additionalProperties: false
      properties:
        changeReason:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ChangeReasonConfigurationDto'
        companyIntegration:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/CompanyIntegrationConfigurationModel'
        personIntegration:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/PersonIntegrationConfigurationModel'
        addressIntegration:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/AddressIntegrationConfigurationModel'
        vatEnabledByDefault:
          type: boolean
          nullable: true
        enableCheckVatStatus:
          type: boolean
          nullable: true
        enableDuplicateCprNumbers:
          type: boolean
          nullable: true
        enableDuplicateCvrNumbers:
          type: boolean
          nullable: true
        enableCprLookup:
          type: boolean
          description: 'Determines whether CprLookup button should be visible'
          example: false
          nullable: true
        subscribedToCvrEvents:
          type: boolean
          nullable: true
    ChangeReasonConfigurationDto:
      type: object
      additionalProperties: false
      properties:
        useChangeReason:
          type: string
          nullable: true
        maxCharacters:
          type: string
          nullable: true
    CompanyIntegrationConfigurationModel:
      type: object
      additionalProperties: false
      properties:
        supportedCountries:
          type: array
          nullable: true
          items:
            type: string
    PersonIntegrationConfigurationModel:
      type: object
      additionalProperties: false
      properties:
        supportedCountries:
          type: array
          nullable: true
          items:
            type: string
    AddressIntegrationConfigurationModel:
      type: object
      additionalProperties: false
      properties:
        supportedCountries:
          type: array
          nullable: true
          items:
            type: string
    CustomersSearchActionResult:
      type: object
      additionalProperties: false
      required:
      - result
      properties:
        result:
          $ref: '#/components/schemas/PagedResultOfCustomersWithColumnHeaders'
    PagedResultOfCustomersWithColumnHeaders:
      type: object
      additionalProperties: false
      required:
      - currentPage
      - pageCount
      - pageSize
      - rowCount
      - columnsHeaders
      properties:
        currentPage:
          type: integer
          format: int32
        pageCount:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        rowCount:
          type: integer
          format: int32
        maxCount:
          type: integer
          format: int32
          nullable: true
        results:
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/CustomerViewModel'
        columnsHeaders:
          type: array
          items:
            $ref: '#/components/schemas/DisplayedColumn'
    CustomerViewModel:
      type: object
      additionalProperties: false
      required:
      - id
      - protectedName
      - vat
      - isPerson
      - changedByUserId
      - changeDate
      - countryCode
      properties:
        id:
          type: string
          format: guid
        name:
          type: string
          nullable: true
        additionalName:
          type: string
          nullable: true
        protectedName:
          type: boolean
        attention:
          type: string
          nullable: true
        vat:
          type: boolean
        customerNumber:
          type: string
          nullable: true
        personCountryCode:
          type: string
          nullable: true
        dateOfBirth:
          type: string
          nullable: true
        cprNumber:
          type: string
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true
        companyCountryCode:
          type: string
          nullable: true
        cvrNumber:
          type: string
          nullable: true
        maintainAutomatically:
          type: boolean
          nullable: true
        comment:
          type: string
          nullable: true
        additionalCvrNumber:
          type: string
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        customerAddressId:
          type: string
          format: guid
          nullable: true
        protectedAddress:
          type: boolean
          nullable: true
        contactName:
          type: string
          nullable: true
        contactName2:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        mobile:
          type: string
          nullable: true
        addressId:
          type: string
          format: guid
          nullable: true
        streetName:
          type: string
          nullable: true
        streetCode:
          type: integer
          format: int32
          nullable: true
        houseNumber:
          type: string
          nullable: true
        floor:
          type: string
          nullable: true
        room:
          type: string
          nullable: true
        citySubDivision:
          type: string
          nullable: true
        postalCode:
          type: string
          nullable: true
        postOfficeBox:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: integer
          format: int32
          nullable: true
        countryCode:
          type: string
          nullable: false
        addressComment:
          type: string
          nullable: true
        addressType:
          type: integer
          format: int32
          nullable: true
        address:
          type: string
          nullable: true
        addressSort:
          type: string
          nullable: true
        addressPublicRegistryId:
          type: string
          nullable: true
        houseNumberId:
          type: string
          format: guid
          nullable: true
        companyStartDate:
          type: string
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          format: date-time
          nullable: true
        companyStatus:
          type: string
          nullable: true
        companyIndustryCodeName:
          type: string
          nullable: true
        companyIndustryCodeValue:
          type: string
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          nullable: true
        registrationNumber:
          type: string
          nullable: true
        isPerson:
          type: boolean
        externalIdentifiers:
          type: string
          nullable: true
        rowVersion:
          type: string
          format: byte
          nullable: true
        addressRowVersion:
          type: string
          format: byte
          nullable: true
        changedByUserId:
          type: string
          format: guid
        changeReason:
          type: string
          nullable: true
        changeDate:
          type: string
          format: date-time
    DisplayedColumn:
      type: object
      additionalProperties: false
      required:
      - propertyName
      - propertyNameTranslationKey
      - isVisible
      - isNotSortable
      - showTooltip
      - exportOnly
      - propertyNameTranslation
      - isNavigable
      properties:
        propertyName:
          type: string
        propertyNameTranslationKey:
          type: string
        sortPropertyName:
          nullable: true
          type: string
        isVisible:
          type: boolean
        isNotSortable:
          type: boolean
        showTooltip:
          type: boolean
        exportOnly:
          type: boolean
        propertyNameTranslation:
          type: string
        isNavigable:
          type: boolean
        id:
          type: string
          format: guid
          nullable: true
    BffListQueryModelWithCsvConfiguration:
      type: object
      additionalProperties: false
      nullable: false
      properties:
        page:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/Page'
        sort:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/Sort'
        filter:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/Filter'
        csvConfiguration:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/CsvConfigurationQueryModel'

    CsvConfigurationQueryModel:
      type: object
      additionalProperties: false
      required:
      - delimiter
      - decimalSeparator
      - textStart
      - textEnd
      properties:
        delimiter:
          $ref: '#/components/schemas/CsvDelimiter'
        decimalSeparator:
          $ref: '#/components/schemas/CsvDecimalSeparator'
        textStart:
          $ref: '#/components/schemas/CsvTextStart'
        textEnd:
          $ref: '#/components/schemas/CsvTextEnd'
    CsvDelimiter:
      type: string
      description: 'Csv delimiter.'
      x-enumNames:
      - Semicolon
      - Tab
      - Space
      - Comma
      enum:
      - Semicolon
      - Tab
      - Space
      - Comma
    CsvDecimalSeparator:
      type: string
      description: 'CsvDecimalSeparator'
      x-enumNames:
      - Comma
      - Dot
      enum:
      - Comma
      - Dot
    CsvTextStart:
      type: string
      description: 'CsvTextStart'
      x-enumNames:
      - Quote
      - EqualQuote
      enum:
      - Quote
      - EqualQuote
    CsvTextEnd:
      type: string
      description: 'CsvTextEnd'
      x-enumNames:
      - Quote
      enum:
      - Quote
    Page:
      type: object
      additionalProperties: false
      required:
      - number
      - size
      properties:
        number:
          type: integer
          format: int32
        size:
          type: integer
          format: int32
    Sort:
      type: object
      additionalProperties: false
      required:
      - propertyName
      - direction
      properties:
        propertyName:
          type: string
        nestedProperty:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/NestedPropertySort'
        direction:
          type: string
    NestedPropertySort:
      allOf:
      - $ref: '#/components/schemas/Sort'
      - type: object
        additionalProperties: false
    Filter:
      type: object
      additionalProperties: false
      required:
      - properties
      properties:
        properties:
          type: array
          items:
            $ref: '#/components/schemas/PropertyFilter'
        propertiesForNestedCollections:
          type: array
          items:
            $ref: '#/components/schemas/NestedCollectionItemFilter'
    PropertyFilter:
      type: object
      additionalProperties: false
      required:
      - name
      - condition
      properties:
        name:
          type: string
        condition:
          $ref: '#/components/schemas/FilterCondition'
        value:
          type: object
          nullable: true
    FilterCondition:
      type: string
      description: 'Filter condition'
      x-enumNames:
      - Like
      - Equal
      - In
      - LessThan
      - LessThanOrEqual
      - GreaterThan
      - GreaterThanOrEqual
      - StartsWith
      enum:
      - Like
      - Equal
      - In
      - LessThan
      - LessThanOrEqual
      - GreaterThan
      - GreaterThanOrEqual
      - StartsWith
    NestedCollectionItemFilter:
      allOf:
      - $ref: '#/components/schemas/Filter'
      - type: object
        additionalProperties: false
        properties:
          name:
            type: string
    CustomerDetailsModel:
      type: object
      additionalProperties: false
      required:
      - id
      - protectedName
      - vat
      - isPerson
      - primaryAddress
      - changedByUserId
      - changeDate
      properties:
        id:
          type: string
          format: guid
        name:
          type: string
          nullable: true
        customerNumber:
          type: string
          nullable: true
        additionalName:
          type: string
          nullable: true
        attention:
          type: string
          nullable: true
        protectedName:
          type: boolean
        vat:
          type: boolean
        dateOfBirth:
          type: string
          format: date-time
          nullable: true
        personCountryCode:
          type: string
          nullable: true
        cprNumber:
          type: string
          nullable: true
        companyCountryCode:
          type: string
          nullable: true
        companyIndustryCodeName:
          type: string
          nullable: true
        companyIndustryCodeValue:
          type: string
          nullable: true
        cvrNumber:
          type: string
          nullable: true
        maintainAutomatically:
          type: boolean
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true
        additionalCvrNumber:
          type: string
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        comment:
          type: string
          nullable: true
        isPerson:
          type: boolean
        companyStartDate:
          type: string
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          format: date-time
          nullable: true
        companyStatus:
          type: string
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          nullable: true
        rowVersion:
          type: string
          format: byte
          nullable: true
        primaryAddress:
          $ref: '#/components/schemas/CustomerAddressModel'
        additionalAddresses:
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/CustomerAddressModel'
        companyContactPersons:
          type: array
          description: Must be empty when Cvr Number is also empty.
          nullable: true
          items:
            $ref: '#/components/schemas/CompanyContactPersonModel'
        externalIdentifiers:
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/CustomerExternalIdentifierModel'
        changedByUserId:
          type: string
          format: guid
        changeReason:
          type: string
          nullable: true
        changeDate:
          type: string
          format: date-time
    CustomerAddressModel:
      type: object
      additionalProperties: false
      required:
      - addressTypeId
      - address
      properties:
        addressTypeId:
          type: integer
          description: Must be positive address type id integer.
          format: int32
          maximum: 2147483647
          minimum: 0
        contactName:
          type: string
          maxLength: 132
          minLength: 0
          nullable: true
        contactName2:
          type: string
          maxLength: 132
          minLength: 0
          nullable: true
        email:
          type: string
          description: If not empty, it has to be valid email address.
          maxLength: 60
          minLength: 0
          nullable: true
        phone:
          type: string
          maxLength: 20
          minLength: 0
          nullable: true
        mobile:
          type: string
          maxLength: 20
          minLength: 0
          nullable: true
        comment:
          type: string
          maxLength: 500
          minLength: 0
          nullable: true
        address:
          $ref: '#/components/schemas/Address'
    Address:
      type: object
      additionalProperties: false
      required:
      - id
      - countryCode
      properties:
        id:
          type: string
          format: guid
        protectedAddress:
          type: boolean
          nullable: true
        streetName:
          type: string
          nullable: true
        streetCode:
          type: integer
          format: int32
          minimum: 1
          maximum: 9999
          nullable: true
        houseNumber:
          type: string
          nullable: true
        houseNumberId:
          type: string
          format: guid
          nullable: true
        floor:
          type: string
          nullable: true
        room:
          type: string
          nullable: true
        citySubDivision:
          type: string
          nullable: true
        postalCode:
          type: string
          nullable: true
        postOfficeBox:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: integer
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        countryCode:
          type: string
          nullable: false
        comment:
          type: string
          nullable: true
        publicRegistryId:
          type: string
          nullable: true
        rowVersion:
          type: string
          description: The concurrency token.
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true
        concatenatedAddress:
          type: string
          nullable: true
    CompanyContactPersonModel:
      type: object
      additionalProperties: false
      properties:
        id:
          type: string
          format: guid
          nullable: true
        name:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        mobile:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        role:
          type: string
          nullable: true
        comment:
          type: string
          nullable: true
    CustomerExternalIdentifierModel:
      type: object
      additionalProperties: false
      required:
      - externalSystemId
      properties:
        externalSystemId:
          type: string
          description: Has to point to external system in the database.
          format: guid
        externalCustomerId:
          type: string
          nullable: true
    ObfuscatedCustomerDetailsModel:
      type: object
      additionalProperties: false
      required:
      - id
      - protectedName
      - vat
      - isPerson
      - primaryAddress
      properties:
        id:
          type: string
          format: guid
        name:
          type: string
          nullable: true
        customerNumber:
          type: string
          nullable: true
        additionalName:
          type: string
          nullable: true
        attention:
          type: string
          nullable: true
        protectedName:
          type: boolean
        vat:
          type: boolean
        cprNumber:
          type: string
          nullable: true
        cvrNumber:
          type: string
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true
        additionalCvrNumber:
          type: string
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        comment:
          type: string
          nullable: true
        isPerson:
          type: boolean
        companyStartDate:
          type: string
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          format: date-time
          nullable: true
        companyStatus:
          type: string
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          nullable: true
        rowVersion:
          type: string
          format: byte
          nullable: true
        primaryAddress:
          $ref: '#/components/schemas/CustomerAddressModel'
        customerAddresses:
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/CustomerAddressModel'
    IdModel:
      type: object
      additionalProperties: false
      required:
      - id
      properties:
        id:
          type: string
          description: Id of created resource.
          format: guid
    ErrorDescription:
      type: object
      additionalProperties: false
      required:
      - errorCode
      - defaultMessage
      properties:
        errorCode:
          type: string
        defaultMessage:
          type: string
        args:
          type: array
          description: Array of arguments for error messages.
          nullable: true
          items: {}
    CreateCustomerModel:
      type: object
      additionalProperties: false
      required:
      - protectedName
      - vat
      - primaryAddress
      properties:
        name:
          type: string
          description: >-
            Remarks:

            - Max 132 characters.

            Can't be empty.
          maxLength: 132
          minLength: 0
          nullable: true
        additionalName:
          type: string
          description: >-
            Remarks:

            - Max 132 characters.

            Can't be empty.
          maxLength: 132
          minLength: 0
          nullable: true
        attention:
          type: string
          description: >-
            Remarks:

            Optional.

            Max 40 characters.
          maxLength: 40
          minLength: 0
          nullable: true
        protectedName:
          type: boolean
          description: >-
            Remarks:

            Optional.

            Default set to false.
        vat:
          type: boolean
          description: >-
            Remarks:

            - Required."
        dateOfBirth:
          type: string
          description: >-
            Remarks:

            - Must be valid date when set

            - Can be empty if CprNumber is set, but only if date matches one in CprNumber's first 6 digits

            - Must be empty if CvrNumber is set.
          format: date-time
          nullable: true
        personCountryCode:
          type: string
          description: >-
            Remarks:

            - Max 2 characters.

            - Must be empty when CvrNumber is set.

            - Has to be valid country code.
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: true
        cprNumber:
          type: string
          description: >-
            Remarks:

            - Can't be empty if CvrNumber is also empty.

            - Must be empty if CvrNumber is not empty.

            - If not empty, it has to be valid Cpr Number (10 characters or 11 characters with hyphen).
          nullable: true
        additionalCprNumber:
          type: string
          description: >-
            Remarks:

            - If not empty, it has to be valid CprNumber (10 characters or 11 characters with hyphen).
          nullable: true
        companyCountryCode:
          type: string
          description: >-
            Remarks:

            - Max 2 characters.

            - Must be empty when CprNumber is set.

            - Has to be valid country code.
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: true
        cvrNumber:
          type: string
          description: >-
            Remarks:

            - Can't be empty if CprNumber is also empty.

            - Must be empty if CprNumber is not empty.

            - If not empty, it has to be valid Cvr Number - exact 8 characters.
          nullable: true
        additionalCvrNumber:
          type: string
          description: >-
            Remarks:

            - If not empty, it has to be valid CvrNumber - exact 8 characters.

            - Must be empty if CvrNumber is also empty.
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        comment:
          type: string
          maxLength: 500
          minLength: 0
          nullable: true
        companyStartDate:
          type: string
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          format: date-time
          nullable: true
        companyStatus:
          type: string
          nullable: true
        companyIndustryCodeName:
          type: string
          nullable: true
        companyIndustryCodeValue:
          type: string
          nullable: true
        maintainAutomatically:
          type: boolean
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          nullable: true
        primaryAddress:
          $ref: '#/components/schemas/CreateCustomerAddressModel'
        companyContactPersons:
          type: array
          description: Must be empty when Cvr Number is also empty.
          nullable: true
          items:
            $ref: '#/components/schemas/CompanyContactPersonModel'
        additionalAddresses:
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/CreateCustomerAddressModel'
        externalIdentifiers:
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/CustomerExternalIdentifierModel'
        changeReason:
          type: string
          nullable: true
    CreateCustomerAddressModel:
      type: object
      additionalProperties: false
      required:
      - addressTypeId
      - address
      properties:
        addressTypeId:
          type: integer
          description: Must be positive address type id integer.
          format: int32
          maximum: 2147483647
          minimum: 0
        contactName:
          type: string
          maxLength: 132
          minLength: 0
          nullable: true
        contactName2:
          type: string
          maxLength: 132
          minLength: 0
          nullable: true
        email:
          type: string
          description: If not empty, it has to be valid email address.
          maxLength: 60
          minLength: 0
          nullable: true
        phone:
          type: string
          maxLength: 20
          minLength: 0
          nullable: true
        mobile:
          type: string
          maxLength: 20
          minLength: 0
          nullable: true
        comment:
          type: string
          maxLength: 500
          minLength: 0
          nullable: true
        address:
          $ref: '#/components/schemas/CreateAddressModel'
    CreateAddressModel:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        protectedAddress:
          type: boolean
          nullable: true
        streetName:
          type: string
          nullable: true
        streetCode:
          type: integer
          format: int32
          minimum: 1
          maximum: 9999
          nullable: true
        houseNumber:
          type: string
          nullable: true
        houseNumberId:
          type: string
          format: guid
          nullable: true
        floor:
          type: string
          nullable: true
        room:
          type: string
          nullable: true
        citySubDivision:
          type: string
          nullable: true
        postalCode:
          type: string
          nullable: true
        postOfficeBox:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: integer
          description: It should be null when countryCode is different than DK.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        countryCode:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: false
        comment:
          type: string
          nullable: true
        publicRegistryId:
          type: string
          nullable: true
        changeReason:
          type: string
          nullable: true
    UpdateCustomerModel:
      type: object
      additionalProperties: false
      required:
      - vat
      - primaryAddress
      properties:
        name:
          type: string
          description: >-
            Remarks:

            - Max 132 characters.

            - Can't be empty.
          maxLength: 132
          minLength: 0
          nullable: true
        additionalName:
          type: string
          description: >-
            Remarks:

            - Max 132 characters.

            - Can't be empty.
          maxLength: 132
          minLength: 0
          nullable: true
        attention:
          type: string
          description: >-
            - Optional.

            - Max 40 characters.
          maxLength: 40
          minLength: 0
          nullable: true
        protectedName:
          type: boolean
          nullable: true
        vat:
          type: boolean
          description: Required.
        dateOfBirth:
          type: string
          description: >-
            Remarks:

            - Must be valid date when set

            - Can be empty if CprNumber is set, but only if date matches one in CprNumber's first 6 digits

            - Must be empty if CvrNumber is set.
          format: date-time
          nullable: true
        cprNumber:
          type: string
          maxLength: 11
          minLength: 10
          description: |-
            Remarks:
            - Can't be empty if CvrNumber is also empty.
            - Must be empty if CvrNumber is not empty.
            - If not empty, it has to be valid Cpr Number (10 characters or 11 characters with hyphen).
          nullable: true
        additionalCprNumber:
          type: string
          maxLength: 11
          minLength: 10
          description: |-
            Remarks:
            - If not empty, it has to be valid CprNumber (10 characters or 11 characters with hyphen).
          nullable: true
        cvrNumber:
          type: string
          description: >-
            Remarks:

            - Can't be empty if CprNumber is also empty.

            - Must be empty if CprNumber is not empty.

            - If not empty, it has to be valid Cvr Number - exact 8 characters.
          nullable: true
        maintainAutomatically:
          type: boolean
          description: Required when CVR number is set.
          nullable: true
        additionalCvrNumber:
          type: string
          description: >-
            Remarks:

            - If not empty, it has to be valid CvrNumber - exact 8 characters.

            - Must be empty if CvrNumber is also empty.
          nullable: true
        eanNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: ************
        glnNumber:
          type: string
          nullable: true
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        personCountryCode:
          type: string
          description: >-
            Remarks:

            - Max 2 characters.

            - Can't be empty when CPR or BirthDate is set.

            - Has to be valid country
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: true
        companyCountryCode:
          type: string
          description: >-
            Remarks:

            - Max 2 characters.

            - Can't be empty when CVR is set.

            - Has to be valid country code.
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: true
        comment:
          type: string
          description: >-
            Remarks:

            - Max 500 characters.
          maxLength: 500
          minLength: 0
          nullable: true
        companyStartDate:
          type: string
          description: >-
            Remarks:

            - Optional.
          format: date-time
          nullable: true
        companyEndDate:
          type: string
          description: >-
            Remarks:

            - Optional.
          format: date-time
          nullable: true
        companyStatus:
          type: string
          description: >-
            Remarks:

            - Optional.
          nullable: true
        companyIndustryCodeName:
          type: string
          description: >-
            Remarks:

            - Optional.
          nullable: true
        companyIndustryCodeValue:
          type: string
          description: >-
            Remarks:

            - Optional.
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          description: >-
            Remarks:

            - Optional.
          nullable: true
        primaryAddress:
          $ref: '#/components/schemas/UpdateCustomerAddressModel'
        additionalAddresses:
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/UpdateCustomerAddressModel'
        companyContactPersons:
          type: array
          description: Must be empty when Cvr Number is also empty.
          nullable: true
          items:
            $ref: '#/components/schemas/CompanyContactPersonModel'
        externalIdentifiers:
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/CustomerExternalIdentifierModel'
        rowVersion:
          type: string
          description: >-
            Remarks:

            - The concurrency token.
          format: byte
          nullable: true
        changeReason:
          type: string
          description: >-
            Remarks:

            - Optional.
          nullable: true
    UpdateCustomerAddressModel:
      type: object
      additionalProperties: false
      required:
      - addressTypeId
      - address
      properties:
        id:
          type: string
          format: guid
          nullable: true
        addressTypeId:
          type: integer
          description: Must be positive address type id integer.
          format: int32
          maximum: 2147483647
          minimum: 0
        contactName:
          type: string
          maxLength: 132
          minLength: 0
          nullable: true
        contactName2:
          type: string
          maxLength: 132
          minLength: 0
          nullable: true
        email:
          type: string
          description: If not empty, it has to be valid email address.
          maxLength: 60
          minLength: 0
          nullable: true
        phone:
          type: string
          maxLength: 20
          minLength: 0
          nullable: true
        mobile:
          type: string
          maxLength: 20
          minLength: 0
          nullable: true
        comment:
          type: string
          maxLength: 500
          minLength: 0
          nullable: true
        address:
          $ref: '#/components/schemas/UpdateAddressModel'
        customerRowVersion:
          type: string
          description: The concurrency token.
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true
    UpdateAddressModel:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        id:
          type: string
          format: guid
          nullable: true
        protectedAddress:
          type: boolean
          nullable: true
        streetName:
          type: string
          nullable: true
        streetCode:
          type: integer
          format: int32
          minimum: 1
          maximum: 9999
          nullable: true
        houseNumber:
          type: string
          nullable: true
        houseNumberId:
          type: string
          format: guid
          nullable: true
        floor:
          type: string
          nullable: true
        room:
          type: string
          nullable: true
        citySubDivision:
          type: string
          nullable: true
        postalCode:
          type: string
          nullable: true
        postOfficeBox:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: integer
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        countryCode:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: false
        comment:
          type: string
          nullable: true
        publicRegistryId:
          type: string
          nullable: true
        rowVersion:
          type: string
          description: The concurrency token.
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true
    CustomerCprModel:
      type: object
      additionalProperties: false
      properties:
        cprNumber:
          type: string
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true
    UpdateCustomerCprNumbersModel:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        cprNumber:
          type: string
          nullable: true
        dateOfBirth:
          type: string
          format: date-time
          nullable: true
        additionalCprNumber:
          type: string
          nullable: true
        countryCode:
          type: string
          minLength: 1
          maxLength: 2
          pattern: "^[a-zA-Z]+$"
          nullable: false
        rowVersion:
          type: string
          format: byte
          nullable: true
        changeReason:
          type: string
          nullable: true
    CustomerExternalSystemModel:
      type: object
      additionalProperties: false
      required:
      - id
      properties:
        id:
          type: string
          format: guid
        name:
          type: string
          nullable: true
    DkIndustryCodeModel:
      type: object
      additionalProperties: false
      required:
      - id
      - name
      - value
      - dkIndustryCodeLevel
      properties:
        id:
          type: integer
          format: int32
          maximum: 2147483647
          minimum: -2147483648
        name:
          type: string
        value:
          type: string
        parentValue:
          type: string
          nullable: true
        dkIndustryCodeLevel:
          $ref: '#/components/schemas/DkIndustryCodeLevel'
    DkIndustryCodeLevel:
      type: string
      description: >-
        Industry code levels

        | Code name | Description |

        | --------- | ----------- |

        | Section   |             |

        | MainGroup |             |

        | Group     |             |

        | SubGroup  |             |

        | Industry  |             |
      x-enumNames:
      - Section
      - MainGroup
      - Group
      - SubGroup
      - Industry
      enum:
      - Section
      - MainGroup
      - Group
      - SubGroup
      - Industry
    CustomerCompanyIntegrationModel:
      type: object
      additionalProperties: false
      required:
      - address
      - company
      properties:
        name:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        industryCodeName:
          type: string
          description: Customer company's integration industry code name.
          nullable: true
        industryCodeValue:
          type: string
          description: Customer company's integration industry code value.
          nullable: true
        address:
          $ref: '#/components/schemas/AddressIntegrationModel'
        company:
          $ref: '#/components/schemas/CustomerCompanyIntegrationCompanyModel'
    AddressIntegrationModel:
      type: object
      additionalProperties: false
      required:
        - countryCode
      properties:
        streetName:
          type: string
          description: Street name.
          nullable: true
        streetCode:
          type: string
          description: Street code.
          nullable: true
        houseNumber:
          type: string
          description: House number.
          nullable: true
        floor:
          type: string
          description: Floor information.
          nullable: true
        room:
          type: string
          description: Room information.
          nullable: true
        citySubDivision:
          type: string
          description: City sub division.
          nullable: true
        postalCode:
          type: string
          description: Postal code.
          nullable: true
        city:
          type: string
          description: City.
          nullable: true
        municipalityCode:
          type: string
          description: Municipality / City code from external system.
          nullable: true
        countryCode:
          type: string
          description: ISO CountryCode.
          nullable: false
        publicRegistryId:
          type: string
          description: >-
            External API address identification number.

            In Denmark - DAR ID.
          nullable: true
        houseNumberId:
          type: string
          description: External API house number Id.
          format: guid
          nullable: true
    CustomerCompanyIntegrationCompanyModel:
      type: object
      additionalProperties: false
      properties:
        companyRegistryNumber:
          type: string
          description: >-
            Company's public registry identification number.

            In Denmark - CVR Number.
          nullable: true
        startDate:
          type: string
          description: Public registry information about company's start date.
          nullable: true
        endDate:
          type: string
          description: Public registry information about company's end date.
          nullable: true
        status:
          type: string
          description: Company's, provided by public registry.
          nullable: true
        companyRegistryNumberExternalId:
          type: string
          description: Customer company's integration external API Id response.
          nullable: true
    CustomerPersonIntegrationModel:
      type: object
      additionalProperties: false
      required:
      - nameAndAddressProtection
      - address
      properties:
        name:
          type: string
          nullable: true
        personRegistryNumber:
          type: string
          nullable: true
        nameAndAddressProtection:
          type: boolean
        address:
          $ref: '#/components/schemas/CustomerPersonAddressIntegrationModel'
    CustomerPersonAddressIntegrationModel:
      type: object
      additionalProperties: false
      properties:
        roadAddressingName:
          type: string
          nullable: true
        houseNo:
          type: string
          nullable: true
        floor:
          type: string
          nullable: true
        doorNo:
          type: string
          nullable: true
        postCode:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        municipalityCode:
          type: string
          nullable: true
    SettlementObjectOwnershipQueryResult:
      type: object
      additionalProperties: false
      required:
      - id
      - settlementObjectId
      - settlementObjectTypeId
      - customerId
      - ownershipType
      - startDate
      properties:
        id:
          type: string
          format: guid
        settlementObjectId:
          type: string
          format: guid
        settlementObjectTypeId:
          type: string
          format: guid
        customerId:
          type: string
          format: guid
        ownershipType:
          description: 1 - Owner, 2 - Tenant
          oneOf:
          - $ref: '#/components/schemas/OwnershipTypeDto'
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
          nullable: true
    OwnershipTypeDto:
      type: string
      description: 1 - Owner, 2 - Tenant
      x-enumNames:
      - _1
      - _2
      enum:
      - _1
      - _2
    TenantConfigurationModel:
      type: object
      additionalProperties: false
      properties:
        companyIntegration:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/CompanyIntegrationSettingsModel'
        personIntegration:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/PersonIntegrationSettingsModel'
        customerSettings:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/CustomerSettingsModel'
    CompanyIntegrationSettingsModel:
      type: object
      additionalProperties: false
      properties:
        dkLogicCvr:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/DkLogicCvrSettingsModel'
    DkLogicCvrSettingsModel:
      type: object
      additionalProperties: false
      required:
      - subscribedToCvrEvents
      properties:
        cvrConfigurationId:
          type: string
          nullable: true
        subscriptionId:
          type: string
          nullable: true
        subscribedToCvrEvents:
          type: boolean
    PersonIntegrationSettingsModel:
      type: object
      additionalProperties: false
      properties:
        dkLogicCpr:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/DkLogicCprSettingsModel'
    DkLogicCprSettingsModel:
      type: object
      additionalProperties: false
      properties:
        cprConfigurationId:
          type: string
          nullable: true
        subscriptionId:
          type: string
          nullable: true
    CustomerSettingsModel:
      type: object
      additionalProperties: false
      required:
      - vatEnabledByDefault
      - enableDuplicateCprNumbers
      - enableDuplicateCvrNumbers
      properties:
        vatEnabledByDefault:
          type: boolean
        enableCheckVatStatus:
          type: boolean
        enableDuplicateCprNumbers:
          type: boolean
        enableDuplicateCvrNumbers:
          type: boolean
        enableCprLookup:
          type: boolean
          description: 'Determines whether CprLookup button should be visible'
          nullable: true
          example: false
        dataHubActorType:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/DataHubActorType'
    DataHubActorType:
      type: string
      description: 0 - BalanceResponsible, 1 - BalanceSupplier, 2 - GridCompany, 3 - SystemOwner, 4 - DanishEnergyAgency
      x-enumNames:
      - _0
      - _1
      - _2
      - _3
      - _4
      enum:
      - _0
      - _1
      - _2
      - _3
      - _4
    UserDetailsQueryResults:
      type: object
      additionalProperties: false
      properties:
        userId:
          type: string
          description: User identifier.
          nullable: true
        name:
          type: string
          description: User name.
          nullable: true
        email:
          type: string
          description: User email.
          nullable: true
        tenant:
          type: string
          description: Tenant identifier.
          nullable: true
        roles:
          type: array
          description: Current roles.
          nullable: true
          items:
            $ref: '#/components/schemas/Role'
    Role:
      type: object
      additionalProperties: false
      required:
      - id
      properties:
        id:
          type: string
          description: Role identifier.
          format: guid
        name:
          type: string
          description: Role name.
          nullable: true
        permissions:
          type: array
          description: List of permissions.
          nullable: true
          items:
            $ref: '#/components/schemas/Permission'
    Permission:
      type: object
      additionalProperties: false
      required:
      - id
      properties:
        id:
          type: string
          description: Permission identifier.
          format: guid
        name:
          type: string
          description: Permission name.
          nullable: true
    GetVatStatusResponseModel:
      description: Getting VAT status response model
      type: object
      additionalProperties: false
      properties:
        customerId:
          description: Identifier of customer in KMD Elements.
          type: string
          format: uuid
          example: "23751a83-cae9-44eb-b3ff-50ca1e15c1b8"
        vatNumber:
          description: VAT number (CVR in Denmark).
          type: string
          minLength: 8
          maxLength: 8
          pattern: '^[0-9]{8}$'
          example: "54711962"
        lastValidateTime:
          description: Date and time of last validation accoriding to VIES service.
          type: string
          format: date-time
          nullable: true
          example: "2024-07-01T00:00:00.000Z"
        isVatValid:
          description: Flag of VAT validity status.
          type: boolean
          nullable: true
          example: true
  securitySchemes:
    Bearer:
      type: http
      description: >-
        JWT Authorization header using the Bearer scheme.

        Enter your token in the text input below. 'Bearer' is added automatically

        Example: '12345abcdef'
      scheme: Bearer
      bearerFormat: JWT
