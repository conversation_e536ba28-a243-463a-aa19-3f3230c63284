asyncapi: 2.6.0
id: 'https://async.api.kmdelements.com/meter-frame'
info:
  title: MeterFrame changed
  version: 5.0.4
  contact:
    name: KMD Elements
    url: >-
      https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: 'https://www.kmd.net/terms-of-use'
  description: >
    Whenever meter frame is changed (created/edited/deleted) an event is sent.
  x-maintainers: Team-MD-2
tags:
  - name: Team-MD-2
    description: Maintained by Team MD 2.
servers:
  local:
    url: localhost
    description: Local
    protocol: kafka
    protocolVersion: 2.6.0

defaultContentType: application/json

channels:
  'kmd.elements.{tenantId}.event.connection-points.meter-frame-changed.v5':
    description: Topic for changes on meter frame
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: Information about Meter Frame changes (create/update/delete).
      description: Information about Meter Frame changes (create/update/delete).
      operationId: publishMeterFrameChangedEvent
      message:
        $ref: '#/components/messages/MeterFrameChangedEventMessage'
    subscribe:
      summary: Information about Meter Frame changes (create/update/delete).
      description: Information about Meter Frame changes (create/update/delete).
      operationId: receiveMeterFrameChangedEvent
      message:
        $ref: '#/components/messages/MeterFrameChangedEventMessage'
components:
  messages:
    MeterFrameChangedEventMessage:
      name: MeterFrameChangedEventMessage
      title: MeterFrameChangedEventMessage
      summary: Change event on meter frame.
      contentType: application/json
      headers:
        $ref: "#/components/schemas/MessageHeaders"
      payload:
        $ref: '#/components/schemas/MessagePayload'
  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d
        sourceId:
          name: es-source-id
          description: The name of the source system sending the message.
          type: string
          maxLength: 256
          pattern: "^.*$"
          example: connection-points-api
    MessagePayload:
      title: MessagePayload
      name: MessagePayload
      additionalProperties: false
      type: object
      required:
        - eventType
        - entityFamily
        - entityType
        - timestamp
        - version
        - data
      properties:
        eventType:
          type: string
          pattern: "^(Created|Updated|Deleted)$"
          description: Event type.
          maxLength: 7
          minLength: 7
        entityFamily:
          type: string
          pattern: "^(MeterFrame)$"
          description: Event entity family.
          maxLength: 50
          minLength: 1
        entityType:
          type: string
          pattern: "^(MeterFrame)$"
          description: Event entity type.
          maxLength: 150
          minLength: 1
        timestamp:
          $ref: '#/components/schemas/DateTime'
          description: Event timestamp.
        version:
          type: string
          pattern: "^v[0-9]*$"
          description: Event schema version
          maxLength: 5
          minLength: 2
          example: v5
        data:
          $ref: "#/components/schemas/MeterFrame"

    # --------------------------------------------------------- ACTUAL DATA OBJECT -------------------------------------------------------------------------
    MeterFrame:
      $ref: './meter-frame/MeterFrame.changed.schema.yaml#/MeterFrame'

    # --------------------------------------------------------- COMMON DATA TYPES --------------------------------------------------------------------------
    DateTime:
      type: string
      description: 'Date in UTC ISO 8601 format.'
      format: date-time
      example: 2019-11-14T00:55:31.820Z

  parameters:
    TenantId:
      description: Identifier of a tenant.
      schema:
        type: number
