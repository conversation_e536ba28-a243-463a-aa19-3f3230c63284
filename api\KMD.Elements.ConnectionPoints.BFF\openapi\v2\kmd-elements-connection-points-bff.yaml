openapi: 3.0.3

info:
  title: KMD.Elements.ConnectionPoints.BFF
  x-maintainers: Team-MD-2
  description: |-
    # KMD Elements - Connection Points BFF
    Stability level: V2-PREVIEW
    <br/>
    <br/>
    The **KMD Elements Connection Points BFF** is part of the KMD Element product.
    <br/>

    ## Capabilities
    The BFF is a Frontend Proxy (can be used only by KMD.Elements.Frontend) and allows to:
    - search, identify and retrieve details of Connection Points
    ---

  termsOfService: https://www.kmd.net/terms-of-use

  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>

  license:
    name: License
    url: https://www.kmd.net/terms-of-use

  version: '2.43'

servers:
  - url: "/"
  - url: "/api/connection-points-bff"

security:
  - Jwt: [ ]

tags:
  - name: MeterFrame
    description: MeterFrames operations.
  - name: MeterAttributeRequirements
    description: Section with direct access to Meter Attribute Requirements.
  - name: MeterComponentRequirements
    description: Section with direct access to Meter Component Requirements.
  - name: ConnectionRights
    description: ConnectionRights
  - name: RegisterRequirements
    description: RegisterRequirements
  - name: MeteringPoints
    description: Metering points operations
  - name: Interruptions
    description: Interruptions
  - name: GridAreas
    description: Grid Areas in Market Participants
  - name: ConnectionPoint
    description: API for Connection Points management.
  - name: GisTopology
    description: API for infrastructure topology operations.
  - name: AccessInformation
    description: API for Access Information management.

paths:
  /v2/access-information:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - AccessInformation
      summary: Creates Access Information.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postAccessInformation
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/access-information/schemas/AddAccessInformationModel.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
  /v2/access-information/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - AccessInformation
      summary: Gets Access Information by Id.
      description: |-
        ### Remarks
        Gets Access Information by Id.
      operationId: getAccessInformationById
      parameters:
        - $ref: './parameters/access-information/AccessInformationId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      responses:
        '200':
          description: Found Access Information.
          content:
            application/json:
              schema:
                $ref: './schemas/access-information/schemas/GetAccessInformationModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - AccessInformation
      summary: Gets Access Information by Id.
      description: |-
        ### Remarks
        Updates Access Information by Id.
      operationId: updateAccessInformation
      parameters:
        - $ref: './parameters/access-information/AccessInformationId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/access-information/schemas/UpdateAccessInformationModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - AccessInformation
      summary: Allows to delete Access Information.
      description: Deletes Access Information.
      operationId: deleteAccessInformation
      parameters:
        - $ref: './parameters/access-information/AccessInformationId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/access-information/commands/get-access-information-list-by-meter-frame-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - AccessInformation
      summary: Fetches list of Access Information.
      description: |-
        ### Result
        Returns filtered and paged list of Access Informations by Meter Frame Id.
      operationId: getAccessInformationListByMeterFrameId
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/access-information/schemas/GetAccessInformationListByMeterFrameIdModel.yaml'
        required: true
      responses:
        '200':
          description: Access Informations list returned successfully.
          content:
            application/json:
              schema:
                $ref: './schemas/access-information/schemas/GetAccessInformationListModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/{meterFrameId}/bailiff-cases:
    get:
      tags:
        - MeterFrame
      summary: Get list of meter frame bailiff cases.
      operationId: getBailiffCases
      description: Get list of meter frame bailiff cases.
      x-authorization: BailiffCase.Read
      parameters:
        - $ref: './parameters/meter-frames.yaml#/components/parameters/MeterFrameId'
      responses:
        "200":
          description: List of meter frame bailiff cases.
          content:
            application/json:
              schema:
                description: Array containing bailiff cases for the given meter frame.
                nullable: false
                type: array
                minItems: 0
                maxItems: 100
                items:
                  $ref: './schemas/bailiff-case/BailiffCaseDetailsModel.yaml'
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
        '502':
          $ref: './responses/common/502.yaml'
        '504':
          $ref: './responses/common/504.yaml'

  /v2/meter-frames/export:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Fetches CSV export file with Meter Frames collection.
      description: Fetches CSV export file with Meter Frames collection.
      operationId: meterFrameExportV2
      requestBody:
        description: Meter frames export query model
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/commands/MeterFramesSearchQueryModelWithCsvConfiguration.yaml'
      responses:
        '200':
          description: 'Meter frames export CSV file'
          content:
            text/csv:
              schema:
                description: File content
                type: string
                maxLength: 2147483647
                format: binary
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
      security:
        - Jwt: []

  /v2/meter-frames/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets paged Meter Frames for passed query parameters.
      description: Gets paged Meter Frames for passed query parameters.
      operationId: getPagedMeterFrames
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/commands/SearchMeterFrame.yaml'
        required: true
      responses:
        '200':
          description: Meter Frames collection.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/PagedResponseOfGetMeterFrameSearchModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Creates Meter Frame.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postMeterFrame
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/AddMeterFrameModel.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
  /v2/meter-frames/{meterFrameId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets specific Meter Frame.
      description: Gets specific Meter Frame by technical identifier.
      operationId: getMeterFrameById
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
      responses:
        '200':
          description: Found Meter Frame.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/GetMeterFrameModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/{meterFrameId}/master-data:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update meter frame master data attributes.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrameMasterData
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/UpdateMeterFrameMasterDataModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/{meterFrameId}/address:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update meter frame address details.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrameAddress
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/UpdateMeterFrameAddressModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/{meterFrameId}/geographical-location:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update meter frame geographical location details.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrameGeographicalLocation
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/UpdateMeterFrameGeographicalLocationModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/{meterFrameId}/gis-properties-electricity:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update gis properties for electricity meter frame.
      description: |-
        ### Remarks
        - Can only be used for electricity meter frame.
        - passed model has to meet validation requirements described below.
      operationId: putElectricityMeterFrameGisProperties
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/UpdateElectricityMeterFrameGisPropertiesModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/{meterFrameId}/gis-properties-water:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update gis properties for water meter frame.
      description: |-
        ### Remarks
        - Can only be used for water meter frame.
        - passed model has to meet validation requirements described below.
      operationId: putWaterMeterFrameGisProperties
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/UpdateWaterMeterFrameGisPropertiesModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/{meterFrameId}/gis-properties-heating:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update gis properties for heating meter frame.
      description: |-
        ### Remarks
        - Can only be used for heating meter frame.
        - passed model has to meet validation requirements described below.
      operationId: putHeatingMeterFrameGisProperties
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/UpdateHeatingMeterFrameGisPropertiesModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/{meterFrameId}/electricity-branch-lines:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update main and reserve branch lines for electricity meter frame.
      description: |-
        ### Remarks
        - Can only be used for electricity meter frame.
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrameElectricityBranchLines
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/UpdateMeterFrameElectricityBranchLinesModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/{meterFrameId}/water-branch-lines:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update main and reserve branch lines for water meter frame.
      description: |-
        ### Remarks
        - Can only be used for water meter frame.
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrameWaterBranchLines
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/UpdateMeterFrameWaterBranchLinesModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'


  /v2/meter-frames/commands/existing-by-number:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Checks if Meter Frame exists.
      description: Check if Meter Frame with exact meter frame number exists within connection point.
      operationId: checkIfMeterFrameExistsByNumber
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/MeterFrameExistByNumberModel.yaml'
        required: true
      responses:
        '200':
          description: Meter Frame exists.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/GetMeterFrameExistsResult.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/commands/get-meter-frames-with-unrelated-branch-line:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - Returns meter frames with meter frame numbers containing passed parameter. Returned MFs have filled out branch lines and no connected meter frame id.
      operationId: getMeterFramesWithUnrelatedBranchLine
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/SearchByMeterFrameNumberPagedModel.yaml'
        required: true
      responses:
        '200':
          description: Found Meter Frames.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/PagedResponseOfGetMeterFramesBasicModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/commands/get-meter-frames-with-related-branch-line:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - Returns meter frames with meter frame numbers containing passed parameter. Returned MFs have filled out branch lines and no connected meter frame id.
      operationId: getMeterFramesWithRelatedBranchLines
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/MeterFrameIdWithSupplyTypeInSchema.yaml'
        required: true
      responses:
        '200':
          description: Found Meter Frames.
          content:
            application/json:
              schema:
                type: array
                description: Results array.
                maxItems: 1000000
                items:
                  $ref: './schemas/meter-frame/schemas/GetMeterFrameBasicModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-points:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: Creates Connection Point.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postConnectionPoint
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-point/AddConnectionPointModel.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
           $ref: "./responses/common.yaml#/components/responses/401"
        '403':
           $ref: "./responses/common.yaml#/components/responses/403"
        '409':
           $ref: "./responses/common.yaml#/components/responses/409"
        '422':
           $ref: "./responses/common.yaml#/components/responses/422"
  /v2/connection-points/{id}:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: Allows to update Connection Point.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putConnectionPoint
      parameters:
        - $ref: './parameters/connection-points.yaml#/components/parameters/ConnectionPointId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/connection-point/UpdateConnectionPointModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
           $ref: "./responses/common.yaml#/components/responses/401"
        '403':
           $ref: "./responses/common.yaml#/components/responses/403"
        '404':
           $ref: "./responses/common.yaml#/components/responses/404"
        '409':
           $ref: "./responses/common.yaml#/components/responses/409"
        '422':
           $ref: "./responses/common.yaml#/components/responses/422"
        '429':
           $ref: "./responses/common.yaml#/components/responses/429"
        '500':
           $ref: "./responses/common.yaml#/components/responses/500"
        '502':
           $ref: "./responses/common.yaml#/components/responses/502"
        '503':
           $ref: "./responses/common.yaml#/components/responses/503"
        '504':
           $ref: "./responses/common.yaml#/components/responses/504"

  /v2/metering-points/metering-point-versions/commands/get-latest-by-register-requirement-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeteringPoints
      summary: Gets Metering Point with latest versions by Register Requirement ID
      description: Gets Metering Point with latest versions by Register Requirement ID
      operationId: getMeteringPointWithLatestVersionsByRegisterRequirementId
      requestBody:
        description: Gets Metering Point with latest versions by Register Requirement ID.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/metering-point-version/GetMeteringPointsWithLatestVersionByRegisterRequirementIdCommand.yaml'
      responses:
        '200':
          description: Returns Metering Point with latest versions by Register Requirement ID
          content:
            application/json:
              schema:
                description: Returns Metering Point with latest versions by Register Requirement ID collection
                nullable: false
                type: array
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/metering-point/MeteringPoint.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/metering-points/metering-point-versions/commands/get-latest-by-meter-frame-id-with-latest-formula-version:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeteringPoints
      summary: Gets Metering Points with latest version and formula version by Meter Frame ID
      description: Gets Metering Points with latest version and formula version by Meter Frame ID
      operationId: getMeteringPointsWithLatestVersionAndFormulaVersionByMeterFrameId
      requestBody:
        description: Getst Metering Points with latest version and formula version by Meter Frame ID.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/metering-point-version/GetMeteringPointsWithLatestVersionAndFormulaVersionByMeterFrameIdCommand.yaml'
      responses:
        '200':
          description: Returns Metering Points with latest version and formula version by by Meter Frame ID
          content:
            application/json:
              schema:
                description: Returns Metering Points with latest version and formula version by by Meter Frame ID collection
                nullable: false
                type: array
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/metering-point-version.yaml#/components/schemas/GetMeteringPointWithLatestVersionAndFormulaVersionWithMeteringComponentIdResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/metering-points/commands/get-paged-latest-parents-with-version-and-formula-by-connection-point-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeteringPoints
      summary: Gets paged parent Metering Points with latest version and formula version by Connection Point ID
      description: Gets paged parent Metering Points with latest version and formula version by Connection Point ID.
      operationId: getPagedParentMeteringPointsWithLatestVersionAndFormulaVersionByConnectionPointId
      requestBody:
        description: Gets paged parent Metering Points with latest version and formula version by Connection Point ID.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/metering-point.yaml#/components/schemas/GetPagedLatestParentsWithVersionAndFormulaByConnectionPointIdCommand'
      responses:
        '200':
          description: Returns paged parent Metering Points with latest version and formula version by Connection Point ID.
          content:
            application/json:
              schema:
                description: Returns page of parent Metering Points with latest version and formula version by Connection Point ID.
                nullable: false
                allOf:
                  - $ref: './schemas/metering-point.yaml#/components/schemas/GetPagedLatestParentsWithVersionAndFormulaByConnectionPointIdResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/metering-points/commands/get-latest-children-version-and-formula-by-parent-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeteringPoints
      summary: Gets children Metering Points with latest version and formula version by Parent ID
      description: Gets children Metering Points with latest version and formula version by Parent ID.
      operationId: getChildrenMeteringPointsWithLatestVersionAndFormulaVersionByParentId
      requestBody:
        description: Gets children Metering Points with latest version and formula version by Parent ID.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/metering-point.yaml#/components/schemas/GetChildrenMeteringPointsWithLatestVersionAndFormulaVersionByParentIdCommand'
      responses:
        '200':
          description: Returns children Metering Points with latest version and formula version by Parent ID.
          content:
            application/json:
              schema:
                description: Returns children Metering Points with latest version and formula version by Parent ID collection.
                nullable: false
                type: array
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/metering-point/MeteringPoint.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-attribute-requirements/configuration:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterAttributeRequirements
      summary: Gets Meter Attribute Requirements configuration.
      description: Gets Meter Attribute Requirements configuration.
      operationId: getMeterAttributeRequirementsConfiguration
      responses:
        '200':
          description: Returns Meter Attribute Requirements configuration.
          content:
            application/json:
              schema:
                description: Returns Meter Attribute Requirements configuration object.
                nullable: false
                allOf:
                  - $ref: './schemas/meter-attribute-requirement-configuration.yaml#/components/schemas/MeterAttributeRequirementsConfiguration'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-attribute-requirements/{meterAttributeRequirementsId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterAttributeRequirements
      summary: Gets Meter Attribute Requirements collection by Meter Attribute Requirements identifier.
      description: Gets Meter Attribute Requirements collection by Meter Attribute Requirements identifier.
      operationId: getMeterAttributeRequirementsById
      parameters:
        - $ref: './parameters/meter-attribute-requirements.yaml#/components/parameters/MeterAttributeRequirementsId'
      responses:
        '200':
          description: Returns Meter Attribute Requirements collection or NULL if not found.
          content:
            application/json:
              schema:
                description: Meter Attribute Requirements result object.
                nullable: true
                allOf:
                  - $ref: './schemas/meter-attribute-requirement.yaml#/components/schemas/MeterAttributeRequirementsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterAttributeRequirements
      summary: Allows to Create or Update Meter Attribute Requirements.
      description: Allows to Create or Update Meter Attribute Requirements.
      operationId: addOrUpdateMeterAttributeRequirements
      requestBody:
        description: Meter Attribute Requirements collection to be upserted.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/meter-attribute-requirement.yaml#/components/schemas/MeterAttributeRequirementsRequest'
      parameters:
        - $ref: './parameters/meter-attribute-requirements.yaml#/components/parameters/MeterAttributeRequirementsId'
      responses:
        '204':
          description: Meter Attribute Requirements created or updated successfully.
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-attribute-requirements/get-by-meter-frame-id/{meterFrameId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterAttributeRequirements
      summary: Gets Meter Attribute Requirements collection by Meter Frame identifier.
      description: Gets Meter Attribute Requirements collection by Meter Frame identifier.
      operationId: getMeterAttributeRequirementsByMeterFrameId
      parameters:
        - $ref: './parameters/meter-frames.yaml#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Returns Meter Attribute Requirement or NULL if not found.
          content:
            application/json:
              schema:
                description: Meter Attribute Requirements result object.
                nullable: true
                allOf:
                  - $ref: './schemas/meter-attribute-requirement.yaml#/components/schemas/MeterAttributeRequirementsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-component-requirements/configuration:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterComponentRequirements
      summary: Gets Meter Component Requirements configuration.
      description: Gets Meter Component Requirements configuration.
      operationId: getMeterComponentRequirementsConfiguration
      responses:
        '200':
          description: Returns Meter Component Requirements configuration.
          content:
            application/json:
              schema:
                description: Returns Meter Component Requirements configuration object.
                nullable: false
                allOf:
                  - $ref: './schemas/meter-component-requirement-configuration.yaml#/components/schemas/MeterComponentRequirementsConfiguration'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-component-requirements/{meterComponentRequirementsId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterComponentRequirements
      summary: Gets Meter Component Requirements collection by Meter Component Requirements identifier.
      description: Gets Meter Component Requirements collection by Meter Component Requirements identifier.
      operationId: getMeterComponentRequirementsById
      parameters:
        - $ref: './parameters/meter-component-requirements.yaml#/components/parameters/MeterComponentRequirementsId'
      responses:
        '200':
          description: Returns Meter Component Requirements collection or NULL if not found.
          content:
            application/json:
              schema:
                description: Meter Component Requirements result object.
                nullable: true
                allOf:
                  - $ref: './schemas/meter-component-requirement.yaml#/components/schemas/MeterComponentRequirementsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterComponentRequirements
      summary: Allows to Create or Update Meter Component Requirements.
      description: Allows to Create or Update Meter Component Requirements.
      operationId: addOrUpdateMeterComponentRequirements
      requestBody:
        description: Meter Component Requirements collection to be upserted.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/meter-component-requirement.yaml#/components/schemas/MeterComponentRequirementsRequest'
      parameters:
        - $ref: './parameters/meter-component-requirements.yaml#/components/parameters/MeterComponentRequirementsId'
      responses:
        '204':
          description: Meter Component Requirements created or updated successfully.
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-component-requirements/get-by-meter-frame-id/{meterFrameId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterComponentRequirements
      summary: Gets Meter Component Requirements collection by Meter Frame identifier.
      description: Gets Meter Component Requirements collection by Meter Frame identifier.
      operationId: getMeterComponentRequirementsByMeterFrameId
      parameters:
        - $ref: './parameters/meter-frames.yaml#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Returns Meter Component Requirement or NULL if not found.
          content:
            application/json:
              schema:
                description: Meter Component Requirements result object.
                nullable: true
                allOf:
                  - $ref: './schemas/meter-component-requirement.yaml#/components/schemas/MeterComponentRequirementsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights:
    get:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Fetches Connection Rights list by meter frame
      description: |-
        ### Result
        Returns top connection rights according to query parameters.
      operationId: getConnectionRightsByMeterFrameId
      parameters:
        - name: meterFrameId
          in: query
          description: Id of the MeterFrame.
          example: '48d6567c-0136-4f04-8c7d-9d485232dcba'
          schema:
           nullable: true
           type: string
           format: uuid
        - name: meterNumber
          in: query
          description: Numer of the MeterFrame.
          example: '48d6567c-0136-4f04-8c7d-9d485232dcba'
          schema:
           nullable: true
           type: string
           description: Number on the Meter Frame.
           maxLength: 50
           minLength: 1
           pattern: ""
        - name: supplyType
          in: query
          description: SupplyType
          required: false
          schema:
            $ref: './schemas/common/SupplyType.yaml'
          example: true
      responses:
        '200':
          description: Connection Rights returned successfully.
          content:
            application/json:
              schema:
                description: Connection Rights returned successfully.
                type: array
                maxItems: 1000000
                items:
                  $ref: './schemas/connection-rights/ConnectionRightDetails.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Create Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: createConnectionRight
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/CreateConnectionRightPayload.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
  /v2/connection-rights/{id}:
    put:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Update Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: updateConnectionRight
      parameters:
        - name: id
          in: path
          required: true
          description: Id.
          schema:
           type: string
           format: uuid
          example: ceb572ab-0d9a-4842-a842-f827ccd1f044
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/UpdateConnectionRightPayload.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    get:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Gets specific Connection Right.
      operationId: getConnectionRightById
      description: Returns specific Connection Right by technical GUID identifier.
      parameters:
        - name: id
          in: path
          required: true
          description: Id.
          schema:
           type: string
           format: uuid
          example: ceb572ab-0d9a-4842-a842-f827ccd1f044
      responses:
        '200':
          description: Found Connection Right.
          content:
            application/json:
              schema:
                $ref: './schemas/connection-rights/ConnectionRightDetails.yaml'
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    delete:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Allows to delete Connection Rights.
      description: Deletes Connection Right.
      operationId: deleteConnectionRight
      parameters:
        - name: id
          in: path
          required: true
          description: Id.
          schema:
           type: string
           format: uuid
          example: ceb572ab-0d9a-4842-a842-f827ccd1f044
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights/commands/transfer:
    post:
      x-authorization: ConnectionRights.Write
      tags:
          - ConnectionRights
      summary: Transfers one connection right into many, many into one or one to one. Transfer deactives (sets valid to date) on transfered connection right.
      operationId: transferConnectionRights
      description: |-
        Transfers connection rights.
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/ConnectionRightTransfer.yaml'
        required: true
      responses:
        '204':
          description: Successfully transfered.
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights/commands/stop:
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Stop Connection Right.
      description: Passed model has to meet validation requirements described below.
      operationId: stopConnectionRight
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/StopConnectionRightPayload.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights/total-value:
    post:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Get Total Values.
      description: Get Total Values per unit.
      operationId: getTotalValues
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/ConnectionRightsTotalValuesPayload.yaml'
        required: true
      responses:
        '200':
          description: Successfully calculated.
          content:
            application/json:
              schema:
                description: Connection Rights returned successfully.
                type: array
                maxItems: 1000
                items:
                  $ref: './schemas/connection-rights/ConnectionRightsTotalValue.yaml'
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights/commands/search-installation-form:
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: ConnectionRights.Read
      tags:
          - ConnectionRights
      summary: Search for Installation Form.
      description: |-
          ### Remarks
          - passed model has to meet validation requirements described below.
      operationId: searchInstallationForm
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/SearchInstallationFormCommand.yaml'
        required: true
      responses:
          '200':
            description: Found Installation Form.
            content:
             application/json:
              schema:
                type: array
                maxItems: 1000000
                description: Found Installation Form.
                items:
                  $ref: './schemas/connection-rights/FormReference.yaml'
          '400':
            $ref: "./responses/common.yaml#/components/responses/400"
          '401':
            $ref: './responses/common.yaml#/components/responses/401'
          '403':
            $ref: './responses/common.yaml#/components/responses/403'
          '404':
            $ref: './responses/common.yaml#/components/responses/404'
          '429':
            $ref: './responses/common.yaml#/components/responses/429'
          '500':
            $ref: './responses/common.yaml#/components/responses/500'
          '502':
            $ref: './responses/common.yaml#/components/responses/502'
          '503':
            $ref: './responses/common.yaml#/components/responses/503'
          '504':
            $ref: './responses/common.yaml#/components/responses/504'

  /v2/register-requirements/commands/get-default-values-for-new-register-requirement:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirements
      summary: Get Default Values for new Register Requirement.
      description: Passed model has to meet validation requirements described below.
      operationId: getDefaultValuesForNewRegisterRequirement
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/register-requirements/GetDefaultValuesForNewRegisterRequirementCommand.yaml'
        required: true
      responses:
        '200':
          description: Successfully obtained default values.
          content:
            application/json:
              schema:
                $ref: './schemas/register-requirements/GetDefaultValuesForNewRegisterRequirementResponse.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/interruptions/commands/get-paged-interruptions:
    post:
      x-authorization: Interruptions.Read
      tags:
        - Interruptions
      summary: Returns paged result of interruptions.
      description: Returns paged result of interruptions.
      operationId: getPagedInterruptions
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/interruptions/GetPagedInterruptionsCommand.yaml'
        required: true
      responses:
        '200':
          description: Successfully obtained interruptions list.
          content:
            application/json:
              schema:
                $ref: './schemas/interruption.yaml#/components/schemas/GetPagedInterruptionsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/interruptions/commands/get-grouped-interruptions:
    post:
      x-authorization: Interruptions.Read
      tags:
        - Interruptions
      summary: Returns grouped by meter frame list of interruptions.
      description: Returns grouped by meter frame list of interruptions.
      operationId: getGroupedInterruptions
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/interruptions/GetGroupedInterruptionsCommand.yaml'
        required: true
      responses:
        '200':
          description: Successfully obtained interruptions list.
          content:
            application/json:
              schema:
                $ref: './schemas/interruption.yaml#/components/schemas/GetGroupedInterruptionsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/market-participants/grid-areas:
    get:
      tags:
        - GridAreas
      summary: Get Grid Areas
      description: List of active internal Grid Areas with all details for given Role and Tenant (from JWT token)
      operationId: getGridAreas
      x-authorization: ConnectionPoints.Read
      responses:
        '200':
          description: Object with list of grid areas
          content:
            application/json:
              schema:
                $ref: './schemas/market-participants/GridAreasQueryResponse.yaml'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
      security:
        - Jwt: []

  /v2/gis-topology/commands/get-electricity-equipment-containers-by-bounding-box:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - GisTopology
      summary: Gets electricity equipment containers based on bounding box (region of interest).
      description: |-
        ### Result
        Returns collection of electricity equipment containers with information about location coordinates based on requested bounding box.
      operationId: searchElectricityEquipmentContainersByBoundingBox
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/gis-topology/ElectricityEquipmentContainersSearchByBoundingBoxModel.yaml'
        required: true
      responses:
        '200':
          description: Collection of electricity equipment containers which coordinates intersect with requested bounding box.
          content:
            application/json:
              schema:
                type: array
                description: Equipment container matching search criteria.
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/gis-topology/ElectricityEquipmentContainer.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/gis-topology/commands/get-electricity-topology-navigation-item:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - GisTopology
      summary: Gets electricity topology item containing information about parents and children, based on item identifier.
      description: |-
        ### Result
        Returns electricity topology graph navigation object consisting of information about itself together with its parents and children - upper and lower lever items directly connected to it.
      operationId: getElectricityTopologyNavigationItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/gis-topology/GetElectricityTopologyNodeNavigationItemRequest.yaml'
        required: true
      responses:
        '200':
          description: Topology items directly connected to equipment container. NULL if not found.
          content:
            application/json:
              schema:
                description: Electricity topology navigation item.
                nullable: true
                allOf:
                  - $ref: './schemas/gis-topology/ElectricityTopologyNavigationItem.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/gis-topology/commands/get-electricity-topology-path-from-node-to-container:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - GisTopology
      summary: Gets electricity topology path (colection of elements in route) from topology node to equipment container.
      description: |-
        ### Result
        Returns electricity topology path between specified topology node and equipment container. Path is described as collection of navigation items, each with nearest children and parents.
      operationId: getElectricityTopologyPathFromNodeToContainer
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/gis-topology/GetElectricityTopologyPathFromNodeToContainerRequest.yaml'
        required: true
      responses:
        '200':
          description: Topology items collection in path between topology node and equipment container.
          content:
            application/json:
              schema:
                type: array
                description: Topology items collection in path between topology node and equipment container.
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/gis-topology/ElectricityTopologyNavigationItem.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

components:
  securitySchemes:
    Jwt:
      $ref: "./securitySchemes/Jwt.yaml"
