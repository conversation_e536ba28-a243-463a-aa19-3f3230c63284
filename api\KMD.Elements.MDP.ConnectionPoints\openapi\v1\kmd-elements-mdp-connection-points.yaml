openapi: 3.0.3
info:
  title: KMD.Elements.MDP.ConnectionPoints
  description: |-
    ## Master Data Processes Metering Points API
    Stability level: PREVIEW<br/>

    ## Capabilities
    The API allows to:
    - Modify connection point (initiate master data process).
    - Getting User list.

  termsOfService: "https://www.kmd.net/terms-of-use"

  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>

  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"

  version: "1.38"
  x-maintainers: Team-MD-3

servers:
  - url: http://localhost:5730

security:
  - Bearer: []

tags:
  - name: ConnectionPoint
    description: Methods related to connection point data.
  - name: FeatureSwitch
    description: Methods related to feature switches.
  - name: MeterFrame
    description: Methods related to fetching meter Frame data.
  - name: MeteringPoint
    description: Methods related to metering point data.
  - name: Process
    description: Methods using for all processes
  - name: RegisterRequirement
    description: Methods related to fetching register requirements.
  - name: Users
    description: Methods related to fetching users data.
  - name: ValueLists
    description: Value lists used in mdp connection points.
  - name: WorkOrder
    description: Methods using for work orders management.

paths:
  /v1/processes:
    post:
      tags:
        - Process
      summary: Create a new process
      description: Create a new process if there is no existing one for the given connection point
      operationId: createNewProcess
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: Id of connection point for which will be created new process.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateProcessRequestBody"
        required: true
      responses:
        "200":
          description: "Process draft created"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessCreatedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/connection-point:
    post:
      tags:
        - Process
      summary: Start process of creating a new connection point
      description: Start process of creating a new connection point
      operationId: startCreateConnectionPointProcess
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: Basic data for create connection point using temaplate.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateConnectionPointRequestBody"
        required: true
      responses:
        "200":
          description: "Process draft created"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessCreatedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/restart:
    post:
      tags:
        - Process
      summary: Restart process.
      description: Restart process.
      operationId: restartProcess
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: Date for restarting process.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RestartProcessRequestBody"
        required: true
      responses:
        "202":
          description: "Process restarted"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/subprocesses/{subprocessId}/restart:
    post:
      tags:
        - Process
      summary: Restart subprocess of process.
      description: Restart subprocess of process.
      operationId: restartProcessSubprocess
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/SubprocessId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Subprocess restarted"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/cancel:
    post:
      tags:
        - Process
      summary: Cancel process for draft state.
      description: Cancel process for draft state.
      operationId: cancelProcess
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Process draft canceled"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}:
    get:
      tags:
        - Process
      summary: Get details for process.
      description: Get details for process.
      operationId: getProcessDetails
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Process details found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessDetailsResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    put:
      tags:
        - Process
      summary: Update data on process.
      description: Update data on process.
      operationId: updateProcessData
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for update process data.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateProcessDataRequestBody"
        required: true
      responses:
        "202":
          description: Data changed successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/processes/{processId}/update-target-date:
    put:
      tags:
        - Process
      summary: Update target date on process.
      description: Update target date on process.
      operationId: updateTargetDateForProcess
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateTargetDateRequestBody"
        required: true
      responses:
        "202":
          description: Data changed successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/processes/{processId}/preview-status:
    get:
      tags:
        - Process
      summary: Get information about readiness for preview process.
      description: Get information about readiness for preview process.
      operationId: getPreviewStatus
      x-authorization: ConnectionPoints.Read
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Preview status returned.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PreviewStatusResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/available-process-types:
    get:
      tags:
        - Process
      summary: Get available process types based on permissions and connection point data for specific initialized process.
      description: Get available process types based on permissions and connection point data for specific initialized process.
      operationId: getAvailableProcessTypes
      x-authorization: ConnectionPoints.Read
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Available process types returned.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AvailableProcessTypesResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
  /v1/processes/{processId}/available-supply-types:
    get:
      tags:
        - Process
      summary: Get available supply types based on permissions and connection point data for specific initialized process.
      description: Get available supply types based on permissions and connection point data for specific initialized process.
      operationId: getAvailableProcessSupplyTypes
      x-authorization: ConnectionPoints.Read
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ProcessTypeParam"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Available supply types returned.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AvailableProcessSupplyTypesResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/update-main-data:
    post:
      tags:
        - Process
      summary: Update main data on process manually by user.
      description: Update main data on process manually by user.
      operationId: updateManuallyMainDataForProcess
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateManuallyMainDataProcessRequestBody"
        required: true
      responses:
        "202":
          description: Data changed successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/processes/{processId}/status:
    get:
      tags:
        - Process
      summary: Returns the status of a process.
      description: Returns the status of a process.
      operationId: getProcessStatus
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Process status returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessStatus"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/start:
    post:
      tags:
        - Process
      summary: Start common processes.
      description: Start common processes.
      operationId: startProcess
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model includes characteristics describing target date.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StartProcessRequestBody"
        required: true
      responses:
        "202":
          description: "Process draft started"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}:
    get:
      tags:
        - ConnectionPoint
      summary: Get Connection Point for process.
      description: Get Connection Point for process.
      operationId: getConnectionPoint
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Connection Point data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConnectionPointResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/metering-points:
    get:
      tags:
        - MeteringPoint
      summary: Get Metering Point list
      description: Get Metering Point list
      operationId: getMeteringPoints
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Metering points list data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeteringPointListResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    post:
      tags:
        - MeteringPoint
      summary: Create Metering point draft for process.
      description: Create Metering point draft for process.
      operationId: createMeteringPoint
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      requestBody:
        description: The model includes characteristics describing Metering Point.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeteringPointRequestBody"
        required: true
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Metering point draft created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/work-orders:
    get:
      tags:
        - WorkOrder
      summary: Get Work order list for upsert connection point process.
      description: Get Work order list for upsert connection point process.
      operationId: getWorkOrders
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Work order list data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkOrderListResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    post:
      tags:
        - WorkOrder
      summary: Create work order draft for process.
      description: Create Work order draft for process.
      operationId: createWorkOrder
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      requestBody:
        description: The model includes characteristics describing Work Order.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateWorkOrderRequestBody"
        required: true
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Work order draft created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "409":
          $ref: "#/components/responses/409"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/internal-equipments:
    get:
      tags:
        - ConnectionPoint
      summary: Loads internal equipments list for connection point.
      description: Loads internal equipments list for connection point.
      operationId: getInternalEquipmentsForConnectionPoint
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Connection point data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConnectionPointInternalEquipmentsResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/external-equipments:
    get:
      tags:
        - ConnectionPoint
      summary: Loads external equipments list for meter frame.
      description: Loads external equipments list for meter frame.
      operationId: getExternalEquipmentsForConnectionPoint
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Connection point data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConnectionPointExternalEquipmentsResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/work-orders/last:
    get:
      tags:
        - WorkOrder
      summary: Get last work order for connection point.
      description: Get last work order for connection point.
      operationId: getLastWorkOrderForConnectionPoint
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/WorkOrderType"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Connection point work order configuration data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkOrderConfigurationResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/update:
    post:
      tags:
        - ConnectionPoint
      summary: Update Connection point in selected process.
      description: Update Connection point in selected process.
      operationId: updateConnectionPoint
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model includes characteristics describing Connection Point.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ModifyConnectionPointRequestBody"
        required: true
      responses:
        "202":
          description: "Process draft created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/metering-points/{meteringPointVirtualId}:
    get:
      tags:
        - MeteringPoint
      summary: Get Metering Point for process.
      description: Get Metering Point for process.
      operationId: getMeteringPoint
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Metering Point data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeteringPointResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - MeteringPoint
      summary: Delete Metering point draft for process.
      description: Delete Metering point draft for process.
      operationId: deleteMeteringPoint
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/metering-points/{meteringPointVirtualId}/update-mp:
    get:
      tags:
        - MeteringPoint
      summary: Gets update Metering point draft for process.
      description: Get update Metering point draft for process.
      operationId: getUpdateMeteringPoint
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Metering Point process data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateMeteringPointResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    post:
      tags:
        - MeteringPoint
      summary: Update Metering point draft for process.
      description: Update Metering point draft for process.
      operationId: updateMeteringPoint
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeteringPointRequestBody"
        required: true
      responses:
        "202":
          description: "Metering point draft updated"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - MeteringPoint
      summary: Deletes update Metering point draft for process.
      description: Deletes update Metering point draft for process.
      operationId: deleteUpdateMeteringPoint
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Metering point draft subprocess removed"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/metering-points/{meteringPointVirtualId}/change-settlement-method:
    get:
      tags:
        - MeteringPoint
      summary: Gets update Metering point settlement method draft for process.
      description: Get update Metering point settlement method draft for process.
      operationId: getUpdateMeteringPointSettlementMethod
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Metering Point process data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateMeteringPointSettlementMethodResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    post:
      tags:
        - MeteringPoint
      summary: Update Metering point draft for process.
      description: Update Metering point draft for process.
      operationId: updateMeteringPointSettlementMethod
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeteringPointSettlementMethodRequestBody"
        required: true
      responses:
        "202":
          description: "Metering point draft updated"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - MeteringPoint
      summary: Deletes update Metering point draft for process.
      description: Deletes update Metering point draft for process.
      operationId: deleteUpdateMeteringPointSettlementMethod
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Metering point draft subprocess removed"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/metering-points/{meteringPointVirtualId}/change-address:
    get:
      tags:
        - MeteringPoint
      summary: Gets update Metering point draft for process.
      description: Get update Metering point draft for process.
      operationId: getUpdateMeteringPointChangeAddress
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Metering Point process data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateMeteringPointAddressResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    post:
      tags:
        - MeteringPoint
      summary: Update Metering point draft for process.
      description: Update Metering point draft for process.
      operationId: updateMeteringPointChangeAddress
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeteringPointAddressRequestBody"
        required: true
      responses:
        "202":
          description: "Metering point draft updated"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - MeteringPoint
      summary: Deletes update Metering point draft for process.
      description: Deletes update Metering point draft for process.
      operationId: deleteUpdateMeteringPointChangeAddress
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Metering point draft subprocess removed"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/metering-points/{meteringPointVirtualId}/change-formula:
    get:
      tags:
        - MeteringPoint
      summary: Gets update Metering point draft for process.
      description: Get update Metering point draft for process.
      operationId: getUpdateMeteringPointChangeFormula
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Metering Point process data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateMeteringPointFormulaResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    post:
      tags:
        - MeteringPoint
      summary: Update Metering point draft for process.
      description: Update Metering point draft for process.
      operationId: updateMeteringPointChangeFormula
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeteringPointFormulaRequestBody"
        required: true
      responses:
        "202":
          description: "Metering point draft updated"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - MeteringPoint
      summary: Deletes update Metering point draft for process.
      description: Deletes update Metering point draft for process.
      operationId: deleteUpdateMeteringPointChangeFormula
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Metering point draft subprocess removed"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/metering-points/{meteringPointVirtualId}/close-down:
    post:
      tags:
        - MeteringPoint
      summary: Create close down mp process.
      description: Create close down mp process.
      operationId: closeDownMeteringPoint
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for setting additional information for subprocess.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangeMeteringPointConnectionStatusRequestBody"
        required: false
      responses:
        "202":
          description: "Metering point close down subprocess created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - MeteringPoint
      summary: Deletes close down Metering point draft for process.
      description: Deletes close down Metering point draft for process.
      operationId: deleteCloseDownMeteringPoint
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Metering point draft subprocess removed"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/metering-points/{meteringPointVirtualId}/connect:
    post:
      tags:
        - MeteringPoint
      summary: Create connect mp process.
      description: Create connect mp process.
      operationId: connectMeteringPoint
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for setting additional information for subprocess.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangeMeteringPointConnectionStatusRequestBody"
        required: false
      responses:
        "202":
          description: "Metering point connect subprocess created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - MeteringPoint
      summary: Deletes connect Metering point draft for process.
      description: Deletes connect Metering point draft for process.
      operationId: deleteConnectMeteringPoint
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Metering point draft subprocess removed"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/metering-points/{meteringPointVirtualId}/change-net-settlement-group-attributes:
    get:
      tags:
        - MeteringPoint
      summary: Get change net settlement group attributes of Metering point for process.
      description: Get change net settlement group attributes of Metering point draft for process.
      operationId: getChangeNetSettlementGroupAttributes
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Metering Point change net settlement group attributes processes.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChangeNetSettlementGroupAttributesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    post:
      tags:
        - MeteringPoint
      summary: Create change net settlement group attributes process.
      description: Create change net settlement group attributes process.
      operationId: changeNetSettlementGroupAttributes
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangeNetSettlementGroupAttributesRequestBody"
        required: true
      responses:
        "202":
          description: "Metering point change net settlement group attributes created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - MeteringPoint
      summary: Deletes change net settlement group Metering point draft for process.
      description: Deletes change net settlement group Metering point draft for process.
      operationId: deleteChangeNetSettlementGroupAttributes
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeteringPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Metering point draft subprocess removed"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/application-users/list:
    post:
      tags:
        - Users
      summary: Returns paged list of users.
      description: Returns paged list of users.
      operationId: getApplicationUsersList
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      requestBody:
        description: Search criteria.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Browse"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Application users found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UsersListPagedResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/grid-areas/list:
    get:
      tags:
        - Process
      summary: Returns list of available grid areas for tenant.
      description: Returns list of available grid areas for tenant.
      operationId: getTenantGridAreaList
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Tenant grid areas found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TenantGridAreas"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/meter-frames:
    get:
      tags:
        - MeterFrame
      summary: Returns paged list of meter frames.
      description: Returns paged list of meter frames.
      operationId: getMeterFrames
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/SupplyType"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Connection Point data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterFrameListResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    post:
      tags:
        - MeterFrame
      summary: Create Meter frame draft for process.
      description: Create Meter frame draft for process.
      operationId: createMeterFrame
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      requestBody:
        description: The model includes characteristics describing Meter Frame.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMeterFrameRequestBody"
        required: true
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Meter frame draft created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/meter-frames/{meterFrameVirtualId}:
    get:
      tags:
        - MeterFrame
      summary: Returns details of meter frame.
      description: Returns details of meter frame.
      operationId: getMeterFrame
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Meter Frame data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterFrameResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    delete:
      tags:
        - MeterFrame
      summary: Delete Meter frame draft for process.
      description: Delete Meter frame  draft for process.
      operationId: deleteMeterFrame
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model includes information about user confirm remove other entities.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RemoveRelatedEntities"
        required: false
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    put:
      tags:
        - MeterFrame
      summary: Update Meter frame draft for process.
      description: Update Meter frame  draft for process.
      operationId: updateMeterFrame
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMeterFrameRequestBody"
        required: true
      responses:
        "204":
          description: Data changed successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/meter-frames/{meterFrameVirtualId}/register-requirements:
    get:
      tags:
        - RegisterRequirement
      summary: Loads Register Requirements list for Meter frame.
      description: Loads Register Requirements list for Meter frame.
      operationId: getRegisterRequirementsForMeterFrame
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Meter Frame data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegisterRequirementsListResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    post:
      tags:
        - RegisterRequirement
      summary: Create Register Requirement draft for Meter frame.
      description: Create Register Requirement draft for Meter frame.
      operationId: createRegisterRequirement
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      requestBody:
        description: The model includes characteristics describing Register Requirement.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateRegisterRequirementRequestBody"
        required: true
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "202":
          description: "Register Requirement draft created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/meter-frames/{meterFrameVirtualId}/internal-equipments:
    get:
      tags:
        - MeterFrame
      summary: Loads internal equipments list for meter frame.
      description: Loads internal equipments list for meter frame.
      operationId: getInternalEquipmentsForMeterFrame
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Meter frame data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterFrameInternalEquipmentsResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/meter-frames/{meterFrameVirtualId}/external-equipments:
    get:
      tags:
        - MeterFrame
      summary: Loads external equipments list for meter frame.
      description: Loads external equipments list for meter frame.
      operationId: getExternalEquipmentsForMeterFrame
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Meter frame data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterFrameExternalEquipmentsResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/work-orders/bulk-upsert:
    post:
      tags:
        - WorkOrder
      summary: Create work orders draft for process.
      description: Create Work orders draft for process.
      operationId: createWorkOrdersBulk
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All
      requestBody:
        description: The model includes characteristics describing Work Orders for create.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BulkCreateWorkOrdersRequestBody"
        required: true
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Work orders draft created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/work-orders/bulk-delete:
    post:
      tags:
        - WorkOrder
      summary: Delete work orders draft for process.
      description: Delete Work orders draft for process.
      operationId: deleteWorkOrdersBulk
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All
      requestBody:
        description: The model includes characteristics describing Work Orders for create.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BulkDeleteWorkOrdersRequestBody"
        required: true
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Work orders draft deleted"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/work-orders/{workOrderVirtualId}:
    get:
      tags:
        - WorkOrder
      summary: Returns details of work order.
      description: Returns details of work order.
      operationId: getWorkOrder
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/WorkOrderVirtualId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Meter Frame data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkOrderResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    delete:
      tags:
        - WorkOrder
      summary: Delete Work order draft for process.
      description: Delete Work order  draft for process.
      operationId: deleteWorkOrder
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/WorkOrderVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/meter-frames/{meterFrameVirtualId}/register-requirements/{registerRequirementVirtualId}:
    get:
      tags:
        - RegisterRequirement
      summary: Get Register Requirement draft for process.
      description: Get Register Requirement draft for process.
      operationId: getRegisterRequirement
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/RegisterRequirementVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "200":
          description: Meter Frame data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegisterRequirementResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - RegisterRequirement
      summary: Delete Register Requirement draft for process.
      description: Delete Register Requirement draft for process.
      operationId: deleteRegisterRequirement
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/RegisterRequirementVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    put:
      tags:
        - RegisterRequirement
      summary: Update Register Requirement draft for process.
      description: Update Register Requirement draft for process.
      operationId: updateRegisterRequirement
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/RegisterRequirementVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      requestBody:
        description: The model for manual update main data process by user.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateRegisterRequirementRequestBody"
        required: true
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/meter-frames/{meterFrameVirtualId}/register-requirements/{registerRequirementVirtualId}/stop:
    post:
      tags:
        - RegisterRequirement
      summary: Create register requirement stop subprocess.
      description: Create register requirement stop subprocess.
      operationId: stopRegisterRequirement
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/RegisterRequirementVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Register requirement stop subprocess created"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
    delete:
      tags:
        - RegisterRequirement
      summary: Deletes register requirement stop subprocess.
      description: Deletes register requirement stop subprocess.
      operationId: deleteStopRegisterRequirement
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/RegisterRequirementVirtualId"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
      responses:
        "202":
          description: "Register requirement stop subprocess removed"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/processes/{processId}/connection-point/{connectionPointVirtualId}/meter-frames/{meterFrameVirtualId}/meter-input-connections:
    get:
      tags:
        - MeterFrame
      summary: Loads Meter Input Connections list for Meter frame.
      description: Loads Meter Input Connections list for Meter frame.
      operationId: getMeterInputConnections
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      parameters:
        - $ref: "#/components/parameters/ProcessId"
        - $ref: "#/components/parameters/ConnectionPointVirtualId"
        - $ref: "#/components/parameters/MeterFrameVirtualId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Meter Input Connections data returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeterInputConnectionListResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/value-lists:
    get:
      x-authorization: ConnectionPoints.Read
      x-authorization-1: Electricity.All,Heating.All,Water.All
      tags:
        - ValueLists
      summary: Fetches Value Lists
      description: |-
        ### Result
        Returns all values lists used in mdp connection points.
      operationId: getValueLists
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Value Lists returned successfully.
          content:
            application/json:
              schema:
                description: Value Lists returned successfully.
                type: array
                maxItems: 1000000
                items:
                  $ref: "#/components/schemas/ValueListModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/conntection-points/{connectionPointId}/active-processes:
    get:
      tags:
        - ConnectionPoint
      summary: Get active processes for connection point.
      description: Get active processes for connection point.
      operationId: getActiveProcessesForConnectionPoint
      x-authorization: ConnectionPoints.Read
      parameters:
        - $ref: "#/components/parameters/ConnectionPointId"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Prcesses list returned successfully.
          content:
            application/json:
              schema:
                description: Prcesses list returned successfully.
                type: array
                maxItems: 100
                items:
                  $ref: "#/components/schemas/ProcessInfo"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"

  /v1/feature-switches:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - FeatureSwitch
      summary: Get feature switches.
      description: Get feature switches.
      operationId: getFeatureSwitches
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeaderNullable"
      responses:
        "200":
          description: Feature switches returned successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FeatureSwitches"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

components:
  schemas:
    Browse:
      description: Browse object.
      allOf:
        - $ref: "./schemas/PagedQueryModel.yaml"
        - type: object
          description: Browse object.
          properties:
            filter:
              $ref: "./schemas/Filter.yaml"
          additionalProperties: false

    UsersListPagedResult:
      description: Users list paged result.
      allOf:
        - $ref: "./schemas/PagedResult.yaml"
        - type: object
          description: User details search result.
          properties:
            results:
              type: array
              description: Array of users objects.
              items:
                $ref: "./schemas/User.yaml"
              maxItems: 256
          additionalProperties: false

    TenantGridAreas:
      description: Grid Area ids available on specific tenant for electricity.
      nullable: true
      type: array
      maxItems: 3
      items:
        $ref: "./schemas/DataTypes/ShortString.yaml"
      additionalProperties: false

    MeterFrameListResult:
      $ref: "./schemas/MeterFrameModel/MeterFramesListResult.yaml"

    PreviewStatusResult:
      $ref: "./schemas/ProcessModel/PreviewStatusResult.yaml"

    AvailableProcessTypesResult:
      $ref: "./schemas/ProcessModel/AvailableProcessTypesResult.yaml"

    AvailableProcessSupplyTypesResult:
      $ref: "./schemas/ProcessModel/AvailableProcessSupplyTypesResult.yaml"

    MeterFrameResult:
      $ref: "./schemas/MeterFrameModel/MeterFrameResult.yaml"

    WorkOrderListResult:
      $ref: "./schemas/WorkOrderModel/WorkOrdersListResult.yaml"

    WorkOrderResult:
      $ref: "./schemas/WorkOrderModel/WorkOrder.yaml"

    WorkOrderConfigurationResult:
      $ref: "./schemas/WorkOrderModel/WorkOrderConfiguration.yaml"

    MeteringPointListResult:
      $ref: "./schemas/MeteringPointModel/MeteringPointsListResult.yaml"

    MeteringPointResult:
      $ref: "./schemas/MeteringPointModel/MeteringPoint.yaml"

    CreateMeteringPointRequestBody:
      $ref: "./schemas/MeteringPointModel/CreateMeteringPoint.yaml"

    UpdateMeteringPointRequestBody:
      $ref: "./schemas/MeteringPointModel/UpdateMeteringPoint.yaml"

    UpdateMeteringPointResponse:
      $ref: "./schemas/MeteringPointModel/UpdateMeteringPoint.yaml"

    UpdateMeteringPointAddressRequestBody:
      $ref: "./schemas/MeteringPointModel/UpdateMeteringPointAddress.yaml"

    UpdateMeteringPointAddressResponse:
      $ref: "./schemas/MeteringPointModel/UpdateMeteringPointAddress.yaml"

    UpdateMeteringPointSettlementMethodRequestBody:
      $ref: "./schemas/MeteringPointModel/UpdateMeteringPointSettlementMethod.yaml"

    UpdateMeteringPointSettlementMethodResponse:
      $ref: "./schemas/MeteringPointModel/UpdateMeteringPointSettlementMethod.yaml"

    UpdateMeteringPointFormulaRequestBody:
      $ref: "./schemas/MeteringPointModel/UpdateMeteringPointFormula.yaml"

    UpdateMeteringPointFormulaResponse:
      $ref: "./schemas/MeteringPointModel/UpdateMeteringPointFormula.yaml"

    ChangeNetSettlementGroupAttributesResponse:
      $ref: "./schemas/MeteringPointModel/ChangeNetSettlementGroupAttributesResult.yaml"

    ChangeNetSettlementGroupAttributesRequestBody:
      $ref: "./schemas/MeteringPointModel/ChangeNetSettlementGroupAttributes.yaml"

    ConnectionPointResult:
      type: object
      additionalProperties: false
      description: Connection point.
      required:
        - isDataLoaded
      properties:
        connectionPoint:
          allOf:
            - $ref: "./schemas/ConnectionPointModel/ConnectionPoint.yaml"
          description: ConnectionPoint object.
          nullable: true
        isDataLoaded:
          allOf:
            - $ref: "./schemas/DataTypes/Boolean.yaml"
          description: This property informs whether object was successfully fetch from other domain
          nullable: false
        isEditable:
          allOf:
            - $ref: "./schemas/DataTypes/Boolean.yaml"
          description: |-
            Indicates whether entity can be editable.

    ModifyConnectionPointRequestBody:
      $ref: "./schemas/ConnectionPointModel/UpsertConnectionPoint.yaml"

    ProcessDetailsResult:
      $ref: "./schemas/ProcessModel/ProcessDetails.yaml"

    UpdateManuallyMainDataProcessRequestBody:
      $ref: "./schemas/ProcessModel/UpdateManuallyMainDataProcess.yaml"

    ProcessCreatedResult:
      $ref: "./schemas/ProcessModel/ProcessCreatedResult.yaml"

    CreateProcessRequestBody:
      type: object
      additionalProperties: false
      description: Parameters of create connection point process.
      required:
        - connectionPointId
      properties:
        connectionPointId:
          allOf:
            - $ref: "./schemas/DataTypes/Guid.yaml"
          description: ConnectionPoint identifier.
          example: "aab4718d-3082-4ded-9756-e12b1270f46b"

    RestartProcessRequestBody:
      type: object
      additionalProperties: false
      description: Body of request to restart process in state requires manual handling.
      properties:
        targetDate:
          allOf:
            - $ref: "./schemas/DataTypes/DateTime.yaml"
          description: Target date for process execution
          nullable: true

    CreateConnectionPointRequestBody:
      $ref: "./schemas/ConnectionPointModel/CreateConnectionPointProcess.yaml"

    UpdateProcessDataRequestBody:
      type: object
      additionalProperties: false
      description: Parameters of process to be updated.
      required:
        - processType
        - supplyType
        - displayPreview
        - templateType
      properties:
        targetDate:
          allOf:
            - $ref: "./schemas/DataTypes/DateTime.yaml"
          description: Target date for process execution
          nullable: true
        supplyType:
          allOf:
            - $ref: "./schemas/SupplyType.yaml"
          description: Supply type for specific process
          nullable: false
        processType:
          allOf:
            - $ref: "./schemas/ProcessModel/ProcessType.yaml"
          description: Process type.
          nullable: false
        displayPreview:
          allOf:
            - $ref: "./schemas/DataTypes/Boolean.yaml"
          description: Indicates, whether user wants to preview changes before starting process.
          nullable: false
        templateId:
          $ref: "./schemas/DataTypes/GuidNullable.yaml"
        templateType:
          allOf:
            - $ref: "./schemas/ProcessModel/TemplateType.yaml"
          description: Template type.
          nullable: false

    UpdateTargetDateRequestBody:
      type: object
      additionalProperties: false
      description: Target date for process to be updated.
      required:
        - targetDate
      properties:
        targetDate:
          $ref: "./schemas/DataTypes/DateTime.yaml"

    ChangeMeteringPointConnectionStatusRequestBody:
      type: object
      additionalProperties: false
      description: Additional parameters for change metering point state.
      properties:
        occurrence:
          $ref: "./schemas/DataTypes/DateTime.yaml"

    StartProcessRequestBody:
      type: object
      additionalProperties: false
      description: Parameters of start close down supply type process process draft.
      properties:
        targetDate:
          $ref: "./schemas/DataTypes/DateTimeNullable.yaml"

    ProcessStatus:
      $ref: "./schemas/ProcessModel/Status.yaml"

    CreateMeterFrameRequestBody:
      $ref: "./schemas/MeterFrameModel/CreateMeterFrame.yaml"

    MeterFrameInternalEquipmentsResult:
      $ref: "./schemas/MeterFrameModel/MeterFrameInternalEquipmentsResult.yaml"

    MeterFrameExternalEquipmentsResult:
      $ref: "./schemas/MeterFrameModel/MeterFrameExternalEquipmentsResult.yaml"

    UpdateMeterFrameRequestBody:
      $ref: "./schemas/MeterFrameModel/UpdateMeterFrame.yaml"

    CreateWorkOrderRequestBody:
      $ref: "./schemas/WorkOrderModel/CreateWorkOrder.yaml"

    BulkCreateWorkOrdersRequestBody:
      $ref: "./schemas/WorkOrderModel/BulkCreateWorkOrder.yaml"

    BulkDeleteWorkOrdersRequestBody:
      $ref: "./schemas/WorkOrderModel/BulkDeleteWorkOrder.yaml"

    RegisterRequirementsListResult:
      $ref: "./schemas/RegisterRequirementModel/RegisterRequirementsListResult.yaml"

    CreateRegisterRequirementRequestBody:
      $ref: "./schemas/RegisterRequirementModel/CreateRegisterRequirement.yaml"

    UpdateRegisterRequirementRequestBody:
      $ref: "./schemas/RegisterRequirementModel/UpdateRegisterRequirement.yaml"

    RegisterRequirementResult:
      $ref: "./schemas/RegisterRequirementModel/RegisterRequirement.yaml"

    MeterInputConnectionListResult:
      $ref: "./schemas/MeterInputConnectionModel/MeterInputConnectionListResult.yaml"

    ValueListModel:
      $ref: "./schemas/ValueListModel/ValueList.yaml"

    ConnectionPointInternalEquipmentsResult:
      $ref: "./schemas/ConnectionPointModel/ConnectionPointInternalEquipmentsResult.yaml"

    ConnectionPointExternalEquipmentsResult:
      $ref: "./schemas/ConnectionPointModel/ConnectionPointExternalEquipmentsResult.yaml"

    ProcessInfo:
      $ref: "./schemas/ProcessModel/ProcessInfo.yaml"

    RemoveRelatedEntities:
      type: object
      additionalProperties: false
      description: Flags inform that user agree with remove other entities.
      properties:
        workOrders:
          $ref: "./schemas/DataTypes/Boolean.yaml"

    FeatureSwitches:
      $ref: "./schemas/FeatureSwitches.yaml"

  parameters:
    ConnectionPointVirtualId:
      $ref: "./parameters/ConnectionPointVirtualId.yaml"
    MeterFrameVirtualId:
      $ref: "./parameters/MeterFrameVirtualId.yaml"
    RegisterRequirementVirtualId:
      $ref: "./parameters/RegisterRequirementVirtualId.yaml"
    MeteringPointVirtualId:
      $ref: "./parameters/MeteringPointVirtualId.yaml"
    EsMessageIdInHeader:
      $ref: "./parameters/EsMessageIdInHeader.yaml"
    EsCorrelationIdInHeader:
      $ref: "./parameters/EsCorrelationIdInHeader.yaml"
    EsCorrelationIdInHeaderNullable:
      $ref: "./parameters/EsCorrelationIdInHeaderNullable.yaml"
    ProcessId:
      $ref: "./parameters/ProcessId.yaml"
    ProcessTypeParam:
      $ref: "./parameters/ProcessTypeParam.yaml"
    SubprocessId:
      $ref: "./parameters/SubprocessId.yaml"
    SupplyType:
      $ref: "./parameters/SupplyType.yaml"
    WorkOrderVirtualId:
      $ref: "./parameters/WorkOrderVirtualId.yaml"
    WorkOrderType:
      $ref: "./parameters/WorkOrderType.yaml"
    ConnectionPointId:
      $ref: "./parameters/ConnectionPointId.yaml"

  responses:
    "400":
      $ref: "./responses/400.yaml"
    "401":
      $ref: "./responses/401.yaml"
    "403":
      $ref: "./responses/403.yaml"
    "404":
      $ref: "./responses/404.yaml"
    "409":
      $ref: "./responses/409.yaml"
    "500":
      $ref: "./responses/500.yaml"
    "503":
      $ref: "./responses/503.yaml"
    "504":
      $ref: "./responses/504.yaml"

  securitySchemes:
    Bearer:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
      type: http
      scheme: bearer
      bearerFormat: JWT
