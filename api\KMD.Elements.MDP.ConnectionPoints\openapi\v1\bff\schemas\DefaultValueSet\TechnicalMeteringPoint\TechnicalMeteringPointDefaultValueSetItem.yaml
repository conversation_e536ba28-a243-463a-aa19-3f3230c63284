type: object
description: Technical Metering Point default value set.
additionalProperties: false
required:
  - id
  - name
  - typeOfMeteringPoint
properties:
  id:
    allOf:
      - $ref: '../../DataTypes/Guid.yaml'
    description: Default Value Set ID.
  name:
    allOf:
      - $ref: '../../DataTypes/ShortString.yaml'
    description: Default Value Set name.
    nullable: false
  typeOfMeteringPoint:
    description: Type of metering point value list reference, can refer 3 different VLs depending on supply type.
    allOf:
      - $ref: '../../DataTypes/Guid.yaml'
  subTypeOfMeteringPoint:
    description: Sub type of Metering Point.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
