type: object
description: |
  Heating attributes for metering point
additionalProperties: false
properties:
  applicationCode:
    nullable: true
    description: Application code for metering point.
    allOf:
      - $ref: '../../DataTypes/MediumString.yaml'
  expectedConsumptionPerYear:
    description: Expected consumption per year.
    allOf:
      - $ref: '../../DataTypes/PositiveIntegerNullable.yaml'
  noMeter:
    type: boolean
    nullable: false
    description: Indicates whether it is a meter-free installation (subscription only).
    example: true
  commonReading:
    type: string
    description: |-
      Indicates whether the meter supplies several units (buildings, apartments, etc.).
    minLength: 1
    maxLength: 1
    pattern: "^(0|1|2)$"
  supplementedInfoBilling:
    pattern: "^.*$"
    type: string
    nullable: true
    minLength: 1
    maxLength: 100
    description: Supply billing info for the Metering Point.
    example: A01
  returnHeatConnected:
    type: boolean
    nullable: false
    example: true
    description: Indicates whether return heating is connected.
  calculateCooling:
    type: boolean
    nullable: false
    example: true
    description: Indicates whether cooling is to be calculated.
  m2Total:
    description: Indicates the total residential area from BBR.
    type: integer
    format: int32
    minimum: 0
    maximum: 2147483647
    example: 12
  m2Business:
    description: Indicates the total business area from BBR.
    type: integer
    format: int32
    minimum: 0
    maximum: 2147483647
    example: 12
  m2Warehouse:
    description: Indicates the total warehouse area from BBR.
    type: integer
    format: int32
    minimum: 0
    maximum: 2147483647
    example: 12
