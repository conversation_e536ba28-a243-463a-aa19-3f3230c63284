asyncapi: 2.6.0
id: https://async.api.kmdelements.com/metering-point-vee-event/
info:
  title: Metering Point data VEE event
  x-maintainers: Team-DP-1
  version: "2.0.4"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    Async API for metering point event needed for VEE process

tags:
  - name: Team-DP-1
    description: Maintained by

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.event.metering-points.vee.v2:
    description: Topic with metering points required by VEE process with all its versions
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Event with all versions off metering point
      description: Event contains all metering point version where metering point version is understood
      operationId: MeteringPointVeeEvent
      message:
        $ref: "#/components/messages/MeteringPointVeeEvent"

components:
  messages:
    MeteringPointVeeEvent:
      name: MeteringPointVeeEvent
      title: Metering Point for VEE process
      summary: Event with metering point version and associated objects for VEE process
      contentType: application/json
      bindings:
        kafka:
          key:
            type: string
            description: Key should be meteringPointId, no compaction
      payload:
        $ref: "#/components/schemas/VeeMeteringPointEventPayload"

  schemas:
    VeeMeteringPointEventPayload:
      title: VeeMeteringPointEventPayload
      description: |
        Metering Point with all its versions
      type: object
      additionalProperties: false
      required:
        - meteringPointId
        - connectionPointId
        - versions
      properties:
        meteringPointId:
          type: string
          description: Metering point id that serves as timeseriesId
          examples:
            - 570715000001318060
        connectionPointId:
          type: string
          examples:
            - 304413e4-da8b-43bd-861d-ec2f894fc67f
        versions:
          type: array
          items:
            $ref: '#/components/schemas/VeeMeteringPointVersion'

    VeeMeteringPointVersion:
      type: object
      additionalProperties: false
      description: |
        VEE Metering point version is smallest overlap between all versioned entities that creates this aggregate.
        Example: when connection point and metering point are versioned and both have 2 version
        we are (usually) getting getting 3 versions of VEMeteringPoint
        +----------------------------------++------------------+
        | Metering Point V1                ||Metering Point V2 |
        +----------------------------------++------------------+
        +----------------------++------------------------------+
        | Connection Point V1  || Connection Point V2          |
        +----------------------++------------------------------+
                                    |
                                    |
                                   \|/
                                    -
        +----------------------++----------++------------------+
        | VEEMeteringPoint V1  || VEEMP V2 ||  VEEMP V3        |
        +----------------------++----------++------------------+
        In other words producer of such messages, must ensure that in period: <applicableAtOrAfter, applicableBefore)
        all provided values are constant thought the whole period. It is not required (but allowed) to create new
        version when none of the fields has changed (eg. change on ConnectionPoint's description or name which are not
        part of connection point in this contract)
      required:
        - applicableAtOrAfter
        - applicableBefore
        - connectionPoint
        - meteringPointType
        - meteringPointSubType
        - meteringPointResolution
      properties:
        applicableAtOrAfter:
          type: string
          description: |
            Date from which this values in this metering point version are true.
            1970-01-01T00:00:00Z if group should be applied since start of the world.
          format: date-time
        applicableBefore:
          type: string
          description: |
            Date until which this values in this metering point version are true.
            9999-12-30T00:00:00Z if group should be applied till the end of the world.
          format: date-time
        sourceMeter:
          $ref: '#/components/schemas/MeteringPointVersionSourceMeter'
        connectionPoint:
          $ref: '#/components/schemas/ConnectionPoint'
        meteringPointType:
          type: string
          $id: MeteringPointType
          description: This will most likely not change between versions/slices
          enum:
            - D01
            - D02
            - D03
            - D04
            - D05
            - D06
            - D07
            - D08
            - D09
            - D10
            - D11
            - D12
            - D13
            - D14
            - D15
            - D16
            - D17
            - D18
            - D19
            - D20
            - E17
            - E18
            - E20
        meteringPointSubType:
          type: string
          $id: MeteringPointSubType
          description: This can change between versions/slices
          enum:
            - D01
            - D02
            - D03
          examples:
            - D01
        meteringPointResolution:
          type: string
          $id: Resolution
          description: Resolution for the metering point readings, which can rarely change between versions/slices, expressed in ISO 8601 duration format.
          enum:
            - PT1M
            - PT5M
            - PT15M
            - PT1H
            - P1D
            - P1M
          default: PT15M
          examples:
            - PT15M
    MeteringPointVersionSourceMeter:
      type: object
      additionalProperties: false
      description: |
        Pointer to source meter's register which is source of metering point readingsf
      properties:
        registerId:
          type: string
          examples:
            - 21a63b40-913e-4209-84e1-9a03cefa6669
    ConnectionPoint:
      type: object
      additionalProperties: false
      title: Connection point
      properties:
        consumerCategory:
          type: string
        netSettlementGroup:
          type: string
        installationTypeValue:
          type: string
        categoryValue:
          type: string
  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
