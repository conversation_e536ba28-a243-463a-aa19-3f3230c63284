type: object
description: Prefilled data of metering point from connection point.
additionalProperties: false
properties:
  connectionPointId:
    description: Connection point id.
    allOf:
      - $ref: '../../DataTypes/Guid.yaml'
  connectionPointNumber:
    description: Connection point number.
    allOf:
      - $ref: '../../DataTypes/ShortString.yaml'
  electricityGridAreaId:
    nullable: true
    description: Electricity available grid areas.
    type: array
    maxItems: 3
    items:
      $ref: '../../DataTypes/ShortString.yaml'
  waterGridAreaId:
    nullable: true
    description: Water available grid areas.
    type: array
    maxItems: 3
    items:
      $ref: '../../DataTypes/ShortString.yaml'
  heatingGridAreaId:
    nullable: true
    description: Heating available grid areas.
    type: array
    maxItems: 3
    items:
      $ref: '../../DataTypes/ShortString.yaml'
  carId:
    description: Central address registry identifier used on connection point.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  netSettlementGroup:
    nullable: true
    description: Net settlement group on connection point.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  availableSupplyTypes:
    description: Available supply types list.
    type: array
    maxItems: 3
    items:
      $ref: '../../SupplyType.yaml'
  supplyTypeMeteringPoints:
    description: List of existing metering points at connection point
    type: array
    maxItems: 3
    items:
      $ref: './SupplyTypeMeteringPoints.yaml'
