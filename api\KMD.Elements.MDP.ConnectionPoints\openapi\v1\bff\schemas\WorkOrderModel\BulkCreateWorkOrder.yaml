type: object
additionalProperties: false
description: Meter Frame model.
required:
  - processId
  - workOrderType
  - workDescription
  - workPurpose
  - useDateForPlanning
  - comment
  - billable
  - requestedAppointmentDate
properties:
  processId:
    description: Internal process identifier'.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  workOrderType:
    description: 'Work Order Type.'
    allOf:
      - $ref: './WorkOrderType.yaml'
  workDescription:
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
    description: 'Work Description - value list identifier.'
  workPurpose:
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
    description: 'Work purpose - value list identifier'
  useDateForPlanning:
    description: 'Indicates that the date should be used for operations planning in workforce because it is important for some reason.'
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
  comment:
    description: 'Comment for work order'
    allOf:
      - $ref: '../DataTypes/LongString.yaml'
  billable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: 'Indicates whether the cost of the work order is billable'
  requestedTechnician:
    allOf:
      - $ref: '../DataTypes/LongStringNullable.yaml'
    description: 'The requested technician to execute the work.'
  requestedAppointmentDate:
    description: 'A date when the work should be carried out by the technician'
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
