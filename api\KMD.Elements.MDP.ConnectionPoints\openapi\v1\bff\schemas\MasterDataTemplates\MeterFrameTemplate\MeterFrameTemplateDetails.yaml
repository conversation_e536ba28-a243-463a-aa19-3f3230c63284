title: MeterFrameTemplateDetails
type: object
description: Metering point template details.
additionalProperties: false
required:
  - id
  - name
properties:
  id:
    $ref: "../../DataTypes/Guid.yaml"
  name:
    $ref: "../../DataTypes/ShortString.yaml"
  description:
    $ref: "../../DataTypes/LongStringNullable.yaml"
  supplyType:
    $ref: "../../SupplyType.yaml"
  meterFrameDefaultValueSet:
    type: object
    description: "Meter frame default value set object"
    nullable: true
    oneOf:
      - $ref: "../DefaultValueSet.yaml"
  registerRequirementDefaultValueSets:
    type: array
    maxItems: 50
    items:
      $ref: "./RegisterRequirementDefaultValueSetDetails.yaml"
    description: List of register requirements default values sets details.
