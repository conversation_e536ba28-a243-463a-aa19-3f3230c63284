title: PaginationParameters
type: object
additionalProperties: false
required:
  - pageNumber
  - pageSize
description: Represents pagination parameters for a paged request.
properties:
  pageNumber:
    type: integer
    format: int32
    description:  The number of the page to retrieve. Defaults to 1.
    default: 1
    minimum: 1
    maximum: 10000
  pageSize:
    type: integer
    format: int32
    description: The number of items per page. Defaults to 50.
    default: 50
    minimum: 1
    maximum: 200
