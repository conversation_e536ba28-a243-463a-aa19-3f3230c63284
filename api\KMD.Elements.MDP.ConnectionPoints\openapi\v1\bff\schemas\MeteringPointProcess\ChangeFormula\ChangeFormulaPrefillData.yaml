type: object
description: |
  Formula for metering point.
additionalProperties: false
required:
  - formula
  - unitType
  - meterReadingOccurrence
  - connectionPointId
properties:
  formula:
    description: Formula for metering point.
    allOf:
      - $ref: "../Formula.yaml"
  unitType:
    description: |-
      Type of Metering Point Unit.
    type: string
    nullable: false
    minLength: 2
    maxLength: 7
    pattern: "^(MWH|M3|Celsius)$"
    example: "MWH"
  meterReadingOccurrence:
    nullable: false
    description: Meter reading occurrence
    minLength: 1
    maxLength: 25
    pattern: "^.*$"
    type: string
  typeOfMeteringPoint:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `TypeOfMeteringPoint`
    pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D21|D22|D23|D24|D25|D26|D27|D28|D29|D30|D99|E17|E18|E20|VA17|VD07|FV17|FV18|FD06|FD07|FD14|FD10|FD11)$
  subTypeOfMeteringPoint:
    nullable: true
    type: string
    maxLength: 3
    pattern: ^(D01|D02|D03)$
    description: Is mapped to `SubTypeOfMeteringPoint`
  connectionPointId:
    nullable: false
    description: Connection point, which metering point belongs to.
    allOf:
      - $ref: "../../DataTypes/Guid.yaml"
  meteringPointVersionId:
    description: Id of Metering Point Version.
    allOf:
      - $ref: "../../DataTypes/ShortStringNullable.yaml"
    example: "571313190000020132_2023-07-17"
  meteringPointConnectionStatus:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `MeteringPointConnectionStatus`
    pattern: ^(D02|D03|E22|E23)$
