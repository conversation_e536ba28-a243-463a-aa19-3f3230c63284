type: object
description: |
  Price element association that connects a price element to a metering point in a given time period.
additionalProperties: false
required:
  - isSimpleFormula
properties:
  isSimpleFormula:
    description: |
      Indicates whether a formula is calculated on the basis of a formula expression or calculated on the basis
      of a meter reading on a specific meter frame register requirement (simple formula).
      When it is a "simple formula" the formula will not have a reference to formulaType.
    allOf:
      - $ref: '../../DataTypes/Boolean.yaml'
  formulaType:
    description: |
      A reference to a FormulaType that indicates which formula template to use.
      If isSimpleFormula is False then FormulaType is Mandatory.
    allOf:
      - $ref: './DefaultValueSetsFormulaType.yaml'
    nullable: true
  formula:
    description: |
      The formula that describes how data for the measuring point's time series is calculated using data
      from the individual formula parameters. The formula is sent to MDM, and all the parameters' data
      must be found in MDM in order for MDM to calculate the result profile.
    allOf:
      - $ref: '../../DataTypes/DescriptionString.yaml'
  formulaParameters:
    description: Metering Point Formula Parameters collection.
    type: array
    minItems: 0
    maxItems: 10000
    items:
      $ref: './DefaultValueSetsFormulaParameter.yaml'
  description:
    description: A description of what resulting data the formula generates.
    allOf:
      - $ref: '../../DataTypes/DescriptionString.yaml'
    nullable: true
