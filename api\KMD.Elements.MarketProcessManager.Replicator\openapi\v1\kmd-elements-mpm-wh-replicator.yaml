openapi: 3.0.3
info:
  title: KMD.Elements.MarketProcessManager.Replicator
  x-maintainers: Team-MC-1
  description: |-
    ## MarketProcessManager.Replicator
    Service responsible for replicating messages published in the DHM/DHS, transferring to the DHS/DHM layer with simultaneous tenant mapping.
  termsOfService: "https://www.kmd.net/terms-of-use"
  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>
  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"
  version: "1.18"

servers:
  - url: "https://localhost:5051"
    description: Localhost

tags:
  - name: VvMeteringPointResults
    description: Methods (on DHM side) allow to send result of started processes of create or update Metering point in EVII and push it into dedicated kafka topics.
  - name: VvMeteringPoints
    description: Process group (on DHS side) responsible for create process for creating/updating MeteringPoint. The processes created will be sent to DataHub.

paths:
  /v1/metering-point-process-changed:
    post:
      tags:
        - VvMeteringPointResults
      summary: Receive result of process and information about success or failure.
      description: Receive result of process of address suggestion for metering point.
      operationId: meteringPointProcessChanged
      x-authorization: Water.All
      x-authorization-1: Heating.All
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MeteringPointProcessChanged"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMpmCorrelationId"
        - $ref: "#/components/parameters/SupplyType"
        - $ref: "#/components/parameters/AcquiredMessageId"
      responses:
        "202":
          description: Add metering point update request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/metering-point-vv-address-suggestion:
    post:
      tags:
        - VvMeteringPoints
      summary: Start of the process address suggestion
      description: Start of the process of address suggestion
      operationId: createAddressSuggestion
      x-authorization: Water.All
      x-authorization-1: Heating.All
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddressSuggestion"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMpmCorrelationId"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: Add metering point update request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/metering-point-vv-update-processes:
    post:
      tags:
        - VvMeteringPoints
      summary: Start of the process for sending Metering Point to EVII
      description: Start of the process in MPM for sending update to EVII
      operationId: updateVvMeteringPoint
      x-authorization: Electricity.All
      x-authorization-1: PCMarketCom.Read
      requestBody:
        description:
          The model includes characteristics describing Metering Point version.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateVvMeteringPoint"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: Add metering point update request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/metering-point-vv-create-processes:
    post:
      tags:
        - VvMeteringPoints
      summary: Start of the process for sending Metering Point to EVII
      description: Start of the process in MPM for sending create to EVII
      operationId: createVvMeteringPoint
      x-authorization: Electricity.All
      x-authorization-1: PCMarketCom.Read
      requestBody:
        description:
          The model includes characteristics describing Metering Point version.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateVvMeteringPoint"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: Add metering point creation request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/change-vv-metering-point-connection-status-processes:
    post:
      tags:
        - VvMeteringPoints
      summary: Start of the process for change Metering point connection status to EVII
      description: Start of the process in MPM for sending change Metering point connection status to EVII
      operationId: changeVvMeteringPointConnectionStatus
      x-authorization: Electricity.All
      x-authorization-1: PCMarketCom.Read
      requestBody:
        description:
          The model includes base data for change connection status of Metering Point version.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangeVvMeteringPointConnectionStatus"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: Add metering point creation request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/vv-create-meter:
    post:
      tags:
        - VvMeteringPoints
      summary: CreateMeter
      description: CreateMeter
      operationId: createMeter
      x-authorization: Water.All
      x-authorization-1: Heating.All
      x-authorization-2: PCMarketCom.Read
      requestBody:
        description:
          The model includes base data for change connection status of Metering Point version.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ManageMeter"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: Add metering point creation request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/vv-change-meter:
    post:
      tags:
        - VvMeteringPoints
      summary: ChangeMeter
      description: ChangeMeter
      operationId: changeMeter
      x-authorization: Water.All
      x-authorization-1: Heating.All
      x-authorization-2: PCMarketCom.Read
      requestBody:
        description:
          The model includes base data for change connection status of Metering Point version.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ManageMeter"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: ChangeMeter.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/vv-close-meter:
    post:
      tags:
        - VvMeteringPoints
      summary: CloseMeter
      description: CloseMeter
      operationId: closeMeter
      x-authorization: Water.All
      x-authorization-1: Heating.All
      x-authorization-2: PCMarketCom.Read
      requestBody:
        description:
          CloseMeter.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CloseMeter"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: CloseMeter.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

components:
  schemas:
    ManageMeter:
      $ref: './schemas/MeteringPoint/Vv/ManageMeter.yaml'

    CloseMeter:
      $ref: './schemas/MeteringPoint/Vv/CloseMeter.yaml'

    AddressSuggestion:
      $ref: './schemas/VvAddressSuggestion.yaml'

    UpdateVvMeteringPoint:
      $ref: './schemas/VvUpdateMeteringPoint.yaml'

    CreateVvMeteringPoint:
      $ref: './schemas/VvCreateMeteringPoint.yaml'

    MeteringPointProcessChanged:
      $ref: './schemas/VvMeteringPointProcessChanged.yaml'

    ChangeVvMeteringPointConnectionStatus:
      $ref: './schemas/VvChangeMeteringPointConnectionStatus.yaml'

  responses:
    "400":
      $ref: "./responses/400.yaml"
    "401":
      $ref: "./responses/401.yaml"
    "403":
      $ref: "./responses/403.yaml"
    "500":
      $ref: "./responses/500.yaml"

  parameters:
    EsMessageIdInHeader:
      $ref: "./parameters/EsMessageIdInHeader.yaml"
    EsCorrelationIdInHeader:
      $ref: "./parameters/EsCorrelationIdInHeader.yaml"
    EsMpmCorrelationId:
      $ref: "./parameters/EsMpmCorrelationId.yaml"
    SupplyType:
      $ref: "./parameters/SupplyTypeHeader.yaml"
    AcquiredMessageId:
      $ref: "./parameters/AcquiredMessageId.yaml"


  securitySchemes:
    Jwt:
      description: |-
        Jwt Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: Jwt
