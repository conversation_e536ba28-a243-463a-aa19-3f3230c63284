asyncapi: 2.6.0
#openapi: 3.0.3
id: https://async.api.kmdelements.com/meter-readings/
info:
  title: Meter-readings event
  x-maintainers: Team-DP-1
  version: "0.1.0"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    Async API emitting translated Meter Readings events (readings aggregated by meter as in CIM format)

tags:
  - name: Team-DP-1
    description: Maintained by

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.event.register-not-found.vee.v1:
    description: |
      Topic with events informing which register needs
      to be created in order for readings matching to be successful
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Event required registers for performing reading matching in vee process
      description: Event required registers for performing reading matching in vee process
      operationId: NotFoundRegisters
      message:
        $ref: "#/components/messages/RegisterNotFoundEvent"

components:
  messages:
    RegisterNotFoundEvent:
      name: MeterReadingsEvent
      title: MeterReadingsEvent
      summary: MeterReadingsEvent.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/RegisterNotFoundEvent"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    RegisterNotFoundEvent:
      type: object
      description: Required register for performing reading matching in vee process
      required:
        - meterId
        - meterNumber
        - registerRequiredFrom
        - registerProperties
      properties:
        meterId:
          type: string
          description: id of meter for which register is missing
        meterNumber:
          type: string
          description: number of meter for which register is missing
        registerRequiredFrom:
          type: string
          format: date-time
          description: |
            Date at which first usage of register was detected.
            DateTime specified in ISO 8601, preferred usage of 'Z' suffix instead timezone offset.
          examples:
            - '2020-01-01T00:00:00.000Z'
        registerProperties:
          $ref: '#/components/schemas/RegisterProperties'
    RegisterProperties:
      description: Properties of register that is missing
      title: ReadingMetadata
      type: object
      required:
        - supplyType
        - readingType
        - direction
        - measurementKind
        - phase
        - aggregateType
        - measuringPeriod
      properties:
        supplyType:
          $ref: '#/components/schemas/SupplyType'
        readingType:
          $ref: '#/components/schemas/ReadingType'
        direction:
          $ref: '#/components/schemas/Direction'
        measurementKind:
          $ref: '#/components/schemas/MeasurementKind'
        aggregateType:
          $ref: '#/components/schemas/AggregateType'
    SupplyType:
      type: string
      enum:
        - ELECTRICITY
      description: "Type of supply being measured"
    ReadingType:
      type: string
      enum:
        - ACCUMULATIVE
      description: "Type of reading"
    Direction:
      type: string
      enum:
        - FORWARD
      description: "Flow direction of energy"
    MeasurementKind:
      type: string
      enum:
        - ENERGY
        - REACTIVE_ENERGY
      description: "Kind of measurement being performed"
    AggregateType:
      type: string
      enum:
        - NONE
      description: "Aggregation type of readings"

    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
