type: object
additionalProperties: false
description: Heating attributes model.
required:
  - connectionPointCategoryValue
  - connectionStatus
  - installationTypeValue
  - heatWaterHeater
  - waterHeaterType
properties:
  connectionPointCategoryValue:
    description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific codelist.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  connectionStatus:
    description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  installationTypeValue:
    description: Defines type of installation type. Eg. For apartments, single households, Industrial, Agricultural.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  installationDescription:
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
    description: This information comes from the "Installationsblanket". It contains the installer's note from the installation form if applicable.
  heatWaterHeater:
    description: List of different hot water heating controls that can be installed.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  numberOfWaterHeater:
    description: The number of Water heaters.
    allOf:
      - $ref: '../DataTypes/PositiveIntegerNullable.yaml'
  heatPlantType:
    description: Lists the different plant types.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  heatExchange:
    description: List of different heat exchanger options.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  flexAttributeObject:
    description: An UUID reference to a Settlement Object that is used to register flexible attributes about the entity.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  decommissioned:
    description: Decommissioned date.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
