type: object
additionalProperties: false
description: Electricity attributes model.
required:
  - connectionStatus
  - temporary
properties:
  connectionPointCategoryValue:
    description: Categorization of a ConnectionPoint.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  installationTypeValue:
    description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  connectionStatus:
    description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  consumerCategory:
    description: Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  deMasterDataForms:
    description: DEMasterDataForm that comes from the settlement calculation.
    nullable: true
    allOf:
      - $ref: '../DataTypes/Integer.yaml'
  installationDescription:
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
    description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
  netSettlementGroup:
    description: This field register the net settlement group, which is also used in the market communication (DataHub).
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  gridAreaId:
    description: Grid area id.
    allOf:
      - $ref: '../DataTypes/OneWordStringNullable.yaml'
  temporary:
    type: boolean
    description: Set to true, if the Connection Point is temporary.
  temporaryUntil:
    description: |-
      When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
      grid company.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  flexAttributeObject:
    description: |-
      An UUID reference to a Settlement Object that is used to register flexible attributes about the entity.
      In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an entity.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  decommissioned:
    description: Decommissioned date.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
