title: GetConnectionPointsRequestBody
type: object
description: Get connection points request body.
additionalProperties: false
properties:
  carId:
    type: string
    description: CAR id guid
    format: uuid
    example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
  formType:
    description: An installation form type
    nullable: false
    $ref: "../Forms/FormType.yaml"
  supplyType:
    description: Supply type filter.
    nullable: false
    allOf:
      - $ref: "../Common/SupplyTypes.yaml"
  meterNumber:
    type: string
    minLength: 1
    maxLength: 100
    nullable: true
    pattern: "^.*$"
    description: Meter number used for connection statuses validation (valid for EP forms)
