type: object
description: Object to provide pagination in results.
properties:
  currentPage:
    type: integer
    description: Number of current page.
    format: int32
    minimum: 0
    maximum: 2147483647
  pageCount:
    type: integer
    description: Number of page count.
    format: int32
    minimum: 0
    maximum: 2147483647
  pageSize:
    type: integer
    description: Number of page size.
    format: int32
    minimum: 1
    maximum: 1000
  rowCount:
    type: integer
    description: Number of row count.
    format: int32
    minimum: 0
    maximum: 2147483647
additionalProperties: false
