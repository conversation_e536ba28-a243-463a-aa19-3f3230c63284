asyncapi: 2.6.0
id: https://async.api.kmdelements.com/communication-channels
info:
  title: Communication Merge Service kafka API
  version: 1.0.0
  x-maintainers: Team-MO-1
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description:
    API allows to asynchronously receive data to merge with a template.
    API allows to asynchronously receive a status if the merged document was saved successfully in the document storage.

tags: 
  - name: Team-MO-1
    description: Maintained by Team-MO-1
    
servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka
    
defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.event.mergeservice.document-merged.v1:
    description: This topic is used for receiving the status of the merged document.
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: Request if the merged document process is complete. 
      description: Request if the merged document process is complete.
      operationId: MergeDataResultEvent
      message:
        $ref: '#/components/messages/MergeDataResultEvent'
  kmd.elements.{tenantId}.command.mergeservice.merge-data.v1:
    description: This topic is used for receiving data and template and them merge the data into template.
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: Request data to merge
      description: Request data to merge.
      operationId: MergeDataObjectCommand
      message:
        $ref: '#/components/messages/MergeDataObjectCommand'

components:
  messages:
    MergeDataResultEvent:
      name: MergeDataResultEvent
      title: Merge result command
      summary: Receive the status of the merged document.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/MergeDataResultEventPayload"
    MergeDataObjectCommand:
      name: MergeDataObjectCommand
      title: Merge data object command message
      summary: Request of merging data with a template.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/MergeDataObjectCommandPayload"

  schemas:
    MergeDataResultEventPayload:
      title: MergeDataResultEventMessage
      type: object
      additionalProperties: false
      required:
        - externalId
        - resultStatus
        - targetService
      properties:
        externalId:
          description: Id of template to merge
          type: string
          format: uuid
        resultStatus:
          $ref: '#/components/schemas/ResultStatus'
        targetService:
          $ref: '#/components/schemas/TargetService'
      examples:
        [
          {
            externalId: "c0bdeeef-9ff6-41d7-a4a2-ff17b16db5c9",
            resultStatus: Success,
            targetService: General
          }
        ] 
    ResultStatus:
      enum:
        - None
        - Success
        - MissingData
        - TemplateNotFound
        - ServerError
      type: string
      description: The status of the data merge operation to the template.
      examples:
        [ Success ]
    TargetService:
      enum:
        - None
        - General
        - UI
      type: string
      description: The target service for which ResultStatus is created.
      examples:
        [ General ]
    MergeDataObjectCommandPayload:
      title: MergeDataObjectCommandMessage
      type: object
      additionalProperties: false
      required:
        - templateId
        - mergeDataObjects
        - targetService
      properties:
        templateId:
          description: Id of template to merge.
          type: string
          format: uuid
        mergeDataObjects:
          description: List of objects with data to merge.
          type: array
          items:
            $ref: "#/components/schemas/MergeDataObjectItem"
        targetService:
          $ref: '#/components/schemas/TargetService'
      examples:
        [
          {
            templateId: "1e69fb13-bb19-49a4-b13b-1ae37e3af50f",
            targetService: General,
            mergeDataObjects: 
              [
                {
                  externalId: "abc1a245-7d0a-47ac-b5b3-01910f313626",
                  mergeData:
                    {
                        Customer: "John Smith"
                    }
                },
                {
                  externalId: "abc1a245-7d0a-47ac-b5b3-01910f313626",
                  mergeData:
                    {
                        Customer: "Mary Jane"
                    }
                }
              ]
          }
        ]

    MergeDataObjectItem:
      title: Data to merge
      type: object
      additionalProperties: false
      required:
        - externalId
        - mergeData
      properties:
        externalId:
          description: Name of merged file that will be save on DocumentStorage.
          type: string
          format: uuid
        mergeData:
          description: Object with data to merge.
          type: object
      examples:
        [
          {
            externalId: "abc1a245-7d0a-47ac-b5b3-01910f313626",
            mergeData:
              {
                  Customer: "John Smith"
              }
          },
          {
            externalId: "abc1a245-7d0a-47ac-b5b3-01910f313626",
            mergeData:
              {
                  Customer: "Mary Jane"
              }
          }
        ]

  parameters:
    TenantId:
      description: Identifier of a tenant.
      schema:
        type: number
