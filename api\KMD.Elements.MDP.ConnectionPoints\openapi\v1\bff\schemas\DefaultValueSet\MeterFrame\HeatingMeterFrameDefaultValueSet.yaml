type: object
description: Model containing heating attributes of Meter Frame Default Value Set.
properties:
  calculateCooling:
      description: 'DK: BeregnAfk<PERSON>ling.'
      allOf:
        - $ref: '../../DataTypes/Boolean.yaml'
      nullable: true
  coolingLimit:
      description: 'DK: Afkølingsgrænse.'
      type: [ 'number', 'null' ]
      format: decimal
      example: 1234
  criticalCustomer:
    description: 'DK: KritiskKundekategori.'
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  planEffect:
      description: 'DK: Anlægsydelse.'
      allOf:
        - $ref: '../../DataTypes/IntegerNullable.yaml'
  plantType:
      description: 'DK: Anlægstype.'
      allOf:
        - $ref: '../../DataTypes/GuidNullable.yaml'
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
  returnHeatingConnected:
      description: 'DK: Returvarme.'
      allOf:
        - $ref: '../../DataTypes/Boolean.yaml'
      nullable: true
  counterPlacement:
      description: Counter Placement.
      allOf:
        - $ref: '../../DataTypes/ShortStringNullable.yaml'
  connectionType:
    description: 'DK: Tilslutningstype.'
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
