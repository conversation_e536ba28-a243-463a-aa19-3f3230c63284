type: object
description: Update metering point payload for any supply type
additionalProperties: false
properties:
  connectionPointId:
    type: string
    description: Connection point object private identifier.
    format: uuid
    nullable: true
  netSettlementGroup:
    nullable: true
    type: string
    maxLength: 256
    description: Is mapped to `NetSettlementGroup`
    pattern: ""
  powerPlant:
    type: string
    nullable: true
    description: Value of domain location identifier
    maxLength: 18
    minLength: 1
    pattern: ""
  meteringPointConnectionType:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `MPConnectionType`
    pattern: ^(D01|D02)$
  assetType:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `AssetType`
    pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D99)$
  meteringPointCapacity:
    nullable: true
    type: number
    description: Is mapped to `MPCapacity`
    format: decimal
    exclusiveMinimum: false
    minimum: 0
    maximum: 99999999.9
