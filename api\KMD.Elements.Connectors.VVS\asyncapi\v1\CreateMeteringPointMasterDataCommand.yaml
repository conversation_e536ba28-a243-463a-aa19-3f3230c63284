asyncapi: 2.6.0
id: https://async.api.kmdelements.com/send-request-create-metering-point-master-data
info:
  title: Command for send create metering point master data to EWII
  x-maintainers: Team-MC-1
  version: 1.0.6
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    API to initiate the process of create metering point master data.
    Descriptions in properties are connected with RSM-VV021 contract

tags:
  - name: Team-MC-1
    description: Maintainer

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.command.connector-vvs.create-metering-point.v1:
    description: This topic is used for exchanging RSM-VV021
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Request sending RSM-VV021
      description: Request sending RSM-VV021
      operationId: sendCreateMeteringPointMasterDataCommand
      message:
        $ref: "#/components/messages/CreateMeteringPointCommand"

components:
  messages:
    CreateMeteringPointCommand:
      title: Create metering point
      name: CreateMeteringPointCommand
      contentType: application/json
      headers:
        $ref: "#/components/schemas/MessageHeaders"
      payload:
        $ref: "#/components/schemas/CreateMeteringPointCommandPayload"

  schemas:
    MessageHeaders:
      $ref: "./schemas/Shared/CommandHeaders.yaml"

    CreateMeteringPointCommandPayload:
      title: CreateMeteringPointCommandPayload
      description: Payload start process for create metering point in EWII.
      type: object
      additionalProperties: false
      required:
        - supplyType
        - meteringPointId
        - occurrence
        - meteringPointDetails
        - meteringPointAddress
      properties:
        supplyType:
          $ref: "./schemas/Shared/SupplyType.yaml"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        meteringPointVersionId:
          type: string
          description: Metering Point Version Id - <GSRN_NUMBER>_<YYYY-MM-DD>.
          nullable: true
          pattern: '^\d{18}_\d{4}-\d{2}-\d{2}$'
          minLength: 18
          maxLength: 30
          example: "123456789012345678_2022-01-13"
        parentMeteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        occurrence:
          type: string
          description: When metering points starts being valid. Also known as validFrom.
          format: date-time
          example: "2023-07-01T22:00:00Z"
        occurrenceTo:
          type: string
          description: Also known as "Valid To". End of Given Version Validity date.
          format: date-time
          nullable: true
          example: "2023-07-01T22:00:00Z"
        meteringPointDetails:
          $ref: "#/components/schemas/MeteringPointDetails"
        meteringPointAddress:
          $ref: "#/components/schemas/MeteringPointAddress"
        meter:
          $ref: "./schemas/MeteringPoints/Meter.yaml"

    MeteringPointDetails:
      type: object
      description: The characteristic of the metering point
      additionalProperties: false
      required:
        - meteringGridAreaId
        - subTypeOfMeteringPoint
        - readingFrequency
        - connectionPointId
      properties:
        meteringGridAreaId:
          type: integer
          format: int32
          nullable: true
          minimum: 0
          maximum: 2147483647
          example: 111
          description: |-
            Network area is a term for a network that is managed by a network company.
        locationDescription:
          type: string
          description: Free text detailed description of location. Can be used if there is something completely extraordinary about the location.
          maxLength: 60
        disconnectKind:
          description: |-
            Type of Heat Metering Point Disconnect Kind.
            | CodeListName                            | DisplayName            | Code | Name              | Translation         |
            |-----------------------------------------|------------------------|------|-------------------|---------------------|
            | System-HeatMeteringPoint-DisconnectKind | D01 – fjernafbrydelig  | D01  | Fjern afbrydelig  | Remote disconnection|
            | System-HeatMeteringPoint-DisconnectKind | D02 – Manual afbrydelig| D02  | Manual afbrydelig | Manual disconnection|
          type: string
          nullable: true
          minLength: 3
          maxLength: 3
          pattern: "^(D01|D02)$"
          example: D01
        supplyInfoBilling: #todo rename
          pattern: "^.*$"
          type: string
          nullable: true
          minLength: 1
          maxLength: 100
          description: Supply billing info for the Metering Point.
          example: A01
        commonReading:
          $ref: "./schemas/MeteringPoints/MpCommonReading.yaml"
        readingFrequency:
          $ref: "./schemas/MeteringPoints/MpReadingFrequency.yaml"
        connectionStatus:
          $ref: "./schemas/MeteringPoints/MpConnectionStatus.yaml"
        expectedConsumptionPerYear:
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
          description: The expected consumption in the period.
          example: 15
        subTypeOfMeteringPoint:
          $ref: "./schemas/MeteringPoints/MpSubTypeOfMeteringPoint.yaml"
        applicationCode:
          pattern: "^.*$"
          type: string
          minLength: 1
          maxLength: 50
          description: Indicates the application code for the single device. By default, it comes from the meter frame's property element, but must be able to be overwritten manually.
          example: A01
        noMeter:
          type: boolean
          nullable: false
          description: Indicates whether it is a meter-free installation (subscription only).
          example: true
        heatingMeteringPointAttributes:
          $ref: "./schemas/MeteringPoints/Heating/HeatingMeteringPointAttributes.yaml"
        waterMeteringPointAttributes:
          $ref: "./schemas/MeteringPoints/Water/WaterMeteringPointAttributes.yaml"
        connectionPointId:
          $ref: "./schemas/DataTypes/Guid.yaml"
        connectionPointNumber:
          type: string
          description: Number of the ConnectionPoint.
          example: "20000001"
          nullable: false

    MeteringPointId:
      type: string
      description: Metering Point Container Id.
      minLength: 18
      maxLength: 18
      pattern: ^\d{18}$
      example: "571313190000020132"

    #todo check if evdrything matches
    MeteringPointAddress:
      description: The detailed address
      type: object
      additionalProperties: false
      properties:
        streetName:
          type: string
          description: Is mapped to `StreetName`
          maxLength: 40
        streetCode:
          type: string
          maxLength: 4
          description: Is mapped to `StreetCode`
          pattern: ^$|^\d{4}$
        buildingNumber:
          type: string
          description: Is mapped to `BuildingNumber`
          maxLength: 6
        floorIdentification:
          type: string
          description: Is mapped to `FloorIdentification`
          maxLength: 4
        roomIdentification:
          type: string
          description: Is mapped to `RoomIdentification`
          maxLength: 4
        citySubDivisionName:
          type: string
          description: Is mapped to `CitySubDivisionName`
          maxLength: 34
        postCode:
          type: string
          description: Is mapped to `PostCode`
          maxLength: 10
        cityName:
          type: string
          description: Is mapped to `CityName`
          maxLength: 25
        municipalityCode:
          type: string
          description: Is mapped to `MunicipalityCode`
          pattern: ^$|^.{3}$
          maxLength: 3
        countryCode:
          type: string
          maxLength: 2
          description: Is mapped to `CountryName`
          pattern: ^$|^[A-Z]{2}$
        addressWashInstruction:
          type: string
          maxLength: 3
          description: Is mapped to `AddressWashInstruction`
          pattern: ^(D00|D01|D02|D03)$
        darReference:
          type: string
          description: Is mapped to `DARReference`
          format: uuid
        carId:
          type: string
          format: uuid
          description: Central address registry identifier.
        postBox:
          description: Post box number / location.
          type: [ 'string', 'null' ]
          pattern: "^.*$"
          minLength: 0
          maxLength: 25
          example: 'N/A'
        protectedAddress:
          description: Is address protected.
          type: [ 'boolean', 'null' ]
          example: true

  parameters:
    TenantId:
      $ref: "./parameters/TenantId.yaml"
