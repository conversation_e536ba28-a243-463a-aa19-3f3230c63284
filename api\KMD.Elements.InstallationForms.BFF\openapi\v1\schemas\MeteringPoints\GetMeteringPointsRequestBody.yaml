title: GetMeteringPointsRequestBody
type: object
description: Get metering points request body.
additionalProperties: false
properties:
  connectionPointId:
    type: string
    description: Connection point id
    format: uuid
    nullable: false
    example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
  connectionPointElectricityConnectionStatusId:
    type: string
    description: Electricity connection status id of selected connection point (valid only for Electricity), which determines metering point allowed statuses.
    format: uuid
    nullable: true
    example: 87b5fb3d-e71a-4d04-b839-7e26fef03b4b
  meteringPointType:
    description: Metering point type filter.
    nullable: false
    allOf:
      - $ref: "./MeteringPointType.yaml"
  formType:
    description: An installation form type
    nullable: false
    $ref: "../Forms/FormType.yaml"
  supplyType:
    description: Supply type filter.
    nullable: false
    allOf:
      - $ref: "../Common/SupplyTypes.yaml"
  meterNumber:
    type: string
    minLength: 1
    maxLength: 100
    nullable: true
    pattern: "^.*$"
    description: Meter number used for connection / metering point statuses validation (valid for EP forms).
