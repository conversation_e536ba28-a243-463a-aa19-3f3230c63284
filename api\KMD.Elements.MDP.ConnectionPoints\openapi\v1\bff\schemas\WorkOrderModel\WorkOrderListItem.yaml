type: object
additionalProperties: false
description: Work order list item model.
required:
  - objectType
  - workDescription
  - workPurpose
  - requestedAppointmentDate
properties:
  id:
    nullable: true
    description: Work order identifier'.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  virtualId:
    nullable: true
    description: Internal MP MDP identifier'.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  workOrderType:
    description: 'Work Order Type.'
    allOf:
      - $ref: './WorkOrderType.yaml'
  objectType:
    allOf:
      - $ref: './WorkOrderObjectType.yaml'
    description: |-
      Work order object type
    example: "MeterFrame"
  objectNo:
    description: Connection point or meter frame number.
    allOf:
      - $ref: '../DataTypes/LongString.yaml'
  workDescription:
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
    description: 'Work Description - value list identifier.'
  workPurpose:
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
    description: 'Work purpose - value list identifier.'
  requestedAppointmentDate:
    description: |-
      A date when the work should be carried out by the technician.
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
