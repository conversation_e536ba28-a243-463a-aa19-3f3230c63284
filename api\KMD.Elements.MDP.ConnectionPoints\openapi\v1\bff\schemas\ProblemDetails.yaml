title: ProblemDetails
type: object
description: |-
  ProblemDetails provides detailed information about an errors that occurred during an api call execution.
  This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
properties:
  type:
    allOf:
      - $ref: ./DataTypes/DescriptionString.yaml
    description: The error type.
    nullable: true
    example: "https://errors.kmdelements.com/500"
  title:
    allOf:
      - $ref: ./DataTypes/DescriptionString.yaml
    description: "A short, human-readable summary of the problem type."
    nullable: true
    example: Error short description
  status:
    description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
    type: integer
    format: int32
    nullable: true
    example: 500
    minimum: 4
    maximum: 599
  detail:
    allOf:
      - $ref: ./DataTypes/DescriptionString.yaml
    description: A human-readable explanation for what exactly happened (in English).
    nullable: true
    example: Description what exactly happened
  instance:
    allOf:
      - $ref: ./DataTypes/DescriptionString.yaml
    description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
    nullable: true
    example: /resources-path/1
additionalProperties: false
