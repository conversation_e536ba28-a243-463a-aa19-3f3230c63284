type: object
description: Change of Address on Metering point.
required:
  - meteringPointVersionId
  - centralAddressRegistryId
  - meteringPointId
  - supplyType
  - occurrence
additionalProperties: false
properties:
  meteringPointVersionId:
    description: Id of Metering Point Version. Metering Point Version Id contains of {GSRN}_{DATE}. so <MeteringPointId>_<Occurrence>
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    example: 317575025980776095_2022-01-02
  centralAddressRegistryId:
    description: Central Address Registry Id Id.
    nullable: true
    allOf:
      - $ref:  '../DataTypes/Guid.yaml'
    example: da85baa6-a66a-11ea-bb37-0242ac130002
  meteringPointId:
    type: string
    description: Metering Point Container Id (GSRN).
    minLength: 18
    maxLength: 18
    pattern: ^\d{18}$
    example: "571313190000020132"
  supplyType:
    type: string
    description: Allowed values depends on tenant configuration.
    enum:
      - Electricity
      - Water
      - Heating
    example: Electricity
  occurrence:
    $ref: '../DataTypes/DateTimeIso8601.yaml'
  addressWashInstruction:
    nullable: true
    description: Mapped from DH 'LocationAddress.washable' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
