type: object
additionalProperties: false
description: Data model for create or update connection point.
required:
  - virtualId
  - supplyType
  - tagAssignments
  - address
properties:
  virtualId:
    description: Internal MP MDP identifier'.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  connectionPointNumber:
    allOf:
      - $ref: '../DataTypes/ShortStringObsolete.yaml'
    description: Number of the ConnectionPoint.
    nullable: true
    example: "20000001"
  alternativeInstallationNumber:
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
    description: Old installation's number printed on the physical device at the consumer.
    nullable: true
  priceGroupExcluded:
    description: Used to indicate if a connection point is excluded from being included in a price group.
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
  installationNumber:
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
    description: Installation number.
  description:
    allOf:
      - $ref: '../DataTypes/LongStringObsoleteNullable.yaml'
    description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.
    nullable: true
  address:
    description: An UUID reference to a master data address.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  supplyType:
    $ref: '../SupplyType.yaml'
  tagAssignments:
    type: array
    description: Tags.
    maxItems: 100
    items:
      $ref: '../TagAssignmentModel.yaml'
  electricityAttributes:
    nullable: true
    type: object
    description: Electricity attributes.
    oneOf:
      - $ref: './ElectricityAttributesModel.yaml'
  heatingAttributes:
    nullable: true
    type: object
    description: Heating attributes.
    oneOf:
      - $ref: './HeatingAttributesModel.yaml'
  waterAttributes:
    nullable: true
    type: object
    description: Water attributes.
    oneOf:
      - $ref: './WaterAttributesModel.yaml'
