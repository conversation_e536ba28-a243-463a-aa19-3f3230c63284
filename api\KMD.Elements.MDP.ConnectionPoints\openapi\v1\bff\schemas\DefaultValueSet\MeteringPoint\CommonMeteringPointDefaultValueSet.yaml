type: object
description: Model containing the base attributes of Metering Point Default Value Set.
required:
  - name
  - typeOfMeteringPoint
properties:
  name:
    description: Name of Metering Default Value Set.
    allOf:
      - $ref: '../../DataTypes/ShortString.yaml'
  typeOfMeteringPoint:
    description: Type of Metering Point.
    allOf:
      - $ref: '../../MeteringPointModel/DataTypes/TypeOfMeteringPoint.yaml'
    nullable: false
  subTypeOfMeteringPoint:
    description: Sub type of Metering Point.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  meterReadingOccurrence:
    description: Meter reading occurrence.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  settlementMethod:
    description: Settlement method.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  meteringGridAreaId:
    description: ID of the metering grid area.
    allOf:
      - $ref: '../../DataTypes/OneWordStringNullable.yaml'
  locationDescription:
    description: Location description.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  formulaVersion:
    description: |
      The formula that describes how data for the measuring point's time series is calculated using data
      from the individual formula parameters.
    allOf:
      - $ref: './DefaultValueSetsFormulaVersion.yaml'
    nullable: true
