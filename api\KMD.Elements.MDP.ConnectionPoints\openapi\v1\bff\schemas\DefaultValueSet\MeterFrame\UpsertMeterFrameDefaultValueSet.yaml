type: object
description: Model used to get Meter Frame Default Value Set details.
additionalProperties: false
required:
  - supplyType
  - commonMeterFrameDefaultValueSet
properties:
  id:
    description: Meter Frame Default Value Set identifier.
    allOf:
      - $ref: "../../DataTypes/GuidNullable.yaml"
  supplyType:
    $ref: '../../SupplyType.yaml'
    description: Supply type of the default value set related to Meter Framet.
  commonMeterFrameDefaultValueSet:
    description: The base attributes of Meter Frame Default Value Set.
    allOf:
      - $ref: './CommonMeterFrameDefaultValueSet.yaml'
  electricityMeterFrameDefaultValueSet:
    description: Electricity attributes of Meter Frame Default Value Set.
    allOf:
      - $ref: './ElectricityMeterFrameDefaultValueSet.yaml'
    nullable: true
  waterMeterFrameDefaultValueSet:
    description: Water attributes of Meter Frame Default Value Set.
    allOf:
      - $ref: './WaterMeterFrameDefaultValueSet.yaml'
    nullable: true
  heatingMeterFrameDefaultValueSet:
    description: Heating attributes of Meter Frame Default Value Set.
    allOf:
      - $ref: './HeatingMeterFrameDefaultValueSet.yaml'
    nullable: true
  rowVersion:
    description: Row version.
    allOf:
      - $ref: '../../DataTypes/RowVersion.yaml'
    nullable: true
