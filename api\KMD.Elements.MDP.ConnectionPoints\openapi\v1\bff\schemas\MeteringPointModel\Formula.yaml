type: object
description: Formula version object of Metering Point version.
additionalProperties: false
properties:
  formula:
    description: Formula expression.
    minLength: 0
    nullable: true
    allOf:
      - $ref: '../DataTypes/DescriptionEmptyStringNullable.yaml'
    example: "Max((SUMOfD05)-(SUMOfD06);0)"
  meterFrameVirtualId:
    description: Meter frame id.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    example: fb19ba41-5467-4aa7-8891-f58ea5d91a23
  registerRequirementVirtualId:
    description: Register Requirement Virtual Id.
    nullable: true
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    example: fb19ba41-5467-4aa7-8891-f58ea5d91a23
