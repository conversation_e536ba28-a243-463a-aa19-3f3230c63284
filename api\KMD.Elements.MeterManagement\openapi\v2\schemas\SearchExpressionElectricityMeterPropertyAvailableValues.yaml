description: Pattern for property name for search expression.
pattern: "^(Id|SupplyTypeCode|MeterNumber|MeterTypeId|AlternativeMeterNumber|BarCodeEanNumber|CommunicationMethodId|FirstCommissioningDate|HesSystemId|ProductionYear|PurchaseDate|RemoteDisconnectable|RemoteReadable|SerialNumber|ManufacturerId|FabricationNumber|FabricationTypeId|MeterControlTypeCode|MainControlMeterRoleCode|RemoteRegisterConfigurationId|RemoteDisplayConfigurationId|MeterStatusId|SecondaryStatusId|MeterConfigurationId|MeterBatchTechnicalId|MeterBatchId|AccuracyClassId|CurrentId|CustomerOutputStateId|CustomerDataOutputActivationDate|CustomerDataOutputEncryptionKey|ElectricityConnectionTypeId|ElectricityConnectionRemarkId|ElectricityMeteringPrincipleId|ImpulsesPerkWhVarh|ItemNameId|MeterRoleCode|MaxPlcDistanceId|MaxRadioDistanceId|NoOfPulseInputsId|NumberOfPhasesId|OrderNumber|RatioCtId|RatioVtId|RelatedMeter|Temporary|TypeApproval|VendorOrderNumber|VoltageId|TechnicalCurrentId|TechnicalVoltageId|MeterTagIds|StartTimeForMeterBatchCode|MeterApprovedAfterCode)$"
minLength: 1
maxLength: 50
type: string
nullable: false
example: "Id"
