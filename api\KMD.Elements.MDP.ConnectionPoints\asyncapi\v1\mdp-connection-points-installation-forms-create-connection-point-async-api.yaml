asyncapi: 2.6.0
id: https://dev.kmdelements.com/api/mdp-connection-points-api/
info:
  title: MDP.ConnectionPoints.Modify.API
  version: 1.3.0
  contact:
    name: Team-MD-3
    url: https://dev.azure.com/kmddk/COMBAS/_wiki/wikis/COMBAS.wiki/6375/Development-Team-MD-3
    email: <EMAIL>
  license:
    name: License
    url: 'https://www.kmd.net/terms-of-use'
  description: |
    Master Data Processes Connection Points API allows you to start processes that create connection point data.
  x-maintainers: Team-MD-3

tags:
  - name: MasterDataProcessesConnectionPoints
    description: "API that is responsible for starting master data create connection point processes initiated from installation forms"
  - name: Team-MD-3
    description: maintained by

servers:
  local:
    url: localhost:5001
    description: Local server
    protocol: kafka
    protocolVersion: 2.6.0

  development:
    url: 60000-kafkaBrokers
    description: Strimzi server which is stored under 60000-kafkaBrokers secret in KeyVault
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.command.installation-forms-mdp.create-connection-point-process.v1:
    description: The topic on which master data create connection point process commands from installation forms are sent.
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    subscribe:
      description: Consume modify connection point process command.
      operationId: onModifyProcess
      traits:
        - $ref: '#/components/operationTraits/kafka'
      message:
        $ref: '#/components/messages/CreateConnectionPointProcessCommand'
    publish:
      description: Push modify connection point process command to the topic
      operationId: emitModifyConnectionPointProcessCommand
      message:
        $ref: '#/components/messages/CreateConnectionPointProcessCommand'

components:
  messages:
    CreateConnectionPointProcessCommand:
      name: InstallationFormsCreateConnectionPointProcessCommand
      title: Create connection point process command.
      contentType: application/json
      headers:
        $ref: "#/components/schemas/MessageHeaders"
      payload:
        $ref: "#/components/schemas/CreateConnectionPointProcessCommandPayload"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    CreateConnectionPointProcessCommandPayload:
      type: object
      title: CreateConnectionPointProcessCommandPayload
      additionalProperties: false
      required:
        - templateId
        - address
        - addressLine1
        - addressLine2
        - processId
      properties:
        processId:
          type: string
          format: uuid
          description: Id of Create Connection Point process to create.
          nullable: false
        templateId:
          type: string
          format: uuid
          description: Master data process template Id
          nullable: false
        installationFormReference:
          type: string
          description: Reference number from the installation form (form number)
          nullable: true
          minLength: 1
          maxLength: 50
        installationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Installation number.
        alternativeInstallationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        tagAssignments: #MOVE TO TEMPLATE!
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/TagAssignmentModel'
          nullable: true #changed
        priceGroupExcluded: #MOVE TO TEMPLATE!
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/Boolean'
          nullable: true #changed
        address:
          description: An UUID reference to a master data address.
          allOf:
            - $ref: '#/components/schemas/Guid'
        electricityAttributes:
          nullable: true
          type: object
          description: Electricity attributes.
          oneOf:
            - $ref: '#/components/schemas/ElectricityAttributesModel'
        meterFrame:
          $ref: '#/components/schemas/CreateMeterFrame'
        meteringPoint:
          $ref: '#/components/schemas/CreateMeteringPoint'
        parentProcess:
          $ref: './schemas/ParentProcess.yaml'

    ElectricityAttributesModel:
      type: object
      additionalProperties: false
      description: Electricity attributes model.
      required:
        - temporaryInstallationInfo
        - connectionStatus
      properties:
        # Used to cover following mappings:
        # temporary - temporaryInstallationInfo.temporary
        # temporaryUntil - temporaryInstallationInfo.temporaryUntil
        temporaryInstallationInfo:
          description: Connection point temporary installation info
          $ref: "./schemas/TemporaryInstallationInfo.yaml"

        connectionStatus:
          description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
          allOf:
            - $ref: '#/components/schemas/Guid'
        consumerCategory:
          type: string
          description: Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.
          format: uuid
          nullable: true
        netSettlementGroup: #MOVE TO TEMPLATE!
          description: This field register the net settlement group, which is also used in the market communication (DataHub).
          nullable: true
          allOf:
            - $ref: '#/components/schemas/Guid'
        gridAreaId:
          description: Grid area id.
          allOf:
            - $ref: '#/components/schemas/OneWordStringNullable'
        deMasterDataForms:
          description: DEMasterDataForm that comes from the settlement calculation.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/Integer'
        installationDescription:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true

    TagAssignmentModel:
      type: object
      additionalProperties: false
      description: Add tag assignment model.
      required:
        - tagCodeListValueId
      properties:
        tagCodeListValueId:
          description: Tag code list value identifier.
          allOf:
            - $ref: '#/components/schemas/Guid'

    CreateMeteringPoint:
      type: object
      description: |
        Extended metering point version.
      additionalProperties: false
      properties:
        meteringPointDomainData:
          description: Is mapped to `MeteringPointDomainData`
          $ref: "#/components/schemas/MeteringPointDomainData"
        meteringPointDomainLocationId:
          description: Is mapped to `MeteringPointDomainLocationId`
          allOf:
            - $ref: '#/components/schemas/ShortString'
          nullable: true
        detailMeteringPointCharacteristic:
          description: Is mapped to `ScheduledMeterReadingDate`
          $ref: "#/components/schemas/MeteringPointCharacteristic"
        addressWashInstruction:
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `MPAddressWashInstruction`
          pattern: ^(D00|D01|D02|D03)$
        meteringInstallationMeterFacilityId:
          description: Is mapped to `MeteringInstallationMeterFacilityId`
          allOf:
            - $ref: '#/components/schemas/ShortString'
          nullable: true
        parentMeteringPointDomainLocationId:
          description: Is mapped to `ParentMeteringPointDomainLocationId`
          allOf:
            - $ref: '#/components/schemas/ShortString'
          nullable: true
        meterInstallationMeterCharacteristic:
          description: Is mapped to `MeterInstallationMeterCharacteristic`
          allOf:
            - $ref: '#/components/schemas/MeterCharacteristic'
          nullable: true

    MeterCharacteristic:
      nullable: true
      description: The characteristic of the meter
      type: object
      additionalProperties: false
      properties:
        numberOfDigits:
          nullable: true
          type: string
          description: Is mapped to `NumberOfDigits`
          maxLength: 5
          pattern: ""
        conversionFactor:
          type: number
          format: double
          nullable: true
          description: Is mapped to `ConversionFactor`
        unitType:
          description: Register Requirement Virtual Id.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
        meterReadingType:
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `MeterReadingType`
          pattern: ^(D01|D02)$

    MeteringPointCharacteristic:
      type: object
      description: The characteristic of the metering point
      additionalProperties: false
      properties:
        settlementMethod: #MOVE TO TEMPLATE!
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `SettlementMethod`
          pattern: ^(D01|E01|E02)$
        physicalStatusOfMeteringPoint:
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `PhysicalStatusOfMeteringPoint`
          pattern: ^(D02|D03|E22|E23)$
        scheduledMeterReadingDate:
          nullable: true
          type: array
          maxItems: 9999
          description: Is mapped to `ScheduledMeterReadingDate`
          pattern: ^\d{4}$
          items:
            type: string
            maxLength: 4
            pattern: ""
        readingCharacteristics:
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `MPReadingCharacteristics`
          pattern: ^(D01|D02)$
        typeOfMeteringPoint: #MOVE TO TEMPLATE!
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `TypeOfMeteringPoint`
          pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D21|D22|D23|D24|D25|D26|D27|D28|D29|D30|D99|E17|E18|E20)$
        subTypeOfMeteringPoint: #MOVE TO TEMPLATE!
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `SubTypeOfMeteringPoint`
          pattern: ^(D01|D02|D03)$
        meterReadingOccurrence: #MOVE TO TEMPLATE!
          nullable: true
          type: string
          maxLength: 5
          description: Duration in ISO8601
          pattern: ^(-?)P(?=\d|T\d)(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)([DW]))?(?:T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?)?$
        hourlyTimeSeries: #MOVE TO TEMPLATE!
          nullable: true
          type: boolean
          description: Is mapped to `HourlyTimeSeries`
        netSettlementGroup:
          nullable: true
          type: string
          maxLength: 256
          description: Is mapped to `NetSettlementGroup`
          pattern: ""
        limitationContractedCapacityCharacteristics:
          description: Is mapped to `LimitationContractedCapacityCharacteristics`
          $ref: "#/components/schemas/ContractedCapacityCharacteristics"
          nullable: true
        meteringGridAreaUsedDomainLocationId:
          description: Is mapped to `MeteringGridAreaUsedDomainLocationId`
          allOf:
            - $ref: '#/components/schemas/ShortString'
          nullable: true
        toGridDomainLocationId:
          description: Is mapped to `ToGridDomainLocationId`
          allOf:
            - $ref: '#/components/schemas/ShortString'
          nullable: true
        fromGridDomainLocationId:
          description: Is mapped to `FromGridDomainLocationId`
          allOf:
            - $ref: '#/components/schemas/ShortString'
          nullable: true
        productionObligation:
          nullable: true
          type: boolean
          description: Is mapped to `ProductionObligation`
        powerPlantDomainLocationId:
          description: Is mapped to `PowerPlantDomainLocationId`
          allOf:
            - $ref: '#/components/schemas/ShortString'
          nullable: true
        locationDescription:
          nullable: true
          type: string
          description: Is mapped to `LocationDescription`
          maxLength: 60
          pattern: ""
        includedProductCharacteristic:
          description: Is mapped to `IncludedProductCharacteristic`
          $ref: "#/components/schemas/ProductCharacteristic"
        disconnectionType:
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `DisconnectionType`
          pattern: ^(D01|D02)$
        connectionType:
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `MPConnectionType`
          pattern: ^(D01|D02)$
        capacityInKiloWatts:
          description: Is mapped to `MPCapacity`
          $ref: "#/components/schemas/DecimalNullable"
        assetType:
          nullable: true
          type: string
          maxLength: 3
          description: Is mapped to `AssetType`
          pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D99)$

    ContractedCapacityCharacteristics:
      description: The characteristic of contracted capacity
      nullable: true
      type: object
      additionalProperties: false
      properties:
        maximumCurrentInAmperes:
          nullable: true
          type: number
          description: Is mapped to `MaximumCurrent`
          format: decimal
          minimum: 1
          maximum: 999999
        maximumPowerInKiloWatts:
          nullable: true
          type: number
          description: Is mapped to `MaximumPower`
          format: decimal
          minimum: 1
          maximum: 999999

    ProductCharacteristic:
      description: The characteristic of the product
      type: object
      nullable: true
      additionalProperties: false
      required:
        - identification
      properties:
        identification:
          nullable: false
          type: string
          description: Is mapped to `Identification`
          minLength: 1
          maxLength: 13
          pattern: ^(5790001330590|5790001330606|8716867000016|8716867000023|8716867000030|8716867000047)$
        unitType:
          description: Measurement unit
          nullable: true
          allOf:
            - $ref: '#/components/schemas/UnitType'

    UnitType:
      type: string
      maxLength: 3
      description: Measurement unit
      pattern: ^(K3|KWH|KWT|MAW|MWH|TNE|Z03)$
      example: KWH

    MeteringPointDomainData:
      type: object
      nullable: true
      description: Data used internally by metering point domain.
      additionalProperties: false
      required:
        - carId
      properties:
        carId:
          description: Central address registry identifier.
          allOf:
            - $ref: '#/components/schemas/Guid'
        formula: #MOVE TO TEMPLATE!
          description: |
            The formula that describes how data for the measuring point's time series is calculated using data
            from the individual formula parameters. The formula is sent to MDM, and all the parameters' data
            must be found in MDM in order for MDM to calculate the result profile.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/String1000'
        formulaValidFrom:
          description: |
            Validity date to be specified. For all updates/changes to formulas,
            a date for when the update applies from must be specified.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTime'

    CreateMeterFrame:
      type: object
      additionalProperties: false
      description: Meter Frame model.
      required:
        - commonReading
        - meterMissing
        - supplyStatus
        - tagAssignments
        - supplyType
        - meterDataElectricity
        - addressId
      properties:
        applicationsElectricity:
          type: array
          items:
            $ref: "./schemas/ApplicationEntryElectricity.yaml"

        # Used to cover following mappings:
        # placementCode - meterDataElectricity.meterPlacementId
        # meterSealDate - meterDataElectricity.meterSealDate
        # meterSealed - meterDataElectricity.meterSealed
        meterDataElectricity:
          description: Meter data for electricity
          $ref: "./schemas/MeterDataElectricity.yaml"

        commonReading:
          allOf:
            - $ref: '#/components/schemas/Guid'
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
        meterMissing:
          allOf:
            - $ref: '#/components/schemas/Boolean'
          description: 'DK: MålerVæk.'

        meterWorkConsumerBilled: #MOVE TO TEMPLATE
          allOf:
            - $ref: '#/components/schemas/Boolean'
          description: |-
            DK: MålerYdelseFaktureresEjer.
            Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
          nullable: true #changed
        totalConnectionRight:
          type: number
          description: 'DK: SamletTilslutningsRet.'
          format: decimal
          nullable: true

        # Calculated field!
        # connectionStatus:
        #   description: 'DK: Tilslutningsstatus.'
        #   allOf:
        #     - $ref: '#/components/schemas/Guid'

        decommissioned:
          description: Decommissioned date.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTime'
        statusChanged:
          description: Latest status change date.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTime'
        electricityAttributes:
          description: ElectricityAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameElectricityAttributesModel'
        gisPropertiesElectricity:
          description: MeterFrameGisPropertiesElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesElectricityModel'
        addressId:
          description: An UUID reference to a master data address.
          allOf:
            - $ref: '#/components/schemas/Guid'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/TagAssignmentModel'
        supplyStatus:
          $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        meterReadingType:
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
          description: 'A list of different reading methods a meter can have.'
        supplyDisconnectType:
          $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        noMeter: #MOVE TO TEMPLATE
          type: boolean
          description: 'DK: Målerfri.'
          nullable: true #changed
        supplyType:
          $ref: '#/components/schemas/SupplyType'
        registerRequirement: #MOVE TO TEMPLATE
          $ref: '#/components/schemas/CreateRegisterRequirement'
          nullable: true #changed

    CreateRegisterRequirement:
      type: object
      additionalProperties: false
      description: Register Requirement model.
      required:
        - name
        - meteringComponentId
      properties:
        registerRequirementType:
          $ref: '#/components/schemas/RegisterRequirementTypeModel'
        name:
          description: |-
            DK: Navn.
            Name of requirement (typically from MeteringComponent value list).
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          nullable: false
        meteringComponentId:
          description: |-
            DK: MålingsKomponentId.
            Shared value list with Meter domain.
            The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
            It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
          allOf:
            - $ref: '#/components/schemas/Guid'
        mainMeterFrameRegisterRequirementId:
          description: |-
            DK: HovedMalerrammeRegisterKravId.
            Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          nullable: true
        mainMeterFrameRegisterRequirementName:
          description: Used to refer to the main meter register requirement.
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
        outsourcedToMeterFrameRegisterRequirementId:
          description: |-
            DK: HjemtagesAfMalerrammeRegisterKravId.
            Can refer to another meter frame which is actually the one that receives data through an external input.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        outsourcedToMeterFrameRegisterRequirementName:
          description: Used to refer to another meter frame which is actually the one that receives data through an external input.
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
        futureRequirement:
          description: |-
            DK: FremtidigtRegisterKra
            Indicates whether the register requirement should only be a future requirement. Default false.
            If set to true Validity period will be ignored.
          type: boolean
          nullable: false
        meterReadingRequired:
          description: |-
            DK: AflæsningPåkrævet
            Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
          type: boolean
          nullable: false
        requiredFrom:
          description: |-
            DK: GyldigFra.
            Indicates when the meter must meet the requirement.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTime'
        requiredUntil:
          description: |-
            DK: GyldigTil.
            Indicates from when the meter does not have to meet the requirement.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTime'

    RegisterRequirementTypeModel:
      nullable: false
      description: List of possible register requirement "ControlRequirement", "MarketRequirement", "TechnicalRequirement"
      type: integer
      format: int32
      minimum: 1
      maximum: 3
      x-enumFlags: false
      x-enumNames:
        - ControlRequirement
        - MarketRequirement
        - TechnicalRequirement
      enum:
        - 1
        - 2
        - 3

    SupplyType:
      type: integer
      format: int32
      minimum: 1
      maximum: 3
      description: Possible supply types "Electricity", "Water", "Heating"
      x-enumFlags: true
      x-enumNames:
        - Electricity
        - Water
        - Heating
      enum:
        - 1
        - 2
        - 3

    MeterFrameSupplyDisconnectTypeModel:
      type: integer
      nullable: true
      format: int32
      minimum: 1
      maximum: 8
      description: List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation".
      x-enumNames:
        - MeterNoVoltage
        - DisconnectedWithBreakerInMeter
        - DisconnectedBeforeMeter
        - DisconnectedInKabinet
        - DisconnectedAfterMeter
        - DisconnectedStation
        - Connected
        - Unknown
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8

    MeterFrameGisPropertiesElectricityModel:
      title: MeterFrameGisPropertiesElectricityModel
      description: |-
        GIS properties for electricity supply type in Meter Frame.
      type: object
      additionalProperties: false
      properties:
        gisId:
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
          description: Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.
        gidEquipmentContainerId:
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
          description: Top level (Equipment container) topology item identifier. Top level parent of topology node specified in gisId.
        # Used to cover following mappings:
        # branchLineFuseAmps - branchLine.mainProtection.size
        # branchLineFuseType - branchLine.mainProtection.protectionType
        # cableNumber - branchLine.numberOfPairs
        branchLine:
          description: Branch line for electricity
          $ref: "./schemas/BranchLineElectricity.yaml"

        # Used to cover following mappings:
        # cabinetNumber - connectionData.cabinetNumber
        # stationNumber - connectionData.transformerStationNumber
        connectionData:
          description: Connection point for electricity
          $ref: "./schemas/ConnectionDataElectricity.yaml"

        connectionPointLevel:
          description: 'Indicates at which level the branch line is connected in the network. Ex. C, B1, B2, A1, A2 and A0.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'B1'
        shinNumber:
          description: 'Number on the rail in the locker.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 15
        transformerNumber:
          description: 'Transformer number, for several transformers in a station. Ex. 1, 2, 3 etc.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 2

    MeterFrameSupplyStatusModel:
      type: integer
      format: int32
      minimum: 1
      maximum: 2
      description: List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
      x-enumNames:
        - Connected
        - Disconnected
      enum:
        - 1
        - 2

    MeterFrameElectricityAttributesModel:
      type: object
      additionalProperties: false
      description: Meter frame electricity attributes model.
      required:
        - meterType
        - preProtection
      properties:
        # Used to cover following mappings:
        # connectionType - meterType.connectionTypeId
        # ratioCt - meterType.meterTransformerId
        meterType:
          $ref: "./schemas/MeterTypeElectricity.yaml"

        # Used to cover following mappings:
        # breakerBeforeMeter - preProtection.breakerBeforeMeter
        # meterFrameFuse - preProtection.size
        preProtection:
          description: Pre-protection
          $ref: "./schemas/PreProtection.yaml"
          nullable: false

        powerLimitA:
          type: number
          description: 'DK: EffektgrænseA.'
          format: decimal
          nullable: true
        powerLimitKw:
          type: number
          description: 'DK: EffektgrænseKW.'
          format: decimal
          nullable: true
        productionCapacity:
          type: number
          description: 'DK: Anlægskapacitet.'
          format: decimal
          nullable: true
        purpose: #MOVE TO TEMPLATE!
          description: 'DK: Formål.'
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
        ratioVt:
          description: 'DK: OmsætningsforholdVT.'
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
        tariffConnectionPoint:
          description: 'DK: TarifTilslutningspunkt.'
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
        flexAttributeObject:
          description: |-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the entity.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an entity.
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
        lossFactor:
          type: number
          description: |-
            DK: NettabsFaktor.
            Nettabsfaktor som bruges på målerrammen i MDM. Vi skal lige gennemgå den fra MDM siden. Skal indgå både på fuldttidserie niveau så faktoren ganges på både 15/60 forbrug samt
            tællerstande.
            Mains loss factor used on the measuring frame in MDM. We just need to review it from the MDM page. Must be included both at full-time series level so the factor is multiplied by
            both 15/60 consumption and meter readings.
          format: decimal
          nullable: true
        meterDisplaySettings:
          description: |-
            DK: VisningMåler.
            En bruger skal kunne stille krav til visning af alle målerer som installeres i målerrammen.
            Skal medfører målerrammekrav på målerammen som gennemtvinger specifik display visning.
            EN:
            A user must be able to set requirements for displaying all meters that are installed in the meter frame.
            Must entail meter frame requirements on the meter frame which enforces specific display.
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
        connectionRemark:
          description: |-
            DK: TilslutningsBemærkning.
            Specielle forhold omkring tilslutning, f.eks. adapter, kvadrat, klemmetype etc.
            EN:
            Special conditions regarding connection, e.g. adapter, square, terminal type etc.
          allOf:
            - $ref: '#/components/schemas/GuidNullable'

    # SimpleTypes
    MediumStringObsoleteNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 1
      maxLength: 256
      description: "Max. 256 characters long string with spaces."
      example: "Max. 256 chars."

    Guid:
      description: globally unique identifier.
      type: string
      format: uuid
      nullable: false
      example: "8902FA98-E40C-4434-ADFF-AA85A80F0FC0"

    Boolean:
      type: boolean
      description: Boolean field.
      example: true

    DateTime:
      description: date time model
      type: string
      format: date-time
      nullable: false
      example: '2021-01-30T08:30:00Z'

    String1000:
      description: String1000
      pattern: "^.*$"
      type: string
      minLength: 0
      maxLength: 1000

    OneWordString:
      pattern: "^.*$"
      type: string
      minLength: 1
      maxLength: 25
      description: "Max. 25 characters long string with spaces."
      example: "Max. 25 chars. w. spaces."

    OneWordStringNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 1
      maxLength: 25
      description: 'Max. 25 characters long string with spaces.'
      example: 'Max. 25 chars. w. spaces.'

    Integer:
      type: integer
      nullable: false
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Integer type in <-2147483648,2147483647> range.'
      example: 111

    ShortStringNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 1
      maxLength: 50
      description: "OPTIONAL Max. 50 characters long string with spaces."

    GuidNullable:
      description: Nullable globally unique identifier.
      type: ['string', 'null']
      format: uuid
      example: "8902FA98-E40C-4434-ADFF-AA85A80F0FC0"

    ShortStringObsolete:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 128
      description: 'Max. 128 characters long string with spaces.'
      example: 'Max. 128 characters long string with spaces.'

    DecimalNullable:
      type: number
      format: decimal
      minimum: 0
      maximum: 99999999.9
      description: Positive decimal value.'
      example: 1.02
      nullable: true

    IntegerNullable:
      type: integer
      nullable: true
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Integer type in <-2147483648,2147483647> range.'
      example: 111

    ShortStringObsoleteNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 1
      maxLength: 128
      description: 'Max. 128 characters long string with spaces.'
      example: 'Max. 128 characters long string with spaces.'

    ShortString:
      pattern: "^.*$"
      type: string
      minLength: 1
      maxLength: 50
      description: "Max. 50 characters long string with spaces."
      example: "Max. 50 characters long string with spaces."

  parameters:
    TenantId:
      description: The tenant identifier.
      schema:
        type: number

  operationTraits:
    kafka:
      bindings:
        kafka:
          groupId:
            type: string
            enum: ['mdp-metering-points-group']
          key:
            type: string
            description: Message key is a process Id.
          bindingVersion: 1.0.0
