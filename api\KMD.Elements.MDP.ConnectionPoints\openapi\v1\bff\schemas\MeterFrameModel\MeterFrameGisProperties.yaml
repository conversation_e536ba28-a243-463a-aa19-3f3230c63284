title: MeterFrameGisProperties
type: object
description: |-
  Common properties for GIS in MeterFrame.
additionalProperties: false
required:
  - gisId
properties:
  gisId:
    description: 'Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.'
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  gidEquipmentContainerId:
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    description: Top level (Equipment container) topology item identifier. Top level parent of topology node specified in gisId.
  gisDescription:
    description: 'The name of the connection point in the physical topology. The name is set when the gis id is selected from the GIS system.'
    allOf:
      - $ref: '../DataTypes/DescriptionString.yaml'
    example: 'GisDescription is any 1000 characters long string with spaces allowed.'
