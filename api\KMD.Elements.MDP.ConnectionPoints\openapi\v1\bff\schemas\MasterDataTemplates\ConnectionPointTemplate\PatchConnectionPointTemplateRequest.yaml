title: PatchConnectionPointTemplateRequest
type: object
description: Data required to create a new connection point template.
additionalProperties: false
required:
  - name
properties:
  name:
    $ref: "../../DataTypes/ShortString.yaml"
  description:
    $ref: "../../DataTypes/LongStringNullable.yaml"
  connectionPointDefaultValueSetId:
    $ref: "../../DataTypes/GuidNullable.yaml"
  meteringPointsDefaultValueSetsIds:
    type: array
    maxItems: 50
    items:
      $ref: "../../DataTypes/Guid.yaml"
    description: List of metering points default value sets identifiers.
  meteringPointsTemplatesIds:
    type: array
    maxItems: 50
    items:
      $ref: "../../DataTypes/Guid.yaml"
    description: List of metering points templates identifiers.
  meterFrameDefaultValueSetsIds:
    type: array
    maxItems: 50
    items:
      $ref: "../../DataTypes/Guid.yaml"
    description: List of metering frame default value set identifiers.
  meterFrameTemplatesIds:
    type: array
    maxItems: 50
    items:
      $ref: "../../DataTypes/Guid.yaml"
    description: List of metering points templates identifiers.
  parentChildRelations:
    type: array
    maxItems: 50
    items:
      $ref: "./ConnectionPointParentChildRelation.yaml"
    description: List of metering points parent-child relation objects.
