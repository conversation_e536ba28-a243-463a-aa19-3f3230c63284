type: object
description: Update metering point payload for any supply type
additionalProperties: false
required:
  - meteringPointVersionId
  - typeOfMeteringPoint
  - validFrom
  - minValidFrom
properties:
  meteringPointVersionId:
    nullable: false
    description: Metering point version.
    allOf:
      - $ref: '../../MeteringPointModel/DataTypes/MeteringPointVersionId.yaml'
  typeOfMeteringPoint:
    nullable: false
    description: Type of metering point.
    allOf:
      - $ref: '../../MeteringPointModel/DataTypes/TypeOfMeteringPoint.yaml'
  validFrom:
    nullable: false
    description: Date from which this metering point applies .
    allOf:
      - $ref: '../../DataTypes/DateTime.yaml'
  minValidFrom:
    nullable: false
    description: Min date from which this metering point applies.
    allOf:
      - $ref: '../../DataTypes/DateTime.yaml'
  meterReadingOccurrence:
    nullable: true
    description: Mapped from DH 'meterReadingOccurrence' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumString.yaml'
  locationDescription:
    nullable: true
    description: Mapped from DH 'locationDescription' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/LongString.yaml'
  unitType:
    nullable: true
    description: Mapped from DH 'unitType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumString.yaml'
  supplementedInfoBilling:
    pattern: "^.*$"
    type: string
    nullable: true
    minLength: 1
    maxLength: 100
    description: Supplemented billing info for the Metering Point.
    example: A01
  disconnectionType:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `DisconnectionType`
    pattern: ^(D01|D02)$
  inheritDisconnectionTypeFromMeter:
    nullable: true
    type: boolean
    description: Is mapped to `InheritDisconnectionTypeFromMeter`
  maximumCurrentInAmperes:
    pattern: "^\\d+$"
    nullable: true
    type: string
    description: Is mapped to `MaximumCurrent`
    maxLength: 6
  maximumPowerInKiloWatts:
    pattern: "^(\\d*(?:[.]\\d+)?)?$"
    nullable: true
    type: string
    description: Is mapped to `MaximumPower`
    maxLength: 128
