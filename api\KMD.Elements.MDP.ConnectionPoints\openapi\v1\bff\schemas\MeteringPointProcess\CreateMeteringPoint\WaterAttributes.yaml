type: object
description: |
  Water specific attributes
additionalProperties: false
properties:
  applicationCode:
    nullable: true
    description: Application code for metering point.
    allOf:
      - $ref: '../../DataTypes/MediumString.yaml'
  expectedConsumptionPerYear:
    description: Expected consumption per year.
    allOf:
      - $ref: '../../DataTypes/PositiveIntegerNullable.yaml'
  noMeter:
    type: boolean
    nullable: false
    description: Indicates whether it is a meter-free installation (subscription only).
    example: true
  commonReading:
    type: string
    description: |-
      Indicates whether the meter supplies several units (buildings, apartments, etc.).
    minLength: 1
    maxLength: 1
    pattern: "^(0|1|2)$"
  supplementedInfoBilling:
    pattern: "^.*$"
    type: string
    nullable: true
    minLength: 1
    maxLength: 100
    description: Supplemented billing info for the Metering Point.
    example: A01
  meterSize:
    pattern: '^.*$'
    minLength: 0
    maxLength: 100
    type: string
    nullable: true
    description: Indicates the total business area from BBR.
    example: 50m2
