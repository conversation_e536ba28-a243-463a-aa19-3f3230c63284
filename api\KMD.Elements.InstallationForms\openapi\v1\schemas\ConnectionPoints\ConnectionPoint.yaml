title: ConnectionPoint
type: object
description: Connections point model
additionalProperties: false
nullable: true
properties:
  id:
    type: string
    description: Connection point id
    format: uuid
    example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
    nullable: true
  connectionPointNumber:
    type: string
    description: The connectionPointNumber property
    pattern: "^.*$"
    minLength: 1
    maxLength: 128
  electricityConnectionStatusId:
    type: string
    description: Electricity connection status id of selected connection point (valid only for Electricity).
    format: uuid
    nullable: true
    example: 87b5fb3d-e71a-4d04-b839-7e26fef03b4b
