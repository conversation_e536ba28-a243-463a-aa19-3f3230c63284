openapi: 3.0.0
info:
  title: KMD.Elements.ConnectionPoints.BFF
  description: KMD Elements - Connection Points BFF - dedicated BFF for KMD.Elements.ConnectionPoints.Api
  termsOfService: https://www.kmd.net/terms-of-use
  contact:
    name: KMD Elements
    url: https://www.kmd.net/solutions/kmd-elements
    email: <EMAIL>
  license:
    name: License
    url: https://www.kmd.net/terms-of-use
  version: '1.43'
  x-maintainers: Team-MD-2
servers:
  - url: "/"
  - url: "/api/connection-points-bff"
paths:
  /:
    get:
      tags:
        - Home
      summary: Gets the name and version of the API.
      operationId: Home_Get
      responses:
        "200":
          description: "\n            Returns the API name and version.\n            Sample responses:\n            - On local environment: ```\"KMD.Elements.ConnectionPoints.BFF.Api, Version: Local Development\"```\n            - On remote environment: ```\"KMD.Elements.ConnectionPoints.BFF.Api, Version: 20200713.25_master/20200713.11_master\"```.\n            "
          content:
            application/json:
              schema:
                type: string
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
  /api/connection-rights:
    get:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Fetches Connection Rights list by meter frame or meter number
      description: |-
        ### Result
        Returns top connection rights according to query parameters.
      operationId: ConnectionRights_GetCollectionOfConnectionRights
      parameters:
        - name: meterFrameId
          in: query
          description: Id of the MeterFrame.
          schema:
            nullable: true
            type: string
            format: guid
          x-position: 1
        - name: meterNumber
          in: query
          description: Numer of the MeterFrame.
          schema:
            nullable: true
            type: string
            description: Number on the Meter Frame.
            maxLength: 50
            minLength: 1
          x-position: 2
      responses:
        '200':
          description: Connection Rights returned successfully.
          content:
            application/json:
              schema:
                description: Connection Rights returned successfully.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/ConnectionRightModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Create Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: createConnectionRight
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConnectionRightModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

      security:
        - Jwt: [ ]

  /v1/connection-rights/total-value:
    post:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Calculate Total Connection Rights.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: calculateTotalConnectionRights
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionRightsTotalValueBody'
        required: true
      responses:
        '200':
          description: Successfully calculated.
          content:
            application/json:
              schema:
                description: Connection Rights returned successfully.
                type: array
                maxItems: 1000
                items:
                  $ref: '#/components/schemas/ConnectionRightsTotalValueModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-rights/{id}:
    put:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Update Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: updateConnectionRight
      parameters:
        - name: id
          in: path
          required: true
          description: Internal Id.
          schema:
            type: string
            format: guid
          x-position: 1
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConnectionRightModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Allows to delete Connection Rights.
      description: Deletes Connection Right.
      operationId: deleteConnectionRight
      parameters:
        - name: id
          in: path
          required: true
          description: Internal Id.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

    get:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Gets specific Connection Right.
      operationId: getConnectionRightById
      description: Returns specific Connection Right by technical GUID identifier.
      parameters:
        - name: id
          in: path
          required: true
          description: Internal Id.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        '200':
          description: Found Connection Right.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionRightModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-rights/commands/stop:
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Stop Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: stopConnectionRight
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StopConnectionRightModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-rights/commands/transfer:
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Transfers one connection right into many, many into one or one to one. Transfer deactives (sets valid to date) on transfered connection right.
      operationId: transferConnectionRights
      description: |-
        Transfers connection rights.
      requestBody:
        description: Request body.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionRightTransfer'
      responses:
        '204':
          description: Successfully transfered.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-rights/commands/searchInstallationForm:
    post:
      x-authorization: InstallationForms.Read
      x-authorization-2: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Search for Installation Form.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: searchInstallationForm
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchInstallationFormCommand'
        required: true
      responses:
        '200':
          description: Found Installation Form.
          content:
            application/json:
              schema:
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/FormReference'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /api/code-lists:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - CodeList
      summary: Fetches list of Code Lists by Code List Area / Code List Type or all when no parameters are passed.
      description: >-
        ### Result

        Returns top codelist according to query parameters.
      operationId: CodeList_GetCollectionOfCodeLists
      parameters:
        - name: codeListUsageArea
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/CodeListUsageAreasModel'
          x-position: 1
        - name: codeListTypeKind
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/CodeListTypeKindModel'
          x-position: 2
      responses:
        "200":
          description: Code Lists returned successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CodeListModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: [ ]
  /api/configuration:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - Configuration
      summary: Fetches configuration.
      description: Returns configuration.
      operationId: Configuration_Get
      responses:
        "200":
          description: Configuration returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: [ ]
  /api/internal-equipments/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipment
      summary: Fetches list of Internal Equipments.
      description: >-
        ### Result

        Returns filtered, sorted and paged list of connection points according to passed criteria.
      operationId: InternalEquipment_Search
      requestBody:
        x-name: dataQueryModel
        description: Filter/sorting/paging model..
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchListQueryModel'
        required: true
        x-position: 1
      responses:
        "200":
          description: Internal Equipments returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetInternalEquipmentSearchModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []

  /v1/internal-equipments/export:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipment
      summary: Fetches CSV export file with Internal Equipments filtered by body filter params.
      description: >-
        ### Result
        Returns filtered internal equipment list exported to CSV file.
      operationId: InternalEquipment_Export
      requestBody:
        x-name: dataQueryModel
        description: Filter/sorting/paging model..
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchListQueryModelWithCsvConfiguration'
        required: true
      responses:
        '200':
          description: Internal Equipments export CSV file.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
      security:
        - Jwt: []

  /api/internal-equipments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipment
      summary: Fetches list of Internal Equipments by equipment number.
      description: >-
        ### Result

        Returns top 100 internal equipments.
      operationId: InternalEquipment_SearchNonAssignedByEquipmentNumber
      parameters:
        - name: equipmentNumber
          in: query
          schema:
            type: string
            nullable: true
          x-position: 1
        - name: installationPossibility
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/InstallationPossibilityModel'
          x-position: 2
      responses:
        200:
          description: Internal Equipments returned successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetInternalEquipmentSearchModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    head:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipment
      summary: Checks if Internal Equipment with equipment number exists.
      operationId: InternalEquipment_CheckByEquipmentNumber
      parameters:
        - name: equipmentNumber
          required: true
          in: query
          description: Equipment number.
          schema:
            type: string
            nullable: false
            minLength: 1
            maxLength: 1000
          x-position: 1
      responses:
        "200":
          description: Equipment exists.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipment
      summary: Creates Internal Equipment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: InternalEquipment_Post
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddInternalEquipmentModel'
        required: true
        x-position: 1
      responses:
        "201":
          $ref: '#/components/responses/201'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/internal-equipments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipment
      summary: Gets specific Internal Equipment.
      operationId: InternalEquipment_GetById
      parameters:
        - name: id
          in: path
          required: true
          description: Internal Equipment Id.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "200":
          description: Found Internal Equipment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetInternalEquipmentByIdModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipment
      summary: Allows to update Internal Equipment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: InternalEquipment_Put
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
      requestBody:
        x-name: model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInternalEquipmentModel'
        required: true
        x-position: 2
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipment
      summary: Allows to delete Internal Equipment.
      operationId: InternalEquipment_Delete
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/meter-input-connections:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnection
      summary: Gets Meter input connections for Meter Frame.
      operationId: MeterInputConnection_GetMeterInputConnectionsByMeterFrameId
      parameters:
        - name: meterFrameId
          in: query
          description: Meter Frame Id.
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "200":
          description: Meter Input Connection collection.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MeterInputConnectionModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/meter-input-connections/commands/validate-active-meter-on-meter-frame-register-requirements:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnection
      summary: Validates active meter on meter frame register requirements.
      description: Validates active meter on meter frame register requirements.
      operationId: MeterInputConnection_ValidateActiveMeterOnMeterFrameRegisterRequirements
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameRegisterRequirements'
        required: true
      responses:
        "204":
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /api/meter-input-connections/commands/validate-active-meter-on-meter-frame-attributes-requirements:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnection
      summary: Validates active meter on meter frame attributes requirements.
      description: Validates active meter on meter frame attributes requirements.
      operationId: MeterInputConnection_ValidateActiveMeterOnMeterFrameAttributesRequirements
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameAttributesRequirements'
        required: true
      responses:
        "204":
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /api/register-requirements:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirement
      summary: Gets paged Register Requirements for given Meter Frame or if meterFrameId gets Register Requirements starting with registerRequirementIdStartsWithInQueryString.
      operationId: RegisterRequirement_GetPagedRegisterRequirements
      parameters:
        - name: meterFrameId
          in: query
          description: Meter Frame Id.
          schema:
            type: string
            format: guid
            nullable: true
          x-position: 1
        - name: registerRequirementIdStartsWithInQueryString
          in: query
          description: Partial Register Requirement Id.
          schema:
            type: integer
            format: int32
            nullable: true
          x-position: 2
        - name: Number
          in: query
          description: Page number.
          schema:
            type: integer
            format: int32
            maximum: 2147483647
            minimum: 0
          x-position: 3
        - name: Size
          in: query
          description: Page size.
          schema:
            type: integer
            format: int32
            maximum: 2147483647
            minimum: 0
          x-position: 4
      responses:
        "200":
          description: Register Requirements collection.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRegisterRequirementResponseBody'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - RegisterRequirement
      summary: Creates Register Requirement.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: RegisterRequirement_Post
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddRegisterRequirementModel'
        required: true
        x-position: 1
      responses:
        "201":
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntIdResult'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/register-requirements/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirement
      summary: Gets specific Register Requirement.
      operationId: RegisterRequirement_GetById
      parameters:
        - name: id
          in: path
          required: true
          description: Register Requirement id.
          schema:
            type: integer
            format: int32
          x-position: 1
      responses:
        "200":
          description: Found Register Requirement.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterRequirementModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - RegisterRequirement
      summary: Allows to update Register Requirement.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: RegisterRequirement_Put
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int32
          x-position: 1
      requestBody:
        x-name: model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRegisterRequirementModel'
        required: true
        x-position: 2
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - RegisterRequirement
      summary: Allows to delete Register Requirement.
      operationId: RegisterRequirement_Delete
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int32
          x-position: 1
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /v1/register-requirements/commands/calculate-default-valid-from:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirement
      summary: Calculate default valid from for specific meter frame.
      description: |-
        ### Result
        Returns valid from date.
      operationId: calculateDefaultValidFromForSpecificMeterFrame
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateDefaultValidFromBody'
        required: true
      responses:
        '200':
          description: Default valid from returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateDefaultValidFromResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /api/special-agreements/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - SpecialAgreement
      summary: Fetches list of Special Agreements.
      description: >-
        ### Result

        Returns filtered, sorted and paged list of Special Agreements according to passed criteria.
      operationId: SpecialAgreement_Search
      requestBody:
        x-name: dataQueryModel
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchListQueryModel'
        required: true
        x-position: 1
      responses:
        "200":
          description: Special Agreements returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetSpecialAgreementsSearchModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []

  /v1/special-agreements/export:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - SpecialAgreement
      summary: Fetches CSV export file with Special Agreements filtered by body filter params.
      description: >-
        ### Result

        Returns filtered Special Agreements exported to CSV file.
      operationId: SpecialAgreement_Export
      requestBody:
        x-name: dataQueryModel
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchListQueryModelWithCsvConfiguration'
        required: true
      responses:
        '200':
          description: Special Agreements export CSV file.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
      security:
        - Jwt: []

  /api/special-agreements/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - SpecialAgreement
      summary: Gets specific Special Agreement.
      operationId: SpecialAgreement_GetById
      parameters:
        - name: id
          in: path
          required: true
          description: Special Agreement id.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "200":
          description: Found Special Agreement.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSpecialAgreementByIdModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - SpecialAgreement
      summary: Allows to update Special Agreement.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: SpecialAgreement_Put
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
      requestBody:
        x-name: model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSpecialAgreementModel'
        required: true
        x-position: 2
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - SpecialAgreement
      summary: Allows to delete Special Agreement.
      operationId: SpecialAgreement_Delete
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/special-agreements:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - SpecialAgreement
      summary: Creates Special Agreement.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: SpecialAgreement_Post
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSpecialAgreementModel'
        required: true
        x-position: 1
      responses:
        "201":
          $ref: '#/components/responses/201'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/translations/{language}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - Translations
      summary: Returns list of translations associated with connection points bounded context.
      operationId: Translations_Get
      parameters:
        - name: language
          in: path
          required: true
          description: 'Language e.g.: en-US.'
          schema:
            type: string
            nullable: false
          x-position: 1
      responses:
        "200":
          description: Translations for bounded context.
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: string
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
  /api/translations/special-agreements/{language}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - Translations
      summary: Returns list of translations associated with connection points bounded context - special agreement aggregate root.
      operationId: Translations_GetSpecialAgreementsTranslations
      parameters:
        - name: language
          in: path
          required: true
          description: 'Language e.g.: en-US.'
          schema:
            type: string
            nullable: false
          x-position: 1
      responses:
        "200":
          description: Translations for bounded context.
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: string
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'

  /v1/meter-frames/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Fetches list of Meter Frames.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Meter Frames according to passed criteria.
      operationId: searchMeterFrames
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFramesListSearchQueryModel'
        required: true
      responses:
        '200':
          description: Meter Frames returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResponseOfPostMeterFrameSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':

          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'


  /api/meter-frames:
    get:
      deprecated: true
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frames collection for given Connection Point.
      operationId: MeterFrame_GetPagedMeterFrames
      parameters:
        - name: ConnectionPointId
          in: query
          schema:
            type: string
            format: guid
            nullable: true
        - name: MeterFrameId
          in: query
          schema:
            type: string
            format: guid
            nullable: true
        - name: ConnectionPointBusinessId
          in: query
          schema:
            type: string
            nullable: true
        - name: SupplyType
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/SupplyTypesModel'
        - name: SupplyStatus
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        - name: SupplyDisconnectType
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        - name: CommonReading
          in: query
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
          schema:
            nullable: true
            $ref: '#/components/schemas/GuidField'
          example: 'f56d5e72-d9f1-4d4d-95ac-ca06079dfc0a'
        - name: noMeter
          in: query
          description: No meter.
          schema:
            description: No Meter.
            nullable: true
            type: boolean
          example: true
        - name: MeterMissing
          in: query
          schema:
            type: boolean
            nullable: true
        - name: MeterReadingType
          in: query
          description: 'A list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".'
          schema:
            nullable: true
            $ref: '#/components/schemas/GuidField'
          example: 'd988b796-225b-4eef-8ec4-191ee69a3a80'
        - name: MeterFrameNumber
          in: query
          schema:
            type: string
            nullable: true
        - name: AddressLine1
          in: query
          schema:
            type: string
            nullable: true
        - name: AddressLine2
          in: query
          schema:
            type: string
            nullable: true
        - name: AddressStatus
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/AddressStatus'
        - name: AddressType
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/AddressType'
        - name: LifeCycleStatus
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/LifeCycleStatus'
        - name: DarStatus
          in: query
          schema:
            nullable: true
            oneOf:
              - $ref: '#/components/schemas/DarStatus'
        - name: CabinetNumber
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: CableNumber
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: StationNumber
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: TransformerNumber
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: ShinNumber
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: HeatingBranchLineNumber
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: HeatingPlantId
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: HeatingPlantName
          in: query
          schema:
            type: string
            nullable: true
        - name: HeatingPlantPipeName
          in: query
          schema:
            type: string
            nullable: true
        - name: HeatingPlantPipeNumber
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: WaterBranchLineNumber
          in: query
          schema:
            type: string
            nullable: true
        - name: SectionId
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: SectionName
          in: query
          schema:
            type: string
            nullable: true
        - name: WaterPlantId
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: WaterPlantName
          in: query
          schema:
            type: string
            nullable: true
        - name: WaterPlantPipeId
          in: query
          schema:
            type: integer
            format: int32
            nullable: true
        - name: WaterPlantPipeName
          in: query
          schema:
            type: string
            nullable: true
        - name: MainZone
          in: query
          schema:
            type: string
            nullable: true
        - name: SuperZone
          in: query
          schema:
            type: string
            nullable: true
        - name: Number
          in: query
          description: Page number.
          schema:
            type: integer
            format: int32
            maximum: 2147483647
            minimum: 0
        - name: Size
          in: query
          description: Page size.
          schema:
            type: integer
            format: int32
            maximum: 2147483647
            minimum: 0
        - name: SortBy
          in: query
          description: Name of the property to sort by.
          schema:
            $ref: '#/components/schemas/SortByNullable'
          example: 'connectionPointNumber'
        - name: SortOrder
          in: query
          description: Direction of sorting. Can only be 'asc' for ascending or 'desc' for descending.
          schema:
            $ref: '#/components/schemas/SortOrderNullable'
          example: 'asc'
      responses:
        "200":
          description: Meter Frames collection.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResponseOfGetMeterFrameSearchModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    post:
      deprecated: true
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Creates Meter Frame. DEPRECATED. Please use v2 instead.
      description: >-
        DEPRECATED. Please use v2 instead.
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: MeterFrame_Post
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddMeterFrameModel'
        required: true
        x-position: 1
      responses:
        "201":
          $ref: '#/components/responses/201'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /v1/meter-frames/export:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Fetches CSV export file with Meter Frames collection.
      operationId: MeterFrame_Export
      requestBody:
        description: Meter frames export query model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFramesSearchQueryModelWithCsvConfiguration'
      responses:
        '200':
          description: 'Meter frames export CSV file'
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        '500':
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /api/meter-frames/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets specific Meter Frame.
      operationId: MeterFrame_GetById
      parameters:
        - name: id
          in: path
          required: true
          description: Meter Frame id.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "200":
          description: Found Meter Frame.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMeterFrameModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    put:
      deprecated: true
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update Meter Frame. DEPRECATED. Please use v2 instead.
      description: >-
        DEPRECATED. Please use v2 instead.
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: MeterFrame_Put
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
      requestBody:
        x-name: model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMeterFrameModel'
        required: true
        x-position: 2
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []

  /api/meter-frames/commands/exists-by-number:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Checks if Meter Frame with exact meter frame number exists with in specified connection point.
      operationId: MeterFrame_CheckIfMeterFrameExistsByNumber
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFrameExistByNumberModel'
        required: true
        x-position: 1
      responses:
        "200":
          description: Meter frame exists information.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMeterFrameExistsResult'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /v1/meter-frames/commands/get-meter-frames-with-unrelated-branch-line:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - Returns meter frames with meter frame numbers containing passed parameter. Returned MFs have filled out branch lines and no connected meter frame id.
      operationId: getMeterFramesWithUnrelatedBranchLine
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchByMeterFrameNumberPagedModel'
        required: true
      responses:
        '200':
          description: Found Meter Frames.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResponseOfGetMeterFramesBasicModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/commands/get-meter-frames-with-branch-line-related-to-meter-frame:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - Returns meter frames with meter frame numbers containing passed parameter. Returned MFs have filled out branch lines and no connected meter frame id.
      operationId: getMeterFramesWithRelatedBranchLines
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFrameIdWithSupplyTypeInSchema'
        required: true
      responses:
        '200':
          description: Found Meter Frames.
          content:
            application/json:
              schema:
                type: array
                description: Results array.
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/GetMeterFrameBasicModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /api/meter-frames/{meterFrameId}/external-equipment-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameExternalEquipmentAssignments
      summary: Gets All External Equipments assigned to the Meter Frame.
      operationId: MeterFrameExternalEquipmentAssignments_GetAll
      parameters:
        - name: meterFrameId
          in: path
          required: true
          description: Id of the MeterFrame.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "200":
          description: Meter Frames Equipment Assignments collection.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ExternalEquipmentAssignmentModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameExternalEquipmentAssignments
      summary: Creates External Equipment assignment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: MeterFrameExternalEquipmentAssignments_Post
      parameters:
        - name: meterFrameId
          in: path
          required: true
          description: Id of the Meter Frame.
          schema:
            type: string
            format: guid
          x-position: 1
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExternalEquipmentAssignmentUpsertModel'
        required: true
        x-position: 2
      responses:
        "201":
          $ref: '#/components/responses/201'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/meter-frames/{meterFrameId}/external-equipment-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameExternalEquipmentAssignments
      summary: Gets specific External Equipment Assignment for the Meter Frame.
      operationId: MeterFrameExternalEquipmentAssignments_GetMeterFrameExternalEquipmentAssignmentById
      parameters:
        - name: id
          in: path
          required: true
          description: External Equipment Assignment id.
          schema:
            type: string
            format: guid
          x-position: 1
        - name: meterFrameId
          in: path
          required: true
          description: Id of the MeterFrame.
          schema:
            type: string
            format: guid
          x-position: 2
      responses:
        "200":
          description: Found External Equipment Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalEquipmentAssignmentModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameExternalEquipmentAssignments
      summary: Allows to update External Equipment Assignment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: MeterFrameExternalEquipmentAssignments_Put
      parameters:
        - name: meterFrameId
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 2
      requestBody:
        x-name: model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExternalEquipmentAssignmentUpsertModel'
        required: true
        x-position: 3
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/meter-frames/{meterFrameId}/internal-equipment-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameInternalEquipmentAssignments
      summary: Gets All Internal Equipments assigned to the Meter Frame.
      operationId: MeterFrameInternalEquipmentAssignments_GetAll
      parameters:
        - name: meterFrameId
          in: path
          required: true
          description: Id of the MeterFrame.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "200":
          description: Meter Frames Equipment Assignments collection.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InternalEquipmentAssignmentModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameInternalEquipmentAssignments
      summary: Creates Internal Equipment assignment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: MeterFrameInternalEquipmentAssignments_Post
      parameters:
        - name: meterFrameId
          in: path
          required: true
          description: Id of the Meter Frame.
          schema:
            type: string
            format: guid
          x-position: 1
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalEquipmentAssignmentUpsertModel'
        required: true
        x-position: 2
      responses:
        "201":
          $ref: '#/components/responses/201'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/meter-frames/{meterFrameId}/internal-equipment-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameInternalEquipmentAssignments
      summary: Gets specific Internal Equipment Assignment for the Meter Frame.
      operationId: MeterFrameInternalEquipmentAssignments_GetMeterFrameInternalEquipmentAssignmentById
      parameters:
        - name: id
          in: path
          required: true
          description: Internal Equipment Assignment id.
          schema:
            type: string
            format: guid
          x-position: 1
        - name: meterFrameId
          in: path
          required: true
          description: Id of the MeterFrame.
          schema:
            type: string
            format: guid
          x-position: 2
      responses:
        "200":
          description: Found Internal Equipment Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalEquipmentAssignmentModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameInternalEquipmentAssignments
      summary: Allows to update Internal Equipment Assignment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: MeterFrameInternalEquipmentAssignments_Put
      parameters:
        - name: meterFrameId
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 2
      requestBody:
        x-name: model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalEquipmentAssignmentUpsertModel'
        required: true
        x-position: 3
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/connection-points/search:
    post:
      deprecated: true
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Fetches list of Connection Points - OBSOLETE.
      description: >-
        ### Result

        Returns filtered, sorted and paged list of connection points according to passed criteria.
      operationId: ConnectionPoint_SearchObsolete
      requestBody:
        x-name: dataQueryModel
        description: Filter/sorting/paging model..
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchListQueryModel'
        required: true
        x-position: 1
      responses:
        "200":
          description: Connection Points returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetConnectionPointsSearchModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []

  /v1/connection-points/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Fetches list of Connection Points.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Connection Points according to passed criteria.
      operationId: searchConnectionPoints
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionPointsListSearchQueryModel'
        required: true
      responses:
        '200':
          description: Connection Points returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetConnectionPointsSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
      security:
        - Jwt: []

  /v1/connection-points/commands/export:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      operationId: ConnectionPoints_Export
      summary: Fetches CSV export file with connection points filtered by body filter params.
      description: |-
        ### Result
        Returns filtered connection points exported to CSV file.
      requestBody:
        description: Export request body.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionPointsListSearchQueryModelWithCsvConfiguration'
      responses:
        '200':
          description: 'Connection points export CSV file.'
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '400':
          $ref: "#/components/responses/400"
        '401':
          $ref: "#/components/responses/401"
        '403':
          $ref: "#/components/responses/403"
        '404':
          $ref: "#/components/responses/404"
        '429':
          $ref: "#/components/responses/429"
        '499':
          $ref: "#/components/responses/499"
        '500':
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /api/connection-points:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Gets paged ConnectionPoints for passed query parameters.
      operationId: ConnectionPoint_GetPagedConnectionPoints
      parameters:
        - name: connectionPointNumber
          in: query
          description: Optional filter parameter.
          schema:
            type: string
            nullable: true
          x-position: 1
        - name: number
          in: query
          schema:
            type: integer
            format: int32
          x-position: 2
        - name: size
          in: query
          schema:
            type: integer
            format: int32
          x-position: 3
      responses:
        "200":
          description: Connection Points collection.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResponseOfGetPagedConnectionPointsMasterDataModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: Creates Connection Point.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: ConnectionPoint_Post
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddConnectionPointModel'
        required: true
        x-position: 1
      responses:
        "201":
          $ref: '#/components/responses/201'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/connection-points/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Gets specific Connection Point.
      operationId: ConnectionPoint_GetById
      parameters:
        - name: id
          in: path
          required: true
          description: Connection Point Id.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "200":
          description: Found Connection Point.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionPointModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: Allows to update Connection Point.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: ConnectionPoint_Put
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
      requestBody:
        x-name: model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConnectionPointModel'
        required: true
        x-position: 2
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: Allows to delete Connection Point.
      operationId: ConnectionPoint_Delete
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "204":
          description: Successfully deleted.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/connection-points/commands/get-by-connection-point-number:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Gets specific Connection Point by business Connection Point number.
      operationId: ConnectionPoint_GetByConnectionPointNumber
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetByConnectionPointNumberModel'
      responses:
        "200":
          description: Found Connection Point.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionPointModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/connection-points/{connectionPointId}/external-equipment-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointExternalEquipmentAssignments
      summary: Gets All External Equipments assigned to the Connection Point.
      operationId: ConnectionPointExternalEquipmentAssignments_GetAll
      parameters:
        - name: connectionPointId
          in: path
          required: true
          description: Id of the Connection Point.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "200":
          description: Connection Points collection.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ExternalEquipmentAssignmentModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointExternalEquipmentAssignments
      summary: Creates External Equipment assignment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: ConnectionPointExternalEquipmentAssignments_Post
      parameters:
        - name: connectionPointId
          in: path
          required: true
          description: Id of the Connection Point.
          schema:
            type: string
            format: guid
          x-position: 1
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExternalEquipmentAssignmentUpsertModel'
        required: true
        x-position: 2
      responses:
        "201":
          $ref: '#/components/responses/201'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/connection-points/{connectionPointId}/external-equipment-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointExternalEquipmentAssignments
      summary: Gets specific External Equipment Assignment.
      operationId: ConnectionPointExternalEquipmentAssignments_GetConnectionPointExternalEquipmentAssignmentById
      parameters:
        - name: id
          in: path
          required: true
          description: External Equipment Assignment id.
          schema:
            type: string
            format: guid
          x-position: 1
        - name: connectionPointId
          in: path
          required: true
          description: Connection Point id.
          schema:
            type: string
            format: guid
          x-position: 2
      responses:
        "200":
          description: Found External Equipment Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalEquipmentAssignmentModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointExternalEquipmentAssignments
      summary: Allows to update External Equipment Assignment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: ConnectionPointExternalEquipmentAssignments_Put
      parameters:
        - name: connectionPointId
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 2
      requestBody:
        x-name: model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExternalEquipmentAssignmentUpsertModel'
        required: true
        x-position: 3
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/connection-points/{connectionPointId}/internal-equipment-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointInternalEquipmentAssignments
      summary: Gets All Internal Equipments assigned to the Connection Point.
      operationId: ConnectionPointInternalEquipmentAssignments_GetAll
      parameters:
        - name: connectionPointId
          in: path
          required: true
          description: Id of the Connection Point.
          schema:
            type: string
            format: guid
          x-position: 1
      responses:
        "200":
          description: Internal Equipments assigned collection.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InternalEquipmentAssignmentModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointInternalEquipmentAssignments
      summary: Creates Internal Equipment assignment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: ConnectionPointInternalEquipmentAssignments_Post
      parameters:
        - name: connectionPointId
          in: path
          required: true
          description: Id of the Connection Point.
          schema:
            type: string
            format: guid
          x-position: 1
      requestBody:
        x-name: model
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalEquipmentAssignmentUpsertModel'
        required: true
        x-position: 2
      responses:
        "201":
          $ref: '#/components/responses/201'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/connection-points/{connectionPointId}/internal-equipment-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointInternalEquipmentAssignments
      summary: Gets specific Internal Equipment Assignment.
      operationId: ConnectionPointInternalEquipmentAssignments_GetConnectionPointInternalEquipmentAssignmentById
      parameters:
        - name: id
          in: path
          required: true
          description: Internal Equipment Assignment id.
          schema:
            type: string
            format: guid
          x-position: 1
        - name: connectionPointId
          in: path
          required: true
          description: Connection Point id.
          schema:
            type: string
            format: guid
          x-position: 2
      responses:
        "200":
          description: Found Internal Equipment Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalEquipmentAssignmentModel'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointInternalEquipmentAssignments
      summary: Allows to update Internal Equipment Assignment.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: ConnectionPointInternalEquipmentAssignments_Put
      parameters:
        - name: connectionPointId
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 1
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: guid
          x-position: 2
      requestBody:
        x-name: model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalEquipmentAssignmentUpsertModel'
        required: true
        x-position: 3
      responses:
        "204":
          description: Successfully updated.
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "409":
          $ref: '#/components/responses/409'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []
  /api/batch/internal-equipments:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - BatchImport
      operationId: BatchImport_BatchImportInternalEquipments
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                importFile:
                  type: string
                  format: binary
                  nullable: true
      responses:
        "204":
          description: 'File uploaded for processing.'
        "400":
          $ref: '#/components/responses/400'
        "401":
          $ref: '#/components/responses/401'
        "403":
          $ref: '#/components/responses/403'
        "404":
          $ref: '#/components/responses/404'
        "422":
          $ref: '#/components/responses/422'
        "429":
          $ref: '#/components/responses/429'
        "500":
          $ref: '#/components/responses/500'
        "503":
          $ref: '#/components/responses/503'
        "504":
          $ref: '#/components/responses/504'
      security:
        - Jwt: []

  /v1/metering-point-versions:
    get:
      tags:
        - MeteringPointVersions
      operationId: getPagedMeteringPointVersions
      summary: Gets paged metering point versions collection.
      x-authorization: MeteringPoints.Read
      description: |-
        ### Remarks
        - Gets paged Metering Point Versions collection. If no results meet query parameters, empty collection will be returned with 200 OK status.
      parameters:
        - name: connectionPointId
          in: query
          description: Connection Point technical identifier.
          required: true
          schema:
            $ref: '#/components/schemas/Guid'
          example: 'dc23d9bb-67fa-4595-9077-7a71cb548613'
        - name: occurrenceValidAt
          required: true
          in: query
          schema:
            $ref: '#/components/schemas/DateTime'
          description: |-
            ### Remarks
            - It filters Versions as so all versions are returned, where Occurrence <= occurrenceValidAt < OccurrenceTo, so it returns only versions which are valid at the date of parameter passed.
            - IMPORTANT: If only "future" version is available (relatively to parameter), so only versions with Occurrence > occurrenceValidAt are available, future versions won't be returned (as they don't match the rule above). If it's needed to find closest future version on MPV, occurrenceFrom parameter should be used, and additional filtration is required on client side.
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
        - $ref: '#/components/parameters/SortByNullableForMeteringPointVersions'
        - $ref: '#/components/parameters/SortOrderNullableForMeteringPointVersions'
      responses:
        "200":
          description: Success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MeteringPointVersionsCollectionResponseModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"

  /v1/technical-metering-point-versions:
    get:
      tags:
        - TechnicalMeteringPointVersions
      operationId: getPagedTechnicalMeteringPointVersions
      summary: Gets paged metering points collection.
      x-authorization: MeteringPoints.Read
      description: |-
        ### Remarks
        - Gets paged Technical Metering Points collection. If no results meet query parameters, empty collection will be returned with 200 OK status.
      parameters:
        - name: connectionPointId
          in: query
          description: Connection Point technical identifier.
          required: true
          schema:
            $ref: '#/components/schemas/Guid'
          example: 'dc23d9bb-67fa-4595-9077-7a71cb548613'
        - name: occurrenceValidAt
          required: true
          in: query
          schema:
            $ref: '#/components/schemas/DateTime'
          description: |-
            ### Remarks
            - It filters Versions as so all versions are returned, where Occurrence <= occurrenceValidAt < OccurrenceTo, so it returns only versions which are valid at the date of parameter passed.
            - IMPORTANT: If only "future" version is available (relatively to parameter), so only versions with Occurrence > occurrenceValidAt are available, future versions won't be returned (as they don't match the rule above). If it's needed to find closest future version on MPV, occurrenceFrom parameter should be used, and additional filtration is required on client side.
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
        - $ref: '#/components/parameters/SortByNullableForMeteringPointVersions'
        - $ref: '#/components/parameters/SortOrderNullableForMeteringPointVersions'
      responses:
        "200":
          description: Success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TechnicalMeteringPointVersionsCollectionResponseModel"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"

components:
  parameters:
    PageNumber:
      name: pageNumber
      in: query
      description: Page number for pagination - defaults to 1.
      required: true
      schema:
        description: Page number for pagination - defaults to 1.
        type: integer
        nullable: true
        format: int32
        minimum: -2147483648
        maximum: 2147483647
      example: 1
    PageSize:
      name: pageSize
      in: query
      description: Page size for pagination - limited by application if not passed.
      required: true
      schema:
        description: Page size for pagination - limited by application if not passed.
        type: integer
        nullable: true
        format: int32
        minimum: 0
        maximum: 1000
      example: 1
    SortByNullableForMeteringPointVersions:
      name: sortBy
      in: query
      description: Name of the property to sort by.
      required: false
      schema:
        pattern: '^(meteringPointVersionId|meteringPointId|occurrence|typeOfMeteringPoint|settlementMethod|supplyStart|connectionStatus|netSettlementGroup|address|streetName|houseNumber|floor|door|postalCode|cityName|municipalityCode|streetCode|subTypeOfMeteringPoint|meterNumber|locationDescription|meterReadingType|numberOfDigits|meterUnitType|conversionFactor|meteringGridAreaId|maximumCurrent|maximumPower|disconnectionType|meterReadingOccurrence|assetType|productionCapacityInKiloWatts|powerSupplierGsrn|connectionType|productId|unitType)$'
        type: string
        nullable: true
        maxLength: 21
        minLength: 2
        description: Name of the property to sort by.
      example: meteringPointVersionId
    SortOrderNullableForMeteringPointVersions:
      name: sortOrder
      in: query
      description: Direction of sorting. Can only be 'asc' for ascending or 'desc' for descending.
      required: false
      schema:
        pattern: '^(asc|desc)$'
        type: string
        nullable: true
        maxLength: 4
        minLength: 3
        description: Direction of sorting. Can only be 'asc' for ascending or 'desc' for descending.
      example: asc

  schemas:
    PagedResponseOfGetMeterFramesBasicModel:
      type: object
      additionalProperties: false
      description: Paged response of get meter frame basic model.
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
          description: Current page.
        pageSize:
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
          description: Page size.
        totalPages:
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
          description: Total pages.
        totalRows:
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
          description: Total rows.
        results:
          type: array
          description: Results array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/GetMeterFrameBasicModel'

    GetMeterFrameBasicModel:
      type: object
      additionalProperties: false
      description: Get meter frame basic model.
      required:
        - id
        - meterFrameNumber
        - supplyType
        - connectionPointId
      properties:
        id:
          $ref: '#/components/schemas/Guid'
        meterFrameNumber:
          description: |-
            DK: Målerrammenummer.
            Number on the Meter Frame.
          allOf:
            - $ref: '#/components/schemas/ShortString'
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        connectionPointId:
          type: string
          description: ConnectionPointId.
          format: uuid

    SearchByMeterFrameNumberPagedModel:
      title: SearchByMeterFrameNumberPagedModel
      type: object
      description: |-
        Meter Frame Number property.
      additionalProperties: false
      required:
        - meterFrameNumber
        - supplyType
      properties:
        meterFrameNumber:
          description: Meter Frame number.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/ShortString'
          example: 'aa123'
        supplyType:
          description: Type of supply as described by object Supply Types DTO.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
          example: 1
        pageNumber:
          description: Page number for pagination - defaults to 1.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/PositiveIntegerNullable'
          example: 1
        pageSize:
          description: Page size for pagination - limited by application if not passed.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/PositiveIntegerNullable'
          example: 1

    MeterFrameIdWithSupplyTypeInSchema:
      title: MeterFrameIdInSchema
      type: object
      description: |-
        Meter Frame Id property.
      additionalProperties: false
      required:
        - meterFrameId
        - supplyType
      properties:
        meterFrameId:
          description: Meter Frame Id.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/Guid'
        supplyType:
          description: Type of supply as described by object Supply Types DTO.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
          example: 1

    ProblemDetails:
      type: object
      additionalProperties:
        nullable: true
      required:
        - extensions
      properties:
        type:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        status:
          type: integer
          format: int32
          nullable: true
        detail:
          type: string
          nullable: true
        instance:
          type: string
          nullable: true
        extensions:
          type: object
          additionalProperties: { }
    ConfigurationModel:
      type: object
      description: Configuration model.
      additionalProperties: false
      required:
        - createMeteringPointOptionAvailable
        - maintainPriceLinksOptionAvailable
        - updateNetSettlementGroupOptionAvailable
        - isDhmTenant
      properties:
        createMeteringPointOptionAvailable:
          type: boolean
          description: Determine if create metering point option is available.
        maintainPriceLinksOptionAvailable:
          type: boolean
          description: Determine if maintain price links option is available.
        updateNetSettlementGroupOptionAvailable:
          type: boolean
          description: Determine if update update net settlement group option is available.
        isDhmTenant:
          type: boolean
          default: false
          description: Determine if service runs in DHM namespace.

    ConnectionRightModel:
      type: object
      description: Connection Right model
      additionalProperties: false
      required:
        - id
        - changeType
        - validFrom
        - validTo
        - value
        - unit
        - createdAt
        - electricityCategory
        - rightType
        - branchLineType
        - invoices
        - meterFrameId
        - rowVersion
      properties:
        id:
          type: string
          description: Connection Right Id.
          format: guid
        changeType:
          $ref: '#/components/schemas/ConnectionRightChangeType'
        validFrom:
          type: string
          format: date-time
          nullable: true
          description: Valid from date.
          example: '2023-01-01T06:50:30.870Z'
        validTo:
          type: string
          format: date-time
          nullable: true
          description: Valid until date.
          example: '2023-01-01T06:50:30.870Z'
        value:
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
          description: value of measurument of given unit.
        unit:
          $ref: '#/components/schemas/ConnectionRightElectricityUnitType'
        createdAt:
          type: string
          format: date-time
          description: When Connection Right was created.
          example: '2023-01-01T06:50:30.870Z'
        electricityCategory:
          $ref: '#/components/schemas/ConnectionRightElectricityCategory'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        rightType:
          $ref: '#/components/schemas/ConnectionRightType'
        branchLineType:
          $ref: '#/components/schemas/BranchLineType'
        comment:
          allOf:
            - $ref: '#/components/schemas/LongStringNullable'
          description: Comment.
          example: 'comment'
        invoices:
          type: array
          maxItems: 1000
          description: Connection Right Invoices
          items:
            $ref: '#/components/schemas/ConnectionRightInvoiceModel'
        connectionRightReferences:
          type: array
          maxItems: 1000
          description: List of Connection Right reference ids.
          items:
            $ref: '#/components/schemas/ConnectionRightReference'
        meterFrameId:
          $ref: '#/components/schemas/Guid'
        connectionPointId:
          $ref: '#/components/schemas/Guid'
        formReference:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/FormReference'



    ConnectionRightTransfer:
      type: object
      description: Code list model.
      additionalProperties: false
      required:
        - fromConnectionRights
        - toConnectionRights
      properties:
        fromConnectionRights:
          type: array
          maxItems: 1000
          description: List of Connection Right Ids. Those will be transfered to connection rights in "to" collection.
          items:
            $ref: '#/components/schemas/ConnectionRightTransferItem'
        toConnectionRights:
          type: array
          maxItems: 1000
          description: List of Connection Right Ids. The ones from 'from' Collection will be trasfered into cthese connection rights.
          items:
            $ref: '#/components/schemas/ConnectionRightTransferItem'

    ConnectionRightTransferItem:
      type: object
      description: Code list header model.
      additionalProperties: false
      required:
        - id
      properties:
        id:
          $ref: '#/components/schemas/Guid'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'

    ConnectionRightReference:
      type: object
      description: Code list header model.
      additionalProperties: false
      required:
        - id
        - meterFrameId
        - connectionPointId
        - category
        - unit
        - value
      properties:
        id:
          $ref: '#/components/schemas/Guid'
        meterFrameId:
          $ref: '#/components/schemas/Guid'
        connectionPointId:
          $ref: '#/components/schemas/Guid'
        category:
          $ref: '#/components/schemas/ConnectionRightElectricityCategory'
        unit:
          $ref: '#/components/schemas/ConnectionRightElectricityUnitType'
        value:
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
          description: Value of measurement of a given unit.
          example: 1

    CreateConnectionRightModel:
      type: object
      additionalProperties: false
      description: Add or edit connection right.
      required:
        - meterFrameId
        - changeType
        - rightType
        - branchLineType
        - electricityCategory
        - value
      properties:
        meterFrameId:
          $ref: '#/components/schemas/Guid'
        changeType:
          $ref: '#/components/schemas/ConnectionRightChangeType'
        rightType:
          $ref: '#/components/schemas/ConnectionRightType'
        branchLineType:
          $ref: '#/components/schemas/BranchLineType'
        electricityCategory:
          $ref: '#/components/schemas/ConnectionRightElectricityCategory'
        value:
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
          description: value of measurument of given unit.
        comment:
          allOf:
            - $ref: '#/components/schemas/LongStringNullable'
          description: Comment.
          example: 'comment'
        connectionRightReferences:
          type: array
          maxItems: 1000
          description: List of Connection Right reference ids.
          items:
            $ref: '#/components/schemas/Guid'
        formReference:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/FormReference'


    UpdateConnectionRightModel:
      type: object
      additionalProperties: false
      description: Add or edit connection right.
      required:
        - changeType
        - rightType
        - electricityCategory
        - value
        - rowVersion
      properties:
        changeType:
          $ref: '#/components/schemas/ConnectionRightChangeType'
        rightType:
          $ref: '#/components/schemas/ConnectionRightType'
        electricityCategory:
          $ref: '#/components/schemas/ConnectionRightElectricityCategory'
        value:
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
          description: value of measurument of given unit.
        comment:
          allOf:
            - $ref: '#/components/schemas/LongStringNullable'
          description: Comment.
          example: 'comment'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        connectionRightReferences:
          type: array
          maxItems: 1000
          description: List of Connection Right reference ids.
          items:
            $ref: '#/components/schemas/Guid'
        formReference:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/FormReference'

    StopConnectionRightModel:
      type: object
      additionalProperties: false
      description: Add or edit connection right.
      required:
        - connectionRightId
        - validTo
        - rowVersion
      properties:
        connectionRightId:
          $ref: '#/components/schemas/Guid'
        validTo:
          type: string
          format: date-time
          nullable: false
          description: Valid until date.
          example: '2023-01-01T06:50:30.870Z'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'

    FormReference:
      type: object
      additionalProperties: false
      description: Form reference.
      required:
        - formId
        - formNumber
        - formType
      properties:
        formId:
          $ref: '#/components/schemas/Guid'
        formNumber:
          $ref: '#/components/schemas/FormNumber'
        formType:
          $ref: '#/components/schemas/FormType'

    SearchInstallationFormCommand:
      type: object
      additionalProperties: false
      description: Search for installation form.
      required:
        - formNumber
      properties:
        formNumber:
          $ref: '#/components/schemas/FormNumber'

    ConnectionRightElectricityCategory:
      type: string
      description: 'Connection Right Electricity category'
      x-enumNames:
        - Parcel
        - TerracedHouse
        - SmallApartment
        - LargeApartment
        - Allotment
        - YoungOldAndNursery
        - SmallInstallation
        - Other
        - ExtensionAHigh
        - ExtensionALow
        - ExtensionBHigh
        - ExtensionBLow
        - ExtensionCLevel
        - kWMaxBHigh
        - kWMaxBLow
        - kWMaxCLevel
        - None
        - LimitedNetAccess

      enum:
        - Parcel
        - TerracedHouse
        - SmallApartment
        - LargeApartment
        - Allotment
        - YoungOldAndNursery
        - SmallInstallation
        - Other
        - ExtensionAHigh
        - ExtensionALow
        - ExtensionBHigh
        - ExtensionBLow
        - ExtensionCLevel
        - kWMaxBHigh
        - kWMaxBLow
        - kWMaxCLevel
        - None
        - LimitedNetAccess

    BranchLineType:
      type: string
      description: 'Branch Line Type'
      enum:
        - Main
        - Reserve

    FormType:
      description: Form type enum
      type: string
      example: "NewInstallation"
      x-enumNames:
        - NewInstallation
        - Extension
        - SealBreach
        - ChangeMeter
        - MoveMeter
        - Termination
        - ChangeBranchLine
        - EnergyProduction
      enum:
        - NewInstallation
        - Extension
        - SealBreach
        - ChangeMeter
        - MoveMeter
        - Termination
        - ChangeBranchLine
        - EnergyProduction

    FormNumber:
      type: string
      description: Form number
      minLength: 1
      maxLength: 10
      pattern: "^.*$"
      example: "S24007058"

    ConnectionRightChangeType:
      type: string
      description: 'Connection Right Change Type'
      x-enumNames:
        - Creation
        - Extension
      enum:
        - Creation
        - Extension

    ConnectionRightType:
      type: string
      description: 'Connection Right Type'
      x-enumNames:
        - Consumption
        - Production
      enum:
        - Consumption
        - Production

    ConnectionRightElectricityUnitType:
      type: string
      description: 'Connection Right Electricity Unit Type'
      x-enumNames:
        - A
        - kVA
        - kW
        - SharedBranchLineConnectionRight
        - None
      enum:
        - A
        - kVA
        - kW
        - SharedBranchLineConnectionRight
        - None

    CodeListModel:
      type: object
      description: Code list model.
      additionalProperties: false
      required:
        - id
        - name
        - from
        - to
        - codeListTypeKind
        - codeListValueModels
        - headers
      properties:
        id:
          type: string
          description: Code List Id.
          format: guid
        name:
          type: string
          description: Name of the Code List.
        systemIdentifier:
          type: string
          description: System identifier of the Code List
          nullable: true
          maxLength: 100
          pattern: '^.*$'
        parentId:
          type: string
          description: Value list parent ID (GUID).
          format: uuid
          nullable: true
        from:
          type: string
          description: Value list form.
          format: date-time
        to:
          type: string
          description: Value list to.
          format: date-time
        codeListTypeKind:
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/CodeListTypeKindModel'
        codeListValueModels:
          type: array
          description: Values - elements of a single Code List.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/CodeListValueModel'
        headers:
          type: array
          description: Headers of a Code List - available only if Code List contains any Attributes.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/CodeListHeaderModel'
    CodeListTypeKindModel:
      type: integer
      description: Enum identifying Code List usage purpose.
      x-enumNames:
        - ConnectionPointElectricityConnectionPointCategoryValue
        - ConnectionPointElectricityInstallationType
        - ConnectionPointElectricityConnectionStatus
        - ConnectionPointElectricityNetSettlementGroup
        - ConnectionPointElectricityConsumerCategory
        - ConnectionPointHeatingConnectionPointCategoryValue
        - ConnectionPointHeatingInstallationType
        - ConnectionPointHeatingConnectionStatus
        - ConnectionPointHeatingHeatWaterHeater
        - ConnectionPointHeatingWaterHeaterType
        - ConnectionPointHeatingHeatPlantType
        - ConnectionPointHeatingHeatExchange
        - ConnectionPointWaterConnectionPointCategoryValue
        - ConnectionPointWaterInstallationType
        - ConnectionPointWaterConnectionStatus
        - ConnectionPointTag
        - SpecialAgreementZone
        - MeterFrameElectricityConnectionType
        - MeterFrameElectricityMeterDisplaySetting
        - MeterFrameElectricityConnectionRemark
        - MeterFrameElectricityTarifConnectionPoint
        - MeterFrameElectricityPurpose
        - MeterFrameElectricityRatioCT
        - MeterFrameElectricityRatioVT
        - MeterFrameElectricityConnectionStatus
        - MeterFrameHeatPlantModelType
        - MeterFrameHeatMeterFrameConnectionStatus
        - MeterFrameHeatCustomerCriticalityCategory
        - MeterFrameHeatHeatMeterConnectionType
        - MeterFrameHeatingMeterDisplaySetting
        - MeterFrameWaterCustomerCriticalityCategory
        - MeterFrameWaterMeterFrameConnectionStatus
        - MeterFrameWaterMediumCategory
        - MeterFrameWaterDriveByType
        - MeterFrameWaterMeterDisplaySetting
        - MeterFrameTag
        - MeterFramePlacementCode
        - MeterFrameRegisterRequirementMeteringComponent
        - MeterFrameCommonReading
        - MeterFrameMeterReadingType
        - MeterManagementGeneric
        - ElectricityMeteringPointType
        - HeatingMeteringPointType
        - WaterMeteringPointType
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 30
        - 31
        - 32
        - 33
        - 34
        - 35
        - 36
        - 60
        - 61
        - 62
        - 90
        - 120
        - 231
        - 232
        - 233
        - 234
        - 235
        - 236
        - 237
        - 238
        - 252
        - 253
        - 254
        - 255
        - 256
        - 281
        - 282
        - 283
        - 284
        - 285
        - 290
        - 291
        - 292
        - 293
        - 294
        - 300
        - 330
        - 331
        - 332
    CodeListValueModel:
      type: object
      description: Code list value model.
      additionalProperties: false
      required:
        - id
        - displayValue
        - attributes
        - from
        - to
      properties:
        id:
          type: string
          description: Code List Value Id.
          format: guid
        displayValue:
          type: string
          description: Code List Value Display Value.
        attributes:
          type: array
          description: Attributes collection.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/CodeListAttributeModel'
        parentId:
          type: string
          description: Value list parent ID (GUID).
          format: uuid
          nullable: true
        from:
          type: string
          description: Value list form.
          format: date-time
        to:
          type: string
          description: Value list to.
          format: date-time
    CodeListAttributeModel:
      type: object
      description: Code list attribute model.
      additionalProperties: false
      required:
        - id
        - codeListHeaderId
        - codeListValueId
        - value
      properties:
        id:
          type: string
          description: Code List Attribute id.
          format: guid
        codeListHeaderId:
          type: string
          description: Code List Header id.
          format: guid
        codeListValueId:
          type: string
          description: Code List Value id.
          format: guid
        value:
          type: string
          description: Code List Attribute value.
    CodeListHeaderModel:
      type: object
      description: Code list header model.
      additionalProperties: false
      required:
        - id
        - headerText
        - headerOrder
      properties:
        id:
          type: string
          description: Code List header identifier.
          format: guid
        headerText:
          type: string
          description: Header display text.
        headerOrder:
          type: integer
          description: Column order - lowest should come first.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
    CodeListUsageAreasModel:
      type: integer
      description: Filter for contextual usage area - ex. ConnectionPoints.
      x-enumNames:
        - ConnectionPoints
        - SpecialAgreements
        - MeterFrames
        - MeterAttributeRequirements
      enum:
        - 1
        - 2
        - 3
        - 8
    PagedResultOfGetInternalEquipmentSearchModel:
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          description: Paged results of get internal equipment search model.
          additionalProperties: false
          properties:
            results:
              type: array
              description: Results.
              maxItems: 1000000
              nullable: true
              items:
                $ref: '#/components/schemas/GetInternalEquipmentSearchModel'
    GetInternalEquipmentSearchModel:
      allOf:
        - $ref: '#/components/schemas/GetInternalEquipmentByIdModel'
        - type: object
          description: Get internal equipment search model.
          additionalProperties: false
    GetInternalEquipmentByIdModel:
      type: object
      description: Get Internal Equipment by id result model.
      additionalProperties: false
      required:
        - id
        - installationPossibility
        - equipmentNumber
        - equipmentType
        - vendor
        - rowVersion
      properties:
        id:
          type: string
          description: Entity identifier.
          format: guid
        installationPossibility:
          description: “All”, “ConnectionPoint”, “MeterFrame”.
          oneOf:
            - $ref: '#/components/schemas/InstallationPossibilityModel'
        equipmentNumber:
          type: string
          description: Equipment serial number from the manufacture.
        equipmentType:
          type: string
          description: The type of equipment such as repeater or concentrator.
        vendor:
          type: string
          description: Vendor.
        description:
          type: string
          description: Description.
          nullable: true
        flexAttributeObject:
          type: string
          description: >-
            An UUID reference to a flexattributeobject that is used to register flexible attributes about the Equipment.

            In order to register these attributes a tenant specific flexattribute object type will be defined to register specific flexible attributes about an Equipment.
          format: guid
          nullable: true
        rowVersion:
          type: string
          description: Row version.
          format: byte
    InstallationPossibilityModel:
      type: integer
      description: Installation possibility that equipment can have "All", "ConnectionPoint", "MeterFrame".
      x-enumNames:
        - All
        - ConnectionPoint
        - MeterFrame
      enum:
        - 0
        - 1
        - 2
    PagedResultBase:
      type: object
      description: Paged result.
      additionalProperties: false
      required:
        - currentPage
        - pageCount
        - pageSize
        - rowCount
      properties:
        currentPage:
          type: integer
          description: Current page.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
        pageCount:
          type: integer
          description: Page count.
          format: int32
          maximum: 2147483647
          minimum: 0
        pageSize:
          type: integer
          description: Page size.
          format: int32
          maximum: 2147483647
          minimum: 0
        rowCount:
          type: integer
          description: Row count.
          format: int32
          maximum: 2147483647
          minimum: 0
        maxCount:
          type: integer
          description: Max count.
          format: int32
          maximum: 2147483647
          minimum: 0
          nullable: true

    ConnectionPointsListSearchFilterModel:
      description: Properties for filtration of Connection Points.
      type: object
      additionalProperties: false
      properties:
        supplyType:
          description: Filter by Supply Type Enum Flag - integer value.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
        connectionPointId:
          description: Filter by Connection Point Identifier - must be valid GUID.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        connectionPointNumber:
          description: Filter by Connection Point Number (business identifier) - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        installationNumber:
          description: Filter by Installation Number - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        alternativeInstallationNumber:
          description: Filter by Alternative Installation Number - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        addressLine1:
          description: Filter by Address Line 1 - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        addressLine2:
          description: Filter by Address Line 2 - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        addressStatus:
          description: Filter by Address Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          description: Filter by Address Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressType'
        darStatus:
          description: Filter by Address DAR Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          description: Filter by Address Life Cycle Status (MDR) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        electricityAttributesInstallationTypeValue:
          description: Filter by Electricity Attributes Installation Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        electricityAttributesConnectionPointCategoryValue:
          description: Filter by Electricity Attributes Connection Point Category - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        electricityAttributesConnectionStatus:
          description: Filter by Electricity Attributes Connection Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        electricityAttributesConsumerCategory:
          description: Filter by Electricity Attributes Consumer Category - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        electricityAttributesNetSettlementGroup:
          description: Filter by Electricity Attributes Net Settlement Group - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        electricityAttributesGridAreaId:
          description: Grid area id.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        electricityAttributesTemporary:
          description: Filter by Electricity Attributes Temporary Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributesStartPeriod:
          description: Filter by Electricity Attributes Temporary Until Date (TemporaryUntil <= StartPeriod) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        electricityAttributesEndPeriod:
          description: Filter by Electricity Attributes Temporary Until Date (TemporaryUntil < EndPeriod) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        heatingAttributesConnectionPointCategoryValue:
          description: Filter by Heating Attributes Connection Point Category - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        heatingAttributesInstallationTypeValue:
          description: Filter by Heating Attributes Installation Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        heatingAttributesConnectionStatus:
          description: Filter by Heating Attributes Connection Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        heatingAttributesHeatExchange:
          description: Filter by Heating Attributes Heat Exchange Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        heatingAttributesHeatPlantType:
          description: Filter by Heating Attributes Heat Plant Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        waterAttributesConnectionPointCategoryValue:
          description: Filter by Water Attributes Connection Point Category - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        waterAttributesInstallationTypeValue:
          description: Filter by Water Attributes Installation Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        waterAttributesConnectionStatus:
          description: Filter by Water Attributes Connection Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        waterAttributesTemporary:
          description: Filter by Water Attributes Temporary Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        waterAttributesStartPeriod:
          description: Filter by Water Attributes Temporary Until Date (TemporaryUntil <= StartPeriod) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        waterAttributesEndPeriod:
          description: Filter by Water Attributes Temporary Until Date (TemporaryUntil < EndPeriod) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'

    MeterIdNullable:
      allOf:
        - $ref: '#/components/schemas/PositiveIntegerNullable'
      example: 360
      description: The technical key that uniquely identifies the Meter

    StartsWithStringFilterParameter:
      type: string
      description: StartsWith (like) search query parameter.
      nullable: true
      minLength: 1
      maxLength: 1000
      example: AnyStringWhichWillBeUsedForLikeConditionInQuery

    GuidField:
      type: string
      description: GUID field.
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8

    GuidFieldNullable:
      nullable: true
      type: string
      description: GUID field.
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8

    ShortStringNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 0
      maxLength: 50
      description: 'Max. 50 characters long string with spaces.'
      example: 'Max. 50 characters long string with spaces.'

    SortByNullable:
      pattern: '^(id|connectionPointId|connectionPointNumber|meterframeNumber|supplyType|supplyStatus|supplyDisconnectType|meterMissing|addressLine1|addressLine2|addressStatus|addressType|darStatus|lifeCycleStatus)$'
      type: string
      nullable: true
      maxLength: 21
      minLength: 2
      description: Name of the property to sort by.
      example: connectionPointNumber

    SortOrderNullable:
      pattern: '^(asc|desc)$'
      type: string
      nullable: true
      maxLength: 4
      minLength: 3
      description: Direction of sorting. Can only be 'asc' for ascending or 'desc' for descending.
      example: asc

    OneWordString:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 25
      description: 'Max. 25 characters long string with spaces.'
      example: 'Max. 25 chars. w. spaces.'

    BooleanField:
      type: boolean
      description: Boolean field.
      example: true

    BooleanFieldNullable:
      nullable: true
      type: boolean
      description: Boolean field nullable.
      example: true

    MeterFramesListSearchQueryModel:
      type: object
      additionalProperties: false
      description: Meter Frames list search query model.
      required:
        - filter
      properties:
        page:
          nullable: true
          type: object
          description: Page object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Page'
        sort:
          nullable: true
          type: object
          description: Sort object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Sort'
        filter:
          description: Filter object.
          type: object
          nullable: false
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/MeterFramesListSearchFilterModel'

    MeterFramesListSearchFilterModel:
      description: Properties for filtration of Connection Points.
      type: object
      additionalProperties: false
      properties:
        supplyType:
          description: Filter by Type of supply as described by object Supply Types DTO.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
          example: 1
        connectionPointId:
          description: Filter by Connection Point Identifier - must be valid GUID.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        meterFrameId:
          description: Filter by Id of the Meter Frame.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        connectionPointBusinessId:
          description: Filter by Connection point business identifier.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
          example: 1100000016
        meterFrameNumber:
          description: Filter by Meter frame number.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 1100000016-1
        supplyStatus:
          description: Filter by List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
          example: 1
        supplyDisconnectType:
          description: Filter by List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation", "Connected", "Unknown".
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
          example: 1
        commonReading:
          description: Filter by Common Reading
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        noMeter:
          description: Filter by No meter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: true
        meterMissing:
          description: Filter by meter is missing.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: false
        meterReadingType:
          description: Filter by a list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        addressLine1:
          description: Filter by Address Line 1 - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
          example: 'Trælastgade 21'
        addressLine2:
          description: Filter by Address Line 2 - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
          example: '8000 Aarhus C'
        addressStatus:
          description: Filter by Address Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressStatus'
          example: 1
        addressType:
          description: Filter by Address Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressType'
          example: 1
        lifeCycleStatus:
          description: Filter by LifeCycleStatus.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
          example: 1
        darStatus:
          description: Filter by Address DAR Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DarStatus'
          example: 1
        cabinetNumber:
          description: Filter by Number of the cable box where the plug is connected. Ex. 123658.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        cableNumber:
          description: Filter by Number on execution in station. Ex. 1, 2, 3 etc.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        stationNumber:
          description: Filter by The station from which the branch line is supplied. Ex. 98756.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        transformerNumber:
          description: Filter by Transformer number, for several transformers in a station. Ex. 1, 2, 3 etc.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        shinNumber:
          description: Number on the rail in the locker.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        heatingBranchLineNumber:
          description: Filter by Number on branch line. Ex. 246810.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        heatingPlantId:
          description: Filter by Heating plant Id - Heater "kID".
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        heatingPlantName:
          description: Filter by Heating plant name.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'plant'
        heatingPlantPipeName:
          description: Filter by Pipe name / outlet marking.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'heating plan name'
        heatingPlantPipeNumber:
          description: Filter by Outlet marking number.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        waterBranchLineNumber:
          description: Filter by Number on branch line. Ex. XF2500.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 111
        sectionId:
          description: Filter by an area (polygon) in which the water meter frame is located (for checking water balance).
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        sectionName:
          description: Filter by Section name.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'section name'
        waterPlantId:
          description: Filter by Water Plant Id.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        waterPlantName:
          description: Filter by Water Plant Name.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'water plant name'
        waterPlantPipeId:
          description: Filter by Water Plant Pipe Id.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        waterPlantPipeName:
          description: Filter by Water Plant Pipe name.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'water plant pipe name'
        mainZone:
          description: Filter by an area (higher level than section) = an operating area (polygon).
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'main zone'
        superZone:
          description: Filter by an area <Section> (higher level than MainZone) = e.g. a city (polygon).
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'super zone'
        hasMainBranchLine:
          description: Filter by Main Branch Line.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: true
        hasReserveBranchLine:
          description: Filter by Reserve Branch Line.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: true
        connectionStatus:
          description: Filter by Connection Status.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        branchLineNumber:
          description: Filter by Branch Line Number.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: '1234a'
        branchLineType:
          description: Filter by Branch Line Type.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'type c'
        branchLineSize:
          description: Filter by Branch Line Size.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'branch line size'
        reserveBranchLineNumber:
          description: Filter by Reserve Branch Line Number.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: '1234a'
        reserveBranchLineType:
          description: Filter by Reserve Branch Line Type.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'branch line type'
        reserveBranchLineSize:
          description: Filter by Reserve Branch Line Size.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'branch line size'
        connectedThroughOtherMeterFrame:
          description: Filter by Connected Through Other Meter Frame.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: true
        connectedThroughOtherMeterFrameNumber:
          description: Filter by Connected Through Other Meter Frame NUmber.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 1100000016-1
        meterNumber:
          description: 'Filter by Meter Number.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        registerRequirementValidation:
          description: 'Filter by Register Requirement Validation.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
        attributeRequirementValidation:
          description: 'Filter by Attribute Requirement Validation.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'

    ConnectionPointsListSearchQueryModel:
      type: object
      additionalProperties: false
      description: Connection Points list search query model.
      required:
        - filter
      properties:
        page:
          description: Page object.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/Page'
        sort:
          nullable: true
          type: object
          description: Sort object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Sort'
        filter:
          description: Filter object.
          type: object
          nullable: false
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/ConnectionPointsListSearchFilterModel'

    ConnectionPointsListSearchQueryModelWithCsvConfiguration:
      allOf:
        - $ref: '#/components/schemas/ConnectionPointsListSearchQueryModel'
        - type: object
          additionalProperties: false
          properties:
            csvConfiguration:
              nullable: true
              oneOf:
                - $ref: '#/components/schemas/CsvConfigurationQueryModel'

    CsvConfigurationQueryModel:
      type: object
      additionalProperties: false
      required:
        - delimiter
        - decimalSeparator
        - textStart
        - textEnd
      properties:
        delimiter:
          $ref: '#/components/schemas/CsvDelimiter'
        decimalSeparator:
          $ref: '#/components/schemas/CsvDecimalSeparator'
        textStart:
          $ref: '#/components/schemas/CsvTextStart'
        textEnd:
          $ref: '#/components/schemas/CsvTextEnd'

    CsvDelimiter:
      type: string
      description: ''
      x-enumNames:
        - Semicolon
        - Tab
        - Space
        - Comma
      enum:
        - Semicolon
        - Tab
        - Space
        - Comma

    CsvDecimalSeparator:
      type: string
      description: ''
      x-enumNames:
        - Comma
        - Dot
      enum:
        - Comma
        - Dot

    CsvTextStart:
      type: string
      description: ''
      x-enumNames:
        - Quote
        - EqualQuote
      enum:
        - Quote
        - EqualQuote

    CsvTextEnd:
      type: string
      description: ''
      x-enumNames:
        - Quote
      enum:
        - Quote

    SearchListQueryModel:
      type: object
      description: Search list query model.
      additionalProperties: false
      required:
        - filter
      properties:
        page:
          description: Page object.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/Page'
        sort:
          description: Sort object.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/Sort'
        filter:
          description: Filter object.
          oneOf:
            - $ref: '#/components/schemas/Filter'

    SearchListQueryModelWithCsvConfiguration:
      allOf:
        - $ref: '#/components/schemas/SearchListQueryModel'
        - type: object
          additionalProperties: false
          properties:
            csvConfiguration:
              nullable: true
              oneOf:
                - $ref: '#/components/schemas/CsvConfigurationQueryModel'

    Page:
      type: object
      description: Page object.
      additionalProperties: false
      required:
        - number
        - size
      properties:
        number:
          type: integer
          description: Page number.
          format: int32
          maximum: 2147483647
          minimum: 0
        size:
          type: integer
          description: Page size.
          format: int32
          maximum: 10000
          minimum: 0
    Sort:
      description: Sort object.
      additionalProperties: false
      properties:
        direction:
          type: string
          nullable: true
          description: Direction of sorting. Can only be 'asc' for ascending or 'desc' for descending.
          example: asc
          pattern: '^(asc|desc)$'
        propertyName:
          type: string
          nullable: true
          description: Name of the property to sort by.
          minLength: 1
          maxLength: 200
    Filter:
      allOf:
        - $ref: '#/components/schemas/FilterAbstraction'
        - type: object
          description: Filter.
          additionalProperties: false

    FilterAbstraction:
      type: object
      description: Filter abstraction.
      additionalProperties: false
      properties:
        properties:
          type: array
          description: Array.
          maxItems: 1000000
          nullable: true
          items:
            $ref: '#/components/schemas/PropertyFilter'
        propertiesForNestedCollections:
          type: array
          description: Properties for nested collections.
          maxItems: 1000000
          nullable: true
          items:
            $ref: '#/components/schemas/NestedCollectionItemFilter'
    PropertyFilter:
      type: object
      description: Property filter.
      additionalProperties: false
      required:
        - condition
      properties:
        name:
          type: string
          description: Property name.
          nullable: true
        condition:
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/FilterCondition'
        value:
          description: Property value.
          nullable: true
    FilterCondition:
      type: integer
      description: Filter condition.
      x-enumNames:
        - Like
        - Equal
        - In
        - LessThan
        - LessThanOrEqual
        - GreaterThan
        - GreaterThanOrEqual
        - StartsWith
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
    NestedCollectionItemFilter:
      allOf:
        - $ref: '#/components/schemas/FilterAbstraction'
        - type: object
          description: Nested collection item filter.
          additionalProperties: false
          properties:
            name:
              type: string
              description: Name.
              nullable: true
    IdResult:
      type: object
      description: Id result.
      additionalProperties: false
      required:
        - id
      properties:
        id:
          type: string
          description: Id.
          format: guid
    ValidationProblemDetails:
      allOf:
        - $ref: '#/components/schemas/HttpValidationProblemDetails'
        - type: object
          additionalProperties:
            nullable: true
          properties:
            errors:
              type: object
              additionalProperties:
                type: array
                items:
                  type: string
    HttpValidationProblemDetails:
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
        - type: object
          additionalProperties:
            nullable: true
          properties:
            errors:
              type: object
              additionalProperties:
                type: array
                items:
                  type: string
    ErrorDescription:
      type: object
      description: Error description.
      additionalProperties: false
      required:
        - errorCode
        - defaultMessage
      properties:
        errorCode:
          type: string
          description: Error code.
        defaultMessage:
          type: string
          description: Default message.
    AddInternalEquipmentModel:
      allOf:
        - $ref: '#/components/schemas/InternalEquipmentCommonModel'
        - type: object
          description: Add internal equipment model.
          additionalProperties: false
          properties:
            installationPossibility:
              maximum: 2147483647
              minimum: -2147483648
              oneOf:
                - $ref: '#/components/schemas/InstallationPossibilityModel'
            equipmentNumber:
              type: string
              description: Equipment serial number from the manufacture.
    InternalEquipmentCommonModel:
      type: object
      description: Internal equipment common model.
      additionalProperties: false
      required:
        - equipmentType
        - vendor
      properties:
        equipmentType:
          type: string
          description: The type of equipment such as repeater or concentrator.
        vendor:
          type: string
          description: Vendor
        description:
          type: string
          description: Description.
          nullable: true
        flexAttributeObject:
          type: string
          description: >-
            An UUID reference to a flexattributeobject that is used to register flexible attributes about the Equipment.

            In order to register these attributes a tenant specific flexattribute object type will be defined to register specific flexible attributes about an Equipment.
          format: guid
          nullable: true
    UpdateInternalEquipmentModel:
      allOf:
        - $ref: '#/components/schemas/InternalEquipmentCommonModel'
        - type: object
          description: Update internal equipment model.
          additionalProperties: false
          properties:
            rowVersion:
              type: string
              description: Row version.
              format: byte
    ValidateActiveMeterOnMeterFrameRegisterRequirements:
      type: object
      description: Model for validation currently assigned active Meter on Meter Frame Register Requirement.
      additionalProperties: false
      required:
        - meterFrameId
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
    ValidateActiveMeterOnMeterFrameAttributesRequirements:
      type: object
      description: Model for validation currently assigned active Meter on Meter Frame Attributes Requirement.
      additionalProperties: false
      required:
        - meterFrameId
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          allOf:
            - $ref: '#/components/schemas/GuidField'

    MeterInputConnectionModel:
      type: object
      description: Model of Meter Input Connection.
      additionalProperties: false
      required:
        - meterFrameId
        - meterId
        - meterInputNumber
        - meterInputType
        - meterNumber
        - meterType
        - fulfillsRequirements
        - validFrom
        - id
      properties:
        id:
          type: string
          description: Meter Input Connection id.
          format: guid
        meterFrameId:
          type: string
          description: Id.
          format: guid
        meterId:
          type: integer
          description: The technical key that uniquely identifies the Meter
          format: int32
          maximum: 2147483647
          minimum: 0
        meterInputNumber:
          type: integer
          description: This field is part of the MeterConfiguration in the Meter domain.
          format: int32
          maximum: 2147483647
          minimum: 0
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          oneOf:
            - $ref: '#/components/schemas/MeterInputType'
        meterNumber:
          type: string
          description: >-
            This is the meter number that is communicated to the DataHub.

            The meter number can deviate from the meter number that can be seen on the physical meter,

            though according to guidelines they should match.
          maxLength: 25
          minLength: 0
        meterType:
          type: string
          description: Values comes from the Meter entity in the Meter domain. The field defines the type of Meter assigned.
          maxLength: 50
          minLength: 0
        fulfillsRequirements:
          type: boolean
          description: Boolean updated by validation method that indicates whether Meter fulfils the register requirements.
        lastChecked:
          type: string
          description: Indicates time of the last validation check.
          format: date-time
          nullable: true
        validationStatus:
          type: string
          description: Status text on the latest validation check.
          maxLength: 50
          minLength: 0
          nullable: true
        validFrom:
          type: string
          description: Defines the validity period, when the meter is assigned to the meter frame.
          format: date-time
        validUntil:
          type: string
          description: Defines the validity period, when the meter is assigned to the meter frame.
          format: date-time
          nullable: true
        meterAttributeRequirementsValidationPassed:
          description: Validation result for Meter on Meter Attributes Requirements.
          type: boolean
          example: true
          nullable: true
        meterAttributeRequirementsInvalidAttributes:
          type: array
          description: Collection of properties failing to fulfill meter attribute requirements.
          nullable: true
          maxItems: 1000
          items:
            type: string
            pattern: "^.*$"
            maxLength: 100
            description: Name of Meter property not meeting meter attribute requirements.
            nullable: false
            example: CableLength
        meterAttributeRequirementsLastValidationDate:
          type: string
          format: date-time
          nullable: true
          example: "2022-09-07T09:50:30.870Z"
          description: Last performed meter attribute requirements validation date.
        displayConfiguration:
          description: |-
            Display configuration.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        registerConfiguration:
          description: |-
            Display configuration.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        meterComponentRequirementsValidationPassed:
          description: Boolean updated by validation method that indicates whether Meter fulfils the component requirements.
          example: true
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        meterComponentRequirementsLastValidationDate:
          description: Indicates time of the last meter component validation check.
          example: "2024-06-03T22:00:00.000Z"
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        meterComponentInvalidRequirements:
          description: Collection of meter components which do not fulfill meter component requirements.
          type: array
          minItems: 0
          maxItems: 100
          items:
            $ref: "#/components/schemas/MeterComponentInvalidRequirement"

    MeterComponentInvalidRequirement:
      type: object
      additionalProperties: false
      description: Meter component which do not fulfill meter component requirements model.
      required:
        - componentId
        - invalidRequirements
      properties:
        componentId:
          description: Component identifier.
          example: 1234
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
        invalidRequirements:
          description: Collection of requirements which do not fulfill meter components requirements.
          type: array
          minItems: 0
          maxItems: 10
          items:
            description: Requirements which do not fulfill meter components requirements.
            example: 'Manufacturer'
            allOf:
              - $ref: '#/components/schemas/MediumString'

    MeterInputType:
      type: string
      description: Shared list of enums between Meter Frame and Meter domain
      x-enumNames:
        - Internal
        - External
        - WiredBus
        - MBus
        - Pulse
      enum:
        - Internal
        - External
        - WiredBus
        - MBus
        - Pulse
    GetRegisterRequirementResponseBody:
      type: object
      description: Paged result of register requirement collection.
      additionalProperties: false
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          type: integer
          description: Current page number.
          format: int32
          maximum: 2147483647
          minimum: 0
        pageSize:
          type: integer
          description: Page size.
          format: int32
          maximum: 2147483647
          minimum: 0
        totalPages:
          type: integer
          description: Number of pages.
          format: int32
          maximum: 2147483647
          minimum: 0
        totalRows:
          type: integer
          description: Number of total rows.
          format: int32
          maximum: 2147483647
          minimum: 0
        results:
          type: array
          description: Array with result models.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/RegisterRequirementModel'
    RegisterRequirementModel:
      type: object
      description: Register Requirement model.
      additionalProperties: false
      required:
        - id
        - meterFrameId
        - registerRequirementType
        - name
        - meteringComponentId
        - futureRequirement
        - meterReadingRequired
        - rowVersion
      properties:
        id:
          type: integer
          description: Register Requirement internal identifier.
          format: int32
          maximum: 2147483647
          minimum: 0
        meterFrameId:
          type: string
          description: Meter Frame id.
          format: guid
        registerRequirementType:
          $ref: '#/components/schemas/RegisterRequirementTypeModel'
        name:
          type: string
          description: >-
            DK: Navn.

            Name of requirement (typically from MeteringComponent value list).
        meteringComponentId:
          type: string
          description: >-
            DK: MålingsKomponentId.

            Shared value list with Meter domain.

            The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.

            It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
          format: guid
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          nullable: true
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        mainMeterFrameId:
          description: Reference to the main Meter Frame
          allOf:
            - $ref: "#/components/schemas/GuidFieldNullable"
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        mainMeterFrameNumber:
          description: Extended reference to the main Meter Frame - Meter Frame Number
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: MF-001122
        inputNumber:
          description: Input number
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 1
        mainMeterFrameRegisterRequirementId:
          type: integer
          description: >-
            DK: HovedMalerrammeRegisterKravId.

            Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        mainMeterFrameRegisterRequirementName:
          type: string
          description: Used to refer to the main meter register requirement.
          nullable: true
        outsourcedToMeterFrameRegisterRequirementId:
          type: integer
          description: >-
            DK: HjemtagesAfMalerrammeRegisterKravId.

            Can refer to another meter frame which is actually the one that receives data through an external input.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        outsourcedToMeterFrameRegisterRequirementName:
          type: string
          description: Used to refer to another meter frame which is actually the one that receives data through an external input.
          nullable: true
        futureRequirement:
          type: boolean
          description: >-
            DK: FremtidigtRegisterKra

            Indicates whether the register requirement should only be a future requirement. Default false.

            If set to true Validity period will be ignored.
        meterReadingRequired:
          type: boolean
          description: >-
            DK: AflæsningPåkrævet

            Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
        requiredFrom:
          type: string
          description: >-
            DK: GyldigFra.

            Indicates when the meter must meet the requirement.

            format: date-time
          format: date-time
          nullable: true
        requiredUntil:
          type: string
          description: >-
            DK: GyldigTil.

            Indicates from when the meter does not have to meet the requirement.
          format: date-time
          nullable: true
        rowVersion:
          type: string
          description: Row version.
          format: byte
    RegisterRequirementTypeModel:
      type: integer
      description: List of possible register requirement "ControlRequirement", "MarketRequirement".
      x-enumNames:
        - ControlRequirement
        - MarketRequirement
      enum:
        - 1
        - 2
    IntIdResult:
      type: object
      description: Integer id result.
      additionalProperties: false
      required:
        - id
      properties:
        id:
          type: integer
          description: Id.
          format: int32
          maximum: 2147483647
          minimum: 0
    AddRegisterRequirementModel:
      type: object
      description: Model for adding Register Requirement.
      additionalProperties: false
      required:
        - registerRequirementType
        - name
        - meteringComponentId
        - futureRequirement
        - meterReadingRequired
        - meterFrameId
      properties:
        registerRequirementType:
          description: >-
            DK: MalerrammeRegisterKravType.

            Indicates various reasons why the claim has been added to the meter frame.

            Ex. To be used for settlement, To be used for technical measurements,

            To be used for main meter/control meter setup.
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/RegisterRequirementTypeModel'
        name:
          type: string
          description: >-
            DK: Navn.

            Name of requirement (typically from MeteringComponent value list).
        meteringComponentId:
          type: string
          description: >-
            DK: MålingsKomponentId.

            Shared value list with Meter domain.

            The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.

            It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
          format: guid
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          nullable: true
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        inputNumber:
          description: Input number
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 1
        mainMeterFrameId:
          description: Reference to the main Meter Frame
          allOf:
            - $ref: "#/components/schemas/GuidFieldNullable"
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        mainMeterFrameRegisterRequirementId:
          type: integer
          description: >-
            DK: HovedMalerrammeRegisterKravId.

            Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        outsourcedToMeterFrameRegisterRequirementId:
          type: integer
          description: >-
            DK: HjemtagesAfMalerrammeRegisterKravId.

            Can refer to another meter frame which is actually the one that receives data through an external input.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        futureRequirement:
          type: boolean
          description: >-
            DK: FremtidigtRegisterKra

            Indicates whether the register requirement should only be a future requirement. Default false.

            If set to true Validity period will be ignored.
        meterReadingRequired:
          type: boolean
          description: >-
            DK: AflæsningPåkrævet

            Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
        requiredFrom:
          type: string
          description: >-
            DK: GyldigFra.

            Indicates when the meter must meet the requirement.
          format: date-time
          nullable: true
        requiredUntil:
          type: string
          description: >-
            DK: GyldigTil.

            Indicates from when the meter does not have to meet the requirement.
          format: date-time
          nullable: true
        meterFrameId:
          type: string
          description: Meter Frame id.
          format: guid
    UpdateRegisterRequirementModel:
      type: object
      description: Model for updating Register Requirement.
      additionalProperties: false
      required:
        - name
        - registerRequirementType
        - futureRequirement
        - meterReadingRequired
        - rowVersion
      properties:
        name:
          type: string
          description: >-
            DK: Navn.

            Name of requirement (typically from MeteringComponent value list).
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          nullable: true
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        registerRequirementType:
          $ref: '#/components/schemas/RegisterRequirementTypeModel'
        inputNumber:
          description: Input number
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 1
        mainMeterFrameId:
          description: Reference to the main Meter Frame
          allOf:
            - $ref: "#/components/schemas/GuidFieldNullable"
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        mainMeterFrameRegisterRequirementId:
          type: integer
          description: >-
            DK: HovedMalerrammeRegisterKravId.

            Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        outsourcedToMeterFrameRegisterRequirementId:
          type: integer
          description: >-
            DK: HjemtagesAfMalerrammeRegisterKravId.

            Can refer to another meter frame which is actually the one that receives data through an external input.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        futureRequirement:
          type: boolean
          description: >-
            DK: FremtidigtRegisterKra

            Indicates whether the register requirement should only be a future requirement. Default false.

            If set to true Validity period will be ignored.
        meterReadingRequired:
          type: boolean
          description: >-
            DK: AflæsningPåkrævet

            Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
        requiredFrom:
          type: string
          description: >-
            DK: GyldigFra.

            Indicates when the meter must meet the requirement.
          format: date-time
          nullable: true
        requiredUntil:
          type: string
          description: >-
            DK: GyldigTil.

            Indicates from when the meter does not have to meet the requirement.
          format: date-time
          nullable: true
        rowVersion:
          type: string
          description: A binary value (base64) used to detect updates to a object and prevent data conflicts. It is incremented each time an update is made to the object.
          format: byte
    PagedResultOfGetSpecialAgreementsSearchModel:
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          description: Paged result of special agreements search model.
          additionalProperties: false
          properties:
            results:
              type: array
              description: Array.
              maxItems: 1000000
              items:
                $ref: '#/components/schemas/GetSpecialAgreementsSearchModel'
    GetSpecialAgreementsSearchModel:
      type: object
      description: Get special agreements search model.
      additionalProperties: false
      required:
        - id
        - description
        - validFrom
      properties:
        id:
          type: string
          description: Id.
          format: guid
        description:
          type: string
          description: Description.
        validFrom:
          type: string
          description: Valid from date.
          format: date-time
        validUntil:
          type: string
          description: Valid until date.
          format: date-time
          nullable: true
        zoneCodeListValueId:
          type: string
          description: Zone code list value identifier.
          format: guid
          nullable: true
        zoneCodeListValueDisplayValue:
          type: string
          description: Zone code list value display value.
          nullable: true
    GetSpecialAgreementByIdModel:
      type: object
      description: Get special agreement by id model.
      additionalProperties: false
      required:
        - id
        - description
        - validFrom
        - specialAgreementConnectionPointAssignments
        - rowVersion
      properties:
        id:
          type: string
          description: Identifier.
          format: guid
        description:
          type: string
          description: Description.
        validFrom:
          type: string
          description: Valid from date.
          format: date-time
        validUntil:
          type: string
          description: Valid until date.
          format: date-time
          nullable: true
        zoneCodeListValueId:
          type: string
          description: Zone code list value identifier.
          format: guid
          nullable: true
        specialAgreementConnectionPointAssignments:
          type: array
          description: Special agreement connection point assignments array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/SpecialAgreementConnectionPointAssignmentReadModel'
        rowVersion:
          type: string
          description: Row version.
          format: byte
    SpecialAgreementConnectionPointAssignmentReadModel:
      type: object
      description: Special agreement connection point assignment read model.
      additionalProperties: false
      required:
        - assignmentId
        - connectionPointId
        - connectionPointBusinessId
      properties:
        assignmentId:
          type: string
          description: Assignment identifier.
          format: guid
        connectionPointId:
          type: string
          description: Connection point identifier.
          format: guid
        connectionPointBusinessId:
          type: string
          description: Connection point business identifier.
        connectionPointAddressId:
          type: string
          description: Connection point address identifier.
          format: guid
          nullable: true
        connectionPointAddressLine1:
          type: string
          description: Connection point address line 1.
          nullable: true
        connectionPointAddressLine2:
          type: string
          description: Connection point address line 2.
          nullable: true
    AddSpecialAgreementModel:
      type: object
      description: Add special agreement model.
      additionalProperties: false
      required:
        - description
        - validFrom
        - specialAgreementConnectionPointAssignments
      properties:
        description:
          type: string
          description: Description.
        zoneCodeListValueId:
          type: string
          description: Zone code list value identifier.
          format: guid
          nullable: true
        validFrom:
          type: string
          description: Valid from date.
          format: date-time
        validUntil:
          type: string
          description: Valid until date.
          format: date-time
          nullable: true
        specialAgreementConnectionPointAssignments:
          type: array
          description: Special agreement connection point assignments.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/SpecialAgreementAssignmentAddUpdateModel'
    SpecialAgreementAssignmentAddUpdateModel:
      type: object
      description: Special agreement assignment add and update model.
      additionalProperties: false
      required:
        - connectionPointId
      properties:
        assignmentId:
          type: string
          description: Assignment identifier.
          format: guid
          nullable: true
        connectionPointId:
          type: string
          description: Connection point identifier.
          format: guid
    UpdateSpecialAgreementModel:
      type: object
      description: Update special agreement model.
      additionalProperties: false
      required:
        - description
        - validFrom
        - specialAgreementConnectionPointAssignments
        - rowVersion
      properties:
        description:
          type: string
          description: Description.
        zoneCodeListValueId:
          type: string
          description: Zone code list value id.
          format: guid
          nullable: true
        validFrom:
          type: string
          description: Valid from date.
          format: date-time
        validUntil:
          type: string
          description: Valid until date.
          format: date-time
          nullable: true
        specialAgreementConnectionPointAssignments:
          type: array
          description: Special agreement connection point assignments array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/SpecialAgreementAssignmentAddUpdateModel'
        rowVersion:
          type: string
          description: Row version.
          format: byte
    PagedResponseOfGetMeterFrameSearchModel:
      type: object
      description: Paged response of get meter frame search model.
      additionalProperties: false
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          type: integer
          description: Current page.
          format: int32
          maximum: 2147483647
          minimum: 0
        pageSize:
          type: integer
          description: Page size.
          format: int32
          maximum: 2147483647
          minimum: 0
        totalPages:
          type: integer
          description: Total pages.
          format: int32
          maximum: 2147483647
          minimum: 0
        totalRows:
          type: integer
          description: Total rows.
          format: int32
          maximum: 2147483647
          minimum: 0
        results:
          type: array
          description: Results array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/GetMeterFrameSearchModel'
    GetMeterFrameSearchModel:
      type: object
      description: Get meter frame search model.
      additionalProperties: false
      required:
        - id
        - meterFrameNumber
        - supplyType
        - connectionPointId
        - commonReading
        - meterMissing
        - meterSealed
        - meterWorkConsumerBilled
        - supplyStatus
        - placementCode
        - hasActiveBailiffCase
      properties:
        id:
          type: string
          description: Meter frame internal identifier.
          format: guid
        meterFrameNumber:
          type: string
          description: >-
            DK: Målerrammenummer.

            Number on the Meter Frame.
        supplyType:
          description: 'Defines usage of the Meter Frame: “Electricity”,”Heating”, “Water”.'
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
        connectionPointId:
          type: string
          description: ConnectionPointId.
          format: guid
        connectionPointBusinessId:
          type: string
          description: ConnectionPointBusinessId.
          nullable: true
        addressId:
          type: string
          description: An UUID reference to a master data address.
          format: guid
          nullable: true
        addressName:
          type: string
          description: AddressName.
          nullable: true
        addressLine1:
          type: string
          description: AddressLine1.
          nullable: true
        addressLine2:
          type: string
          description: AddressLine2.
          nullable: true
        addressStatus:
          description: AddressStatus.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          description: AddressType.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressType'
        lifeCycleStatus:
          description: List of possible life cycle states that the MDR system can put the address into.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        darStatus:
          description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated"..
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/DarStatus'
        commonReading:
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
          allOf:
            - $ref: '#/components/schemas/GuidField'
          example: 'f56d5e72-d9f1-4d4d-95ac-ca06079dfc0a'
        noMeter:
          type: boolean
          description: 'DK: Målerfri.'
          example: true
        meterMissing:
          type: boolean
          description: 'DK: MålerVæk.'
        placementSpecification:
          type: string
          description: 'DK: Placeringsbeskrivelse.'
          nullable: true
        meterSealDate:
          type: string
          description: >-
            DK: Målerplomberingsdato.

            Indicates the date when the meter was sealed.
          format: date-time
          nullable: true
        meterSealed:
          type: boolean
          description: >-
            DK: MålerPlomberet.

            Indicates whether the meter is sealed.
        meterWorkConsumerBilled:
          type: boolean
          description: >-
            DK: MålerYdelseFaktureresEjer.

            Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
        supplyStatus:
          description: >-
            DK: Tilslutningsstatus.

            List of possible statuses that Meter Frame power supply can have.
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        meterReadingType:
          description: 'A list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
          example: 'd988b796-225b-4eef-8ec4-191ee69a3a80'
        supplyDisconnectType:
          description: >-
            DK: AfbrydtVedType.

            Indicates whether the supply is disconnected before the meter, disconnected in the cable cabinet, disconnected at the meter outlet, or in the mains station.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        gisPropertiesElectricity:
          description: MeterFrameGisPropertiesElectricity
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesElectricityModel'
        gisPropertiesHeating:
          description: MeterFrameGisPropertiesHeating
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesHeatingModel'
        gisPropertiesWater:
          description: MeterFrameGisPropertiesWater
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesWaterModel'
        mainBranchLineElectricity:
          description: MainBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineElectricityModel'
        reserveBranchLineElectricity:
          description: ReserveBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineElectricityModel'
        mainBranchLineWater:
          description: MainBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineWaterModel'
        reserveBranchLineWater:
          description: ReserveBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineWaterModel'
        placementCode:
          type: string
          description: Placement code CodeList value id.
          format: guid
        connectionStatus:
          description: 'DK: Tilslutningsstatus.'
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        decommissioned:
          description: 'Decommissioned date.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        statusChanged:
          description: 'Latest status change date.'
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        mainBranchLineNumber:
          description: 'Main Branch Line Number.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        mainBranchLineSize:
          description: 'Main Branch Line Size.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        mainBranchLineType:
          description: 'Main Branch Line Type.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        connectedThroughMeterFrameNumber:
          description: 'DK: Placeringsbeskrivelse.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        meterNumber:
          description: 'Meter number.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        meterId:
          description: 'Meter Id.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MeterIdNullable'
        meterInputConnectionStartDate:
          description: 'Meter input connection start date.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        hasActiveBailiffCase:
          type: boolean
          description: 'Indicates whether there is a bailiff case in Created or Active state associated with the frame.'

    PagedResponseOfPostMeterFrameSearchModel:
      description: Paged response of post command meter frame search model.
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          additionalProperties: false
          properties:
            results:
              type: array
              nullable: true
              description: Results array.
              maxItems: 1000000
              items:
                $ref: '#/components/schemas/GetMeterFrameSearchModel'

    MeterFramesSearchQueryModel:
      type: object
      description: Meter frames search query model.
      additionalProperties: false
      properties:
        connectionPointId:
          type: string
          format: guid
          nullable: true
        meterFrameId:
          type: string
          format: guid
          nullable: true
        connectionPointBusinessId:
          type: string
          nullable: true
        supplyType:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
        supplyStatus:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        supplyDisconnectType:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        commonReading:
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
          nullable: true
          $ref: '#/components/schemas/GuidField'
          example: 'f56d5e72-d9f1-4d4d-95ac-ca06079dfc0a'
        noMeter:
          description: No meter.
          nullable: true
          type: boolean
          example: true
        meterMissing:
          type: boolean
          nullable: true
        meterReadingType:
          description: 'A list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".'
          nullable: true
          $ref: '#/components/schemas/GuidField'
          example: 'd988b796-225b-4eef-8ec4-191ee69a3a80'
        meterFrameNumber:
          type: string
          nullable: true
        addressLine1:
          type: string
          nullable: true
        addressLine2:
          type: string
          nullable: true
        addressStatus:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressType'
        lifeCycleStatus:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        darStatus:
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/DarStatus'
        cabinetNumber:
          type: integer
          format: int32
          nullable: true
        cableNumber:
          type: integer
          format: int32
          nullable: true
        stationNumber:
          type: integer
          format: int32
          nullable: true
        transformerNumber:
          type: integer
          format: int32
          nullable: true
        shinNumber:
          type: integer
          format: int32
          nullable: true
        heatingBranchLineNumber:
          type: integer
          format: int32
          nullable: true
        heatingPlantId:
          type: integer
          format: int32
          nullable: true
        heatingPlantName:
          type: string
          nullable: true
        heatingPlantPipeName:
          type: string
          nullable: true
        heatingPlantPipeNumber:
          type: integer
          format: int32
          nullable: true
        waterBranchLineNumber:
          type: string
          nullable: true
        sectionId:
          type: integer
          format: int32
          nullable: true
        sectionName:
          type: string
          nullable: true
        waterPlantId:
          type: integer
          format: int32
          nullable: true
        waterPlantName:
          type: string
          nullable: true
        waterPlantPipeId:
          type: integer
          format: int32
          nullable: true
        waterPlantPipeName:
          type: string
          nullable: true
        mainZone:
          type: string
          nullable: true
        superZone:
          type: string
          nullable: true
        number:
          description: Page number.
          type: integer
          format: int32
          maximum: 2147483647
          minimum: 0
        size:
          description: Page size.
          type: integer
          format: int32
          maximum: 2147483647
          minimum: 0
        sortBy:
          description: Name of the property to sort by.
          $ref: '#/components/schemas/SortByNullable'
          example: 'connectionPointNumber'
        sortOrder:
          description: Direction of sorting. Can only be 'asc' for ascending or 'desc' for descending.
          $ref: '#/components/schemas/SortOrderNullable'
          example: 'asc'

    MeterFramesSearchQueryModelWithCsvConfiguration:
      allOf:
        - $ref: '#/components/schemas/MeterFramesSearchQueryModel'
        - type: object
          additionalProperties: false
          properties:
            csvConfiguration:
              nullable: true
              oneOf:
                - $ref: '#/components/schemas/CsvConfigurationQueryModel'

    SupplyTypesModel:
      type: integer
      description: List of possible supply types "Electricity", "Heating", "Water"
      x-enumFlags: true
      x-enumNames:
        - Electricity
        - Heating
        - Water
      enum:
        - 1
        - 2
        - 4
    AddressStatus:
      type: integer
      description: List of possible statuses that a master data address can have "Active", "Inactive".
      x-enumNames:
        - Active
        - Inactive
      enum:
        - 1
        - 2
    AddressType:
      type: integer
      description: List of possible address types that a MasterDataAddressDetails object can hold. Eg. "Address", "AccessAddress".
      x-enumNames:
        - Primary
        - Temporary
        - AccessAddress
      enum:
        - 1
        - 2
        - 3
    LifeCycleStatus:
      type: integer
      description: List of possible life cycle states that the MDR system can put the address into.
      x-enumNames:
        - ToBeDeleted
        - Valid
        - UnderInvestigation
      enum:
        - 1
        - 2
        - 3
    DarStatus:
      type: integer
      description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated".
      x-enumNames:
        - Yes
        - No
        - Temporary
      enum:
        - 1
        - 2
        - 3
    MeterFrameSupplyStatusModel:
      type: integer
      description: List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
      x-enumNames:
        - Connected
        - Disconnected
      enum:
        - 1
        - 2
    MeterFrameSupplyDisconnectTypeModel:
      type: integer
      description: List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation".
      x-enumNames:
        - MeterNoVoltage
        - DisconnectedWithBreakerInMeter
        - DisconnectedBeforeMeter
        - DisconnectedInKabinet
        - DisconnectedAfterMeter
        - DisconnectedStation
        - Connected
        - Unknown
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
    MeterFrameGisPropertiesElectricityModel:
      allOf:
        - $ref: '#/components/schemas/MeterFrameGisProperties'
        - type: object
          description: GIS properties for electricity supply type in Meter Frame.
          additionalProperties: false
          properties:
            equipmentContainerId:
              description: 'Top level (Equipment container) topology item identifier. Top level parent of topology node specified in gisId.'
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'
            branchLineFuseAmps:
              type: integer
              description: Indicates the size of, or setting of the maximum circuit breaker in front of the branch line on the network company's side. Ex. 125 Amperes.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            branchLineFuseType:
              type: string
              description: Indicates the type of branch line fuse present. Ex. Fuse, Maximum switch, HSP fuse.
              maxLength: 50
              minLength: 0
              nullable: true
            cabinetNumber:
              type: integer
              description: Number of the cable box where the plug is connected. Ex. 123658.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            cableNumber:
              type: integer
              description: Number on execution in station. Ex. 1, 2, 3 etc.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            connectionPointLevel:
              type: string
              description: Indicates at which level the branch line is connected in the network. Ex. C, B1, B2, A1, A2 and A0.
              maxLength: 50
              minLength: 0
              nullable: true
            shinNumber:
              type: integer
              description: Number on the rail in the locker.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            stationNumber:
              type: integer
              description: The station from which the branch line is supplied. Ex. 98756.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            transformerNumber:
              type: integer
              description: Transformer number, for several transformers in a station. Ex. 1, 2, 3 etc.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
    MeterFrameGisProperties:
      type: object
      description: Common properties for GIS in MeterFrame.
      additionalProperties: false
      required:
        - gisId
        - gisDescription
      properties:
        gisId:
          type: string
          description: Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.
          format: guid
        gisDescription:
          type: string
          description: The name of the connection point in the physical topology. The name is set when the gis id is selected from the GIS system.
          maxLength: 1000
          minLength: 1
    MeterFrameGisPropertiesHeatingModel:
      allOf:
        - $ref: '#/components/schemas/MeterFrameGisProperties'
        - type: object
          description: GIS properties for heating supply type in Meter Frame.
          additionalProperties: false
          properties:
            branchLineSize:
              type: string
              description: Indicates how large a branch line is laid in the ground from the main line to the meter.
              maxLength: 25
              minLength: 1
            branchLineNumber:
              type: integer
              description: Number on branch line. Ex. 246810.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            expectedForwardFlowTemp:
              type: integer
              description: Calculated value of forward-flow temperature.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            expectedPressure:
              type: string
              description: Calculated value of expected pressure - max/min/delta.
              maxLength: 50
              minLength: 0
              nullable: true
            heatingPlantId:
              type: integer
              description: Heating plant Id - Heater "kID".
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            heatingPlantName:
              type: string
              description: Heating plant name.
              maxLength: 50
              minLength: 0
              nullable: true
            heatingPlantPipeName:
              type: string
              description: Pipe name / outlet marking.
              maxLength: 50
              minLength: 0
              nullable: true
            heatingPlantPipeNumber:
              type: integer
              description: Outlet marking number.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            hydraulicZone:
              type: string
              description: Indicates an area, e.g. a city (polygon).
              maxLength: 50
              minLength: 0
              nullable: true
    MeterFrameGisPropertiesWaterModel:
      allOf:
        - $ref: '#/components/schemas/MeterFrameGisProperties'
        - type: object
          description: GIS properties for water supply type in Meter Frame.
          additionalProperties: false
          properties:
            branchLineSizeSquare:
              type: string
              description: Indicates how large a branch line is laid in the ground from Main line to Area.
              maxLength: 25
              minLength: 0
              nullable: true
            branchLineNumber:
              type: string
              description: Number on branch line. Ex. XF2500.
              maxLength: 50
              minLength: 0
              nullable: true
            hardness:
              type: integer
              description: Hardness in dH (german unit for water hardness).
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            mainZone:
              type: string
              description: Indicates an area (higher level than section) = an operating area (polygon).
              maxLength: 50
              minLength: 0
              nullable: true
            pressure:
              type: string
              description: Section/elevation/conduction loss (calculated value).
              maxLength: 50
              minLength: 0
              nullable: true
            sectionId:
              type: integer
              description: Indicates an area (polygon) in which the water meter frame is located (for checking water balance).
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            sectionName:
              type: string
              description: Section name.
              maxLength: 50
              minLength: 0
              nullable: true
            superZone:
              type: string
              description: Indicates an area <Section> (higher level than MainZone) = e.g. a city (polygon).
              maxLength: 50
              minLength: 0
              nullable: true
            waterPlantId:
              type: integer
              description: Water Plant Id.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            waterPlantName:
              type: string
              description: Water Plant Name.
              maxLength: 50
              minLength: 0
              nullable: true
            waterPlantPipeId:
              type: integer
              description: Water Plant Pipe Id.
              format: int32
              maximum: 2147483647
              minimum: -2147483648
              nullable: true
            waterPlantPipeName:
              type: string
              description: Water Plant Pipe name.
              maxLength: 50
              minLength: 0
              nullable: true
    GetMeterFrameModel:
      type: object
      description: Get meter frame model.
      additionalProperties: false
      required:
        - id
        - changedByUserId
        - created
        - meterFrameNumber
        - supplyType
        - commonReading
        - meterMissing
        - meterSealed
        - meterWorkConsumerBilled
        - rowVersion
        - connectionPointId
        - supplyStatus
        - tagAssignments
        - placementCode
        - accessInformation
        - connectionStatus
      properties:
        id:
          type: string
          description: Meter frame internal identifier.
          format: guid
        changedByUserId:
          type: string
          description: Last change user id.
          format: guid
        created:
          type: string
          description: >-
            DK: Oprettet.

            Creation time stamp of the Meter Frame.
          format: date-time
        meterFrameNumber:
          type: string
          description: >-
            DK: Målerrammenummer.

            Number on the Meter Frame.
        supplyType:
          description: >-
            DK: Forbrugsart.

            Defines usage of the Meter Frame: “Electricity”,”Heating”, “Water”.
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
        commonReading:
          allOf:
            - $ref: '#/components/schemas/GuidField'
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
        noMeter:
          type: boolean
          description: 'DK: Målerfri.'
        meterMissing:
          type: boolean
          description: 'DK: MålerVæk.'
        placementSpecification:
          type: string
          description: 'DK: Placeringsbeskrivelse.'
          nullable: true
        meterSealDate:
          type: string
          description: >-
            DK: Målerplomberingsdato.

            Indicates the date when the meter was sealed.
          format: date-time
          nullable: true
        meterSealed:
          type: boolean
          description: >-
            DK: MålerPlomberet.

            Indicates whether the meter is sealed.
        meterWorkConsumerBilled:
          type: boolean
          description: >-
            DK: MålerYdelseFaktureresEjer.

            Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
        connectionStatus:
          type: string
          description: 'DK: Tilslutningsstatus.'
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
        statusChanged:
          type: string
          description: Latest status change date.
          format: date-time
          nullable: true
        electricityAttributes:
          description: ElectricityAttributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameElectricityAttributesModel'
        heatingAttributes:
          description: HeatingAttributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameHeatingAttributesModel'
        waterAttributes:
          description: MeterFrameWaterAttributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameWaterAttributesModel'
        geographicalLocation:
          description: Set of geographical location properties - describing Meter Frame geo location.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/GeographicalLocationModel'
        gisPropertiesElectricity:
          description: MeterFrameGisPropertiesElectricity
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesElectricityModel'
        gisPropertiesHeating:
          description: MeterFrameGisPropertiesHeating
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesHeatingModel'
        gisPropertiesWater:
          description: MeterFrameGisPropertiesWater
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesWaterModel'
        mainBranchLineElectricity:
          description: MainBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineElectricityModel'
        reserveBranchLineElectricity:
          description: ReserveBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineElectricityModel'
        mainBranchLineWater:
          description: MainBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineWaterModel'
        reserveBranchLineWater:
          description: ReserveBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineWaterModel'
        rowVersion:
          type: string
          description: RowVersion.
          format: byte
        connectionPointId:
          type: string
          description: ConnectionPointId.
          format: guid
        addressId:
          type: string
          description: An UUID reference to a master data address.
          format: guid
          nullable: true
        addressName:
          type: string
          description: AddressName from CAR.
          nullable: true
        addressLine1:
          type: string
          description: AddressLine1 from CAR.
          nullable: true
        addressLine2:
          type: string
          description: AddressLine2 from CAR.
          nullable: true
        addressStatus:
          description: AddressStatus from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          description: AddressType from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressType'
        darStatus:
          description: DarStatus from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          description: LifeCycleStatus from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        supplyStatus:
          description: 'DK: Tilslutningsstatus.'
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/TagAssignmentModel'
        meterReadingType:
          description: A list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".
          allOf:
            - $ref: '#/components/schemas/GuidField'
          nullable: true
          example: 'd988b796-225b-4eef-8ec4-191ee69a3a80'
        supplyDisconnectType:
          description: >-
            DK: AfbrydtVedType.

            Indicates whether the supply is disconnected before the meter, disconnected in the cable cabinet, disconnected at the meter outlet, or in the mains station.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        placementCode:
          type: string
          description: Placement code CodeList value id.
          format: guid
        accessInformation:
          type: array
          description: Access information.
          items:
            $ref: '#/components/schemas/AccessInformationModel'
    MeterFrameElectricityAttributesModel:
      type: object
      description: Meter frame electricity attributes model.
      additionalProperties: false
      required:
        - connectionType
        - breakerBeforeMeter
      properties:
        connectionType:
          type: string
          description: 'DK: Tilslutningstype.'
          format: guid
        breakerBeforeMeter:
          type: boolean
          description: 'DK: AfbryderFørMåler.'
        powerLimitA:
          type: number
          description: 'DK: EffektgrænseA.'
          format: decimal
          nullable: true
        powerLimitKw:
          type: number
          description: 'DK: EffektgrænseKW.'
          format: decimal
          nullable: true
        productionCapacity:
          type: number
          description: 'DK: Anlægskapacitet.'
          format: decimal
          nullable: true
        purpose:
          type: string
          description: 'DK: Formål.'
          format: guid
          nullable: true
        ratioCt:
          allOf:
            - $ref: '#/components/schemas/GuidField'
          description: 'DK: OmsætningsforholdCT.'
          nullable: true
          example: null
        ratioVt:
          allOf:
            - $ref: '#/components/schemas/GuidField'
          description: 'DK: OmsætningsforholdVT.'
          nullable: true
          example: null
        tarifConnectionPoint:
          type: string
          description: 'DK: TarifTilslutningspunkt.'
          format: guid
          nullable: true
        flexAttributeObject:
          type: string
          description: >-
            DK: FleksAttributObjekt.

            An UUID reference to a Settlement Object that is used to register flexible attributes about the Meter Frame.

            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical Meter Frame.
          format: guid
          nullable: true
        lossFactor:
          type: number
          description: >-
            DK: NettabsFaktor.

            Nettabsfaktor som bruges på målerrammen i MDM. Vi skal lige gennemgå den fra MDM siden. Skal indgå både på fuldttidserie niveau så faktoren ganges på både 15/60 forbrug samt

            tællerstande.

            Mains loss factor used on the measuring frame in MDM. We just need to review it from the MDM page. Must be included both at full-time series level so the factor is multiplied by

            both 15/60 consumption and meter readings.
          format: decimal
          nullable: true
        meterFrameFuse:
          type: integer
          description: >-
            DK: MålerRammeForsikring.

            Forsikring (tarifsikring, T-ret sikring etc) som ikke er stikledningssikringen eller tilslutningsrettigheden. Kunden kan selv bestemme størrelsen på denne.

            Hvis kunden ikke har oplyst en forsikring, så er stikledningssikringen kundens forsikring.

            Kunden kan selv sikrer op eller ned (stikledningssikringen begrænser ham selvfølgelig).

            Vi har den kun i systemet for at kunne rumme data fra blanketten.

            EN:

            Insurance (tariff fuse, T-right fuse etc) which is not the branch line fuse or the connection right. The customer can decide the size of this.

            If the customer has not stated an insurance, then the branch line insurance is the customer's insurance.

            The customer can secure up or down himself (the branch line protection limits him of course).

            We only have it in the system to be able to hold data from the form.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        meterDisplaySettings:
          type: string
          description: >-
            DK: VisningMåler.

            En bruger skal kunne stille krav til visning af alle målerer som installeres i målerrammen.

            Skal medfører målerrammekrav på målerammen som gennemtvinger specifik display visning.

            EN:

            A user must be able to set requirements for displaying all meters that are installed in the meter frame.

            Must entail meter frame requirements on the meter frame which enforces specific display.
          format: guid
          nullable: true
        connectionRemark:
          type: string
          description: >-
            DK: TilslutningsBemærkning.

            Specielle forhold omkring tilslutning, f.eks. adapter, kvadrat, klemmetype etc.

            EN:

            Special conditions regarding connection, e.g. adapter, square, terminal type etc.
          format: guid
          nullable: true
    MeterFrameHeatingAttributesModel:
      type: object
      description: Meter frame heating attributes model.
      additionalProperties: false
      required:
        - calculateCooling
      properties:
        calculateCooling:
          type: boolean
          description: 'DK: BeregnAfkøling.'
        coolingLimit:
          type: number
          description: 'DK: Afkølingsgrænse.'
          format: decimal
          nullable: true
        criticalCustomer:
          type: string
          description: 'DK: KritiskKundekategori.'
          format: guid
          nullable: true
        planEffect:
          type: integer
          description: 'DK: Anlægsydelse.'
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        plantType:
          description: 'DK: Anlægstype.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        counterPlacement:
          description: Counter Placement.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        heatMeterConnectionType:
          description: Heat Meter Connection Type.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
          example: null
        heatMeterDisplaySettings:
          description: Heat Meter Display Settings.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
          example: '1975CFF1-8FC6-46ED-8D6C-FA05F950CDA9'
        returnHeatingConnected:
          type: boolean
          description: 'DK: Returvarme.'
          nullable: true
        totalAllowedAreaM2:
          description: 'Allowed total area m2'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        allowedBusinessAreaM2:
          description: 'Allowed business area m2'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        allowedStorageAreaM2:
          description: 'Allowed storage area m2'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        flexAttributeObject:
          type: string
          description: >-
            DK: FleksAttributObjekt.

            An UUID reference to a Settlement Object that is used to register flexible attributes about the Meter Frame.

            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical Meter Frame.
          format: guid
          nullable: true
    MeterFrameWaterAttributesModel:
      type: object
      description: Meter frame water attributes model.
      additionalProperties: false
      required:
        - directSprinkling
        - driveBy
      properties:
        directSprinkling:
          type: boolean
          description: DK:DirekteSprinkling.
        driveBy:
          type: string
          description: DK:DriveBy.
          format: guid
        criticalCustomer:
          type: string
          description: DK:KritiskKundekategori.
          format: guid
          nullable: true
        mediumCategory:
          type: string
          description: DK:MediumKategori.
          format: guid
          nullable: true
        ownPump:
          type: boolean
          description: DK:EgenPumpe.
          nullable: true
        pressureEnhancer:
          type: boolean
          description: DK:TrykForøger.
          nullable: true
        qvkSensor:
          type: boolean
          description: DK:QvkSensor.
          nullable: true
        waterMeterDisplaySettings:
          description: Water Meter Display Settings.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: '6D18E71C-49B0-4C7F-BDDB-C7BC234D183B'
        waterFlowLimit:
          type: string
          description: Flow limit
          minLength: 1
          maxLength: 3
          pattern: "^(2,5|6|10|15|25|40|100|150)$"
        flexAttributeObject:
          type: string
          description: DK:FleksAttributObjekt.
          format: guid
          nullable: true
    GeographicalLocationModel:
      type: object
      description: This object contains information about a point on the globe, including information about the projection, source accuracy, etc.
      additionalProperties: false
      required:
        - longitude
        - latitude
      properties:
        accuracy:
          type: string
          description: The accuracy class of the point.
          nullable: true
        name:
          type: string
          nullable: true
        source:
          type: string
          description: Source of the geographical location point.
          nullable: true
        technicalStandard:
          type: string
          description: Technical standard.
          nullable: true
        longitude:
          type: number
          description: Longitude - X in most spatial file formats.
          format: decimal
        latitude:
          type: number
          description: Latitude - Y in most spatial file formats.
          format: decimal
        elevation:
          type: number
          description: Z - elevation in some spatial formats - number of meters above the surface of the water.
          format: decimal
          nullable: true
    TagAssignmentModel:
      type: object
      description: Tag assignment model.
      additionalProperties: false
      required:
        - id
        - assigneeId
        - tagCodeListValueId
        - tagCodeListValue
      properties:
        id:
          type: string
          description: Id.
          format: guid
        assigneeId:
          type: string
          description: Assignee id.
          format: guid
        tagCodeListValueId:
          type: string
          description: Tag code list value id.
          format: guid
        tagCodeListValue:
          type: string
          description: Tag code list value.
    AddMeterFrameModel:
      allOf:
        - $ref: '#/components/schemas/MeterFrameCommonModel'
        - type: object
          description: Add meter frame model.
          additionalProperties: false
          properties:
            supplyType:
              description: >-
                DK: Forbrugsart.

                Defines usage of the Meter Frame: “Electricity”,”Heating”, “Water”.
              maximum: 2147483647
              minimum: -2147483648
              oneOf:
                - $ref: '#/components/schemas/SupplyTypesModel'
    MeterFrameCommonModel:
      type: object
      description: Meter Frame model.
      additionalProperties: false
      required:
        - placementCode
        - connectionPointId
        - commonReading
        - meterMissing
        - meterSealed
        - meterWorkConsumerBilled
        - tagAssignments
        - supplyStatus
        - accessInformation
        - connectionStatus
      properties:
        created:
          type: string
          description: Meter Frame created date.
          format: date-time
          nullable: true
        placementCode:
          type: string
          description: Placement code CodeList value id.
          format: guid
        connectionPointId:
          type: string
          description: Connection point identifier.
          format: guid
          minLength: 1
        commonReading:
          allOf:
            - $ref: '#/components/schemas/GuidField'
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
        noMeter:
          type: boolean
          description: 'DK: Målerfri.'
        meterMissing:
          type: boolean
          description: 'DK: MålerVæk.'
        placementSpecification:
          type: string
          description: 'DK: Placeringsbeskrivelse.'
          nullable: true
        meterSealDate:
          type: string
          description: >-
            DK: Målerplomberingsdato.

            Indicates the date when the meter was sealed.
          format: date-time
          nullable: true
        meterSealed:
          type: boolean
          description: >-
            DK: MålerPlomberet.

            Indicates whether the meter is sealed.
        meterWorkConsumerBilled:
          type: boolean
          description: >-
            DK: MålerYdelseFaktureresEjer.

            Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
        connectionStatus:
          type: string
          description: 'DK: Tilslutningsstatus.'
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
        statusChanged:
          type: string
          description: Latest status change date.
          format: date-time
          nullable: true
        electricityAttributes:
          description: ElectricityAttributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameElectricityAttributesModel'
        heatingAttributes:
          description: HeatingAttributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameHeatingAttributesModel'
        waterAttributes:
          description: MeterFrameWaterAttributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameWaterAttributesModel'
        geographicalLocation:
          description: Set of geographical location properties - describing Meter Frame geo location.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/GeographicalLocationModel'
        gisPropertiesElectricity:
          description: MeterFrameGisPropertiesElectricity
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesElectricityModel'
        gisPropertiesHeating:
          description: MeterFrameGisPropertiesHeating
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesHeatingModel'
        gisPropertiesWater:
          description: MeterFrameGisPropertiesWater
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesWaterModel'
        mainBranchLineElectricity:
          description: MainBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineElectricityCreateUpdateModel'
        reserveBranchLineElectricity:
          description: ReserveBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineElectricityCreateUpdateModel'
        mainBranchLineWater:
          description: MainBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineWaterModel'
        reserveBranchLineWater:
          description: ReserveBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineWaterModel'
        addressId:
          type: string
          description: An UUID reference to a master data address.
          format: guid
          nullable: true
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/AddTagAssignmentModel'
        supplyStatus:
          description: >-
            DK: Tilslutningsstatus.

            List of possible statuses that Meter Frame power supply can have.
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        meterReadingType:
          description: 'A list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
          example: 'd988b796-225b-4eef-8ec4-191ee69a3a80'
        supplyDisconnectType:
          description: >-
            DK: AfbrydtVedType.

            Indicates whether the supply is disconnected before the meter, disconnected in the cable cabinet, disconnected at the meter outlet, or in the mains station.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        accessInformation:
          type: array
          description: Access information.
          items:
            $ref: '#/components/schemas/AccessInformationModel'
    AddTagAssignmentModel:
      type: object
      description: Add tag assignment model.
      additionalProperties: false
      required:
        - tagCodeListValueId
      properties:
        tagCodeListValueId:
          type: string
          description: Tag code list value identifier.
          format: guid
    UpdateMeterFrameModel:
      allOf:
        - $ref: '#/components/schemas/MeterFrameCommonModel'
        - type: object
          description: Update meter frame model.
          additionalProperties: false
          properties:
            rowVersion:
              type: string
              description: Row version.
              format: byte
    GetMeterFrameExistsResult:
      type: object
      description: Result indicating whether the meter frame exists.
      additionalProperties: false
      required:
        - exists
      properties:
        exists:
          type: boolean
          description: Flag that indicates if the meter frame exists.
    MeterFrameExistByNumberModel:
      type: object
      description: Model to check if a meter frame exists by its number.
      additionalProperties: false
      required:
        - connectionPointId
        - meterFrameNumber
      properties:
        connectionPointId:
          type: string
          description: An UUID reference to the connection point.
          format: guid
        meterFrameNumber:
          type: string
          description: Number on the Meter Frame.
          maxLength: 50
          minLength: 1
    ExternalEquipmentAssignmentModel:
      type: object
      description: Assignment of external equipment model.
      additionalProperties: false
      required:
        - id
        - installedDate
        - equipment
      properties:
        id:
          type: string
          description: Id.
          format: guid
        installedDate:
          type: string
          description: Date of installation.
          format: date-time
        removedDate:
          type: string
          description: Date of removal.
          format: date-time
          nullable: true
        equipment:
          description: Equipment.
          oneOf:
            - $ref: '#/components/schemas/EquipmentModel'
    EquipmentModel:
      type: object
      description: Equipment model.
      additionalProperties: false
      required:
        - installationPossibility
        - equipmentNumber
        - equipmentType
        - vendor
      properties:
        installationPossibility:
          description: Installation possibility.
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/InstallationPossibilityModel'
        equipmentNumber:
          type: string
          description: Equipment number.
        equipmentType:
          type: string
          description: Equipment type.
        vendor:
          type: string
          description: Vendor.
        description:
          type: string
          description: Description.
          nullable: true
        flexAttributeObject:
          type: string
          description: Flex attribute object.
          format: guid
          nullable: true
    ExternalEquipmentAssignmentUpsertModel:
      type: object
      description: Model for insert and update external equipment.
      additionalProperties: false
      required:
        - rowVersion
        - installedDate
        - equipment
      properties:
        rowVersion:
          type: string
          description: Row version.
          format: byte
        installedDate:
          type: string
          description: Date of installation.
          format: date-time
        removedDate:
          type: string
          description: Date of removal.
          format: date-time
          nullable: true
        equipment:
          description: Equipment model.
          oneOf:
            - $ref: '#/components/schemas/EquipmentModel'
    InternalEquipmentAssignmentModel:
      type: object
      description: Assignment of internal equipment model.
      additionalProperties: false
      required:
        - id
        - internalEquipmentId
        - installedDate
        - equipment
      properties:
        id:
          type: string
          description: Id.
          format: guid
        internalEquipmentId:
          type: string
          description: Internal equipment id.
          format: guid
        installedDate:
          type: string
          description: Date of installation.
          format: date-time
        removedDate:
          type: string
          description: Date of removal.
          format: date-time
          nullable: true
        equipment:
          description: Equipment model.
          oneOf:
            - $ref: '#/components/schemas/EquipmentModel'
    InternalEquipmentAssignmentUpsertModel:
      type: object
      description: Model for insert and update internal equipment.
      additionalProperties: false
      required:
        - internalEquipmentId
        - rowVersion
        - installedDate
      properties:
        internalEquipmentId:
          type: string
          description: Internal equipment id.
          format: guid
        rowVersion:
          type: string
          description: Row version.
          format: byte
        installedDate:
          type: string
          description: Date of installation.
          format: date-time
        removedDate:
          type: string
          description: Date of removal.
          format: date-time
          nullable: true
    PagedResultOfGetConnectionPointsSearchModel:
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          description: Paged result of get connection points search model.
          additionalProperties: false
          properties:
            results:
              type: array
              description: Results.
              maxItems: 1000000
              nullable: true
              items:
                $ref: '#/components/schemas/GetConnectionPointsSearchModel'
    GetConnectionPointsSearchModel:
      allOf:
        - $ref: '#/components/schemas/GetConnectionPointsSearchQueryResultBase'
        - type: object
          description: Get connection points search model.
          additionalProperties: false
          properties:
            supplyType:
              description: 'Defines usage of Connection Point: “Electricity”, ”Heating”, “Water”.'
              maximum: 2147483647
              minimum: -2147483648
              oneOf:
                - $ref: '#/components/schemas/SupplyTypesModel'
            electricityAttributesConnectionPointCategoryValue:
              type: string
              description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
              nullable: true
            electricityAttributesInstallationTypeValue:
              type: string
              description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
              nullable: true
            electricityAttributesConnectionStatus:
              type: string
              description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
              nullable: true
            electricityAttributesConsumerCategory:
              type: string
              description: Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.
              nullable: true
            electricityAttributesNetSettlementGroup:
              type: string
              description: This field register the net settlement group, which is also used in the market communication (DataHub).
              nullable: true
            electricityAttributesGridAreaId:
              type: string
              description: Grid area id.
              nullable: true
            heatingAttributesConnectionStatus:
              type: string
              description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
              nullable: true
            heatingAttributesConnectionPointCategoryValue:
              type: string
              description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific codelist.
              nullable: true
            heatingAttributesInstallationTypeValue:
              type: string
              description: Defines type of installation type. Eg. For apartments, single households, Industrial, Agricultural.
              nullable: true
            heatingAttributesHeatWaterHeater:
              type: string
              description: List of different hot water heating controls that can be installed.
              nullable: true
            heatingAttributesWaterHeaterType:
              type: string
              description: List of different water heating types that can be installed.
              nullable: true
            heatingAttributesHeatPlantType:
              type: string
              description: Lists the different plant types.
              nullable: true
            heatingAttributesHeatExchange:
              type: string
              description: List of different heat exchanger options.
              nullable: true
            waterAttributesConnectionStatus:
              type: string
              description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
              nullable: true
            waterAttributesConnectionPointCategoryValue:
              type: string
              description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
              nullable: true
            waterAttributesInstallationTypeValue:
              type: string
              description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
              nullable: true
    GetConnectionPointsSearchQueryResultBase:
      type: object
      description: Get connection points search query result base.
      additionalProperties: false
      required:
        - id
        - connectionPointNumber
        - created
        - rowVersion
        - changedByUserId
        - createdWithoutInstallationForm
      properties:
        id:
          type: string
          description: Connection point object private identifier.
          format: guid
        connectionPointNumber:
          type: string
          description: Number of the ConnectionPoint.
        created:
          type: string
          description: Creation time stamp of the ConnectionPoint.
          format: date-time
        description:
          type: string
          description: Custom description.
          nullable: true
        rowVersion:
          type: string
          description: Row version.
          format: byte
        changedByUserId:
          type: string
          description: ID of a user who changed the entity last time.
          format: guid
        address:
          type: string
          description: An UUID reference to a master data address.
          format: guid
          nullable: true
        addressLine1:
          type: string
          description: AddressLine1 from CAR.
          nullable: true
        addressLine2:
          type: string
          description: AddressLine2 from CAR.
          nullable: true
        addressStatus:
          description: AddressStatus from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          description: AddressType from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressType'
        darStatus:
          description: DarStatus from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          description: LifeCycleStatus from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        alternativeInstallationNumber:
          type: string
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        installationNumber:
          type: string
          description: Installation number.
          nullable: true
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributesDeMasterDataForms:
          type: integer
          description: DEMasterDataForm that comes from the settlement calculation.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        electricityAttributesInstallationDescription:
          type: string
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        electricityAttributesTemporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
          nullable: true
        electricityAttributesTemporaryUntil:
          type: string
          description: >-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual

            grid company.
          format: date-time
          nullable: true
        electricityAttributesFlexAttributeObject:
          type: string
          description: >-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.

            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: guid
          nullable: true
        heatingAttributesInstallationDescription:
          type: string
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        heatingAttributesNumberOfWaterHeater:
          type: integer
          description: The number of Water heaters.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        heatingAttributesFlexAttributeObject:
          type: string
          description: >-
            An UUID reference to a settlement object that is used to register flexible attributes about the connection point.

            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: guid
          nullable: true
        waterAttributesTemporaryUntil:
          type: string
          description: >-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual

            grid company.
          format: date-time
          nullable: true
        waterAttributesInstallationDescription:
          type: string
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        waterAttributesTemporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
          nullable: true
        waterAttributesFlexAttributeObject:
          type: string
          description: >-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.

            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: guid
          nullable: true
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form.
          type: boolean
          nullable: false
    PagedResponseOfGetPagedConnectionPointsMasterDataModel:
      type: object
      description: Paged response of get paged connection points master data model.
      additionalProperties: false
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          type: integer
          description: Current page.
          format: int32
          maximum: 2147483647
          minimum: 0
        pageSize:
          type: integer
          description: Page size.
          format: int32
          maximum: 2147483647
          minimum: 0
        totalPages:
          type: integer
          description: Total pages.
          format: int32
          maximum: 2147483647
          minimum: 0
        totalRows:
          type: integer
          description: Total rows.
          format: int32
          maximum: 2147483647
          minimum: 0
        results:
          type: array
          description: Results.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/GetPagedConnectionPointsMasterDataModel'
    GetPagedConnectionPointsMasterDataModel:
      type: object
      description: Get paged connection points master data model.
      additionalProperties: false
      required:
        - id
        - connectionPointNumber
        - created
        - supplyType
      properties:
        id:
          type: string
          description: Connection point object private identifier.
          format: guid
        connectionPointNumber:
          type: string
          description: Number of the ConnectionPoint.
          example: "20000001"
        created:
          type: string
          description: Creation time stamp of the ConnectionPoint.
          format: date-time
        description:
          type: string
          description: Custom description.
          nullable: true
        address:
          type: string
          description: An UUID reference to a master data address.
          format: guid
          nullable: true
        addressLine1:
          type: string
          description: AddressLine1 from CAR.
          nullable: true
        addressLine2:
          type: string
          description: AddressLine2 from CAR.
          nullable: true
        alternativeInstallationNumber:
          type: string
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        installationNumber:
          type: string
          description: Installation number.
          nullable: true
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        supplyType:
          description: 'Defines usage of Connection Point: “Electricity”, ”Heating”, “Water”.'
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
    GetConnectionPointModel:
      type: object
      description: Get connection point model.
      additionalProperties: false
      required:
        - id
        - connectionPointNumber
        - created
        - rowVersion
        - changedByUserId
        - supplyType
        - tagAssignments
        - priceGroupExcluded
        - address
        - addressLine1
        - addressLine2
        - createdWithoutInstallationForm
      properties:
        id:
          type: string
          description: Connection point object private identifier.
          format: guid
        connectionPointNumber:
          type: string
          description: Number of the ConnectionPoint.
          example: "20000001"
        created:
          type: string
          description: Creation time stamp of the ConnectionPoint.
          format: date-time
        description:
          type: string
          description: Custom description.
          nullable: true
        rowVersion:
          type: string
          description: Row version.
          format: byte
        changedByUserId:
          type: string
          description: ID of a user who changed the entity last time.
          format: guid
        address:
          type: string
          description: An UUID reference to a master data address.
          format: guid
        addressName:
          type: string
          description: AddressName from CAR.
          nullable: true
        addressLine1:
          type: string
          description: AddressLine1 from CAR.
        addressLine2:
          type: string
          description: AddressLine2 from CAR.
        addressStatus:
          description: AddressStatus from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          description: AddressType from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AddressType'
        darStatus:
          description: DarStatus from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          description: LifeCycleStatus from CAR.
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        alternativeInstallationNumber:
          type: string
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        installationNumber:
          type: string
          description: Installation number.
          nullable: true
        supplyType:
          description: 'Defines usage of Connection Point: “Electricity”, ”Heating”, “Water”.'
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/TagAssignmentModel'
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributes:
          description: Electricity attributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/ElectricityAttributesGetModel'
        heatingAttributes:
          description: Heating attributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/HeatingAttributesGetModel'
        waterAttributes:
          description: Water attributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/WaterAttributesGetModel'
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form.
          type: boolean
          nullable: false
    ElectricityAttributesGetModel:
      type: object
      additionalProperties: false
      description: Model used for returning connection points electricity attributes.
      allOf:
        - $ref: '#/components/schemas/ElectricityAttributesBaseModel'
        - type: object
          additionalProperties: false
          description: Additional properties relevant only for response information about connection point electricity attributes.
          required:
            - connectionStatus
          properties:
            connectionStatus:
              description: This status is calculated based on the status of the parent metering point for the connection point.
              allOf:
                - $ref: '#/components/schemas/GuidField'

    ElectricityAttributesUpsertModel:
      type: object
      additionalProperties: false
      description: Electricity attributes upsert model.
      allOf:
        - $ref: '#/components/schemas/ElectricityAttributesBaseModel'
        - type: object
          description: Additional properties relevant only for create or update connection point electricity attributes.
          additionalProperties: false
          properties:
            connectionStatus:
              description: Deprecated for upsert operations. This status is calculated based on the status of the parent metering point for the connection point. If provided, it will be ignored.
              deprecated: true
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'

    ElectricityAttributesBaseModel:
      type: object
      additionalProperties: false
      description: Electricity attributes model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - temporary
      properties:
        connectionPointCategoryValue:
          type: string
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
          format: guid
        installationTypeValue:
          type: string
          description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
          format: guid
        consumerCategory:
          type: string
          description: Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.
          format: guid
          nullable: true
        deMasterDataForms:
          type: integer
          description: DEMasterDataForm that comes from the settlement calculation.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        installationDescription:
          type: string
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        netSettlementGroup:
          type: string
          description: This field register the net settlement group, which is also used in the market communication (DataHub).
          format: guid
          nullable: true
        gridAreaId:
          description: Grid area id.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        temporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
        temporaryUntil:
          type: string
          description: >-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual

            grid company.
          format: date-time
          nullable: true
        flexAttributeObject:
          type: string
          description: >-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.

            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: guid
          nullable: true
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    HeatingAttributesGetModel:
      type: object
      additionalProperties: false
      description: Heating attributes get model.
      allOf:
        - $ref: '#/components/schemas/HeatingAttributesBaseModel'
        - type: object
          description: Additional properties relevant for getting connection point heating attributes.
          additionalProperties: false
          required:
            - connectionStatus
          properties:
            connectionStatus:
              description: Connection status.
              allOf:
                - $ref: '#/components/schemas/GuidField'

    HeatingAttributesUpsertModel:
      type: object
      additionalProperties: false
      description: Heating attributes upsert model.
      allOf:
        - $ref: '#/components/schemas/HeatingAttributesBaseModel'
        - type: object
          description: Additional properties relevant for creating or updating connection point heating attributes.
          additionalProperties: false
          properties:
            connectionStatus:
              description: Deprecated for upsert operations. This status is calculated based on the status of the parent metering point for the connection point. If provided, it will be ignored.
              deprecated: true
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'

    HeatingAttributesBaseModel:
      type: object
      additionalProperties: false
      description: Heating attributes model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - heatWaterHeater
      properties:
        connectionPointCategoryValue:
          type: string
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific codelist.
          format: guid
        installationTypeValue:
          type: string
          description: Defines type of installation type. Eg. For apartments, single households, Industrial, Agricultural.
          format: guid
        installationDescription:
          type: string
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        numberOfWaterHeater:
          type: integer
          description: The number of Water heaters.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        heatWaterHeater:
          type: string
          description: List of different hot water heating controls that can be installed.
          format: guid
        waterHeaterType:
          type: string
          description: List of different water heating types that can be installed.
          format: guid
          nullable: true
        heatPlantType:
          type: string
          description: Lists the different plant types.
          format: guid
          nullable: true
        heatExchange:
          type: string
          description: List of different heat exchanger options.
          format: guid
          nullable: true
        flexAttributeObject:
          type: string
          description: >-
            An UUID reference to a settlement object that is used to register flexible attributes about the connection point.

            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: guid
          nullable: true
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true

    WaterAttributesGetModel:
      type: object
      additionalProperties: false
      description: Water attributes get model.
      allOf:
        - $ref: '#/components/schemas/WaterAttributesBaseModel'
        - type: object
          description: Additional properties relevant for getting connection point water attributes.
          additionalProperties: false
          required:
            - connectionStatus
          properties:
            connectionStatus:
              description: Connection status.
              allOf:
                - $ref: '#/components/schemas/GuidField'

    WaterAttributesUpsertModel:
      type: object
      additionalProperties: false
      description: Water attributes upsert model.
      allOf:
        - $ref: '#/components/schemas/WaterAttributesBaseModel'
        - type: object
          description: Additional properties relevant for creating or updating connection point water attributes.
          additionalProperties: false
          properties:
            connectionStatus:
              description: Deprecated for upsert operations. This status is calculated based on the status of the parent metering point for the connection point. If provided, it will be ignored.
              deprecated: true
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'

    WaterAttributesBaseModel:
      type: object
      additionalProperties: false
      description: Water attributes base model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - temporary
      properties:
        connectionPointCategoryValue:
          type: string
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
          format: guid
        installationTypeValue:
          type: string
          description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
          format: guid
        temporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
        temporaryUntil:
          type: string
          description: >-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual

            grid company.
          format: date-time
          nullable: true
        installationDescription:
          type: string
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        flexAttributeObject:
          type: string
          description: >-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.

            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: guid
          nullable: true
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    AddConnectionPointModel:
      type: object
      description: Add connection point model.
      additionalProperties: false
      required:
        - supplyType
        - tagAssignments
        - priceGroupExcluded
        - address
      properties:
        installationNumber:
          type: string
          description: Installation number.
          nullable: true
        alternativeInstallationNumber:
          type: string
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        description:
          type: string
          description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.
          nullable: true
        address:
          type: string
          description: An UUID reference to a master data address.
          format: guid
        supplyType:
          description: 'Defines usage of Connection Point: “Electricity”, ”Heating”, “Water”.'
          minimum: 1
          maximum: 4
          oneOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/AddTagAssignmentModel'
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributes:
          description: Electricity attributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/ElectricityAttributesUpsertModel'
        heatingAttributes:
          description: Heating attributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/HeatingAttributesUpsertModel'
        waterAttributes:
          description: Water attributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/WaterAttributesUpsertModel'
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form. Defaults to true if left empty.
          type: boolean
          nullable: true
    GetByConnectionPointNumberModel:
      type: object
      description: Allows to obtain ConnectionPoint by it's number.
      additionalProperties: false
      required:
        - connectionPointNumber
      properties:
        connectionPointNumber:
          type: string
          description: Installation number.
          nullable: false
          minLength: 1
          maxLength: 1000
    UpdateConnectionPointModel:
      type: object
      description: Update connection point model.
      additionalProperties: false
      required:
        - supplyType
        - tagAssignments
        - priceGroupExcluded
        - address
        - rowVersion
      properties:
        installationNumber:
          type: string
          description: Installation number.
          nullable: true
        alternativeInstallationNumber:
          type: string
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        description:
          type: string
          description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.
          nullable: true
        address:
          type: string
          description: An UUID reference to a master data address.
          format: guid
        supplyType:
          description: 'Defines usage of Connection Point: “Electricity”, ”Heating”, “Water”.'
          maximum: 2147483647
          minimum: -2147483648
          oneOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/UpdateTagAssignmentModel'
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributes:
          description: Electricity attributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/ElectricityAttributesUpsertModel'
        heatingAttributes:
          description: Heating attributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/HeatingAttributesUpsertModel'
        waterAttributes:
          description: Water attributes.
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/WaterAttributesUpsertModel'
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form. Defaults to true if left empty.
          type: boolean
          nullable: true
        rowVersion:
          type: string
          description: Row version.
          format: byte
    UpdateTagAssignmentModel:
      type: object
      description: Udpate tag assignment model.
      additionalProperties: false
      required:
        - tagCodeListValueId
      properties:
        id:
          type: string
          description: Id.
          format: guid
          nullable: true
        tagCodeListValueId:
          type: string
          description: Tag code list value id.
          format: guid
    AccessInformationModel:
      type: object
      description: Indicates the registered access information that the utility has regarding the address, including contact information, key information, codes, etc.
      additionalProperties: false
      required:
        - type
      properties:
        type:
          allOf:
            - $ref: '#/components/schemas/ShortString'
          description: Indicates where the information is accessed. Ex. "Locked basement door".
          example: 'type'
        contactPerson:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Indicates the name of the contact person who can be contacted to gain access.
          example: 'contact person'
        keyNumber:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: Indicates a possible key number when there are multiple keys.
          example: 'key number'
        keyStorageLocation:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Describes storage location, e.g. '60 / 10 SKB.
          example: 60 / 10 SKB
        keyPlacement:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Describes the location of the key, e.g. key cabinet.
          example: key cabinet
        keyComment:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Various remarks.
          example: 'key comment'
        doorCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: Indicates any code for the door.
          example: 'door code'
        doorCodeComment:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Various remarks on the door code.
          example: 'door code comment'

    MeteringPointVersionsCollectionResponseModel:
      title: MeteringPointVersionsCollectionResponseModel
      description: Metering Point versions response model with metadata.
      type: object
      additionalProperties: false
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          $ref: '#/components/schemas/PositiveInteger'
        pageSize:
          description: Page size defined by client in <1,10000> range.
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
        totalPages:
          $ref: '#/components/schemas/PositiveInteger'
        totalRows:
          $ref: '#/components/schemas/PositiveInteger'
        results:
          description: Metering Point Versions collection.
          type: array
          minItems: 0
          maxItems: 10000
          items:
            $ref: '#/components/schemas/MeteringPointVersion'

    TechnicalMeteringPointVersionsCollectionResponseModel:
      title: MeteringPointVersionsCollectionResponseModel
      description: Metering Point Versions response model with metadata.
      type: object
      additionalProperties: false
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          $ref: '#/components/schemas/PositiveInteger'
        pageSize:
          description: Page size defined by client in <1,10000> range.
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
        totalPages:
          $ref: '#/components/schemas/PositiveInteger'
        totalRows:
          $ref: '#/components/schemas/PositiveInteger'
        results:
          description: Metering Point Versions collection.
          type: array
          minItems: 0
          maxItems: 10000
          items:
            $ref: '#/components/schemas/TechnicalMeteringPointVersion'

    MeteringPointVersion:
      type: object
      description: |
        Metering Point version sub-object. Identified with meteringPointVersionId.
        There can be multiple Metering Point Versions for single Metering Point "container", which is identified within meteringPointProperty.
        Metering Point Version can also have another Metering Point as Parent - identified by parentMeteringPointId.
        If this property is null, it means, that Metering Point Version / Metering Point has no parent assigned.
      additionalProperties: false
      required:
        - meteringPointVersionId
        - meteringPoint
        - occurrence
        - scheduledMeterReadingDates
      properties:
        meteringPointVersionId:
          nullable: false
          description: Id of Metering Point Version.
          allOf:
            - $ref: '#/components/schemas/ShortString'
        meteringPoint:
          description: Reference to Metering Point Container.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MeteringPoint'
        addressId:
          description: CAR Address reference.
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
          example: da85baa6-a66a-11ea-bb37-0242ac130002
        assetType:
          description: Mapped from DH 'MeteringPoint' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MediumString'
        balanceSupplierId:
          nullable: true
          description: Mapped from DH 'balanceSupplierId' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        businessReasonCode:
          nullable: false
          description: Mapped from DH 'businessReasonCode' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        connectionStatus:
          nullable: true
          description: Mapped from DH 'connectionStatus' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        connectionType:
          nullable: true
          description: Mapped from DH 'connectionType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        disconnectionType:
          nullable: true
          description: Mapped from DH 'disconnectionType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        fromNet:
          nullable: true
          description: Mapped from DH 'fromNet' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        locationDescription:
          nullable: true
          description: Mapped from DH 'locationDescription' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/LongString'
        maximumCurrent:
          nullable: true
          description: Mapped from DH 'maximumCurrent' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
        maximumPower:
          nullable: true
          description: Mapped from DH 'maximumPower' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
        meteringGridAreaId:
          nullable: true
          description: Mapped from DH 'meteringGridAreaId' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        meterReadingOccurrence:
          nullable: true
          description: Mapped from DH 'meterReadingOccurrence' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        netSettlementGroup:
          nullable: true
          description: Mapped from DH 'netSettlementGroup' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        placementCategory:
          nullable: true
          description: Mapped from DH 'placementCategory' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        powerSupplierGsrn:
          nullable: true
          description: Mapped from DH 'powerSupplierGsrn' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        productId:
          nullable: true
          description: Mapped from DH 'productId' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        productionCapacityInKiloWatts:
          type: number
          nullable: true
          description: Mapped from DH 'capacityInKiloWatts' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          format: decimal
          minimum: 0
          maximum: 99999999
        productionObligation:
          nullable: true
          description: Mapped from DH 'productionObligation' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        remoteReadable:
          nullable: true
          description: Mapped from DH 'remoteReadable' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        scheduledMeterReadingDates:
          nullable: false
          description: Mapped from DH 'scheduledMeterReadingDate' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          type: array
          minItems: 0
          maxItems: 10000
          items:
            $ref: '#/components/schemas/ShortString'
        settlementMethod:
          nullable: true
          description: Mapped from DH 'settlementMethod' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        subTypeOfMeteringPoint:
          nullable: true
          description: Mapped from DH 'subTypeOfMeteringPoint' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        supplierStatus:
          nullable: true
          description: Mapped from DH 'supplierStatus' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        supplyStart:
          nullable: true
          description: Mapped from DH 'supplyStart' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/DateTime'
        toNet:
          nullable: true
          description: Mapped from DH 'toNet' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        unitType:
          nullable: true
          description: Mapped from DH 'unitType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'
        occurrence:
          nullable: false
          description: Mapped from DH 'occurrence' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/DateTime'
        address:
          type: object
          additionalProperties: false
          nullable: true
          description: DataHub Metering Point Version address.
          oneOf:
            - $ref: '#/components/schemas/DataHubMeteringPointAddress'
        meter:
          type: object
          additionalProperties: false
          nullable: true
          description: DataHub Metering Point Version address.
          oneOf:
            - $ref: '#/components/schemas/DataHubMeter'

    TechnicalMeteringPoint:
      type: object
      description: |
        Technical Metering Point.
      additionalProperties: false
      required:
        - meteringPointId
        - supplyType
      properties:
        meteringPointId:
          type: string
          description: Technical Metering Point Id - GSRN number.
          pattern: ^\d{18}$
          minLength: 18
          maxLength: 18
          example: "123456789012345678"
          nullable: false
        connectionPointId:
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
          description: Connection Point Id - unique technical identifier.
          example: "8902FA98-E40C-4434-ADFF-AA85A80F0FC0"
        connectionPointNumber:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: Connection point number.
          nullable: true
          example: 123456789
        parentMeteringPointId:
          type: string
          description: Technical Metering Point Id - GSRN number.
          pattern: ^\d{18}$
          minLength: 18
          maxLength: 18
          example: "123456789012345678"
          nullable: true
        typeOfMeteringPoint:
          type: string
          description: |-
            Type of Technical Metering Point.

            | CodeListName                                      | DisplayName              | Code | Name                | Translation                |
            |-------------------------------------------------- |--------------------------|------|---------------------|----------------------------|
            | System-TechnicalMeteringPoint-TypeOfMeteringPoint | TKC – Kontrol forbrug    | TKC  | Kontrol forbrug     | Check consumption          |
            | System-TechnicalMeteringPoint-TypeOfMeteringPoint | TKP – Kontrol produktion | TKP  | Kontrol produktion  | Check production           |
            | System-TechnicalMeteringPoint-TypeOfMeteringPoint | TKM – Kontrol målin      | TKM  | Kontrol måling      | Check measurement          |
            | System-TechnicalMeteringPoint-TypeOfMeteringPoint | TKO – Kontrol andet      | TKO  | Kontrol andet       | Check other                |
            | System-TechnicalMeteringPoint-TypeOfMeteringPoint | TO - Andet               | TO   | Andet               | Other                      |

          nullable: true
          minLength: 2
          maxLength: 3
          pattern: "^(TKC|TKP|TKM|TKO|TO)$"
          example: FV18
        supplyType:
          $ref: '#/components/schemas/SupplyType'

    TechnicalMeteringPointVersion:
      type: object
      description: |
        Technical Metering Point Version.
      additionalProperties: false
      properties:
        technicalMeteringPointVersionId:
          type: string
          description: Technical Metering Point Version Id - <GSRN_NUMBER>_<YYYY-MM-DD>.
          nullable: false
          pattern: '^\d{18}_\d{4}-\d{2}-\d{2}$'
          minLength: 18
          maxLength: 30
          example: "123456789012345678_2022-01-13"
        meteringPoint:
          $ref: '#/components/schemas/TechnicalMeteringPoint'
        subTypeOfMeteringPoint:
          type: string
          description: |-
            Must be specified for all metering points. Specifies the nature of the metering point.
            - A physical metering point is a metering point with a physical meter.
            - A virtual metering point is a metering point where the amount of energy is calculated by the utility company.

            | CodeListName                                        | DisplayName        | Code | Name    | Translation |
            |---------------------------------------------------  |------------------- |------|---------|-------------|
            | System-TechnicalMeteringPoint-SubTypeOfMeteringPoint| D01 – Fysisk       | D01  | Fysisk  | Physical    |
            | System-TechnicalMeteringPoint-SubTypeOfMeteringPoint| D02 – Virtuel      | D02  | Virtuel | Virtual     |
          minLength: 3
          maxLength: 3
          pattern: "^(D01|D02)$"
          example: D02
        connectionStatus:
          type: string
          description: |-
            Connection Status of Technical Metering Point.

            | CodeListName                                           | DisplayName        | Code | Name      | Translation  |
            |-----------------------------------------------------   |------------------- |------|-----------|--------------|
            | System-TechnicalMeteringPoint-ConnectionStatus         | D02 – Nedlæg       | D02  | Nedlæg    | Closed down  |
            | System-TechnicalMeteringPoint-ConnectionStatus         | D03 – Ny           | D03  | Ny        | New          |
            | System-TechnicalMeteringPoint-ConnectionStatus         | E22 – Tilsluttet   | E22  | Tilsluttet| Connected    |
            | System-TechnicalMeteringPoint-ConnectionStatus         | E23 – Afbrudt      | E23  | Afbrudt   | Disconnected |

          minLength: 3
          maxLength: 3
          pattern: "^(D02|D03|E22|E23)$"
          example: D03
        readingFrequency:
          type: string
          description: |-
            Reading Frequency of Technical Metering Point.

            | CodeListName                                        | DisplayName    | Code | Name   | Translation |
            |---------------------------------------------------  |--------------- |------|--------|-------------|
            | System-TechnicalMeteringPoint-ReadingFrequency      | P1M            | P1M  | Måned  | Month       |
            | System-TechnicalMeteringPoint-ReadingFrequency      | PT1H           | PT1H | Time   | Hour        |
            | System-TechnicalMeteringPoint-ReadingFrequency      | PT15M          | PT15M| Kvarter| 15 mins     |
            | System-TechnicalMeteringPoint-ReadingFrequency      | P1D            | P1D  | Dag    | Day         |
            | System-TechnicalMeteringPoint-ReadingFrequency      | P1Y            | P1Y  | År     | Year        |

          minLength: 3
          maxLength: 5
          pattern: "^(P1M|PT1H|PT15M|P1D|P1Y)$"
          example: PT1H
        unitType:
          type: string
          description: |-
            Unit Type of Technical Metering Point.

            | CodeListName                                | DisplayName    | Code    | Name                       | Translation                    |
            |-------------------------------------------  |--------------- |-------- |--------------------------- |------------------------------- |
            | System-TechnicalMeteringPoint-UnitType      | MWH            | MWH     | Megawatt timer             | Megawatt hours                 |
            | System-TechnicalMeteringPoint-UnitType      | M3             | M3      | Kubikmeter                 | Cubic Meter                    |
            | System-TechnicalMeteringPoint-UnitType      | Celsius        | Celsius | Celsius                    | Celsius                        |
            | System-TechnicalMeteringPoint-UnitType      | AMP            | AMP     | Ampere                     | Ampere                         |
            | System-TechnicalMeteringPoint-UnitType      | K3             | K3      | kVArh                      | KiloVolt-Ampere reactive hour  |
            | System-TechnicalMeteringPoint-UnitType      | KWH            | KWH     | kWh                        | Kilowatt-hour                  |
            | System-TechnicalMeteringPoint-UnitType      | KWT            | KWT     | kW                         | Kilowatt                       |
            | System-TechnicalMeteringPoint-UnitType      | MAW            | MAW     | MW                         | Megawatt                       |
            | System-TechnicalMeteringPoint-UnitType      | TNE            | TNE     | Tonne                      | Metric ton                     |
            | System-TechnicalMeteringPoint-UnitType      | Z03            | Z03     | MVAr                       | MegaVolt-Ampere reactive power |
            | System-TechnicalMeteringPoint-UnitType      | Z14            | Z14     | Danish Tariff code KT      | Tarifkode                      |
            | System-TechnicalMeteringPoint-UnitType      | H87            | H87     | STK                        | Antal styk                     |

          minLength: 2
          maxLength: 7
          pattern: "^(MWH|M3|Celsius|AMP|K3|KWH|KWT|MAW|TNE|Z03|Z14|H87)$"
          example: AMP
        occurrence:
          nullable: false
          description: |
            Also known as "Valid From". Start of Given Version Validity date.
          allOf:
            - $ref: '#/components/schemas/DateTime'
          example: "2019-11-14T23:00:00.000Z"
        occurrenceTo:
          nullable: true
          description: |
            Also known as "Valid To". End of Given Version Validity date.
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
          example: "2019-11-14T23:00:00.000Z"

    SupplyType:
      type: string
      description: Type of Supply - Electricity / Heating / Water.
      enum:
        - Electricity
        - Heating
        - Water
      example: Electricity

    DataHubMeteringPointAddress:
      type: object
      description: Address object of Metering Point version.
      additionalProperties: false
      properties:
        cityName:
          nullable: true
          description: Mapped from DH 'LocationAddress.cityName' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/LongString'
        citySubDivisionName:
          nullable: true
          description: Mapped from DH 'LocationAddress.citySubDivisionName' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        countryCode:
          nullable: true
          description: Mapped from DH 'LocationAddress.countryName' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        darId:
          nullable: true
          description: Mapped from DH 'LocationAddress.darId' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/Guid'
        door:
          nullable: true
          description: Mapped from DH 'LocationAddress.door' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        floor:
          nullable: true
          description: Mapped from DH 'LocationAddress.floor' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        houseNumber:
          nullable: true
          description: Mapped from DH 'LocationAddress.houseNumber' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        municipalityCode:
          nullable: true
          description: Mapped from DH 'LocationAddress.municipalityCode' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        postalCode:
          nullable: true
          description: Mapped from DH 'LocationAddress.postalCode' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        postBox:
          nullable: true
          description: Mapped from DH 'LocationAddress.postBox' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        protectedAddress:
          nullable: true
          description: Mapped from DH 'LocationAddress.protectedAddress' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        streetCode:
          nullable: true
          description: Mapped from DH 'LocationAddress.streetCode' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        streetName:
          nullable: true
          description: Mapped from DH 'LocationAddress.streetName' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/LongString'
        washable:
          nullable: true
          description: Mapped from DH 'LocationAddress.washable' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'

    DataHubMeter:
      type: object
      description: Meter object of Metering Point version.
      additionalProperties: false
      properties:
        conversionFactor:
          description: Mapped from DH 'conversionFactor' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/Decimal'
        meterNumber:
          description: Mapped from DH 'meterNumber' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        meterReadingType:
          description: Mapped from DH 'meterReadingType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MediumString'
        numberOfDigits:
          description: Mapped from DH 'numberOfDigits' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/OneWordString'
        unitType:
          description: Mapped from DH 'unitType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MediumString'

    MeteringPoint:
      type: object
      description: Metering Point "container" - top level object which aggregates metering point versions, metering point customer versions and price element connection versions.
      additionalProperties: false
      required:
        - meteringPointId
      properties:
        meteringPointId:
          nullable: false
          description: Metering Point Container Id.
          allOf:
            - $ref: '#/components/schemas/ShortString'
        connectionPointId:
          description: Connection point ID.
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
          example: da85baa6-a66a-11ea-bb37-0242ac130002
        connectionPointNumber:
          description: Connection point number.
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 123456789
        typeOfSupply:
          type: string
          description: Type of supply.
          enum:
            - Electricity
            - Water
            - Heat
          example: Electricity
        parentMeteringPointId:
          nullable: true
          description: Mapped from DH 'parentMeteringPointId' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/ShortString'
        typeOfMeteringPoint:
          nullable: true
          description: Mapped from DH 'typeOfMeteringPoint' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
          allOf:
            - $ref: '#/components/schemas/MediumString'

    ConnectionRightInvoiceModel:
      type: object
      description: Connection Right Invoice
      additionalProperties: false
      required:
        - invoiceId
      properties:
        invoiceId:
          type: string
          format: uuid
          description: Invoice Id
          example: '00000000-0000-0000-0000-000000000000'
        invoiceNumber:
          type: string
          description: Invoice number returned from ERP.
          maxLength: 25
          pattern: '^.*$'
          nullable: true
          example: "1234567890123456789012345"

    ConnectionRightsTotalValueBody:
      type: object
      additionalProperties: false
      description: Request to ask for connection rights total value.
      properties:
        meterFrameId:
          $ref: '#/components/schemas/GuidField'

    ConnectionRightsTotalValueModel:
      type: object
      required:
        - unit
        - totalValue
      properties:
        unit:
          $ref: '#/components/schemas/ConnectionRightElectricityUnitType'
        totalValue:
          $ref: '#/components/schemas/PositiveInteger'
      additionalProperties: false

    MainBranchLineElectricityModel:
      title: MainBranchLineElectricityModel
      description: MainBranchLineElectricityModel
      type: object
      additionalProperties: false
      properties:
        connectedMeterFrameId:
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
          description: Id of related meterFrameId, when connected via other branchline
        branchLineNumber:
          allOf:
            - $ref: '#/components/schemas/ShortString'
          description: 'Number on branch line. Ex. XF2500.'
          example: 'XF2500'
          nullable: false
        branchLineType:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Branch line type.'
          example: 'ShortStringWithoutSpaces'
          nullable: true
        materialCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Material code.'
          example: 'Material code'
          nullable: true
        neutralSquare:
          allOf:
            - $ref: '#/components/schemas/DecimalNullable'
          description: 'Neutral square.'
          example: 1
          nullable: true
        numberOfCables:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of cables.'
          example: 1
          nullable: true
        numberOfConductors:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of conductors.'
          example: 1
          nullable: true
        systemGrounding:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'System Grounding.'
          example: 'SystemGrounding'
          nullable: true

    MainBranchLineElectricityCreateUpdateModel:
      title: MainBranchLineElectricityCreateUpdateModel
      description: MainBranchLineElectricityCreateUpdateModel
      type: object
      additionalProperties: false
      properties:
        connectedMeterFrameId:
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          description: Id of related meterFrameId, when connected via other branchline
        branchLineType:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Branch line type.'
          example: 'ShortStringWithoutSpaces'
          nullable: true
        materialCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Material code.'
          example: 'Material code'
          nullable: true
        neutralSquare:
          allOf:
            - $ref: '#/components/schemas/DecimalNullable'
          description: 'Neutral square.'
          example: 1
          nullable: true
        numberOfCables:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of cables.'
          example: 1
          nullable: true
        numberOfConductors:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of conductors.'
          example: 1
          nullable: true
        systemGrounding:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'System Grounding.'
          example: 'SystemGrounding'
          nullable: true

    ReserveBranchLineElectricityModel:
      title: ReserveBranchLineElectricityModel
      description: ReserveBranchLineElectricityModel
      type: object
      additionalProperties: false
      properties:
        branchLineNumber:
          allOf:
            - $ref: '#/components/schemas/ShortString'
          description: 'Number on branch line. Ex. XF2500.'
          example: 'XF2500'
          nullable: false
        branchLineType:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Branch line type.'
          example: 'ShortStringWithoutSpaces'
          nullable: true
        materialCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Material code.'
          example: 'Material code'
          nullable: true
        neutralSquare:
          allOf:
            - $ref: '#/components/schemas/DecimalNullable'
          description: 'Neutral square.'
          example: 1
          nullable: true
        numberOfCables:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of cables.'
          example: 1
          nullable: true
        numberOfConductors:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of conductors.'
          example: 1
          nullable: true
        systemGrounding:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'System Grounding.'
          example: 'SystemGrounding'
          nullable: true

    ReserveBranchLineElectricityCreateUpdateModel:
      title: ReserveBranchLineElectricityCreateUpdateModel
      description: ReserveBranchLineElectricityCreateUpdateModel
      type: object
      additionalProperties: false
      properties:
        branchLineType:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Branch line type.'
          example: 'ShortStringWithoutSpaces'
          nullable: true
        materialCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Material code.'
          example: 'Material code'
          nullable: true
        neutralSquare:
          allOf:
            - $ref: '#/components/schemas/DecimalNullable'
          description: 'Neutral square.'
          example: 1
          nullable: true
        numberOfCables:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of cables.'
          example: 1
          nullable: true
        numberOfConductors:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of conductors.'
          example: 1
          nullable: true
        systemGrounding:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'System Grounding.'
          example: 'SystemGrounding'
          nullable: true

    MainBranchLineWaterModel:
      title: MainBranchLineWaterModel
      description: MainBranchLineWaterModel
      type: object
      additionalProperties: false
      properties:
        connectedMeterFrameId:
          allOf:
            - $ref: '#/components/schemas/GuidNullable'
          description: Id of related meterFrameId, when connected via other branchline
        branchLineSize:
          allOf:
            - $ref: '#/components/schemas/OneWordString'
          description: 'Number on branch line. Ex. XF2500.'
          example: 'XF2500'
          nullable: false

    ReserveBranchLineWaterModel:
      title: ReserveBranchLineWaterModel
      description: ReserveBranchLineWaterModel
      type: object
      additionalProperties: false
      properties:
        branchLineSize:
          allOf:
            - $ref: '#/components/schemas/OneWordString'
          description: 'Number on branch line. Ex. XF2500.'
          example: 'XF2500'
          nullable: false

    CalculateDefaultValidFromBody:
      type: object
      additionalProperties: false
      description: Model for calculating default valid from date for specific meter frame.
      required:
        - meterFrameId
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          example: 58acc521-de2c-49ff-b233-47a77f697517
          allOf:
            - $ref: '#/components/schemas/GuidField'

    CalculateDefaultValidFromResult:
      type: object
      additionalProperties: false
      description: Model for returning calculated default valid from date for specific meter frame.
      required:
        - defaultValidFrom
      properties:
        defaultValidFrom:
          description: Default valid from date
          example: "2023-07-01T22:00:00Z"
          allOf:
            - $ref: '#/components/schemas/DateTime'

    # --------------------------------------------------------- COMMON DATA TYPES --------------------------------------------------------------------------

    PositiveInteger:
      type: integer
      format: int32
      minimum: 0
      maximum: 2147483647
      description: 'Integer type in <0-2147483647> range.'
      example: 111

    PositiveIntegerNullable:
      type: integer
      format: int32
      nullable: true
      minimum: 0
      maximum: 2147483647
      description: 'Integer type in <0-2147483647> range.'
      example: 111

    LongString:
      pattern: "^.*$"
      type: string
      minLength: 0
      maxLength: 256
      description: 'Max. 256 characters long string with spaces.'
      example: 'Max. 256 characters long string with spaces.'

    LongStringNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 0
      maxLength: 256
      description: 'Max. 256 characters long string with spaces.'
      example: 'Max. 256 characters long string with spaces.'

    Decimal:
      type: number
      format: decimal
      description: 'Decimal value.'
      example: 1.02

    DateTime:
      type: string
      description: 'Date in UTC ISO 8601 format.'
      format: date-time
      example: 2019-11-14T00:55:31.820Z

    DateTimeNullable:
      type: string
      nullable: true
      description: 'Date in UTC ISO 8601 format.'
      format: date-time
      example: 2019-11-14T00:55:31.820Z

    Guid:
      type: string
      description: 'Guid field.'
      format: uuid
      nullable: false
      example: da85baa6-a66a-11ea-bb37-0242ac130002

    GuidNullable:
      nullable: true
      type: string
      description: 'Guid field.'
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130002

    OneWordStringNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 0
      maxLength: 25
      description: 'Max. 25 characters long string with spaces.'
      example: 'Max. 25 chars. w. spaces.'

    ShortString:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 50
      description: 'Max. 50 characters long string with spaces.'
      example: 'Max. 50 characters long string with spaces.'

    ShortStringObsoleteNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 0
      maxLength: 128
      description: 'Max. 128 characters long string with spaces.'
      example: 'Max. 128 characters long string with spaces.'

    MediumString:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 100
      description: 'Max. 100 characters long string with spaces.'
      example: 'Max. 100 characters long string with spaces.'

    MediumStringNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 0
      maxLength: 100
      description: 'Max. 100 characters long string with spaces.'
      example: 'Max. 100 characters long string with spaces.'

    IntegerNullable:
      type: integer
      nullable: true
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Integer type in <-2147483648,2147483647> range.'
      example: 111

    DecimalNullable:
      type: number
      description: 'Decimal'
      format: decimal
      nullable: true

    RowVersion:
      type: string
      description: RowVersion.
      format: byte
      example: "AAAAAAAAB+I="

  responses:
    "201":
      description: Successfully created.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/IdResult'
    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ValidationProblemDetails'
          examples:
            ItIsABadRequest:
              value:
                type: 'https://errors.kmdelements.com/400'
                title: Bad Request
                status: 400
                detail: 'Invalid request'
                instance: /resources-path/1
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            YouShallNotPass:
              value:
                type: 'https://errors.kmdelements.com/401'
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources-path/1
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            CannotTouchThis:
              value:
                type: 'https://errors.kmdelements.com/403'
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources-path/1
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ItWasHere:
              value:
                type: 'https://errors.kmdelements.com/404'
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources-path/1
    "409":
      description: Conflict - entity updated concurrently and/or incorrect rowversion passed and/or resource is conflicting with unique constraint.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ItsConflictingOrSomethingElseChangedIt:
              value:
                type: 'https://errors.kmdelements.com/409'
                title: Conflict
                status: 409
                detail: Conflict
                instance: /resources-path/1
    "422":
      description: 422 Unprocessable.
      content:
        application/problem+json:
          schema:
            description: 422 Unprocessable.
            type: array
            maxItems: 1000000
            items:
              $ref: '#/components/schemas/ErrorDescription'
          examples:
            CustomValidationsIncorrect:
              value:
                - errorCode: 'Translatable error code.'
                  defaultMessage: 'Default error description in english.'
                - errorCode: 'Other translatable error code.'
                  defaultMessage: 'Other error description in english.'
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
            example: 360
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            TooFastTooQuicklyTooSoon:
              value:
                type: 'https://errors.kmdelements.com/429'
                title: Too Many Requests
                status: 360
                detail: Rate limit is exceeded.
                instance: /resources-path/1
    "499":
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            NotFoundExample:
              value:
                type: 'https://errors.kmdelements.com/499'
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: /resources-path/1
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ThisShouldNotHappen:
              value:
                type: 'https://errors.kmdelements.com/500'
                title: Internal Server Error
                status: 500
                detail: 'body.0.age: Value `Not Int` does not match format `int32`'
                instance: /resources-path/1
    "502":
      description: 502 Bad Gateway.
    "503":
      description: 503 Service Unavailable.
    "504":
      description: 504 Gateway Timeout.
  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
tags:
  - name: Home
    description: Root API.
  - name: Configuration
    description: API for environment configuration
  - name: CodeList
    description: API for Code Lists management.
  - name: InternalEquipment
    description: API for Internal Equipments management.
  - name: MeterInputConnection
    description: API for Meter input connection.
  - name: RegisterRequirement
    description: API for Register Requirement management.
  - name: SpecialAgreement
    description: API for Special Agreements management.
  - name: Translations
    description: API for Translations.
  - name: MeterFrame
    description: API for Meter Frames management.
  - name: MeterFrameExternalEquipmentAssignments
    description: API for managing External Equipments assignments of Meter Frame.
  - name: MeterFrameInternalEquipmentAssignments
    description: API for managing Internal Equipments assignments of Meter Frame.
  - name: ConnectionPoint
    description: API for Connection Points management.
  - name: ConnectionPointExternalEquipmentAssignments
    description: API for managing External Equipments assignments of Connection Point.
  - name: ConnectionPointInternalEquipmentAssignments
    description: API for managing Internal Equipments assignments of Connection Point.
  - name: BatchImport
    description: API for batch imports.

