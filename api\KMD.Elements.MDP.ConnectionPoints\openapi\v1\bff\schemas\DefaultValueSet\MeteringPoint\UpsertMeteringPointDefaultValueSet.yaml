type: object
description: Model used to get Metering Point Default Value Set details.
additionalProperties: false
required:
  - supplyType
  - commonMeteringPointDefaultValueSet
properties:
  id:
    description: Metering Point Default Value Set identifier.
    allOf:
      - $ref: "../../DataTypes/GuidNullable.yaml"
  supplyType:
    $ref: '../../SupplyType.yaml'
    description: Supply type of the default value set related to Metering Point.
  commonMeteringPointDefaultValueSet:
    description: The base attributes of Metering Point Default Value Set.
    allOf:
      - $ref: './CommonMeteringPointDefaultValueSet.yaml'
  electricityMeteringPointDefaultValueSet:
    description: Electricity attributes of Metering Point Default Value Set.
    allOf:
      - $ref: './ElectricityMeteringPointDefaultValueSet.yaml'
    nullable: true
  waterMeteringPointDefaultValueSet:
    description: Water attributes of Metering Point Default Value Set.
    allOf:
      - $ref: './WaterMeteringPointDefaultValueSet.yaml'
    nullable: true
  heatingMeteringPointDefaultValueSet:
    description: Heating attributes of Metering Point Default Value Set.
    allOf:
      - $ref: './HeatingMeteringPointDefaultValueSet.yaml'
    nullable: true
  rowVersion:
    description: Row version.
    allOf:
      - $ref: '../../DataTypes/RowVersion.yaml'
    nullable: true
