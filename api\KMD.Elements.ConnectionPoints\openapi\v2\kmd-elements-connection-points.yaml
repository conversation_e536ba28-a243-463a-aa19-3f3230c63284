openapi: 3.0.3

info:
  title: KMD.Elements.ConnectionPoints.Api
  x-maintainers: Team-MD-2
  description: |-
    # KMD Elements - Connection Points Api API
    Stability level: V2-PREVIEW
    <br/>
    <br/>
    The **KMD Elements Connection Points API** is part of the KMD Element product.
    <br/>

    ## Capabilities
    The API allows to:
    - search, identify and retrieve details of Connection Points
    ---

  termsOfService: https://www.kmd.net/terms-of-use

  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>

  license:
    name: License
    url: https://www.kmd.net/terms-of-use

  version: '2.43'

servers:
  - url: "/"
  - url: "/api/connection-points-api"

security:
  - Jwt: [ ]

tags:
  - name: MeterFrame
    description: MeterFrames operations.
  - name: MeterAttributeRequirements
    description: Section with direct access to Meter Attribute Requirements.
  - name: MeterComponentRequirements
    description: Section with direct access to Meter Component Requirements.
  - name: ConnectionRights
    description: ConnectionRights.
  - name: RegisterRequirements
    description: RegisterRequirements.
  - name: MeteringPoints
    description: Metering points operations.
  - name: ConnectionPoint
    description: API for Connection Points management.
  - name: MeterInputConnections
    description: API for managing information about assigning Meter to a Meter Frame in certain period of time.
  - name: GisTopology
    description: API for infrastructure topology operations.
  - name: BailiffCases
    description: API for Bailiff cases management.
  - name: AccessInformation
    description: API for Access Information management.

paths:
  /v2/access-information:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - AccessInformation
      summary: Creates Access Information.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postAccessInformation
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/access-information/schemas/AddAccessInformationModel.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
  /v2/access-information/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - AccessInformation
      summary: Gets Access Information by Id.
      description: |-
        ### Remarks
        Gets Access Information by Id.
      operationId: getAccessInformationById
      parameters:
        - $ref: './parameters/access-information/AccessInformationId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      responses:
        '200':
          description: Found Access Information.
          content:
            application/json:
              schema:
                $ref: './schemas/access-information/schemas/GetAccessInformationModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - AccessInformation
      summary: Gets Access Information by Id.
      description: |-
        ### Remarks
        Updates Access Information by Id.
      operationId: updateAccessInformation
      parameters:
        - $ref: './parameters/access-information/AccessInformationId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/access-information/schemas/UpdateAccessInformationModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - AccessInformation
      summary: Allows to delete Access Information.
      description: Deletes Access Information.
      operationId: deleteAccessInformation
      parameters:
        - $ref: './parameters/access-information/AccessInformationId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/access-information/commands/get-access-information-list-by-meter-frame-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - AccessInformation
      summary: Fetches list of Access Information.
      description: |-
        ### Result
        Returns filtered and paged list of Access Informations by Meter Frame Id.
      operationId: getAccessInformationListByMeterFrameId
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/access-information/schemas/GetAccessInformationListByMeterFrameIdModel.yaml'
        required: true
      responses:
        '200':
          description: Access Informations list returned successfully.
          content:
            application/json:
              schema:
                $ref: './schemas/access-information/schemas/GetAccessInformationListModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/bailiff-cases:
    put:
      tags:
        - BailiffCases
      summary: Allows to Create or Update bailiff case for meter frame.
      operationId: addOrUpdateBailiffCase
      description: Add or Update bailiff case request.
      x-authorization: BailiffCase.Write
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/bailiff-case/AddOrUpdateBailiffCaseModel.yaml'
        required: true
      responses:
        "200":
          description: Added or Updated Bailiff Case identifier (GUID).
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
        "504":
          $ref: "./responses/common/504.yaml"

  /v2/meter-frames/{meterFrameId}/bailiff-cases:
    get:
      tags:
        - MeterFrame
      summary: Get list of meter frame bailiff cases.
      operationId: getBailiffCases
      description: Get list of meter frame bailiff cases.
      x-authorization: BailiffCase.Read
      parameters:
        - $ref: './parameters/meter-frames.yaml#/components/parameters/MeterFrameId'
      responses:
        "200":
          description: List of meter frame bailiff cases.
          content:
            application/json:
              schema:
                description: Array containing bailiff cases for the given meter frame.
                nullable: false
                type: array
                minItems: 0
                maxItems: 100
                items:
                  $ref: './schemas/bailiff-case/BailiffCaseDetailsModel.yaml'
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
        "504":
          $ref: "./responses/common/504.yaml"

  /v2/meter-frames/{meterFrameId}/no-meter:
    patch:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Updates 'NoMeter' value on a MeterFrame.
      description: |-
        ### Remarks
        Updates 'NoMeter' value on a MeterFrame.
      operationId: setNoMeterOnMeterFrame
      parameters:
        - $ref: './parameters/meter-frames.yaml#/components/parameters/MeterFrameId'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/SetNoMeterOnMeterFrameRequest.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
           $ref: "./responses/common.yaml#/components/responses/401"
        '403':
           $ref: "./responses/common.yaml#/components/responses/403"
        '409':
           $ref: "./responses/common.yaml#/components/responses/409"
        '422':
           $ref: "./responses/common.yaml#/components/responses/422"
  /v2/meter-frames/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets paged Meter Frames for passed query parameters.
      description: Gets paged Meter Frames for passed query parameters.
      operationId: getPagedMeterFrames
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/commands/SearchMeterFrame.yaml'
        required: true
      responses:
        '200':
          description: Meter Frames collection.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/PagedResponseOfGetMeterFrameSearchModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/commands/get-meter-frame-by-metering-point-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Fetches list of Meter Frames based on related meteringpoint id.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Meter Frames according to passed metering point id.
      operationId: getMeterFrameByMeteringPointId
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/queries/GetMeterFramesListByMeteringPointIdQueryModel.yaml'
        required: true
      responses:
        '200':
          description: Meter Frames returned successfully.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/PagedResponseOfPostMeterFrameSearchModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames:
     post:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Creates Meter Frame.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postMeterFrame
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/AddMeterFrameModel.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
  /v2/meter-frames/{meterFrameId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets specific Meter Frame.
      description: Gets specific Meter Frame by technical identifier.
      operationId: getMeterFrameById
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
      responses:
        '200':
          description: Found Meter Frame.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/GetMeterFrameModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update Meter Frame.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrame
      parameters:
        - $ref: './parameters/meter-frames/MeterFrameId.yaml'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/UpdateMeterFrameModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/commands/get-meter-frame-by-number:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: getMeterFrameModelByNumber
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/MeterFrameNumberInSchema.yaml'
        required: true
      responses:
        '200':
          description: Found Meter Frame.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/GetMeterFrameModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/commands/history-as-of:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame history entry as of date.
      description: Gets Meter Frame history entry as of date - historical data by temporal table.
      operationId: getMeterFrameAsOfHistory
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/HistoryAsOf.yaml'
        required: true
      responses:
        '200':
          description: Meter Frame history entry for given parameters.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/MeterFrameWithValidityPeriodModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/commands/existing-by-number:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Checks if Meter Frame exists.
      description: Check if Meter Frame with exact meter frame number exists within connection point.
      operationId: checkIfMeterFrameExistsByNumber
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/MeterFrameExistByNumberModel.yaml'
        required: true
      responses:
        '200':
          description: Meter Frame exists.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/GetMeterFrameExistsResult.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'


  /v2/meter-frames/commands/obtain-meter-frames-by-meter-frames-ids:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: >-
        Get Meter Frames collection by id collection passed in request body.
        Each response model contains metadata with information whether given meter frame was found.
        Maximum 1000 ids in body.
      description: >-
        Get Meter Frames collection by id collection passed in request body.
        Each response model contains metadata with information whether given meter frame was found.
        Maximum 1000 ids in body.
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/MeterFrameIdsModel.yaml'
        required: true
      operationId: getMeterFramesByMeterFramesIds
      responses:
        '200':
          description: Meter Frames collection with metadata. If meter frame searched by given ID does not exist, sub meterFrame property will be null and isFound marked as false.
          content:
            application/json:
              schema:
                description: Meter Frames collection with metadata. If meter frame searched by given ID does not exist, sub meterFrame property will be null and isFound marked as false.
                type: array
                maxItems: 1000000
                items:
                  $ref: './schemas/meter-frame/schemas/MeterFrameWithMetadata.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/commands/get-meter-frames-with-unrelated-branch-line:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - Returns meter frames with meter frame numbers containing passed parameter. Returned MFs have filled out branch lines and no connected meter frame id.
      operationId: getMeterFramesWithUnrelatedBranchLine
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/SearchByMeterFrameNumberPagedModel.yaml'
        required: true
      responses:
        '200':
          description: Found Meter Frames.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-frame/schemas/PagedResponseOfGetMeterFramesBasicModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/commands/get-meter-frames-with-related-branch-line:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - Returns meter frames with meter frame numbers containing passed parameter. Returned MFs have filled out branch lines and no connected meter frame id.
      operationId: getMeterFramesWithRelatedBranchLines
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/MeterFrameIdWithSupplyTypeInSchema.yaml'
        required: true
      responses:
        '200':
          description: Found Meter Frames.
          content:
            application/json:
              schema:
                type: array
                description: Results array.
                maxItems: 1000000
                items:
                  $ref: './schemas/meter-frame/schemas/GetMeterFrameBasicModel.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-frames/commands/import:
    post:
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Migrations.Write
      tags:
        - MeterFrame
      summary: Imports Meter Frames.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: importMeterFrame
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/meter-frame/schemas/ImportMeterFrameModel.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '409':
          $ref: './responses/common.yaml#/components/responses/409'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'

  /v2/connection-points:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: Creates Connection Point.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postConnectionPoint
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-point/AddConnectionPointModel.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
           $ref: "./responses/common.yaml#/components/responses/401"
        '403':
           $ref: "./responses/common.yaml#/components/responses/403"
        '409':
           $ref: "./responses/common.yaml#/components/responses/409"
        '422':
           $ref: "./responses/common.yaml#/components/responses/422"
  /v2/connection-points/{id}:
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: Allows to update Connection Point.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putConnectionPoint
      parameters:
        - $ref: './parameters/connection-points.yaml#/components/parameters/ConnectionPointId'
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/connection-point/UpdateConnectionPointModel.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
           $ref: "./responses/common.yaml#/components/responses/401"
        '403':
           $ref: "./responses/common.yaml#/components/responses/403"
        '404':
           $ref: "./responses/common.yaml#/components/responses/404"
        '409':
           $ref: "./responses/common.yaml#/components/responses/409"
        '422':
           $ref: "./responses/common.yaml#/components/responses/422"
        '429':
           $ref: "./responses/common.yaml#/components/responses/429"
        '500':
           $ref: "./responses/common.yaml#/components/responses/500"
        '502':
           $ref: "./responses/common.yaml#/components/responses/502"
        '503':
           $ref: "./responses/common.yaml#/components/responses/503"
        '504':
           $ref: "./responses/common.yaml#/components/responses/504"

  /v2/metering-points/metering-point-versions/commands/get-latest-by-register-requirement-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeteringPoints
      summary: Gets Metering Point with latest versions by Register Requirement ID
      description: Gets Metering Point with latest versions by Register Requirement ID
      operationId: getMeteringPointWithLatestVersionsByRegisterRequirementId
      requestBody:
        description: Gets Metering Point with latest versions by Register Requirement ID.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/metering-point-version/GetMeteringPointsWithLatestVersionByRegisterRequirementIdCommand.yaml'
      responses:
        '200':
          description: Returns Metering Point with latest versions by Register Requirement ID
          content:
            application/json:
              schema:
                description: Returns Metering Point with latest versions by Register Requirement ID collection
                nullable: false
                type: array
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/metering-point/MeteringPoint.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/metering-points/metering-point-versions/commands/get-latest-by-meter-frame-id-with-latest-formula-version:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeteringPoints
      summary: Gets Metering Points with latest version and formula version by Meter Frame ID
      description: Gets Metering Points with latest version and formula version by Meter Frame ID
      operationId: getMeteringPointsWithLatestVersionAndFormulaVersionByMeterFrameId
      requestBody:
        description: Get Metering Points with latest version and formula version by Meter Frame ID.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/metering-point-version/GetMeteringPointsWithLatestVersionAndFormulaVersionByMeterFrameIdCommand.yaml'
      responses:
        '200':
          description: Returns Metering Points with latest version and formula version by by Meter Frame ID
          content:
            application/json:
              schema:
                description: Returns Metering Points with latest version and formula version by by Meter Frame ID collection
                nullable: false
                type: array
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/metering-point-version.yaml#/components/schemas/GetMeteringPointWithLatestVersionAndFormulaVersionWithMeteringComponentIdResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/metering-points/commands/get-paged-latest-parents-with-version-and-formula-by-connection-point-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeteringPoints
      summary: Gets paged parent Metering Points with latest version and formula version by Connection Point ID
      description: Gets paged parent Metering Points with latest version and formula version by Connection Point ID.
      operationId: getPagedParentMeteringPointsWithLatestVersionAndFormulaVersionByConnectionPointId
      requestBody:
        description: Gets paged parent Metering Points with latest version and formula version by Connection Point ID.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/metering-point.yaml#/components/schemas/GetPagedLatestParentsWithVersionAndFormulaByConnectionPointIdCommand'
      responses:
        '200':
          description: Returns paged parent Metering Points with latest version and formula version by Connection Point ID.
          content:
            application/json:
              schema:
                description: Returns page of parent Metering Points with latest version and formula version by Connection Point ID.
                nullable: false
                allOf:
                  - $ref: './schemas/metering-point.yaml#/components/schemas/GetPagedLatestParentsWithVersionAndFormulaByConnectionPointIdResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/metering-points/commands/get-latest-children-version-and-formula-by-parent-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeteringPoints
      summary: Gets children Metering Points with latest version and formula version by Parent ID
      description: Gets children Metering Points with latest version and formula version by Parent ID.
      operationId: getChildrenMeteringPointsWithLatestVersionAndFormulaVersionByParentId
      requestBody:
        description: Gets children Metering Points with latest version and formula version by Parent ID.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/metering-point.yaml#/components/schemas/GetChildrenMeteringPointsWithLatestVersionAndFormulaVersionByParentIdCommand'
      responses:
        '200':
          description: Returns children Metering Points with latest version and formula version by Parent ID.
          content:
            application/json:
              schema:
                description: Returns children Metering Points with latest version and formula version by Parent ID collection.
                nullable: false
                type: array
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/metering-point/MeteringPoint.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-attribute-requirements/configuration:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterAttributeRequirements
      summary: Gets Meter Attribute Requirements configuration.
      description: Gets Meter Attribute Requirements configuration.
      operationId: getMeterAttributeRequirementsConfiguration
      responses:
        '200':
          description: Returns Meter Attribute Requirements configuration.
          content:
            application/json:
              schema:
                description: Returns Meter Attribute Requirements configuration object.
                nullable: false
                allOf:
                  - $ref: './schemas/meter-attribute-requirement-configuration.yaml#/components/schemas/MeterAttributeRequirementsConfiguration'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-attribute-requirements/{meterAttributeRequirementsId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterAttributeRequirements
      summary: Gets Meter Attribute Requirements collection by Meter Attribute Requirements identifier.
      description: Gets Meter Attribute Requirements collection by Meter Attribute Requirements identifier.
      operationId: getMeterAttributeRequirementsById
      parameters:
        - $ref: './parameters/meter-attribute-requirements.yaml#/components/parameters/MeterAttributeRequirementsId'
      responses:
        '200':
          description: Returns Meter Attribute Requirements collection or NULL if not found.
          content:
            application/json:
              schema:
                description: Meter Attribute Requirements result object.
                nullable: true
                allOf:
                  - $ref: './schemas/meter-attribute-requirement.yaml#/components/schemas/MeterAttributeRequirementsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterAttributeRequirements
      summary: Allows to Create or Update Meter Attribute Requirements.
      description: Allows to Create or Update Meter Attribute Requirements.
      operationId: addOrUpdateMeterAttributeRequirements
      requestBody:
        description: Meter Attribute Requirements collection to upsert.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/meter-attribute-requirement.yaml#/components/schemas/MeterAttributeRequirementsRequest'
      parameters:
        - $ref: './parameters/meter-attribute-requirements.yaml#/components/parameters/MeterAttributeRequirementsId'
      responses:
        '204':
          description: Meter Attribute Requirements created or updated successfully.
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-attribute-requirements/get-by-meter-frame-id/{meterFrameId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterAttributeRequirements
      summary: Gets Meter Attribute Requirements collection by Meter Frame identifier.
      description: Gets Meter Attribute Requirements collection by Meter Frame identifier.
      operationId: getMeterAttributeRequirementsByMeterFrameId
      parameters:
        - $ref: './parameters/meter-frames.yaml#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Returns Meter Attribute Requirement or NULL if not found.
          content:
            application/json:
              schema:
                description: Meter Attribute Requirements result object.
                nullable: true
                allOf:
                  - $ref: './schemas/meter-attribute-requirement.yaml#/components/schemas/MeterAttributeRequirementsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-component-requirements/configuration:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterComponentRequirements
      summary: Gets Meter Component Requirements configuration.
      description: Gets Meter Component Requirements configuration.
      operationId: getMeterComponentRequirementsConfiguration
      responses:
        '200':
          description: Returns Meter Component Requirements configuration.
          content:
            application/json:
              schema:
                description: Returns Meter Component Requirements configuration object.
                nullable: false
                allOf:
                  - $ref: './schemas/meter-component-requirement-configuration.yaml#/components/schemas/MeterComponentRequirementsConfiguration'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-component-requirements/{meterComponentRequirementsId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterComponentRequirements
      summary: Gets Meter Component Requirements collection by Meter Component Requirements identifier.
      description: Gets Meter Component Requirements collection by Meter Component Requirements identifier.
      operationId: getMeterComponentRequirementsById
      parameters:
        - $ref: './parameters/meter-component-requirements.yaml#/components/parameters/MeterComponentRequirementsId'
      responses:
        '200':
          description: Returns Meter Component Requirements collection or NULL if not found.
          content:
            application/json:
              schema:
                description: Meter Component Requirements result object.
                nullable: true
                allOf:
                  - $ref: './schemas/meter-component-requirement.yaml#/components/schemas/MeterComponentRequirementsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterComponentRequirements
      summary: Allows to Create or Update Meter Component Requirements.
      description: Allows to Create or Update Meter Component Requirements.
      operationId: addOrUpdateMeterComponentRequirements
      requestBody:
        description: Meter Component Requirements collection to be upsert.
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/meter-component-requirement.yaml#/components/schemas/MeterComponentRequirementsRequest'
      parameters:
        - $ref: './parameters/meter-component-requirements.yaml#/components/parameters/MeterComponentRequirementsId'
      responses:
        '204':
          description: Meter Component Requirements created or updated successfully.
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-component-requirements/get-by-meter-frame-id/{meterFrameId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterComponentRequirements
      summary: Gets Meter Component Requirements collection by Meter Frame identifier.
      description: Gets Meter Component Requirements collection by Meter Frame identifier.
      operationId: getMeterComponentRequirementsByMeterFrameId
      parameters:
        - $ref: './parameters/meter-frames.yaml#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Returns Meter Component Requirement or NULL if not found.
          content:
            application/json:
              schema:
                description: Meter Component Requirements result object.
                nullable: true
                allOf:
                  - $ref: './schemas/meter-component-requirement.yaml#/components/schemas/MeterComponentRequirementsResponse'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights:
    get:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Fetches Connection Rights list by meter frame
      description: |-
        ### Result
        Returns top connection rights according to query parameters.
      operationId: getConnectionRightsByMeterFrameId
      parameters:
        - name: meterFrameId
          in: query
          description: Id of the Meter Frame.
          required: true
          schema:
            $ref: './schemas/_simple-type/Guid.yaml'
          example: 4d14817c-b622-493b-a515-284c0872d15d
        - name: installationFormId
          in: query
          description: Id of installation form.
          required: false
          schema:
            $ref: './schemas/_simple-type/GuidNullable.yaml'
          example: 4d14817c-b622-493b-a515-284c0872d15d
        - name: onlyWithoutInvoice
          in: query
          description: Should return only connection rights without invoice.
          required: false
          schema:
            $ref: './schemas/_simple-type/BooleanNullable.yaml'
          example: true
        - name: supplyType
          in: query
          description: SupplyType
          required: false
          schema:
            $ref: './schemas/common/SupplyType.yaml'
          example: true
      responses:
        '200':
          description: Connection Rights returned successfully.
          content:
            application/json:
              schema:
                description: Connection Rights returned successfully.
                type: array
                maxItems: 1000000
                items:
                  $ref: './schemas/connection-rights/ConnectionRightDetails.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Create Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: createConnectionRight
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/CreateConnectionRightPayload.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights/{id}:
    put:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Update Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: updateConnectionRight
      parameters:
        - name: id
          in: path
          required: true
          description: Id.
          schema:
           type: string
           format: uuid
          example: ceb572ab-0d9a-4842-a842-f827ccd1f044
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/UpdateConnectionRightPayload.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    get:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Gets specific Connection Right.
      operationId: getConnectionRightById
      description: Returns specific Connection Right by technical GUID identifier.
      parameters:
        - name: id
          in: path
          required: true
          description: Id.
          schema:
           type: string
           format: uuid
          example: ceb572ab-0d9a-4842-a842-f827ccd1f044
      responses:
        '200':
          description: Found Connection Right.
          content:
            application/json:
              schema:
                $ref: './schemas/connection-rights/ConnectionRightDetails.yaml'
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'
    delete:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Allows to delete Connection Rights.
      description: Deletes Connection Right.
      operationId: deleteConnectionRight
      parameters:
        - name: id
          in: path
          required: true
          description: Id.
          schema:
           type: string
           format: uuid
          example: ceb572ab-0d9a-4842-a842-f827ccd1f044
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights/command/import:
    post:
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Migrations.Write
      tags:
        - ConnectionRights
      summary: Imports Connection Rights.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: importConnectionRights
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/ImportConnectionRightsPayload.yaml'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: './schemas/common/IdResult.yaml'
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights/commands/transfer:
    post:
      x-authorization: ConnectionRights.Write
      tags:
          - ConnectionRights
      summary: Transfers one connection right into many, many into one or one to one. Transfer deactivates (sets valid to date) on transferred connection right.
      operationId: transferConnectionRights
      description: |-
        Transfers connection rights.
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/ConnectionRightTransfer.yaml'
        required: true
      responses:
        '204':
          description: Successfully transferred.
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/connection-rights/commands/stop:
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Stop Connection Right.
      description: Passed model has to meet validation requirements described below.
      operationId: stopConnectionRight
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/StopConnectionRightPayload.yaml'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'


  /v2/connection-rights/total-value:
    post:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Get Total Values.
      description: Get Total Values per unit.
      operationId: getTotalValues
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/connection-rights/ConnectionRightsTotalValuesPayload.yaml'
        required: true
      responses:
        '200':
          description: Successfully calculated.
          content:
            application/json:
              schema:
                description: Connection Rights returned successfully.
                type: array
                maxItems: 1000
                items:
                  $ref: './schemas/connection-rights/ConnectionRightsTotalValue.yaml'
        '400':
           $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/register-requirements/commands/get-default-values-for-new-register-requirement:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirements
      summary: Get Default Values for new Register Requirement.
      description: Passed model has to meet validation requirements described below.
      operationId: getDefaultValuesForNewRegisterRequirement
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: './schemas/register-requirements/GetDefaultValuesForNewRegisterRequirementCommand.yaml'
        required: true
      responses:
        '200':
          description: Successfully obtained default values.
          content:
            application/json:
              schema:
                $ref: './schemas/register-requirements/GetDefaultValuesForNewRegisterRequirementResponse.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/register-requirements/commands/get-by-ids:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirements
      summary: Get register requirements by register requirement ids.
      description: Get register requirements by register requirement ids.
      operationId: getRegisterRequirementsByIds
      requestBody:
        description: Collection of register requirement identifiers.
        content:
          application/json:
            schema:
              $ref: './schemas/register-requirements/GetRegisterRequirementsByIdsCommand.yaml'
        required: true
      responses:
        '200':
          description: Successfully obtained default values.
          content:
            application/json:
              schema:
                description: Register requirements collection.
                type: array
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/register-requirements/RegisterRequirement.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '499':
          $ref: './responses/common.yaml#/components/responses/499'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-input-connections/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Fetches list of Meter Input Connections.
      description: |-
        ### Result
        Returns filtered and paged list of Meter Input Connections according to passed criteria.
      operationId: searchMeterInputConnections
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-input-connection/MeterInputConnectionsListSearchCommand.yaml'
        required: true
      responses:
        '200':
          description: Meter Input Connections returned successfully.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-input-connection/PagedResultOfGetMeterInputConnectionsSearchModel.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/meter-input-connections/commands/validate-meter-on-meter-component-attributes-requirements:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Validates meter on meter components attributes requirements.
      description: |-
        ### Result
        Returns validation result.
      operationId: validateMeterOnMeterComponentAttributesRequirements
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/meter-input-connection/MeterComponentAttributesRequirementsValidationRequest.yaml'
        required: true
      responses:
        '200':
          description: Validation returned successfully.
          content:
            application/json:
              schema:
                $ref: './schemas/meter-input-connection/MeterComponentAttributesRequirementsValidationResult.yaml'
        '400':
          $ref: './responses/common.yaml#/components/responses/400'
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '404':
          $ref: './responses/common.yaml#/components/responses/404'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/gis-topology/commands/get-electricity-equipment-containers-by-bounding-box:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - GisTopology
      summary: Gets electricity equipment containers based on bounding box (region of interest).
      description: |-
        ### Result
        Returns collection of electricity equipment containers with information about location coordinates based on requested bounding box.
      operationId: searchElectricityEquipmentContainersByBoundingBox
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/gis-topology/ElectricityEquipmentContainersSearchByBoundingBoxModel.yaml'
        required: true
      responses:
        '200':
          description: Collection of electricity equipment containers which coordinates intersect with requested bounding box.
          content:
            application/json:
              schema:
                type: array
                description: Equipment container matching search criteria.
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/gis-topology/ElectricityEquipmentContainer.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/gis-topology/commands/get-electricity-topology-navigation-item:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - GisTopology
      summary: Gets electricity topology item containing information about parents and children, based on item identifier.
      description: |-
        ### Result
        Returns electricity topology graph navigation object consisting of information about itself together with its parents and children - upper and lower lever items directly connected to it.
      operationId: getElectricityTopologyNavigationItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/gis-topology/GetElectricityTopologyNodeNavigationItemRequest.yaml'
        required: true
      responses:
        '200':
          description: Topology items directly connected to equipment container. NULL if not found.
          content:
            application/json:
              schema:
                description: Electricity topology navigation item.
                nullable: true
                allOf:
                  - $ref: './schemas/gis-topology/ElectricityTopologyNavigationItem.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

  /v2/gis-topology/commands/get-electricity-topology-path-from-node-to-container:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - GisTopology
      summary: Gets electricity topology path (collection of elements in route) from topology node to equipment container.
      description: |-
        ### Result
        Returns electricity topology path between specified topology node and equipment container. Path is described as collection of navigation items, each with nearest children and parents.
      operationId: getElectricityTopologyPathFromNodeToContainer
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/gis-topology/GetElectricityTopologyPathFromNodeToContainerRequest.yaml'
        required: true
      responses:
        '200':
          description: Topology items collection in path between topology node and equipment container.
          content:
            application/json:
              schema:
                type: array
                description: Topology items collection in path between topology node and equipment container.
                minItems: 0
                maxItems: 10000
                items:
                  $ref: './schemas/gis-topology/ElectricityTopologyNavigationItem.yaml'
        '400':
          $ref: "./responses/common.yaml#/components/responses/400"
        '401':
          $ref: './responses/common.yaml#/components/responses/401'
        '403':
          $ref: './responses/common.yaml#/components/responses/403'
        '422':
          $ref: './responses/common.yaml#/components/responses/422'
        '429':
          $ref: './responses/common.yaml#/components/responses/429'
        '500':
          $ref: './responses/common.yaml#/components/responses/500'
        '502':
          $ref: './responses/common.yaml#/components/responses/502'
        '503':
          $ref: './responses/common.yaml#/components/responses/503'
        '504':
          $ref: './responses/common.yaml#/components/responses/504'

components:
  securitySchemes:
    Jwt:
      $ref: "./securitySchemes/Jwt.yaml"
