title: ElectricityAttributesModel
type: object
additionalProperties: false
description: Electricity attributes model.
required:
  - connectionPointCategoryValue
  - installationTypeValue
  - temporary
  - gridAreaId
properties:
  connectionPointCategoryValue:
    description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
    allOf:
      - $ref: '../_simple-type/Guid.yaml'
  installationTypeValue:
    description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
    allOf:
      - $ref: '../_simple-type/Guid.yaml'
  consumerCategory:
    type: string
    description: Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.
    format: uuid
    nullable: true
  deMasterDataForms:
    description: DEMasterDataForm that comes from the settlement calculation.
    nullable: true
    allOf:
      - $ref: '../_simple-type/NonNegativeInteger.yaml'
  installationDescription:
    description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
    nullable: true
    allOf:
      - $ref: '../_simple-type/MediumStringNullable.yaml'
  netSettlementGroup:
    description: This field register the net settlement group, which is also used in the market communication (DataHub).
    nullable: true
    allOf:
      - $ref: '../_simple-type/Guid.yaml'
  gridAreaId:
    description: Grid area id.
    allOf:
      - $ref: '../_simple-type/OneWordString.yaml'
  temporary:
    type: boolean
    description: Set to true, if the Connection Point is temporary.
  temporaryUntil:
    type: string
    description: |-
      When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
      grid company.
    format: date-time
    nullable: true
  flexAttributeObject:
    $ref: './FlexAttributeObject.yaml'
  decommissioned:
    type: string
    description: Decommissioned date.
    format: date-time
    nullable: true
