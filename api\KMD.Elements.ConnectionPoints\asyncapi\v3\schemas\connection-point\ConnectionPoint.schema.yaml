ConnectionPoint:
  title: ConnectionPoint
  type: object
  additionalProperties: false
  description: Connection Point model.
  required:
    - entityId
    - connectionPointNumber
    - created
    - priceGroupExcluded
    - supplyType
    - address
    - addressLine1
    - addressLine2
    - tagAssignments
    - notes
    - createdWithoutInstallationForm
    - changedByUserId
  properties:
    # - REQUIRED FOR BUSINESS EVENTS
    entityId:
      description: Connection Point Entity Id.
      type: string
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
    # - END OF REQUIRED FOR BUSINESS EVENTS
    # - ROOT ENTITY FIELDS
    connectionPointNumber:
      description: The ConnectionPoint Number.
      type: string
      example: '2000001'
    created:
      description: |-
        DK: Oprettet.
        Creation time stamp of the Meter Frame.
      format: date-time
      example: '2022-09-07T09:50:30.870Z'
    installationNumber:
      description: Installation number.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Installation Number example'
    alternativeInstallationNumber:
      description: Old installation's number printed on the physical device at the consumer.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Alternative Installation Number example'
    priceGroupExcluded:
      description: Used to indicate if a connection point is excluded from being included in a price group.
      type: boolean
      example: true
    supplyType:
      description: List of possible supply types "Electricity", "Heating", "Water", "ElectricityHeating", "ElectricityWater", "HeatingWater", "ElectricityHeatingWater"
      type: string
      pattern: "^(Electricity|Heating|Water|ElectricityHeating|ElectricityWater|HeatingWater|ElectricityHeatingWater)$"
      example: Water
    description:
      description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 1000
      example: 'Description example'
    address:
      description: An UUID reference to a master data address.
      type: string
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-reference:
        entityFamily: "CentralAddressRegistry"
        entityType: "Address"
    addressName:
      description: AddressName from CAR.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Address name'
    addressLine1:
      description: AddressLine1 from CAR.
      type: string
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Address line 1'
    addressLine2:
      description: AddressLine2 from CAR.
      type: string
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Address line 2'
    addressStatus:
      description: List of possible statuses that a master data address can have "Active", "Inactive".
      type: [ 'string', 'null' ]
      pattern: "^(Active|Inactive)$"
      example: Active
    addressType:
      description: List of possible address types that a MasterDataAddressDetails object can hold. Eg. "Address", "AccessAddress".
      type: [ 'string', 'null' ]
      pattern: "^(Primary|Temporary|AccessAddress)$"
      example: Primary
    darStatus:
      description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated".
      type: [ 'string', 'null' ]
      pattern: "^(Yes|No|Temporary)$"
      example: Yes
    lifeCycleStatus:
      description: List of possible life cycle states that the MDR system can put the address into.
      type: [ 'string', 'null' ]
      pattern: "^(ToBeDeleted|Valid|UnderInvestigation)$"
      example: UnderInvestigation
    electricityAttributesConnectionPointCategoryValue:
      description: 'Categorization of a Connection Point. The Category is selected from a tenant specific code list.'
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "5b8596cd-d658-432f-89a6-f97bd988d03e"
    electricityAttributesInstallationTypeValue:
      description: 'Defines type of Installation. Eg. For apartments, single households, Industrial, Agricultural.'
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "b963fbc8-f940-4c53-bda2-5f3f75b1759f"
    electricityAttributesConnectionStatus:
      description: 'Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.'
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "799571e4-8377-40c8-80e8-3cbbd4aef6f5"
    electricityAttributesConsumerCategory:
      description: 'Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.'
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "e33405a5-857f-4666-9212-e352c50de2b4"
    electricityAttributesDeMasterDataForms:
      description: DEMasterDataForm that comes from the settlement calculation.
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 1001
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesInstallationDescription:
      description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Installation description example'
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesNetSettlementGroup:
      description: This field register the net settlement group, which is also used in the market communication (DataHub).
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesGridAreaId:
      description: Grid area id.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 25
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesTemporary:
      description: Set to true, if the Connection Point is temporary.
      type: [ 'boolean', 'null' ]
      example: true
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesTemporaryUntil:
      description: >-
        When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
        grid company.
      type: [ 'string', 'null' ]
      format: date-time
      example: '2022-09-07T09:50:30.870Z'
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesFlexAttributeObject:
      description: >-
        An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.
        In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesDecommissioned:
      description: Decommissioned date.
      type: [ 'string', 'null' ]
      format: date-time
      example: 2019-11-14T00:55:31.820Z
      x-nestedObjectName: "ElectricityAttributes"
    heatingAttributesConnectionPointCategoryValue:
      description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific codelist.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "HeatingAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "c44143ad-3b4e-42ae-8a02-a478e251100b"
    heatingAttributesConnectionStatus:
      description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "HeatingAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "3273d682-1b5d-4705-ba8a-8bb51f59849b"
    heatingAttributesInstallationTypeValue:
      description: Defines type of installation type. Eg. For apartments, single households, Industrial, Agricultural.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "HeatingAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "0a354516-16b6-4eeb-984b-4139398d448e"
    heatingAttributesHeatWaterHeater:
      description: List of different hot water heating controls that can be installed.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "HeatingAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "686c3d43-0b41-44f6-bc29-065706d940a6"
    heatingAttributesInstallationDescription:
      description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Installation description example'
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesNumberOfWaterHeater:
      description: The number of Water heaters.
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 2
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesWaterHeaterType:
      description: List of different water heating types that can be installed.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesHeatPlantType:
      description: Lists the different plant types.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "HeatingAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "3c200ba2-d477-42e2-bdb1-b2a953eb1892"
    heatingAttributesHeatExchange:
      description: List of different heat exchanger options.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "HeatingAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "f41d48fc-6f52-4926-b6a6-5f109f09391e"
    heatingAttributesFlexAttributeObject:
      description: >-
        An UUID reference to a settlement object that is used to register flexible attributes about the connection point.
        In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesDecommissioned:
      description: Decommissioned date.
      type: [ 'string', 'null' ]
      format: date-time
      example: 2019-11-14T00:55:31.820Z
      x-nestedObjectName: "HeatingAttributes"
    waterAttributesConnectionPointCategoryValue:
      description: 'Categorization of a Connection Point. The Category is selected from a tenant specific code list.'
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "WaterAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "bded2786-1de8-438e-b504-f3b6aeb95d45"
    waterAttributesInstallationTypeValue:
      description: 'Defines type of Installation. Eg. For apartments, single households, Industrial, Agricultural.'
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "WaterAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "c3bcf47b-c8fc-42a7-9c66-33cb58d105d6"
    waterAttributesConnectionStatus:
      description: 'Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.'
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "WaterAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "35a9a47f-f348-44e7-97b4-3eab65df16e0"
    waterAttributesTemporaryUntil:
      description: >-
        When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
        grid company.
      type: [ 'string', 'null' ]
      format: date-time
      example: '2022-09-07T09:50:30.870Z'
      x-nestedObjectName: "WaterAttributes"
    waterAttributesInstallationDescription:
      description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      x-nestedObjectName: "WaterAttributes"
      example: 'Installation description example'
    waterAttributesFlexAttributeObject:
      description: >-
        An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.
        In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
      type: [ 'string', 'null' ]
      format: uuid
      example: da85baa6-a66a-11ea-bb37-0242ac130003
      x-nestedObjectName: "WaterAttributes"
    waterAttributesDecommissioned:
      description: Decommissioned date.
      type: [ 'string', 'null' ]
      format: date-time
      example: 2019-11-14T00:55:31.820Z
      x-nestedObjectName: "WaterAttributes"
    tagAssignments:
      description: Tags.
      type: array
      maxItems: 1000000
      items:
        $ref: './sub-schemas/ConnectionPointTagAssignmentModel.schema.yaml#/ConnectionPointTagAssignmentModel'
    notes:
      description: Notes.
      type: array
      maxItems: 1000000
      items:
        $ref: './sub-schemas/ConnectionPointNoteModel.schema.yaml#/ConnectionPointNoteModel'
    createdWithoutInstallationForm:
      description: Indicates whether the connection point was created without an installation form.
      type: boolean
    changedByUserId:
      description: Last change user id.
      type: string
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-reference:
        entityFamily: SystemManagement
        entityType: User
