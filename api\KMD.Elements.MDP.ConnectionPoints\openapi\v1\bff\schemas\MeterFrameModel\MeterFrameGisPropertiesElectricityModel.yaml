title: MeterFrameGisPropertiesElectricityModel
description: |-
  GIS properties for electricity supply type in Meter Frame.
additionalProperties: false
allOf:
  - $ref: './MeterFrameGisProperties.yaml'
  - type: object
    additionalProperties: false
    properties:
      branchLineFuseAmps:
        description: "Indicates the size of, or setting of the maximum circuit breaker in front of the branch line on the network company's side. Ex. 125 Amperes."
        nullable: true
        allOf:
          - $ref: '../DataTypes/IntegerNullable.yaml'
        example: 125
      branchLineFuseType:
        nullable: true
        description: 'Indicates the type of branch line fuse present. Ex. Fuse, Maximum switch, HSP fuse.'
        allOf:
          - $ref: '../DataTypes/ShortStringNullable.yaml'
        example: 'Max. 50 characters long string with spaces.'
