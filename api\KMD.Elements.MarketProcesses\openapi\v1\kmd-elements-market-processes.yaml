openapi: 3.0.3
info:
  title: KMD.Elements.MarketProcesses
  x-maintainers: Team-MC-1
  description: |-
    # KMD Elements Market Processes API
  termsOfService: "https://www.kmd.net/terms-of-use"
  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>
  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"
  version: "1.10"

servers:
  - url: https://localhost:5001

security:
  - Jwt: []

tags:

  - name: Brs025
    description: BRS-025 - Request metered data for metering point
  - name: ManualActions
    description: Manual actions to perform on the process
  - name: MarketParticipants
    description: Request to retrieve detailed information about market participants
  - name: Message
    description: Message acquisition
  - name: ProcessDetails
    description: Get process details
  - name: Brs038
    description: BRS-038 - Request pricelinks for metering point
  - name: Users
    description: Get Elements users

paths:
  /bff/brs-process/mark-as-handled-manually:
    post:
      tags:
        - ManualActions
      summary: BrsProcess
      description: BrsProcess
      operationId: markAsHandledManually
      x-authorization: PCMarketCom.Write
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      requestBody:
        required: true
        description: MarkAsHandledManuallyRequest
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MarkAsHandledManuallyRequest"
      responses:
        "204":
          description: "Process marked as handled"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/brs-process/update-process-metadata:
    post:
      tags:
        - ManualActions
      summary: BrsProcess
      description: BrsProcess
      operationId: updateMetaData
      x-authorization: PCMarketCom.Write
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      requestBody:
        required: true
        description: UpdateProcessMetadataRequest
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateProcessMetadataRequest"
      responses:
        "204":
          description: "Process marked as handled"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/brs-process/detail/{processId}:
    get:
      tags:
        - ProcessDetails
      summary: Get process details.
      description: Endpoint for getting process details.
      operationId: getProcessDetails
      x-authorization: PCMarketCom.Read
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/ProcessId"
      responses:
        '200':
          $ref: "#/components/responses/ProcessDetails"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/brs-025/start:
    post:
      tags:
        - Brs025
      operationId: startBrs025
      summary: Initiate BRS-025 process by sending RSM-015
      x-authorization: PCMarketCom.Write
      description: >
        Initiates the BRS-025 process by sending an RSM-015 request to the data provider.
        This endpoint is used to trigger the process of data exchange as defined in BRS-025.
      requestBody:
        description: Payload containing details for the RSM-015 request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartBrs25Request'
      parameters:
        - $ref: '#/components/parameters/EsMessageIdInHeader'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
      responses:
        '200':
          description: UUID of the initiated BRS-025 process.
          content:
            application/json:
              schema:
                description: Unique identifier of the BRS-025.
                type: string
                format: uuid
                example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/users:
    get:
      tags:
        - Users
      operationId: getUsers
      summary: Gets collection of all application users.
      x-authorization: PCMarketCom.Read
      description: Gets collection of all application users.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/Users"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/market-participants:
    get:
      tags:
        - MarketParticipants
      operationId: getMarketParticipants
      summary:  Get market participants
      x-authorization: MarketParticipants.Read
      description: Market participants with role and grid areas.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/MarketParticipants"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/message/{messageId}:
      get:
        tags:
          - Message
        operationId: getMessage
        summary:  Endpoint that allows to get message.
        x-authorization: PCMarketCom.Read
        description: Gets original message
        parameters:
          - $ref: "#/components/parameters/MessageId"
          - $ref: "#/components/parameters/EsMessageIdInHeader"
          - $ref: "#/components/parameters/EsCorrelationIdInHeader"
          - in: query
            name: messageDirection
            schema:
              $ref: "#/components/schemas/MessageDirection"
            description: Direction of a message.
            required: true
            example: Sending
          - in: query
            name: supplyType
            schema:
              $ref: "#/components/schemas/SupplyType"
            description: Indicates the type of supply that the message refers to.
            required: true
            example: Electricity
        responses:
          "200":
            $ref: "#/components/responses/MessageResponse"
          "400":
            $ref: "#/components/responses/400"
          "401":
            $ref: "#/components/responses/401"
          "403":
            $ref: "#/components/responses/403"
          "404":
            $ref: "#/components/responses/404"
          "429":
            $ref: "#/components/responses/429"
          "499":
            $ref: "#/components/responses/499"
          "500":
            $ref: "#/components/responses/500"
          "502":
            $ref: "#/components/responses/502"
          "503":
            $ref: "#/components/responses/503"
          "504":
            $ref: "#/components/responses/504"

  /bff/brs-038/start:
    post:
      tags:
        - Brs038
      summary: StartBrs038
      description: StartBrs038
      operationId: startBrs038
      x-authorization: PCMarketCom.Write
      x-authorization-1: Electricity.All
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      requestBody:
        required: true
        description: StartBrs038Request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StartBrs038Request"
      responses:
        "200":
          description: Process id'
          content:
            application/json:
              schema:
                description: Process id.
                type: string
                format: uuid
                example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

components:
  headers:
    RetryAfter:
      description: Number of seconds until you should try again.
      schema:
        type: integer
        format: int32
        minimum: 1
        maximum: 2678400 # 31 days
      required: true
      example: 3600 # 1 hour

  parameters:
    EsMessageIdInHeader:
      name: es-message-id
      description: Unique message ID. The same message id is used when resending the message.
      in: header
      schema:
        type: string
        format: uuid
      required: true
      example: 35b56ea7-1207-43e5-90c0-9b296c446aeb

    EsCorrelationIdInHeader:
      name: es-correlation-id
      description: |
        This is used to "link" messages together. This can be supplied on a request, so
        that the client can correlate a corresponding reply message.
        The server will place the incoming X-Correlation-ID value as the X-Correlation-ID
        on the outgoing reply. If not supplied on the request, the X-Correlation-ID of the
        reply should be set to the value of the X-Message-ID that was used on the request, if present.
        Given that the X-Correlation-ID is used to ‘link’ messages together,
        it may be reused on more than one message.
      in: header
      schema:
        type: string
        format: uuid
      required: false
      example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    ProcessId:
      name: processId
      description: Unique id of a process
      in: path
      required: true
      schema:
        $ref: "#/components/schemas/ProcessId"
      example: '1ed45519-ab6d-45a4-988d-2b6d974d643e'

    MessageId:
      name: messageId
      description: Unique id of a message
      in: path
      required: true
      schema:
        $ref: "#/components/schemas/MessageId"
      example: '1ed45519-ab6d-45a4-988d-2b6d974d643e'

  schemas:
    UpdateProcessMetadataRequest:
      title: UpdateProcessMetadataRequest
      description: UpdateProcessMetadataRequest
      type: object
      additionalProperties: false
      required:
        - processId
      properties:
        processId:
          $ref: "#/components/schemas/ProcessId"
        comment:
          $ref: "#/components/schemas/Comment"
        assignTo:
          description: Assigned to user Id
          nullable: true
          allOf:
            - $ref: "#/components/schemas/UserId"

    MarkAsHandledManuallyRequest:
      title: MarkAsHandledManuallyRequest
      description: MarkAsHandledManuallyRequest
      type: object
      additionalProperties: false
      required:
        - processId
      properties:
        processId:
          $ref: "#/components/schemas/ProcessId"

    StartBrs038Request:
      title: StartBrs038Request
      description: StartBrs038Request
      type: object
      additionalProperties: false
      required:
        - senderGln
        - meteringPointId
        - periodStart
      properties:
        senderGln:
          $ref: "#/components/schemas/Gln"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        periodStart:
          $ref: "#/components/schemas/DateTime"
        periodEnd:
          $ref: "#/components/schemas/DateTimeNullable"

    ProcessDetails:
      title: ProcessDetails
      description: Process details.
      type: object
      additionalProperties: false
      required:
        - name
        - status
        - supplyType
        - startedAt
        - specificData
        - milestones
      properties:
        name:
          description: Process name.
          type: string
          pattern: '^.*$'
          maxLength: 100
        status:
          description: Status of process.
          allOf:
            - $ref: "#/components/schemas/ProcessStatus"
        supplyType:
          description: Supply type.
          allOf:
            - $ref: "#/components/schemas/SupplyType"
        startedAt:
          description: The moment when the process started.
          allOf:
            - $ref: "#/components/schemas/DateTime"
        completedAt:
          description: The moment of successful completion of the process.
          nullable: true
          allOf:
            - $ref: "#/components/schemas/DateTime"
        startedBy:
          description: Started by user or system
          type: string
          pattern: ^.*$
          maxLength: 100
        onBehalfOf:
          description: On behalf on user or system
          type: string
          nullable: true
          pattern: ^.*$
          maxLength: 100
        assignedTo:
          description: Assigned to user Id
          nullable: true
          allOf:
            - $ref: "#/components/schemas/UserId"
        comment:
          $ref: "#/components/schemas/Comment"
        specificData:
          type: array
          description: Process specific data.
          maxItems: 100
          items:
            $ref: '#/components/schemas/ProcessProperty'
        milestones:
          type: array
          description: Process milestones.
          maxItems: 100
          items:
            $ref: '#/components/schemas/Milestone'

    ProcessProperty:
      title: ProcessProperty
      description: Process property.
      type: object
      additionalProperties: false
      required:
        - name
      properties:
        name:
          description: Property name.
          type: string
          pattern: '^.*$'
          maxLength: 100
        value:
          description: Property value.
          nullable: true
          type: string
          pattern: '^.*$'
          maxLength: 100
        link:
          description: Url to a resource.
          type: string
          pattern: '^.*$'
          maxLength: 100
          nullable: true
          example: '/time-series-flat/c8bbd223-8175-4086-aa3d-fbcc56797305'

    Milestone:
      title: Milestone
      description: Milestone.
      type: object
      additionalProperties: false
      required:
        - name
        - occurredAt
        - status
        - disabled
        - steps
      properties:
        name:
          description: Milestone name.
          type: string
          pattern: '^.*$'
          maxLength: 100
        occurredAt:
          description: Occurred at timestamp.
          allOf:
            - $ref: "#/components/schemas/DateTime"
        status:
          description: Status of milestone.
          allOf:
            - $ref: "#/components/schemas/MilestoneStatus"
        disabled:
          description: Define milestone as disabled
          type: boolean
        steps:
          type: array
          nullable: true
          description: Steps.
          maxItems: 100
          items:
            $ref: '#/components/schemas/MilestoneStep'
        milestoneProperties:
          type: array
          nullable: true
          description: Milestone properties
          maxItems: 100
          items:
            $ref: '#/components/schemas/ProcessProperty'
        actions:
          type: array
          nullable: true
          description: Milestone actions.
          maxItems: 10
          items:
            $ref: '#/components/schemas/MilestoneAction'

    MilestoneStep:
      title: MilestoneStep
      description: Milestone step.
      type: object
      additionalProperties: false
      required:
        - name
        - occurredAt
      properties:
        name:
          description: Step name.
          type: string
          pattern: '^.*$'
          maxLength: 100
        occurredAt:
          $ref: "#/components/schemas/DateTime"

    MilestoneAction:
      title: MilestoneAction
      description: Action to perform on the milestone.
      type: object
      additionalProperties: false
      required:
        - name
        - parameter
      properties:
        name:
          description: Action name.
          type: string
          pattern: '^.*$'
          maxLength: 100
        parameter:
          description: Parameter representing an object required to perform the action. For example it can be body of a request or query parameter.
          type: object

    StartBrs25Request:
      title: StartBrs25Request
      type: object
      description: Send RSM-015 request details
      additionalProperties: false
      required:
       - meteringPointId
       - periodStart
       - periodEnd
       - sender
      properties:
        meteringPointId:
          description: Id of metering point.
          allOf:
            - $ref: "#/components/schemas/MeteringPointId"
        periodStart:
          description: Requested start date.
          allOf:
            - $ref: "#/components/schemas/Date"
        periodEnd:
          description: Requested end date. Must be greater than 'periodStart'
          allOf:
            - $ref: "#/components/schemas/Date"
        sender:
          description: Sender details.
          allOf:
            - $ref: "#/components/schemas/Sender"

    ProcessStatus:
      description: Status of the process.
      type: string
      maxLength: 100
      pattern: "^(New|Completed|InProgress|Failed|ManuallyHandled)$"
      example: 'Completed'

    MilestoneStatus:
      description: Status of the milestone.
      type: string
      maxLength: 100
      pattern: "^(Completed|InProgress|Failed|ManuallyHandled)$"
      example: 'Completed'

    Sender:
      type: object
      additionalProperties: false
      description: Informations about sender
      required:
        - gln
        - gridAreas
      properties:
        gln:
          description: |
            Sender GLN identifier to send request with.
            Must be a valid GLN for grid area of selected metering point.
          allOf:
            - $ref: "#/components/schemas/Gln"
        gridAreas:
          type: array
          maxItems: 1000
          description: Grid areas
          items:
            $ref: "#/components/schemas/GridArea"

    Gln:
      type: string
      description: The GLN (Global Location Number) is a global code that identifies a location.
      minLength: 13
      maxLength: 13
      pattern: '^[0-9]{13}$'
      example: '1234567890123'

    GridArea:
      type: string
      description: The official name or title of the grid area.
      pattern: "^.*$"
      minLength: 1
      maxLength: 100
      example: '900'

    MeteringPointId:
      description: Id of metering point
      type: string
      pattern: '^\d+$'
      minLength: 18
      maxLength: 18
      example: '571313190000010454'

    SupplyType:
      description: Supply type.
      type: string
      maxLength: 100
      pattern: "^(Electricity|Water|Heating)$"
      example: 'Electricity'

    MessageDirection:
      description: Direction of a message.
      type: string
      maxLength: 100
      pattern: "^(Sending|Receiving)$"
      example: 'Receiving'

    MessageId:
      type: string
      format: uuid
      description: Message Id
      example: 1ed45519-ab6d-45a4-988d-2b6d974d643e

    ProcessId:
      type: string
      format: uuid
      description: Process Id
      example: 1ed45519-ab6d-45a4-988d-2b6d974d643e

    UserId:
      type: string
      format: uuid
      description: User Id
      example: 1ed45519-ab6d-45a4-988d-2b6d974d643e

    Comment:
      description: User provided process comment.
      type: string
      pattern: '^.*$'
      nullable: true
      maxLength: 10000

    DateTime:
      description: Date with time in UTC ISO 8601 format.
      type: string
      format: date-time
      example: '2019-11-14T00:55:31.820Z'

    DateTimeNullable:
      description: Date with time in UTC ISO 8601 format.
      type: string
      format: date-time
      nullable: true
      example: '2019-11-14T00:55:31.820Z'

    Date:
      description: Date in 'yyyy-MM-dd' format.
      type: string
      format: date
      example: '2019-11-14'

    Users:
      type: array
      maxItems: 1000
      description: Users collection.
      items:
        $ref: '#/components/schemas/User'

    User:
      type: object
      description: Application user.
      additionalProperties: false
      required:
      - id
      - displayName
      properties:
        id:
          description: User Id.
          allOf:
            - $ref: '#/components/schemas/UserId'
        displayName:
          type: string
          maxLength: 1000
          description: User name in format "FirstName LastName (Email)".
          pattern: "^.*$"
          nullable: false

    MarketParticipants:
      type: array
      maxItems: 1000
      description: Market participants collection.
      items:
        $ref: '#/components/schemas/MarketParticipant'

    MarketParticipant:
      title: MarketParticipant
      description: Market participant details.
      type: object
      additionalProperties: false
      required:
        - gln
        - alias
        - gridAreas
      properties:
        gln:
          description: The GLN (Global Location Number) is a global code that identifies a location.
          allOf:
            - $ref: "#/components/schemas/Gln"
        alias:
          type: string
          description: Alias for Market Participant.
          pattern: "^.*$"
          minLength: 1
          maxLength: 100
          example: '1111111111111'
        gridAreas:
          type: array
          maxItems: 1000
          description: Grid areas
          items:
            $ref: "#/components/schemas/GridArea"

    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          maxLength: 256
          pattern: ^.*$
          nullable: true
          example: 'https://errors.kmdelements.com/500'
        title:
          description: A short, human-readable summary of the problem type.
          type: string
          maxLength: 256
          pattern: ^.*$
          nullable: true
          example: Error short description
        status:
          description: The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem.
          type: integer
          format: int32
          minimum: 400
          maximum: 599
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          maxLength: 2048
          pattern: ^.*$
          nullable: true
          example: Description what exactly happened
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          maxLength: 32779
          pattern: ^.*$
          nullable: true
          example: /resources-path/1

    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1000
              additionalProperties:
                type: array
                maxItems: 5
                description: Array of validation error messages.
                items:
                  type: string
                  maxLength: 2048
                  pattern: ^.*$
              nullable: true

  responses:
    "ProcessDetails":
      description: Details of the process.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ProcessDetails"

    "Users":
      description: Collection of users in the system.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Users"

    "MarketParticipants":
      description: Collection of market participants for given tenant.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/MarketParticipants"

    "MessageResponse":
      description: Message has been provided successfully.
      content:
        application/json:
          schema:
            description: Object representing message payload in json.
            type: string
            format: binary
            maxLength: 2147483647

    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ValidationProblemDetails"
          examples:
            BadRequestExample:
              value:
                type: "https://errors.kmdelements.com/400"
                title: Bad Request
                status: 400
                detail: "Invalid request"
                instance: "/resources/1"
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            UnauthorizedExample:
              value:
                type: "https://errors.kmdelements.com/401"
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn"t satisfy the Token Validation expression.
                instance: "/resources/1"
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ForbiddenExample:
              value:
                type: "https://errors.kmdelements.com/403"
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: "/resources/1"
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/404"
                title: Not Found
                status: 404
                detail: Not Found
                instance: "/resources/1"
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          $ref: "#/components/headers/RetryAfter"
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            TooManyRequestsExample:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 429
                detail: Rate limit is exceeded.
                instance: "/resources/1"
    "499":
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
             ClientClosedRequestExample:
              value:
                type: "https://errors.kmdelements.com/499"
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: "/resources/1"
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            InternalServerErrorExample:
              value:
                type: "https://errors.kmdelements.com/500"
                title: Internal Server Error
                status: 500
                detail: "body.0.age: Value `Not Int` does not match format `int32`"
                instance: "/resources/1"
    "502":
      description: 502 Bad Gateway.
    "503":
      description: 503 Service Unavailable.
    "504":
      description: 504 Gateway Timeout.

  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
      type: http
      scheme: bearer
      bearerFormat: JWT
