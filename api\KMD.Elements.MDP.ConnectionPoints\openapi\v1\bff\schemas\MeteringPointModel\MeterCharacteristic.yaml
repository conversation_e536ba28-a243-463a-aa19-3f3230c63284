nullable: true
description: The characteristic of the meter
type: object
additionalProperties: false
properties:
  numberOfDigits:
    nullable: true
    type: string
    description: Is mapped to `NumberOfDigits`
    maxLength: 5
    pattern: ""
  conversionFactor:
    type: number
    format: double
    nullable: true
    description: Is mapped to `ConversionFactor`
    exclusiveMinimum: false
  unitType:
    description: Register Requirement Virtual Id.
    nullable: true
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  meterReadingType:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `MeterReadingType`
    pattern: ^(D01|D02)$
