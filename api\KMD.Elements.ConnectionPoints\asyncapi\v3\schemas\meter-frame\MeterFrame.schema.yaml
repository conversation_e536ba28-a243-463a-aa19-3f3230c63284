MeterFrame:
  title: MeterFrame
  type: object
  additionalProperties: false
  description: Meter frame model.
  required:
    - entityId
    - changedByUserId
    - created
    - meterFrameNumber
    - supplyType
    - commonReading
    - meterMissing
    - meterSealed
    - meterWorkConsumerBilled
    - connectionPointId
    - supplyStatus
    - tagAssignments
    - placementCode
    - accessInformation
    - connectionStatus
    - propertyUnits
    - expectedConsumptions
    - meterComponentRequirements
  properties:
    # - REQUIRED FOR BUSINESS EVENTS
    entityId:
      description: Meter Frame Entity Id.
      type: string
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
    # - END OF REQUIRED FOR BUSINESS EVENTS
    # - ROOT ENTITY FIELDS
    changedByUserId:
      description: Last change user id.
      type: string
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-reference:
        entityFamily: SystemManagement
        entityType: User
    created:
      description: |-
        DK: Oprettet.
        Creation time stamp of the Meter Frame.
      type: string
      format: date-time
      example: "2022-09-07T09:50:30.870Z"
    meterFrameNumber:
      description: |-
        DK: Målerrammenummer.
        Number on the Meter Frame.
      type: string
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: "1234"
    supplyType:
      description: List of possible supply types "Electricity", "Heating", "Water", "ElectricityHeating", "ElectricityWater", "HeatingWater", "ElectricityHeatingWater"
      type: string
      pattern: "^(Electricity|Heating|Water|ElectricityHeating|ElectricityWater|HeatingWater|ElectricityHeatingWater)$"
      example: "Heating"
    commonReading:
      description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
      type: string
      format: uuid
      example: 'f56d5e72-d9f1-4d4d-95ac-ca06079dfc0a'
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "da99b709-8214-4870-9a0c-2dfeb99e3969"
    meterMissing:
      description: 'DK: MålerVæk.'
      type: boolean
      example: true
    noMeter:
      description: 'No meter.'
      type: boolean
      example: true
    placementSpecification:
      description: 'DK: Placeringsbeskrivelse.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: "placement specification"
    meterSealDate:
      description: |-
        DK: Målerplomberingsdato.
        Indicates the date when the meter was sealed.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2022-09-07T09:50:30.870Z"
    meterSealed:
      description: |-
        DK: MålerPlomberet.
        Indicates whether the meter is sealed.
      type: boolean
      example: true
    meterWorkConsumerBilled:
      description: |-
        DK: MålerYdelseFaktureresEjer.
        Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
      type: boolean
      example: true
    connectionStatus:
      description: 'DK: Tilslutningsstatus.'
      type: string
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    decommissioned:
      description: Decommissioned date.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2022-09-07T09:50:30.870Z"
    statusChanged:
      description: Latest status change date.
      type: [ 'string', 'null' ]
      format: date-time
      example: "2022-09-07T09:50:30.870Z"
    electricityAttributesConnectionType:
      description: 'DK: Tilslutningstype.'
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "3009822f-8c37-4c62-9797-d9ab7369a032"
    electricityAttributesBreakerBeforeMeter:
      description: 'DK: AfbryderFørMåler.'
      type: [ 'boolean', 'null' ]
      example: true
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesPowerLimitA:
      description: 'DK: EffektgrænseA.'
      type: [ 'number', 'null' ]
      format: decimal
      example: 1234
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesPowerLimitKw:
      description: 'DK: EffektgrænseKW.'
      type: [ 'number', 'null' ]
      format: decimal
      example: 1234
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesProductionCapacity:
      description: 'DK: Anlægskapacitet.'
      type: [ 'number', 'null' ]
      format: decimal
      example: 1234
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesPurpose:
      description: 'DK: Formål.'
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "9feedc56-ec29-4b8d-a15e-c65992cd8dcd"
    electricityAttributesRatioCt:
      description: 'DK: OmsætningsforholdCT.'
      type: [ 'string', 'null' ]
      format: uuid
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "D36EA459-54F3-4A97-BBB5-EA80A1C83137"
    electricityAttributesRatioVt:
      description: 'DK: OmsætningsforholdVT.'
      type: [ 'string', 'null' ]
      format: uuid
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "DB41E6E2-402E-4529-A28A-F9F9DB86B02C"
    electricityAttributesTarifConnectionPoint:
      description: 'DK: TarifTilslutningspunkt.'
      type: [ 'string', 'null' ]
      format: uuid
      x-nestedObjectName: "ElectricityAttributes"
      example: "tarif"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "439646f1-53ad-4da0-926a-80e7ecff3eec"
    electricityAttributesFlexAttributeObject:
      description: |-
        DK: FleksAttributObjekt.
        An UUID reference to a Settlement Object that is used to register flexible attributes about the Meter Frame.
        In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical Meter Frame.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesLossFactor:
      description: |-
        DK: NettabsFaktor.
        Nettabsfaktor som bruges på målerrammen i MDM. Vi skal lige gennemgå den fra MDM siden. Skal indgå både på fuldttidserie niveau så faktoren ganges på både 15/60 forbrug samt
        tællerstande.
        Mains loss factor used on the measuring frame in MDM. We just need to review it from the MDM page. Must be included both at full-time series level so the factor is multiplied by
        both 15/60 consumption and meter readings.
      type: [ 'number', 'null' ]
      format: decimal
      example: "lossfactor"
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesMeterFrameFuse:
      description: |-
        DK: MålerRammeForsikring.
        Forsikring (tarifsikring, T-ret sikring etc) som ikke er stikledningssikringen eller tilslutningsrettigheden. Kunden kan selv bestemme størrelsen på denne.
        Hvis kunden ikke har oplyst en forsikring, så er stikledningssikringen kundens forsikring.
        Kunden kan selv sikrer op eller ned (stikledningssikringen begrænser ham selvfølgelig).
        Vi har den kun i systemet for at kunne rumme data fra blanketten.
        EN:
        Insurance (tariff fuse, T-right fuse etc) which is not the branch line fuse or the connection right. The customer can decide the size of this.
        If the customer has not stated an insurance, then the branch line insurance is the customer's insurance.
        The customer can secure up or down himself (the branch line protection limits him of course).
        We only have it in the system to be able to hold data from the form.
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 1234
      x-nestedObjectName: "ElectricityAttributes"
    electricityAttributesMeterDisplaySettings:
      description: |-
        DK: VisningMåler.
        En bruger skal kunne stille krav til visning af alle målerer som installeres i målerrammen.
        Skal medfører målerrammekrav på målerammen som gennemtvinger specifik display visning.
        EN:
        A user must be able to set requirements for displaying all meters that are installed in the meter frame.
        Must entail meter frame requirements on the meter frame which enforces specific display.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "78560bd1-c58b-4c5c-89fb-cae78d519c9c"
    electricityAttributesConnectionRemark:
      description: |-
        DK: TilslutningsBemærkning.
        Specielle forhold omkring tilslutning, f.eks. adapter, kvadrat, klemmetype etc.
        EN:
        Special conditions regarding connection, e.g. adapter, square, terminal type etc.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "ElectricityAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "690328a5-6a2f-4baa-aec4-78da79fd5b44"
    heatingAttributesCalculateCooling:
      description: 'DK: BeregnAfkøling.'
      type: [ 'boolean', 'null' ]
      example: true
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesCoolingLimit:
      description: 'DK: Afkølingsgrænse.'
      type: [ 'number', 'null' ]
      format: decimal
      example: 1234
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesCriticalCustomer:
      description: 'DK: KritiskKundekategori.'
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "HeatingAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "f4d3cfb9-ca8c-40c0-8cd2-d57e300a6e43"
    heatingAttributesPlanEffect:
      description: 'DK: Anlægsydelse.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 1234
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesPlantType:
      description: 'DK: Anlægstype.'
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "HeatingAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "61ebf6ce-1708-41a5-a793-de214aa7ffb4"
    heatingAttributesReturnHeatingConnected:
      description: 'DK: Returvarme.'
      type: [ 'boolean', 'null' ]
      example: true
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesFlexAttributeObject:
      description: |-
        DK: FleksAttributObjekt.
        An UUID reference to a Settlement Object that is used to register flexible attributes about the Meter Frame.
        In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical Meter Frame.
      type: [ 'string', 'null' ]
      format: uuid
      example: "flexattribute"
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesCounterPlacement:
      description: Counter Placement.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: Counter placement.
      x-nestedObjectName: "HeatingAttributes"
    heatingAttributesHeatMeterConnectionType:
      description: Heat Meter Connection Type.
      type: [ 'string', 'null' ]
      format: uuid
      x-nestedObjectName: "HeatingAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "BCF735D9-C297-438E-A0A3-F451E7ABAEA9"
    waterAttributesDirectSprinkling:
      description: DK:DirekteSprinkling.
      type: [ 'boolean', 'null' ]
      example: true
      x-nestedObjectName: "WaterAttributes"
    waterAttributesDriveBy:
      description: DK:DriveBy.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "WaterAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "7c722064-9eb2-4300-ba28-a6fa04f9a7f7"
    waterAttributesCriticalCustomer:
      description: DK:KritiskKundekategori.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "WaterAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "fe499e46-7dd7-42cb-9f84-989067e8bc22"
    waterAttributesMediumCategory:
      description: DK:MediumKategori.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "WaterAttributes"
      x-reference:
        entityFamily: "ReferenceData"
        entityType: "ValueListValue"
        entityCollectionId: "e346fba4-f150-46a3-aeaf-41257526f019"
    waterAttributesOwnPump:
      description: DK:EgenPumpe.
      type: [ 'boolean', 'null' ]
      example: true
      x-nestedObjectName: "WaterAttributes"
    waterAttributesPressureEnhancer:
      description: DK:TrykForøger.
      type: [ 'boolean', 'null' ]
      example: true
      x-nestedObjectName: "WaterAttributes"
    waterAttributesQvkSensor:
      description: DK:QvkSensor.
      type: [ 'boolean', 'null' ]
      example: true
      x-nestedObjectName: "WaterAttributes"
    waterAttributesFlexAttributeObject:
      description: DK:FleksAttributObjekt.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "WaterAttributes"
    geographicalLocationAccuracy:
      description: The accuracy class of the point.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 25
      example: "accuracy"
      x-nestedObjectName: "GeographicalLocation"
    geographicalLocationName:
      description: The name of the point, e.g. Waypoint, Access Point.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 25
      example: "name"
      x-nestedObjectName: "GeographicalLocation"
    geographicalLocationSource:
      description: Source of the geographical location point.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: "source"
      x-nestedObjectName: "GeographicalLocation"
    geographicalLocationTechnicalStandard:
      description: Technical standard.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 25
      example: "technical standard"
      x-nestedObjectName: "GeographicalLocation"
    geographicalLocationLongitude:
      description: Longitude - X in most spatial file formats.
      type: [ 'number', 'null' ]
      format: decimal
      example: 23.4
      x-nestedObjectName: "GeographicalLocation"
    geographicalLocationLatitude:
      description: Latitude - Y in most spatial file formats.
      type: [ 'number', 'null' ]
      format: decimal
      example: 5.33
      x-nestedObjectName: "GeographicalLocation"
    geographicalLocationElevation:
      description: Z - elevation in some spatial formats - number of meters above the surface of the water.
      type: [ 'number', 'null' ]
      format: decimal
      example: 10.5
      x-nestedObjectName: "GeographicalLocation"
    gisPropertiesElectricityGisId:
      description: 'Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.'
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityEquipmentContainerId:
      description: 'Top level GIS topology equipment container identifier.'
      type: ['string', 'null']
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityGisDescription:
      description: 'The name of the connection point in the physical topology. The name is set when the gis id is selected from the GIS system.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 1000
      example: 'GisDescription is any 1000 characters long string with spaces allowed.'
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityBranchLineFuseAmps:
      description: "Indicates the size of, or setting of the maximum circuit breaker in front of the branch line on the network company's side. Ex. 125 Amperes."
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 125
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityBranchLineFuseType:
      description: 'Indicates the type of branch line fuse present. Ex. Fuse, Maximum switch, HSP fuse.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityCabinetNumber:
      description: 'Number of the cable box where the plug is connected. Ex. 123658.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 123658
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityCableNumber:
      description: 'Number on execution in station. Ex. 1, 2, 3 etc.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 3
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityConnectionPointLevel:
      description: 'Indicates at which level the branch line is connected in the network. Ex. C, B1, B2, A1, A2 and A0.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'B1'
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityShinNumber:
      description: 'Number on the rail in the locker.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 15
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityStationNumber:
      description: 'The station from which the branch line is supplied. Ex. 98756.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 98756
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesElectricityTransformerNumber:
      description: 'Transformer number, for several transformers in a station. Ex. 1, 2, 3 etc.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 2
      x-nestedObjectName: "GisPropertiesElectricity"
    gisPropertiesHeatingGisId:
      description: 'Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.'
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingGisDescription:
      description: 'The name of the connection point in the physical topology. The name is set when the gis id is selected from the GIS system.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 1000
      example: 'GisDescription is any 1000 characters long string with spaces allowed.'
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingBranchLineSize:
      description: 'Indicates how large a branch line is laid in the ground from the main line to the meter.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 25
      example: 'SomeStringWithoutSpaces'
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingBranchLineNumber:
      description: 'Number on branch line. Ex. 246810.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 246810
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingExpectedForwardFlowTemp:
      description: 'Calculated value of forward-flow temperature.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 2
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingExpectedPressure:
      description: 'Calculated value of expected pressure - max/min/delta.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingHeatingPlantId:
      description: 'Heating plant Id - Heater "kID".'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 3
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingHeatingPlantName:
      description: 'Heating plant name.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingHeatingPlantPipeName:
      description: 'Pipe name / outlet marking.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingHeatingPlantPipeNumber:
      description: 'Outlet marking number.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 4
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesHeatingHydraulicZone:
      description: 'Indicates an area, e.g. a city (polygon).'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesHeating"
    gisPropertiesWaterGisId:
      description: 'Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.'
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterGisDescription:
      description: 'The name of the connection point in the physical topology. The name is set when the gis id is selected from the GIS system.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 1000
      example: 'GisDescription is any 1000 characters long string with spaces allowed.'
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterBranchLineSizeSquare:
      description: 'Indicates how large a branch line is laid in the ground from Main line to Area.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 25
      example: 'ShortStringWithoutSpaces'
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterBranchLineNumber:
      description: 'Number on branch line. Ex. XF2500.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'XF2500'
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterHardness:
      description: 'Hardness in dH (german unit for water hardness).'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 1
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterMainZone:
      description: 'Indicates an area (higher level than section) = an operating area (polygon).'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterPressure:
      description: 'Section/elevation/conduction loss (calculated value).'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterSectionId:
      description: 'Indicates an area (polygon) in which the water meter frame is located (for checking water balance).'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 2
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterSectionName:
      description: 'Section name.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterSuperZone:
      description: 'Indicates an area <Section> (higher level than MainZone) = e.g. a city (polygon).'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterWaterPlantId:
      description: 'Water Plant Id.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 3
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterWaterPlantName:
      description: 'Water Plant Name.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterWaterPlantPipeId:
      description: 'Water Plant Pipe Id.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      example: 4
      x-nestedObjectName: "GisPropertiesWater"
    gisPropertiesWaterWaterPlantPipeName:
      description: 'Water Plant Pipe name.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "GisPropertiesWater"
    mainBranchLineElectricityConnectedMeterFrameId:
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      description: Id of related meterFrameId, when connected via other branchline
    mainBranchLineElectricityBranchLineNumber:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      description: 'Number on branch line. Ex. XF2500.'
      example: 'XF2500'
    mainBranchLineElectricityBranchLineType:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      description: 'Branch line type.'
      example: 'ShortStringWithoutSpaces'
    mainBranchLineElectricityMaterialCode:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      description: 'Material code.'
      example: 'Material code'
    mainBranchLineElectricityNeutralSquare:
      type: [ 'number', 'null' ]
      format: decimal
      description: 'Neutral square.'
      example: 1.1
    mainBranchLineElectricityNumberOfCables:
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Number of cables.'
      example: 1
    mainBranchLineElectricityNumberOfConductors:
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Number of conductors.'
      example: 1
    mainBranchLineElectricitySystemGrounding:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      description: 'System Grounding.'
      example: 'SystemGrounding'
    reserveBranchLineElectricityBranchLineNumber:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      description: 'Number on branch line. Ex. XF2500.'
      example: 'XF2500'
    reserveBranchLineElectricityBranchLineType:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      description: 'Branch line type.'
      example: 'ShortStringWithoutSpaces'
    reserveBranchLineElectricityMaterialCode:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      description: 'Material code.'
      example: 'Material code'
    reserveBranchLineElectricityNeutralSquare:
      type: [ 'number', 'null' ]
      format: decimal
      description: 'Neutral square.'
      example: 1.1
    reserveBranchLineElectricityNumberOfCables:
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Number of cables.'
      example: 1
    reserveBranchLineElectricityNumberOfConductors:
      type: [ 'integer', 'null' ]
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Number of conductors.'
      example: 1
    reserveBranchLineElectricitySystemGrounding:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      description: 'System Grounding.'
      example: 'SystemGrounding'
    mainBranchLineWaterConnectedMeterFrameId:
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      description: Id of related meterFrameId, when connected via other branchline
    mainBranchLineWaterBranchLineSize:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 25
      description: 'Branch line size.'
      example: 'XF2500'
    reserveBranchLineWaterBranchLineSize:
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 25
      description: 'Branch line size.'
      example: 'XF2500'
    connectionPointId:
      description: ConnectionPointId.
      type: string
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-reference:
        entityFamily: "ConnectionPoint"
        entityType: "ConnectionPoint"
    addressId:
      description: An UUID reference to a master data address.
      type: [ 'string', 'null' ]
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
      x-reference:
        entityFamily: "CentralAddressRegistry"
        entityType: "Address"
    addressName:
      description: AddressName from CAR.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: "address name"
    addressLine1:
      description: AddressLine1 from CAR.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 100
      example: "address line 1"
    addressLine2:
      description: AddressLine2 from CAR.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 100
      example: "address line 2"
    addressStatus:
      description: List of possible statuses that a master data address can have "Active", "Inactive". Statuses from Central Address Registry.
      type: [ 'string', 'null' ]
      pattern: "^(Active|Inactive)$"
      example: Active
    addressType:
      description: List of possible address types that a MasterDataAddressDetails object can hold. Eg. "Address", "AccessAddress".
      type: [ 'string', 'null' ]
      pattern: "^(Primary|Temporary|AccessAddress)$"
      example: Primary
    darStatus:
      description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated".
      type: [ 'string', 'null' ]
      pattern: "^(Yes|No|Temporary)$"
      example: Yes
    lifeCycleStatus:
      description: List of possible life cycle states that the MDR system can put the address into.
      type: [ 'string', 'null' ]
      pattern: "^(ToBeDeleted|Valid|UnderInvestigation)$"
      example: UnderInvestigation
    supplyStatus:
      description: List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
      type: string
      pattern: "^(Connected|Disconnected)$"
      example: Water
    tagAssignments:
      description: Tags.
      type: array
      maxItems: 1000000
      items:
        $ref: "./sub-schemas/TagAssignmentModel.schema.yaml#/TagAssignmentModel"
    meterReadingType:
      description: A list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".
      type: string
      format: uuid
      example: 'd988b796-225b-4eef-8ec4-191ee69a3a80'
    supplyDisconnectType:
      description: |-
        List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation".
        Indicates whether the supply is disconnected before the meter, disconnected in the cable cabinet, disconnected at the meter outlet, or in the mains station.
      type: [ 'string', 'null' ]
      pattern: "^(MeterNoVoltage|DisconnectedWithBreakerInMeter|DisconnectedBeforeMeter|DisconnectedInKabinet|DisconnectedAfterMeter|DisconnectedStation|Connected|Unknown)$"
      example: Connected
    placementCode:
      description: 'Placement code CodeList value id.'
      type: string
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    accessInformation:
      type: array
      description: Access information.
      maxItems: 1000
      items:
        $ref: './sub-schemas/AccessInformationModel.schema.yaml#/AccessInformationModel'
    propertyApplicationCode:
      description: 'Property application code.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 100
      example: 'Max. 100 characters long string with spaces.'
      x-nestedObjectName: "Property"
    propertyBfeNumber:
      description: 'Property BFE number.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "Property"
    propertyMunicipalityCode:
      description: 'Property municipality code.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "Property"
    propertyMunicipalityPropertyNumber:
      description: 'Property municipality property number.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Max. 50 characters long string with spaces.'
      x-nestedObjectName: "Property"
    propertyNumberOfCommercialUnits:
      description: 'Property number of commercial units.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 3
      x-nestedObjectName: "Property"
    propertyNumberOfUnits:
      description: 'Property number of units.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 3
      x-nestedObjectName: "Property"
    propertyTotalBusinessM2:
      description: 'Property total business M2.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 3
      x-nestedObjectName: "Property"
    propertyTotalHousingM2:
      description: 'Property total housing M2.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 3
      x-nestedObjectName: "Property"
    propertyYearOfConstruction:
      description: 'Property year of construction.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 2022
      x-nestedObjectName: "Property"
    propertyYearOfReconstruction:
      description: 'Property year of reconstruction.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 2022
      x-nestedObjectName: "Property"
    propertyBillingFoundationLastUpdated:
      description: 'Property billing foundation last updated.'
      type: [ 'string', 'null' ]
      format: date-time
      example: 2019-11-14T22:00:00.000Z
      x-nestedObjectName: "PropertyBillingFoundation"
    propertyBillingFoundationTotalBusinessM2:
      description: 'Property billing foundation total business M2.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 2
      x-nestedObjectName: "PropertyBillingFoundation"
    propertyBillingFoundationTotalBusinessUnits:
      description: 'Property billing foundation total business units.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 2
      x-nestedObjectName: "PropertyBillingFoundation"
    propertyBillingFoundationTotalHousingM2:
      description: 'Property billing foundation total housing M2.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 2
      x-nestedObjectName: "PropertyBillingFoundation"
    propertyBillingFoundationTotalHousingUnits:
      description: 'Property billing foundation total housing units.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 2
      x-nestedObjectName: "PropertyBillingFoundation"
    propertyBillingFoundationTotalNonHeatedStorage:
      description: 'Property billing foundation total non heated storage.'
      type: [ 'integer', 'null' ]
      format: int32
      minimum: 0
      maximum: 2147483647
      example: 2
      x-nestedObjectName: "PropertyBillingFoundation"
    propertyUnits:
      type: array
      description: 'Property units.'
      minItems: 0
      maxItems: 1000
      items:
        $ref: './sub-schemas/UnitModel.schema.yaml#/UnitModel'
      x-nestedObjectName: "Property"
    bailiffCaseClosed:
      description: 'Bailiff Case closed date.'
      type: [ 'string', 'null' ]
      format: date-time
      example: 2019-11-14T22:00:00.000Z
      x-nestedObjectName: "BailiffCase"
    bailiffCaseCreated:
      description: 'Bailif Case created date.'
      type: [ 'string', 'null' ]
      format: date-time
      example: 2019-11-14T22:00:00.000Z
      x-nestedObjectName: "BailifCase"
    bailiffCaseCreatedBy:
      description: 'Bailiff Case created by.'
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 25
      example: "John"
      x-nestedObjectName: "BailiffCase"
    bailiffCaseNextNotification:
      description: 'Bailiff Case next notification date.'
      type: [ 'string', 'null' ]
      format: date-time
      example: 2019-11-14T22:00:00.000Z
      x-nestedObjectName: "BailiffCase"
    bailiffCaseStatus:
      description: 'Bailiff Case status.'
      type: [ 'string', 'null' ]
      pattern: "^(Created|Closed|Active)$"
      minLength: 6
      maxLength: 7
      example: "Created"
      x-nestedObjectName: "BailiffCase"
    expectedConsumptions:
      type: array
      description: 'Expected Consumptions.'
      minItems: 0
      maxItems: 1000
      items:
        $ref: './sub-schemas/ExpectedConsumptionModel.schema.yaml#/ExpectedConsumptionModel'
      x-nestedObjectName: "ExpectedConsumption"
    meterComponentRequirements:
      type: array
      description: 'Meter Component Requirements.'
      minItems: 0
      maxItems: 1000
      items:
        $ref: './sub-schemas/MeterComponentRequirementModel.schema.yaml#/MeterComponentRequirementModel'
      x-nestedObjectName: "MeterComponentRequirement"
