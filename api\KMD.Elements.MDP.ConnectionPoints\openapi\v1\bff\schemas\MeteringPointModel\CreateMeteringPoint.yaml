type: object
description: |
  Extended metering point version.
additionalProperties: false
required:
  - meteringPointVirtualId
properties:
  meteringPointVirtualId:
    nullable: true
    description: Internal MP MDP identifier'.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  meteringPointDomainData:
    description: Is mapped to `MeteringPointDomainData`
    $ref: "./MeteringPointDomainData.yaml"
  meteringPointDomainLocationId:
    description: Is mapped to `MeteringPointDomainLocationId`
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    nullable: true
  detailMeteringPointCharacteristic:
    description: Is mapped to `ScheduledMeterReadingDate`
    $ref: "./MeteringPointCharacteristic.yaml"
  occurrence:
    description: Is mapped to `Occurrence`
    $ref: "../DataTypes/DateTimeIso8601.yaml"
  addressWashInstruction:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `MPAddressWashInstruction`
    pattern: ^(D00|D01|D02|D03)$
  parentMeteringPointDomainLocationId:
    description: Is mapped to `ParentMeteringPointDomainLocationId`
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    nullable: true

