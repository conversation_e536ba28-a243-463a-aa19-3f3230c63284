title: WaterAttributesModel
type: object
additionalProperties: false
description: Water attributes model.
required:
  - connectionPointCategoryValue
  - installationTypeValue
  - temporary
properties:
  connectionPointCategoryValue:
    description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
    allOf:
      - $ref: '../_simple-type/Guid.yaml'
  installationTypeValue:
    description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
    allOf:
      - $ref: '../_simple-type/Guid.yaml'
  temporary:
    type: boolean
    description: Set to true, if the Connection Point is temporary.
  temporaryUntil:
    type: string
    description: |-
      When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
      grid company.
    format: date-time
    nullable: true
  installationDescription:
    allOf:
      - $ref: '../_simple-type/MediumStringNullable.yaml'
    description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
  flexAttributeObject:
    $ref: './FlexAttributeObject.yaml'
  decommissioned:
    type: string
    description: Decommissioned date.
    format: date-time
    nullable: true
