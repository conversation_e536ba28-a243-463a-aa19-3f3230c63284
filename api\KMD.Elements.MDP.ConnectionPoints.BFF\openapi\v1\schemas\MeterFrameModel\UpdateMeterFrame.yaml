type: object
additionalProperties: false
description: Meter Frame model.
required:
  - virtualId
  - isCommonReading
  - isCollectiveReading
  - meterMissing
  - meterSealed
  - meterWorkConsumerBilled
  - supplyStatus
  - tagAssignments
  - placementCode
  - connectionStatus
properties:
  virtualId:
    nullable: true
    description: Internal MeterFrame MDP identifier'.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  placementCode:
    description: 'Placement code.'
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  isCommonReading:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: 'Common Reading flag'
  isCollectiveReading:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: 'Is Collective Reading.'
  meterMissing:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: 'DK: MålerVæk.'
  placementSpecification:
    description: 'DK: Placeringsbeskrivelse.'
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  meterSealDate:
    nullable: true
    description: |-
      DK: Målerplomberingsdato.
      Indicates the date when the meter was sealed.
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  meterSealed:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      DK: MålerPlomberet.
      Indicates whether the meter is sealed.
  meterWorkConsumerBilled:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      DK: MålerYdelseFaktureresEjer.
      Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
  connectionStatus:
    description: 'DK: Tilslutningsstatus.'
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  decommissioned:
    description: Decommissioned date.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  statusChanged:
    description: Latest status change date.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  electricityAttributes:
    description: ElectricityAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameElectricityAttributesModel.yaml'
  heatingAttributes:
    description: HeatingAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameHeatingAttributesModel.yaml'
  waterAttributes:
    description: WaterAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameWaterAttributesModel.yaml'
  gisPropertiesElectricity:
    description: MeterFrameGisPropertiesElectricity
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameGisPropertiesElectricityModel.yaml'
  mainBranchLineElectricity:
    description: MainBranchLineElectricity
    nullable: true
    type: object
    oneOf:
      - $ref: './MainBranchLineElectricityModel.yaml'
  reserveBranchLineElectricity:
    description: ReserveBranchLineElectricity
    nullable: true
    type: object
    oneOf:
      - $ref: './ReserveBranchLineElectricityModel.yaml'
  mainBranchLineWater:
    description: MainBranchLineWater
    nullable: true
    type: object
    oneOf:
      - $ref: './MainBranchLineWaterModel.yaml'
  reserveBranchLineWater:
    description: ReserveBranchLineWater
    nullable: true
    type: object
    oneOf:
      - $ref: './ReserveBranchLineWaterModel.yaml'
  addressId:
    description: An UUID reference to a master data address.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  tagAssignments:
    type: array
    description: Tags.
    maxItems: 100
    items:
      $ref: '../TagAssignmentModel.yaml'
  supplyStatus:
    $ref: './MeterFrameSupplyStatusModel.yaml'
  meterReadingType:
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    description: 'A list of different reading methods a meter can have.'
  supplyDisconnectType:
    $ref: './MeterFrameSupplyDisconnectTypeModel.yaml'
  supplyDisconnectedComment:
    $ref: '../DataTypes/MediumStringNullable.yaml'
  noMeter:
    type: boolean
    description: 'DK: Målerfri.'
