type: object
description: Value list model.
additionalProperties: false
required:
  - id
  - name
  - values
  - headers
properties:
  id:
    $ref: '../DataTypes/Guid.yaml'
  name:
    description: Name of the Value List.
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
  values:
    type: array
    maxItems: 1000000
    description: Values - elements of a single Value List.
    items:
      $ref: './ValueListValue.yaml'
  headers:
    type: array
    maxItems: 1000000
    description: Headers of a Value List - available only if Value List contains any Attributes.
    items:
      $ref: './ValueListHeader.yaml'