description: Collection of paged and sorted connection point Default Value Sets.
allOf:
  - $ref: '../../PagedResult.yaml'
  - type: object
    description: Collection of Default Value Sets.
    properties:
      defaultValueSets:
        type: array
        description: Default Value Sets objects.
        items:
          $ref: './ConnectionPointDefaultValueSetListItem.yaml'
        maxItems: 256
    additionalProperties: false
