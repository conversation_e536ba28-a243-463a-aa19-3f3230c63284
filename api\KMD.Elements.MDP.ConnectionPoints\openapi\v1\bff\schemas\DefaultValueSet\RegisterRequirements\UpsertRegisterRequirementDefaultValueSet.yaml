type: object
description: Model used to get Register Requirement Default Value Set details.
additionalProperties: false
required:
  - name
  - supplyType
properties:
  id:
    description: Register Requirement Default Value Set identifier.
    allOf:
      - $ref: "../../DataTypes/GuidNullable.yaml"
  supplyType:
    $ref: '../../SupplyType.yaml'
    description: Supply type of the default value set related to Meter Frame.
  name:
    description: |-
      DK: Navn.
      Name of requirement (typically from MeteringComponent value list).
    allOf:
      - $ref: '../../DataTypes/ShortString.yaml'
  registerRequirementType:
    nullable: true
    description: List of possible register requirement "ControlRequirement", "MarketRequirement", "TechnicalRequirement"
    type: integer
    format: int32
    minimum: 1
    maximum: 3
    x-enumFlags: false
    x-enumNames:
      - ControlRequirement
      - MarketRequirement
      - TechnicalRequirement
    enum:
      - 1
      - 2
      - 3
  meteringComponentId:
    description: |-
      DK: MålingsKomponentId.
      Shared value list with Meter domain.
      The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
      It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  meterReadingRequired:
    description: |-
      DK: AflæsningPåkrævet
      Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
    type: boolean
    nullable: true
  rowVersion:
    description: Row version.
    allOf:
      - $ref: '../../DataTypes/RowVersion.yaml'
    nullable: true
