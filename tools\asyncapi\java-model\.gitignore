# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
package-lock.json
yarn.lock
pnpm-lock.yaml

# Logs
logs
*.log
lerna-debug.log*
.log
.DS_Store

# IntelliJ IDEA files
.idea/
*.iml
*.iws
out/
.idea_modules/
.gradle/
build/

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Temporary files
tmp/
temp/
dist/
coverage/
.cache/

# Debug files
*.swp
*.swo
*.swn

# TypeScript
*.tsbuildinfo

# Next.js
.next/
.vercel/

# Docker
docker-compose.override.yml

# Misc
.vscode/
Thumbs.db
*.bak
*.orig
/__transpiled/
