type: object
additionalProperties: false
description: Register Requirement model.
required:
  - virtualId
  - meterFrameVirtualId
  - registerRequirementType
  - name
  - futureRequirement
  - meterReadingRequired
properties:
  id:
    description: Register Requirement identifier.
    example: 1234
    allOf:
      - $ref: '../DataTypes/PositiveIntegerNullable.yaml'
  virtualId:
    description: Register Requirement internal identifier in MDP Connection point domain.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  meterFrameId:
    description: Meter Frame id.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  meterFrameVirtualId:
    description: VirtualId for Meter Frame id.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  registerRequirementType:
    description: Type of register requirement.
    nullable: true
    allOf:
      - $ref: './RegisterRequirementTypeModel.yaml'
  name:
    description: |-
      DK: Navn.
      Name of requirement (typically from MeteringComponent value list).
    allOf:
      - $ref: '../DataTypes/ShortStringObsolete.yaml'
    nullable: false
  meteringComponentId:
    nullable: true
    description: |-
      DK: MålingsKomponentId.
      Shared value list with Meter domain.
      The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
      It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  mainMeterFrameRegisterRequirementId:
    description: |-
      DK: HovedMalerrammeRegisterKravId.
      Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
    allOf:
      - $ref: '../DataTypes/IntegerNullable.yaml'
    nullable: true
  mainMeterFrameRegisterRequirementName:
    description: Used to refer to the main meter register requirement.
    allOf:
      - $ref: '../DataTypes/ShortStringObsoleteNullable.yaml'
  futureRequirement:
    description: |-
      DK: FremtidigtRegisterKra
      Indicates whether the register requirement should only be a future requirement. Default false.
      If set to true Validity period will be ignored.
    type: boolean
    nullable: false
  meterReadingRequired:
    description: |-
      DK: AflæsningPåkrævet
      Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
    type: boolean
    nullable: false
  requiredFrom:
    description: |-
      DK: GyldigFra.
      Indicates when the meter must meet the requirement.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  requiredUntil:
    description: |-
      DK: GyldigTil.
      Indicates from when the meter does not have to meet the requirement.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
  isStopped:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether register requirement should be stopped and has process for it.
  needAttention:
    nullable: true
    description: Describe if process need attention.
    allOf:
      - $ref: '../NeedAttention.yaml'
