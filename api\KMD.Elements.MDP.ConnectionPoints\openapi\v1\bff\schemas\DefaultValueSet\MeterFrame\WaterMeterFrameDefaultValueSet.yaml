type: object
description: Model containing water attributes of Meter Frame Default Value Set.
properties:
  driveBy:
      description: DK:DriveBy.
      allOf:
        - $ref: '../../DataTypes/GuidNullable.yaml'
  criticalCustomer:
      description: DK:KritiskKundekategori.
      allOf:
        - $ref: '../../DataTypes/GuidNullable.yaml'
  mediumCategory:
      description: DK:MediumKategori.
      allOf:
        - $ref: '../../DataTypes/GuidNullable.yaml'
  ownPump:
      description: DK:EgenPumpe.
      allOf:
        - $ref: '../../DataTypes/Boolean.yaml'
      nullable: true
  pressureEnhancer:
      description: DK:TrykForøger.
      allOf:
        - $ref: '../../DataTypes/Boolean.yaml'
      nullable: true
  qvkSensor:
      description: DK:QvkSensor.
      allOf:
        - $ref: '../../DataTypes/Boolean.yaml'
      nullable: true
