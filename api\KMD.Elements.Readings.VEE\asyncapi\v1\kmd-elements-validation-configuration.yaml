asyncapi: 2.6.0
id: https://async.api.kmdelements.com/meter-readings/
info:
  title: Validation configuration event
  x-maintainers: Team-DP-1
  version: "1.0.2-preview"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    Async API emitting translated Meter Readings events (readings aggregated by meter as in CIM format)

tags:
  - name: Team-DP-1
    description: Maintained by

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:

  kmd.elements.{tenantId}.event.validation-configuration.vee.v1:
    description: Topic with validation configuration upsert events
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Event with meter frame validation configuration.
      description: Event with meter frame validation configuration.
      operationId: MeterFrameValidationConfiguration
      message:
        bindings:
          kafka:
            key:
              description: |
                Key should be "app-validation-configuration" as there is only one
                configuration for whole application, this key is set only to enable
                compaction and keep this message on broker forever
              type: string
        $ref: "#/components/messages/ValidationConfigurationMsg"

components:
  messages:
    ValidationConfigurationMsg:
      name: ValidationConfigurationEvent
      title: Validation Configuration event
      summary: All validation configuration existing in the system
      description: |
        All validation configurations existing in MDM system
      contentType: application/json
      payload:
        $ref: "#/components/schemas/ValidationConfiguration"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      description: |
        Headers
      required:
        - tenantId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1

    ValidationConfiguration:
      type: object
      additionalProperties: false
      description: |
        All validation configurations existing in MDM system
      required:
        - groups
      properties:
        groups:
          type: array
          nullable: false
          maxLength: 1024
          items:
            $ref: '#/components/schemas/ValidationGroup'

    ValidationGroup:
      type: object
      description: |
        Validation group that is used to match and perform validation rules and execute actions
        on meter+readings message in VEE process
      required:
        - applicableAtOrAfter
        - applicableBefore
        - meterFilter
        - rules
      properties:
        priority:
          description: |
            Unique priority of validation group (1 is the highest)
            in whole ValidationConfiguration
          type: integer
          format: int64
          minimum: 1
          maximum: 10000
        applicableAtOrAfter:
          type: string
          format: date-time
        applicableBefore:
          type: string
          format: date-time
        meterFilter:
          $ref: '#/components/schemas/ValidationMeterMatchFilter'
        rules:
          type: array
          items:
            $ref: '#/components/schemas/ValidationRule'
    ValidationMeterMatchFilter:
      type: object
      description: |
        Filter part of ValidationGroup that is used to match ValidationGroups to given meter in given time
      required:
        - meter
        - meterFrame
        - connectionPoint
      properties:
        meter:
          $ref: '#/components/schemas/ValidationMeterFilter'
        meterFrame:
          $ref: '#/components/schemas/ValidationMeterFrameFilter'
        connectionPoint:
          $ref: '#/components/schemas/ValidationConnectionPointFilter'
    ValidationMeterFilter:
      type: object
      description: |
        Filter fields to be applied to meter
      required:
        - meterType
        - hesId
      properties:
        meterType:
          type: string
          maxLength: 256
          minLength: 1
          pattern: "^[A-z_]*$"
        hesId:
          type: string
          maxLength: 256
          minLength: 1
          pattern: "^[A-z_0-9\\-]*$"
    ValidationMeterFrameFilter:
      type: object
      description: |
        Filter fields to be applied to meter frame
      properties:
        purpose:
          type: string
          maxLength: 256
          minLength: 1
          pattern: "^[A-z_0-9\\-]*$"
        tariffConnectionPoint:
          type: string
          maxLength: 256
          minLength: 1
          pattern: "^[A-z_0-9\\-]*$"
    ValidationConnectionPointFilter:
      type: object
      description: |
        Filter fields to be applied to connection point
      required:
        - consumerCategory
        - netSettlementGroup
        - installationTypeValue
        - connectionPointCategoryValue
      properties:
        consumerCategory:
          type: string
          maxLength: 256
          minLength: 1
          pattern: "^[A-z0-9-]*$"
        netSettlementGroup:
          type: string
          maxLength: 256
          minLength: 1
          pattern: "^[A-z0-9-]*$"
        installationTypeValue:
          type: string
          maxLength: 256
          minLength: 1
          pattern: "^[A-z0-9-]*$"
        connectionPointCategoryValue:
          type: string
          maxLength: 256
          minLength: 1
          pattern: "^[A-z0-9-]*$"
    ValidationRule:
      type: object
      description: |
        Validation rule that should be performed on readings, this object is parent of
        many predefined validation rules
      required:
        - configurationType
        - id
      properties:
        id:
          type: string
          format: uuid
        configurationType:
          type: string
          maxLength: 256
          minLength: 1
          pattern: "^[A-z_]*$"
      discriminator: configurationType
      oneOf:
        - $ref: '#/components/schemas/ValidationRuleNotLessThanPrevious'
    ValidationRuleNotLessThanPrevious:
      type: object
      description: |
        Validation rule that checks that readings on register (incremental) A+ are not decreasing
      required:
        - tolerance
      properties:
        tolerance:
          type: number
          format: double

  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
