type: object
additionalProperties: false
description: Meter Frame model.
required:
  - meterMissing
  - meterSealed
  - meterWorkConsumerBilled
  - supplyStatus
  - tagAssignments
properties:
  virtualId:
    description: Internal MP MDP identifier.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  meterFrameNumber:
    description: Meter frame number
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  meterFrameIdentifier:
    description: Meter frame identifier set when new meter frame is saved to create.
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  placementCode:
    description: 'Placement code'
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  commonReading:
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    description: 'Common Reading.'
  meterMissing:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: 'DK: MålerVæk.'
  placementSpecification:
    description: 'DK: Placeringsbeskrivelse.'
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  meterSealDate:
    nullable: true
    description: |-
      DK: <PERSON><PERSON>lerplomberingsdato.
      Indicates the date when the meter was sealed.
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  meterSealed:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      DK: MålerPlomberet.
      Indicates whether the meter is sealed.
  meterWorkConsumerBilled:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      DK: MålerYdelseFaktureresEjer.
      Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
  connectionStatus:
    description: 'DK: Tilslutningsstatus.'
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  decommissioned:
    description: Decommissioned date.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  statusChanged:
    description: Latest status change date.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  electricityAttributes:
    description: ElectricityAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameElectricityAttributesModel.yaml'
  gisPropertiesElectricity:
    description: MeterFrameGisPropertiesElectricity
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameGisPropertiesElectricityModel.yaml'
  mainBranchLineElectricity:
    description: MainBranchLineElectricity
    nullable: true
    type: object
    oneOf:
      - $ref: './MainBranchLineElectricityModel.yaml'
  reserveBranchLineElectricity:
    description: ReserveBranchLineElectricity
    nullable: true
    type: object
    oneOf:
      - $ref: './ReserveBranchLineElectricityModel.yaml'
  addressId:
    description: An UUID reference to a master data address.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  tagAssignments:
    type: array
    description: Tags.
    maxItems: 100
    items:
      $ref: '../TagAssignmentModel.yaml'
  supplyStatus:
    $ref: './MeterFrameSupplyStatusModel.yaml'
  meterReadingType:
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    description: 'A list of different reading methods a meter can have.'
  supplyDisconnectType:
    $ref: './MeterFrameSupplyDisconnectTypeModel.yaml'
  noMeter:
    nullable: true
    type: boolean
    description: 'DK: Målerfri.'
  registerRequirements:
    type: array
    description: Register requirements.
    maxItems: 1000
    items:
      $ref: '../RegisterRequirementModel/RegisterRequirement.yaml'
  created:
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
    nullable: true
    description: |-
      DK: Oprettet.
      Creation time stamp of the Meter Frame.
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
