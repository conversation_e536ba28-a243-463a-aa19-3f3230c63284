title: ConnectionPointParentChildRelationDetails
type: object
description: Represents connection point template relations between metering points and register requirement objects.
additionalProperties: false
required:
  - relationId
properties:
  relationId:
    $ref: "../../DataTypes/Guid.yaml"
  parentMeteringPointDefaultValueSet:
    type: object
    description: "Parent metering point default value set"
    nullable: true
    oneOf:
      - $ref: "../MeteringPointsTemplate/MeteringPointDefaultValueSetDetails.yaml"
  childMeteringPointDefaultValueSet:
    type: object
    description: "Child metering point default value set"
    nullable: true
    oneOf:
      - $ref: "../MeteringPointsTemplate/MeteringPointDefaultValueSetDetails.yaml"
  registerRequirementDefaultValueSet:
    type: object
    description: "Register requirement default value set"
    nullable: true
    oneOf:
      - $ref: "../MeterFrameTemplate/RegisterRequirementDefaultValueSetDetails.yaml"
  meterFrameTemplateId:
    $ref: "../../DataTypes/GuidNullable.yaml"
