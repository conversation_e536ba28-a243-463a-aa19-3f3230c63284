type: object
description: Model for update manually main data by user, ignores
required:
  - comment
  - processStatus
properties:
  comment:
    type: string
    description: Comment on process.
    maxLength: 512
    pattern: "^.*$"
    nullable: true
  assignedToUser:
    type: string
    description: User assigned to process.
    format: uuid
    nullable: true
  processStatus:
    description: New process status.
    allOf:
      - $ref: "./Status.yaml"
    nullable: true
additionalProperties: false
