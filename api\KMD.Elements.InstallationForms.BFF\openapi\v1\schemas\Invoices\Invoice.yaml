title: Invoice
description: Invoice entry model
type: object
additionalProperties: false
required:
  - invoiceEntryId
  - number
  - createdDate
  - status
  - isRequired
  - type
properties:
  invoiceEntryId:
    type: string
    format: uuid
    description: Invoice entry id
  invoiceId:
    type: string
    format: uuid
    description: Invoice id (from SingleInvoicing service)
    nullable: true
  number:
    type: string
    description: "Invoice number"
    maxLength: 25
    pattern: "^.*$"
    nullable: true
  status:
    $ref: "./InvoiceState.yaml"
  totalSum:
    description: Total sum of invoice.
    type: number
    format: decimal
    minimum: -10000000000000000
    maximum: +10000000000000000
    nullable: true
    example: 23534.99
  currency:
    description: Currency of total sum.
    type: string
    pattern: "^(NoUnit|DKK|EUR|NOK|SEK|GBP|USD|EuroCent|Ore)$"
    example: DKK
    nullable: true
    maxLength: 10
  createdDate:
    type: string
    format: date-time
    description: "Entry created date"
  createdInErpDate:
    type: string
    format: date-time
    description: "Created in ERP date"
    nullable: true
  isRequired:
    description: Is invoice required for automatic setting of connection paid flag
    type: boolean
  type:
    $ref: "./InvoiceType.yaml"
