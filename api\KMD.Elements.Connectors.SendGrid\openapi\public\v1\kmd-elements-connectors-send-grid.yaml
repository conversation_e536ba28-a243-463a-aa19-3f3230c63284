openapi: 3.0.3
info:
  title: KMD.Elements.Connectors.SendGrid
  description: |-
    # KMD Elements Connector Send Grid system

    Stability level: PREVIEW<br/>
    <br/>
    The **KMD Elements Connecter Send Grid** is part of the KMD Element product.<br/>

    ## Capabilities
    The API allows to:
    - communicate with Send Grid system
    ---

  termsOfService: "https://www.kmd.net/terms-of-use"

  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>

  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"

  version: "0.11"

  x-maintainers: Team-MO-1

servers:
  - url: "https://localhost:5094"
    description: Localhost

security:
  - TwilioSignature: []
    TwilioTimestamp: []

tags:
  - name: SendGridConnector
    description: Connector service to communicate with Send Grid system

paths:
  /v1/send-grid/commands/update-email-status:
    post:
      tags:
        - SendGridConnector
      operationId: updateEmailStatus
      description: Update email status.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Events'
        required: true
      summary: Endpoint for updating email status.
      responses:
        "202":
          $ref: "#/components/responses/202"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - TwilioSignature: []
          TwilioTimestamp: []

  /v1/send-grid/commands/inbound-email:
    post:
      tags:
        - SendGridConnector
      operationId: receiveInboundEmail
      summary: Receive inbound email from SendGrid
      description: Receive inbound email from SendGrid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: string
              format: binary
              maxLength: 52428800
      responses:
        "202":
          $ref: "#/components/responses/202"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - TwilioSignature: []
          TwilioTimestamp: []

components:
  schemas:
    Events:
      description: Array of Events from SendGrid
      type: array
      maxItems: 1000
      items:
        $ref: "#/components/schemas/EventWebhook"

    EventWebhook:
      type: object
      additionalProperties: false
      description: Object contains an Event data from SendGrid
      properties:
        email:
          type: string
          description: Recipients email address.
          example: "<EMAIL>"
          pattern: ".*"
          nullable: false
          maxLength: 100
        timestamp:
          description: Timestamp.
          type: integer
          format: int32
          minimum: 1
          maximum: 2147483647
          example: 1732012861
          nullable: false
        event:
          $ref: "#/components/schemas/EventType"
        bounceEventType:
          $ref: "#/components/schemas/BounceEventType"
        sendGridEventId:
          type: string
          description: SendGridEventId.
          example: "123123"
          pattern: ".*"
          nullable: false
          maxLength: 100
          x-json-property: sg_event_id
        sendGridMessageId:
          type: string
          description: SendGridMessageId.
          example: "123123"
          pattern: ".*"
          nullable: false
          maxLength: 100
          x-json-property: sg_message_id
        tls:
          type: string
          description: TLS.
          example: "123123"
          nullable: true
          pattern: ".*"
          maxLength: 100
        marketingCampainId:
          type: string
          description: MarketingCampainId.
          example: "123123"
          nullable: true
          pattern: ".*"
          maxLength: 100
        marketingCampainName:
          type: string
          description: MarketingCampainName.
          example: "123123"
          nullable: true
          pattern: ".*"
          maxLength: 100
        reason:
          type: string
          description: Error response returned by the receiving server that describes the reason this event type was triggered.
          example: "Bounced Address"
          pattern: ".*"
          nullable: true
          maxLength: 200
        response:
          type: string
          description: The full text of the HTTP response error returned from the receiving server.
          example: "250 OK"
          pattern: ".*"
          nullable: true
          maxLength: 500
        bounceClassification:
          type: string
          description: Bounce classification.
          example: "Invalid Address"
          pattern: "^(Invalid Address|Technical|Content|Reputation|Frequency/Volume|Mailbox Unavailable|Unclassified)$"
          nullable: true
          maxLength: 100
          x-json-property: bounce_classification
        tenantId:
          description: Identifier of a tenant.
          type: integer
          format: int32
          minimum: 1
          maximum: 9999
          example: 650
          nullable: false
        correlationId:
          type: string
          format: uuid
          description: The correlation id.
          example: '09001C06-6C61-4D71-A5B3-ECB0E0E230F0'
          nullable: false

    EventType:
      description: Type of received event.
      type: string
      enum:
        - processed
        - deferred
        - delivered
        - open
        - click
        - bounce
        - dropped
        - spamreport
        - unsubscribe
        - group_unsubscribe
        - group_resubscribe

    BounceEventType:
      description: Type of received bounced event.
      type: string
      enum:
        - Bounce
        - Blocked
        - Expired

    ProblemDetails:
        title: ProblemDetails
        type: object
        description: |-
          ProblemDetails provides detailed information about an errors that occurred during an api call execution.
          This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
        properties:
          type:
            description: The error type.
            type: string
            pattern: ".*"
            maxLength: 64
            nullable: true
            example: "https://errors.kmdelements.com/500"
          title:
            description: "A short, human-readable summary of the problem type."
            type: string
            pattern: ".*"
            maxLength: 1024
            nullable: true
            example: Error short description
          status:
            description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
            type: integer
            format: int32
            minimum: 400
            maximum: 599
            nullable: true
            example: 500
          detail:
            description: A human-readable explanation for what exactly happened (in English).
            type: string
            pattern: ".*"
            maxLength: 1024
            nullable: true
            example: Description what exactly happened
          instance:
            description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
            type: string
            pattern: ".*"
            maxLength: 1024
            nullable: true
            example: /resources-path/1
        additionalProperties: false

    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          properties:
            error:
              type: string
              description: Error message.
              pattern: ".*"
              maxLength: 1000

  responses:
    "202":
      description: 202 Accepted.
      content:
        application/json:
          schema:
            type: string
            minLength: 1
            maxLength: 50
            description: Request was accepted and will be forwarded.
            pattern: ".*"
          examples:
            CreatedExample:
              value: "Accepted"
    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ValidationProblemDetails"
          examples:
            BadRequestExample:
              value:
                type: "https://errors.kmdelements.com/400"
                title: Bad Request
                status: 400
                detail: "Invalid request"
                instance: /resouces-path/1
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            UnauthorizedExample:
              value:
                type: "https://errors.kmdelements.com/401"
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources-path/1
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ForbiddenExample:
              value:
                type: "https://errors.kmdelements.com/403"
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources-path/1
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/404"
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources-path/1
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            format: int64
            example: 360
            minimum: 1
            maximum: 1000
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            TooManyRequestsExample:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 400
                detail: Rate limit is exceeded.
                instance: /resources-path/1
    "499":
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/499"
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: /resources-path/1
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            InternalServerErrorExample:
              value:
                type: "https://errors.kmdelements.com/500"
                title: Internal Server Error
                status: 500
                detail: "body.0.age: Value `Not Int` does not match format `int32`"
                instance: /resources-path/1
    "503":
      description: 503 Service Unavailable.
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            type: integer
            format: int64
            example: 360
            minimum: 1
            maximum: 1000
    "504":
      description: 504 Gateway Timeout.
  securitySchemes:
    TwilioSignature:
      type: apiKey
      description: Signature verification HTTP header name for the signature being sent.
      in: header
      name: X-Twilio-Email-Event-Webhook-Signature
    TwilioTimestamp:
      type: apiKey
      description: Timestamp HTTP header name for timestamp.
      in: header
      name: X-Twilio-Email-Event-Webhook-Timestamp
