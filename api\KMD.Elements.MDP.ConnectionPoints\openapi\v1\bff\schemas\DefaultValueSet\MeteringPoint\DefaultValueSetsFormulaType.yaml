type: object
description: |
  Formula Type indicates which formula template to use.
additionalProperties: false
required:
  - code
  - name
  - formulaTemplate
properties:
  code:
    description: |
      The formula can be created from a template, which enables reuse.
      The template has a machine-readable short code.
    allOf:
      - $ref: '../../DataTypes/OneWordString.yaml'
    nullable: true
  name:
    description: |
      The formula can be created from a template, which enables reuse.
      The template has a name which is presented in the user interface.
    allOf:
      - $ref: '../../DataTypes/MediumString.yaml'
    nullable: true
  formulaTemplate:
    description: |
      Formula template describes how the formula should look like and be built,
      as well as which formula parameters the formula should consist of.
    allOf:
      - $ref: '../../DataTypes/DescriptionString.yaml'
    nullable: true
  description:
    description: |
      The formula can be created from a template, which enables reuse.
      The template has a longer description that can be presented in the user interface.
    allOf:
      - $ref: '../../DataTypes/DescriptionString.yaml'
    nullable: true
