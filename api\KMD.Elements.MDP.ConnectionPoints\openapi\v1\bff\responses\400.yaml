description: Bad request
content:
  application/problem+json:
    schema:
      description: Bad request
      allOf:
        - $ref: "../schemas/ProblemDetailsWithErrors.yaml"
    examples:
      BadRequestExample:
        value:
          type: "https://errors.kmdelements.com/400"
          title: Bad Request
          status: 400
          detail: "Invalid request"
          instance: "/resources/1"
          errors:
            name:
              - name is too long
            doors:
              - invalid value
