<!-- Created with WSDL to XSD Generator (https://www.UtilityArena.com) -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://sap.com/xi/SAPGlobal20/Global" xmlns="http://sap.com/xi/SAPGlobal20/Global"><!--##SCHEMA_TARGET_NAMESPACE##:http://sap.com/xi/SAPGlobal/GDT--><!--##SCHEMA_IMPORTED_NAMESPACE##:-->
   <xsd:simpleType name="Indicator_Variation1">
      <xsd:restriction base="xsd:boolean"/>
   </xsd:simpleType>
   <xsd:attribute name="DeletionIndicator" type="Indicator_Variation1"/>
   <!--##SCHEMAEND##-->
   <!--##SCHEMA_TARGET_NAMESPACE##:http://sap.com/xi/IS-U/Global2-->
   <!--##SCHEMA_IMPORTED_NAMESPACE##:http://dongenergy.dk/mdms/ISU-U/enhancement-->
   <xsd:simpleType name="AgencyIdentificationCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="3"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="BusinessDocumentMessageHeader">
      <xsd:sequence>
         <xsd:element minOccurs="0" name="ID" type="BusinessDocumentMessageID"/>
         <xsd:element minOccurs="0" name="UUID" type="UUID"/>
         <xsd:element minOccurs="0" name="ReferenceID" type="BusinessDocumentMessageID"/>
         <xsd:element minOccurs="0" name="ReferenceUUID" type="UUID"/>
         <xsd:element name="CreationDateTime" type="GLOBAL_DateTime"/>
         <xsd:element minOccurs="0" name="TestDataIndicator" type="Indicator_Variation2"/>
         <xsd:element minOccurs="0"
                       name="ReconciliationIndicator"
                       type="Indicator_Variation2"/>
         <xsd:element minOccurs="0" name="SenderBusinessSystemID" type="BusinessSystemID"/>
         <xsd:element minOccurs="0"
                       name="RecipientBusinessSystemID"
                       type="BusinessSystemID"/>
         <xsd:element minOccurs="0"
                       name="SenderParty"
                       type="BusinessDocumentMessageHeaderParty"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="RecipientParty"
                       type="BusinessDocumentMessageHeaderParty"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="BusinessScope"
                       type="BusinessScope"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="BusinessDocumentMessageHeaderParty">
      <xsd:sequence>
         <xsd:element minOccurs="0" name="InternalID" type="PartyInternalID"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="StandardID"
                       type="PartyStandardID"/>
         <xsd:element minOccurs="0"
                       name="ContactPerson"
                       type="BusinessDocumentMessageHeaderPartyContactPerson"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="BusinessDocumentMessageHeaderPartyContactPerson">
      <xsd:sequence>
         <xsd:element minOccurs="0" name="InternalID" type="ContactPersonInternalID"/>
         <xsd:element maxOccurs="4"
                       minOccurs="0"
                       name="OrganisationFormattedName"
                       type="LANGUAGEINDEPENDENT_MEDIUM_Name"/>
         <xsd:element maxOccurs="4"
                       minOccurs="0"
                       name="PersonFormattedName"
                       type="LANGUAGEINDEPENDENT_LONG_Name"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="PhoneNumber"
                       type="PhoneNumber"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="FaxNumber"
                       type="PhoneNumber"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="EmailURI"
                       type="EmailURI"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="BusinessDocumentMessageID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="BusinessDocumentMessageID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencySchemeAgencyID" type="AgencyIdentificationCode"/>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="BusinessDocumentMessageID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="35"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="BusinessScope">
      <xsd:sequence>
         <xsd:element name="TypeCode" type="BusinessScopeTypeCode"/>
         <xsd:element minOccurs="0" name="InstanceID" type="BusinessScopeInstanceID"/>
         <xsd:element minOccurs="0" name="ID" type="BusinessScopeID"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="BusinessScopeID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="BusinessScopeID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="BusinessScopeID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="36"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="BusinessScopeInstanceID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="BusinessScopeInstanceID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="BusinessScopeInstanceID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="36"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="BusinessScopeTypeCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="BusinessScopeTypeCode.Content">
            <xsd:attribute name="listID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listVersionID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="15"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencySchemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencySchemeAgencyID" type="AgencyIdentificationCode"/>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="BusinessScopeTypeCode.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="4"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="BusinessSystemID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="60"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="ContactPersonInternalID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="ContactPersonInternalID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="ContactPersonInternalID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="32"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="CountryCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="3"/>
         <xsd:minLength value="2"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="CountryDiallingCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="10"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="Date">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Date</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:date">
         <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="DateTime">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>DateTime</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="DateTime.Content">
            <xsd:attribute name="timeZoneCode" type="TimeZoneCode"/>
            <xsd:attribute name="daylightSavingTimeIndicator" type="xsd:boolean"/>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="DateTime.Content">
      <xsd:restriction base="xsd:dateTime">
         <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}(.[0-9]{1,7})?Z"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="DecimalValuePrecision">
      <xsd:sequence>
         <xsd:element minOccurs="0" name="TotalDigitNumberValue" type="NumberValue"/>
         <xsd:element minOccurs="0" name="FractionDigitNumberValue" type="NumberValue"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="EmailURI">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>URI</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="xsd:anyURI">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="GLOBAL_DateTime">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>DateTime</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:dateTime">
         <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}(.[0-9]{1,7})?Z"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="HouseID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="10"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="Indicator_Variation2">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Indicator</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:boolean"/>
   </xsd:simpleType>
   <xsd:complexType name="InstallationPointID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="InstallationPointID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="InstallationPointID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="40"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="LANGUAGEINDEPENDENT_LONG_Name">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Name</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="80"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="LANGUAGEINDEPENDENT_MEDIUM_Name">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Name</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="40"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="LanguageCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:language">
         <xsd:maxLength value="9"/>
         <xsd:minLength value="2"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="MEDIUM_Name">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Name</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="MEDIUM_Name.Content">
            <xsd:attribute name="languageCode" type="LanguageCode"/>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="MEDIUM_Name.Content">
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="40"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="MeasureUnitCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="3"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="MeterReadingResultAdjustmentFactorValue">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Value</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:decimal">
         <xsd:totalDigits value="12"/>
         <xsd:fractionDigits value="5"/>
         <xsd:maxInclusive value="9999999.99999"/>
         <xsd:minInclusive value="-9999999.99999"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="NumberValue">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Value</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:int">
         <xsd:maxInclusive value="999999999"/>
         <xsd:minInclusive value="0"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="PartyInternalID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="PartyInternalID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="PartyInternalID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="32"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="PartyStandardID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="PartyStandardID.Content">
            <xsd:attribute name="schemeAgencyID" use="required">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="3"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="PartyStandardID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="13"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="PhoneNumber">
      <xsd:sequence>
         <xsd:element minOccurs="0" name="AreaID" type="PhoneNumberAreaID"/>
         <xsd:element minOccurs="0" name="SubscriberID" type="PhoneNumberSubscriberID"/>
         <xsd:element minOccurs="0" name="ExtensionID" type="PhoneNumberExtensionID"/>
         <xsd:element minOccurs="0" name="CountryCode" type="CountryCode"/>
         <xsd:element minOccurs="0" name="CountryDiallingCode" type="CountryDiallingCode"/>
         <xsd:element minOccurs="0" name="CountryName" type="MEDIUM_Name"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:simpleType name="PhoneNumberAreaID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="10"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="PhoneNumberExtensionID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="10"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="PhoneNumberSubscriberID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="30"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="PostalCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="10"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="ProductInternalID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="ProductInternalID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="ProductInternalID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="60"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="ProductStandardID_V1">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="ProductStandardID_V1.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="ProductStandardID_V1.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="50"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="RegionCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="RegionCode.Content">
            <xsd:attribute name="listID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listVersionID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="15"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencySchemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencySchemeAgencyID" type="AgencyIdentificationCode"/>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="RegionCode.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="6"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="SerialID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="30"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="StreetName">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Name</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="60"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="Time">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Time</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:time">
         <xsd:pattern value="[0-9]{2}:[0-9]{2}:[0-9]{2}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="TimeZoneCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="10"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UUID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UUID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UUID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:length value="36"/>
         <xsd:pattern value="[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesAdvancedMeterCapabilityCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesAdvancedMeterCapabilityCode.Content">
            <xsd:attribute name="listID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listVersionID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="15"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesAdvancedMeterCapabilityCode.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="4"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesAdvancedMeteringSystemID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesAdvancedMeteringSystemID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesAdvancedMeteringSystemID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="4"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesConnectionStatusChangeRequestID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesConnectionStatusChangeRequestID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesConnectionStatusChangeRequestID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="12"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesDataUsageStatusCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesDataUsageStatusCode.Content">
            <xsd:attribute name="listID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listVersionID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="15"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencySchemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencySchemeAgencyID" type="AgencyIdentificationCode"/>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesDataUsageStatusCode.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="10"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesDeviceID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesDeviceID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesDeviceID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="18"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesDeviceRegisterGroupID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesDeviceRegisterGroupID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeVersionID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="15"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesDeviceRegisterGroupID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="8"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesDeviceRelationshipRoleCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesDeviceRelationshipRoleCode.Content">
            <xsd:attribute name="listID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listVersionID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="15"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesDeviceRelationshipRoleCode.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="2"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="UtilitiesDivisionCategoryCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="2"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesMeasurementRecurrenceCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesMeasurementRecurrenceCode.Content">
            <xsd:attribute name="listID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesMeasurementRecurrenceCode.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="4"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="UtilitiesMeasurementTaskCategoryCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="2"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesMeasurementTaskDeviceAssignmentTypeCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesMeasurementTaskDeviceAssignmentTypeCode.Content">
            <xsd:attribute name="listID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listVersionID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="15"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesMeasurementTaskDeviceAssignmentTypeCode.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="2"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesMeasurementTaskID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesMeasurementTaskID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesMeasurementTaskID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="18"/>
         <xsd:minLength value="1"/>
         <xsd:pattern value="\d+"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesMeasurementTaskValidationProfileCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesMeasurementTaskValidationProfileCode.Content">
            <xsd:attribute name="listID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listVersionID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="15"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesMeasurementTaskValidationProfileCode.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="4"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="UtilitiesObjectIdentificationSystemCodeText">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Text</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="15"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesPointOfDeliveryPartyID">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Identifier</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesPointOfDeliveryPartyID.Content">
            <xsd:attribute name="schemeID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="schemeAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesPointOfDeliveryPartyID.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="50"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="UtilitiesQuantityAdjustmentFactorValue">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Value</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:decimal">
         <xsd:totalDigits value="12"/>
         <xsd:fractionDigits value="5"/>
         <xsd:maxInclusive value="9999999.99999"/>
         <xsd:minInclusive value="-9999999.99999"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilitiesTimeOfUseCode">
      <xsd:annotation>
         <xsd:documentation xml:lang="EN">
            <RepresentationTerm>Code</RepresentationTerm>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:simpleContent>
         <xsd:extension base="UtilitiesTimeOfUseCode.Content">
            <xsd:attribute name="listID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
            <xsd:attribute name="listAgencyID">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:token">
                     <xsd:maxLength value="60"/>
                     <xsd:minLength value="1"/>
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:attribute>
         </xsd:extension>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:simpleType name="UtilitiesTimeOfUseCode.Content">
      <xsd:restriction base="xsd:token">
         <xsd:maxLength value="10"/>
         <xsd:minLength value="1"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnBulkReqMsg">
      <xsd:sequence>
         <xsd:element name="MessageHeader" type="BusinessDocumentMessageHeader"/>
         <xsd:element maxOccurs="unbounded"
                       name="UtilitiesDeviceERPSmartMeterReplicationRequestMessage"
                       type="UtilsDvceERPSmrtMtrRplctnReqMsg"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqConncnSts">
      <xsd:sequence>
         <xsd:element name="DisconnectionStartDateTime" type="GLOBAL_DateTime"/>
         <xsd:element name="DisconnectionEndDateTime" type="GLOBAL_DateTime"/>
         <xsd:element minOccurs="0"
                       name="ChangeRequestID"
                       type="UtilitiesConnectionStatusChangeRequestID"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqContrAssgmt">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqDataUsgeSts">
      <xsd:sequence>
         <xsd:element name="DateTimeFrom" type="DateTime"/>
         <xsd:element name="DateTimeTo" type="DateTime"/>
         <xsd:element name="DataUsageStatus" type="UtilitiesDataUsageStatusCode"/>
         <xsd:element name="CreationDate" type="Date"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqDvceAssgmt">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element name="UtilitiesMeasurementTaskDeviceAssignmentTypeCode"
                       type="UtilitiesMeasurementTaskDeviceAssignmentTypeCode"/>
         <xsd:element minOccurs="0"
                       name="UtilitiesQuantityAdjustmentFactorValue"
                       type="UtilitiesQuantityAdjustmentFactorValue"/>
         <xsd:element name="AssignedUtilitiesDeviceID" type="UtilitiesDeviceID"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqExtIdn">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="StartTime" type="Time"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element name="EndTime" type="Time"/>
         <xsd:element name="UtilitiesPointOfDeliveryPartyID"
                       type="UtilitiesPointOfDeliveryPartyID"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqIndivMatlMfrInfo">
      <xsd:sequence>
         <xsd:element minOccurs="0" name="PartyInternalID" type="PartyInternalID"/>
         <xsd:element minOccurs="0" name="PartNumberID" type="ProductInternalID"/>
         <xsd:element minOccurs="0" name="SerialID" type="SerialID"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqInstPtAddrInfo">
      <xsd:sequence>
         <xsd:element minOccurs="0" name="HouseID" type="HouseID"/>
         <xsd:element minOccurs="0" name="StreetPostalCode" type="PostalCode"/>
         <xsd:element minOccurs="0"
                       name="CityName"
                       type="LANGUAGEINDEPENDENT_MEDIUM_Name"/>
         <xsd:element minOccurs="0" name="StreetName" type="StreetName"/>
         <xsd:element name="CountryCode" type="CountryCode"/>
         <xsd:element minOccurs="0" name="RegionCode" type="RegionCode"/>
         <xsd:element minOccurs="0" name="TimeZoneCode" type="TimeZoneCode"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqInstPtHierRelshp">
      <xsd:sequence>
         <xsd:element name="ParentInstallationPointID" type="InstallationPointID"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqLgclLoc">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element name="LogicalInstallationPointID" type="InstallationPointID"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="ConnectionStatus"
                       type="UtilsDvceERPSmrtMtrRplctnReqConncnSts"/>
      </xsd:sequence>
      <xsd:attribute name="ConnectionStatusListCompleteTransmissionIndicator"
                      type="Indicator_Variation2"/>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqLoc">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element name="InstallationPointID" type="InstallationPointID"/>
         <xsd:element name="InstallationPointAddressInformation"
                       type="UtilsDvceERPSmrtMtrRplctnReqInstPtAddrInfo"/>
         <xsd:element minOccurs="0"
                       name="ModificationInformation"
                       type="UtilsDvceERPSmrtMtrRplctnReqModifInfo"/>
         <xsd:element name="InstallationPointHierarchyRelationship"
                       type="UtilsDvceERPSmrtMtrRplctnReqInstPtHierRelshp"/>
         <xsd:group ref="LocationEnhancementReplication"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqModifInfo">
      <xsd:sequence>
         <xsd:element minOccurs="0" name="InstallationDate" type="Date"/>
         <xsd:element minOccurs="0" name="InstallationTime" type="Time"/>
         <xsd:element minOccurs="0" name="RemoveDate" type="Date"/>
         <xsd:element minOccurs="0" name="RemoveTime" type="Time"/>
         <xsd:element minOccurs="0" name="TimeZoneCode" type="TimeZoneCode"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqMsg">
      <xsd:sequence>
         <xsd:element name="MessageHeader" type="BusinessDocumentMessageHeader"/>
         <xsd:element name="UtilitiesDevice" type="UtilsDvceERPSmrtMtrRplctnReqUtilsDvce"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqReg">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element name="UtilitiesMeasurementTaskID" type="UtilitiesMeasurementTaskID"/>
         <xsd:element minOccurs="0"
                       name="UtilitiesObjectIdentificationSystemCodeText"
                       type="UtilitiesObjectIdentificationSystemCodeText"/>
         <xsd:element minOccurs="0"
                       name="UtilitiesMeasurementTaskCategoryCode"
                       type="UtilitiesMeasurementTaskCategoryCode"/>
         <xsd:element minOccurs="0"
                       name="UtilitiesDivisionCategoryCode"
                       type="UtilitiesDivisionCategoryCode"/>
         <xsd:element minOccurs="0"
                       name="UtilitiesMeasurementRecurrenceCode"
                       type="UtilitiesMeasurementRecurrenceCode"/>
         <xsd:element minOccurs="0" name="TimeZoneCode" type="TimeZoneCode"/>
         <xsd:element maxOccurs="unbounded"
                       name="Specifications"
                       type="UtilsDvceERPSmrtMtrRplctnReqSpecs"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="PointOfDeliveryAssignment"
                       type="UtilsDvceERPSmrtMtrRplctnReqUtilsPtDelivAssgmt"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="DeviceAssignment"
                       type="UtilsDvceERPSmrtMtrRplctnReqDvceAssgmt"/>
      </xsd:sequence>
      <xsd:attribute name="deviceAssignmentListCompleteTransmissionIndicator"
                      type="Indicator_Variation2"
                      use="required"/>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqRelshp">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element name="RoleCode" type="UtilitiesDeviceRelationshipRoleCode"/>
         <xsd:element name="RelatedUtilitiesDeviceID" type="UtilitiesDeviceID"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqSmrtMtr">
      <xsd:sequence>
         <xsd:element name="UtilitiesAdvancedMeteringSystemID"
                       type="UtilitiesAdvancedMeteringSystemID"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqSmrtMtrActvCapblts">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element minOccurs="0"
                       name="UtilitiesAdvancedMeteringSystemID"
                       type="UtilitiesAdvancedMeteringSystemID"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="Capability"
                       type="UtilsDvceERPSmrtMtrRplctnReqSmrtMtrActvCapbltsCapblt"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqSmrtMtrActvCapbltsCapblt">
      <xsd:sequence>
         <xsd:element name="UtilitiesAdvancedMeterCapabilityCode"
                       type="UtilitiesAdvancedMeterCapabilityCode"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqSpecs">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element minOccurs="0"
                       name="UtilitiesTimeOfUseCode"
                       type="UtilitiesTimeOfUseCode"/>
         <xsd:element name="MeasureUnitCode" type="MeasureUnitCode"/>
         <xsd:element name="DecimalValuePrecision" type="DecimalValuePrecision"/>
         <xsd:element minOccurs="0"
                       name="MeterReadingResultAdjustmentFactorValue"
                       type="MeterReadingResultAdjustmentFactorValue"/>
         <xsd:element minOccurs="0"
                       name="UtilitiesMeasurementTaskValidationProfileCode"
                       type="UtilitiesMeasurementTaskValidationProfileCode"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqUtilsDvce">
      <xsd:sequence>
         <xsd:element name="ID" type="UtilitiesDeviceID"/>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element name="SerialID" type="SerialID"/>
         <xsd:element name="MaterialID" type="ProductInternalID"/>
         <xsd:element minOccurs="0"
                       name="ProductUniqueItemID"
                       type="ProductStandardID_V1"/>
         <xsd:element minOccurs="0"
                       name="IndividualMaterialManufacturerInformation"
                       type="UtilsDvceERPSmrtMtrRplctnReqIndivMatlMfrInfo"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="RegisterGroup"
                       type="UtilsDvceERPSmrtMtrRplctnReqUtilsDvceRegGrp"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="Register"
                       type="UtilsDvceERPSmrtMtrRplctnReqReg"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="LogicalLocation"
                       type="UtilsDvceERPSmrtMtrRplctnReqLgclLoc"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="Location"
                       type="UtilsDvceERPSmrtMtrRplctnReqLoc"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="Relationship"
                       type="UtilsDvceERPSmrtMtrRplctnReqRelshp"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="ContractAssignment"
                       type="UtilsDvceERPSmrtMtrRplctnReqContrAssgmt"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="SmartMeterActiveCababilities"
                       type="UtilsDvceERPSmrtMtrRplctnReqSmrtMtrActvCapblts"/>
         <xsd:element name="SmartMeter" type="UtilsDvceERPSmrtMtrRplctnReqSmrtMtr"/>
         <xsd:element maxOccurs="unbounded"
                       minOccurs="0"
                       name="DataUsageStatus"
                       type="UtilsDvceERPSmrtMtrRplctnReqDataUsgeSts"/>
         <xsd:group ref="DeviceEnhancementReplication"/>
      </xsd:sequence>
      <xsd:attribute name="RegisterGroupCompleteTransmissionIndicator"
                      type="Indicator_Variation2"/>
      <xsd:attribute name="registerListCompleteTransmissionIndicator"
                      type="Indicator_Variation2"
                      use="required"/>
      <xsd:attribute name="logicalLocationListCompleteTransmissionIndicator"
                      type="Indicator_Variation2"/>
      <xsd:attribute name="locationListCompleteTransmissionIndicator"
                      type="Indicator_Variation2"
                      use="required"/>
      <xsd:attribute name="relationshipListCompleteTransmissionIndicator"
                      type="Indicator_Variation2"
                      use="required"/>
      <xsd:attribute name="dataUsageStatusListCompleteTransmissionIndicator"
                      type="Indicator_Variation2"/>
      <xsd:attribute name="SmartMeterActiveCapabailtiesCompleteTransmissionIndicator"
                      type="Indicator_Variation2"/>
      <xsd:attribute name="ContractAssignmentCompleteTransmissionIndicator"
                      type="Indicator_Variation2"/>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqUtilsDvceRegGrp">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element name="RegisterGroupID" type="UtilitiesDeviceRegisterGroupID"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="UtilsDvceERPSmrtMtrRplctnReqUtilsPtDelivAssgmt">
      <xsd:sequence>
         <xsd:element name="StartDate" type="Date"/>
         <xsd:element name="EndDate" type="Date"/>
         <xsd:element maxOccurs="unbounded"
                       name="ExternalIdentification"
                       type="UtilsDvceERPSmrtMtrRplctnReqExtIdn"/>
         <xsd:group ref="DataHubTransferMarkerReplication"/>
         <xsd:group ref="InstallationReplication"/>
         <xsd:group ref="PODEnhancementReplication"/>
         <xsd:group ref="UtilitiesDeviceReplication"/>
      </xsd:sequence>
   </xsd:complexType>
   <!--##SCHEMAEND##-->
   <!--##SCHEMA_TARGET_NAMESPACE##:http://dongenergy.dk/mdms/ISU-U/enhancement-->
   <!--##SCHEMA_IMPORTED_NAMESPACE##:http://sap.com/xi/SAPGlobal/GDT#http://dongenergy.dk/mdms/ISU-U-->
   <xsd:group name="DataHubTransferMarkerReplication">
      <xsd:sequence>
         <xsd:element form="qualified"
                       maxOccurs="unbounded"
                       minOccurs="0"
                       name="DataHubTransferMarker"
                       type="DataHubTransferMarker"/>
      </xsd:sequence>
   </xsd:group>
   <xsd:attributeGroup name="DataHubTransferMarkerReplication"/>
   <xsd:group name="DeviceEnhancementReplication">
      <xsd:sequence>
         <xsd:element form="qualified"
                       minOccurs="0"
                       name="MeterSpecifications"
                       type="MeterSpecifications"/>
      </xsd:sequence>
   </xsd:group>
   <xsd:attributeGroup name="DeviceEnhancementReplication"/>
   <xsd:group name="InstallationReplication">
      <xsd:sequence>
         <xsd:element form="qualified"
                       minOccurs="0"
                       name="Installation"
                       type="InstallationDT"/>
      </xsd:sequence>
   </xsd:group>
   <xsd:attributeGroup name="InstallationReplication"/>
   <xsd:group name="LocationEnhancementReplication">
      <xsd:sequence>
         <xsd:element form="qualified"
                       minOccurs="0"
                       name="LocationSpecifications"
                       type="LocationSpecifications"/>
      </xsd:sequence>
   </xsd:group>
   <xsd:attributeGroup name="LocationEnhancementReplication"/>
   <xsd:group name="PODEnhancementReplication">
      <xsd:sequence>
         <xsd:element form="qualified"
                       minOccurs="0"
                       name="NetSettlement"
                       type="NetSettlmentDT"/>
      </xsd:sequence>
   </xsd:group>
   <xsd:attributeGroup name="PODEnhancementReplication"/>
   <xsd:group name="UtilitiesDeviceReplication">
      <xsd:sequence>
         <xsd:element form="qualified"
                       minOccurs="0"
                       name="UtilitiesDevice"
                       type="UtilitiesDevice_DT"/>
      </xsd:sequence>
   </xsd:group>
   <xsd:attributeGroup name="UtilitiesDeviceReplication"/>
   <xsd:complexType name="DataHubTransferMarker">
      <xsd:sequence>
         <xsd:element name="StartDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="EndDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="DHTrMarkerValue" type="xsd:boolean"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="InstallationDT">
      <xsd:sequence>
         <xsd:element name="Industry">
            <xsd:simpleType>
               <xsd:restriction base="xsd:string">
                  <xsd:maxLength value="10"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element maxOccurs="unbounded" name="RateCategory" type="RateCategory"/>
         <xsd:element minOccurs="0" name="TypeOfProduction">
            <xsd:simpleType>
               <xsd:restriction base="xsd:decimal">
                  <xsd:totalDigits value="2"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element minOccurs="0" name="PowerEffect">
            <xsd:simpleType>
               <xsd:restriction base="xsd:decimal">
                  <xsd:totalDigits value="6"/>
                  <xsd:fractionDigits value="1"/>
                  <xsd:maxInclusive value="99999.9"/>
                  <xsd:minInclusive value="-99999.9"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="ZZ_FROM_GRID" type="xsd:string"/>
         <xsd:element name="ZZ_TO_GRID" type="xsd:string"/>
         <xsd:element name="POWER_KW" type="xsd:string"/>
         <xsd:element name="ZZPOD_COMMENT" type="xsd:string"/>
         <xsd:element name="ZZ_VAERK_GSRN" type="xsd:string"/>
         <xsd:element maxOccurs="unbounded"
                       name="MeteringPointConnectionType"
                       type="MeteringPointConnectionType"/>
         <xsd:element name="ASSET_TYPE" type="xsd:string"/>
         <xsd:element name="ZZ_PROD_OBL_SUPPLIER" type="xsd:string"/>
         <xsd:element maxOccurs="unbounded"
                       name="MeasuringPointType"
                       type="MeasuringPointType"/>
         <xsd:element name="ZZCONSTATUS" type="xsd:string"/>
         <xsd:element name="ZZDATE" type="xsd:string"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="LocationSpecifications">
      <xsd:sequence>
         <xsd:element name="XCoordinate" nillable="true">
            <xsd:complexType>
               <xsd:simpleContent>
                  <xsd:extension base="xsd:string">
                     <xsd:attribute ref="DeletionIndicator"/>
                  </xsd:extension>
               </xsd:simpleContent>
            </xsd:complexType>
         </xsd:element>
         <xsd:element name="YCoordinate" nillable="true">
            <xsd:complexType>
               <xsd:simpleContent>
                  <xsd:extension base="xsd:string">
                     <xsd:attribute ref="DeletionIndicator"/>
                  </xsd:extension>
               </xsd:simpleContent>
            </xsd:complexType>
         </xsd:element>
         <xsd:element name="ZZ_SCOPEOFDEL_CONS" type="xsd:string"/>
         <xsd:element name="ZZ_LIMGRIDACC_CONS" type="xsd:string"/>
         <xsd:element name="ZZ_SCOPEOFDEL_PROD" type="xsd:string"/>
         <xsd:element name="ZZ_LIMGRIDACC_PROD" type="xsd:string"/>
         <xsd:element name="ZZ_SCOPEOFDEL_CAT" type="xsd:string"/>
         <xsd:element name="ZZ_SETUP_METHOD" type="xsd:string"/>
         <xsd:element name="Premise" type="xsd:string"/>
         <xsd:element name="STORT" type="xsd:string"/>
         <xsd:element name="STORTZUS" type="xsd:string"/>
         <xsd:element name="ZZSTIKBEN" type="xsd:string"/>
         <xsd:element name="FunctLocationLongText" type="xsd:string"/>
         <xsd:element name="ZZ_ZZ_PHASES" type="xsd:string"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="MeasuringPointType">
      <xsd:sequence>
         <xsd:element name="DATE_FROM" type="xsd:string"/>
         <xsd:element name="DATE_TO" type="xsd:string"/>
         <xsd:element name="ZZ_BASIC_POD_TYPE" type="xsd:string"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="MeterSpecifications">
      <xsd:sequence>
         <xsd:element name="ConstructionClass" type="xsd:string"/>
         <xsd:element name="FunctionClass" type="xsd:string"/>
         <xsd:element name="ConstructionForm" type="xsd:string"/>
         <xsd:element name="Tilslutningspunkt" type="xsd:string"/>
         <xsd:element name="CONSTRUCTION_YEAR" type="xsd:string"/>
         <xsd:element name="SERGE" type="xsd:string"/>
         <xsd:element maxOccurs="unbounded"
                       name="TransformationRatio"
                       type="TransformationRatio"/>
         <xsd:element name="RemoteDisconnection" type="xsd:string"/>
         <xsd:element name="RemoteReconnection" type="xsd:string"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="MeteringPointConnectionType">
      <xsd:sequence>
         <xsd:element name="DATE_FROM" type="xsd:string"/>
         <xsd:element name="DATE_TO" type="xsd:string"/>
         <xsd:element name="ZZ_CONNECTION_TYPE" type="xsd:string"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="MeteringPointSubType">
      <xsd:sequence>
         <xsd:element name="StartDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="EndDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="MPSubTypeValue">
            <xsd:simpleType>
               <xsd:restriction base="xsd:string">
                  <xsd:maxLength value="3"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="NetSettlementGroup">
      <xsd:sequence>
         <xsd:element name="StartDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="EndDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="SettlementGroup">
            <xsd:simpleType>
               <xsd:restriction base="xsd:decimal">
                  <xsd:totalDigits value="1"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="NetSettlementMethod">
      <xsd:sequence>
         <xsd:element name="StartDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="EndDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="SettlementMethod">
            <xsd:simpleType>
               <xsd:restriction base="xsd:string">
                  <xsd:maxLength value="3"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="NetSettlmentDT">
      <xsd:sequence>
         <xsd:element name="MasterPoD">
            <xsd:simpleType>
               <xsd:restriction base="xsd:string">
                  <xsd:maxLength value="50"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="MeteringPointType">
            <xsd:simpleType>
               <xsd:restriction base="xsd:string">
                  <xsd:maxLength value="4"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element maxOccurs="unbounded"
                       name="MeteringPointSubType"
                       type="MeteringPointSubType"/>
         <xsd:element maxOccurs="unbounded"
                       name="NetSettlementGroup"
                       type="NetSettlementGroup"/>
         <xsd:element maxOccurs="unbounded"
                       name="NetSettlementMethod"
                       type="NetSettlementMethod"/>
         <xsd:element name="AMIPriorityGroup">
            <xsd:simpleType>
               <xsd:restriction base="xsd:string">
                  <xsd:maxLength value="1"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="GRID">
            <xsd:complexType>
               <xsd:simpleContent>
                  <xsd:extension base="xsd:string">
                     <xsd:attribute ref="DeletionIndicator"/>
                  </xsd:extension>
               </xsd:simpleContent>
            </xsd:complexType>
         </xsd:element>
         <xsd:element name="GLN" nillable="true" type="xsd:string"/>
         <xsd:element name="Installation" type="xsd:string"/>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="RateCategory">
      <xsd:sequence>
         <xsd:element name="StartDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="EndDate">
            <xsd:simpleType>
               <xsd:restriction base="xsd:date">
                  <xsd:pattern value="[0-9]{4}-[0-9]{2}-[0-9]{2}"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
         <xsd:element name="RCValue">
            <xsd:simpleType>
               <xsd:restriction base="xsd:string">
                  <xsd:maxLength value="10"/>
               </xsd:restriction>
            </xsd:simpleType>
         </xsd:element>
      </xsd:sequence>
   </xsd:complexType>
   <xsd:complexType name="TransformationRatio">
      <xsd:sequence>
         <xsd:element name="DATE_FROM" type="xsd:string"/>
         <xsd:element name="DATE_TO" type="xsd:string"/>
         <xsd:element name="ZSPANNP" type="xsd:string"/>
         <xsd:element name="ZSPANNS" type="xsd:string"/>
         <xsd:element name="ZSTROMP" type="xsd:string"/>
         <xsd:element name="ZSTROMS" type="xsd:string"/>
         <xsd:element name="UEBERVER" type="xsd:string"/>
      </xsd:sequence>
   </xsd:complexType>
   <!--##SCHEMAEND##-->
   <!--##SCHEMA_TARGET_NAMESPACE##:http://sap.com/xi/SAPGlobal20/Global-->
   <!--##SCHEMA_IMPORTED_NAMESPACE##:http://sap.com/xi/IS-U/Global2-->
   <xsd:element name="UtilitiesDeviceERPSmartMeterReplicationBulkRequest"
                 type="UtilsDvceERPSmrtMtrRplctnBulkReqMsg"/>
   <!--##SCHEMAEND##-->
   <!--##SCHEMA_TARGET_NAMESPACE##:http://dongenergy.dk/mdms/ISU-U-->
   <!--##SCHEMA_IMPORTED_NAMESPACE##:http://sap.com/xi/IS-U/Global2-->
   <xsd:complexType name="UtilitiesDevice_DT">
      <xsd:sequence>
         <xsd:element name="UtilitiesDeviceID" type="UtilitiesDeviceID"/>
      </xsd:sequence>
   </xsd:complexType>
</xsd:schema>
<!--##SCHEMAEND##-->

