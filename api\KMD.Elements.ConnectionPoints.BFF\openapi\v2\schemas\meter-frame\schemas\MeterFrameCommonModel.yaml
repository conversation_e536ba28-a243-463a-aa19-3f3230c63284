﻿title: MeterFrameCommonModel
type: object
additionalProperties: false
description: Meter <PERSON>ame model.
required:
  - connectionPointId
  - meterMissing
  - meterSealed
  - meterWorkConsumerBilled
  - supplyStatus
  - tagAssignments
  - placementCode
  - commonReading
  - collectiveReading
properties:
  created:
    type: string
    description: Meter Frame created date.
    format: date-time
    nullable: true
  placementCode:
    type: string
    description: 'Placement code CodeList value id.'
    format: uuid
    nullable: false
  connectionPointId:
    type: string
    format: uuid
    minLength: 1
    description: Connection point identifier.
  meterMissing:
    type: boolean
    description: 'DK: MålerVæk.'
  placementSpecification:
    description: 'DK: Placeringsbeskrivelse.'
    allOf:
      - $ref: '../../_simple-type/ShortStringNullable.yaml'
  meterSealDate:
    type: string
    description: |-
      DK: Målerplomberingsdato.
      Indicates the date when the meter was sealed.
    format: date-time
    nullable: true
  meterSealed:
    type: boolean
    description: |-
      DK: MålerPlomberet.
      Indicates whether the meter is sealed.
  meterWorkConsumerBilled:
    type: boolean
    description: |-
      DK: MålerYdelseFaktureresEjer.
      Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
  decommissioned:
    type: string
    description: Decommissioned date.
    format: date-time
    nullable: true
  statusChanged:
    type: string
    description: Latest status change date.
    format: date-time
    nullable: true
  electricityAttributes:
    description: ElectricityAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameElectricityAttributesModel.yaml'
  heatingAttributes:
    description: HeatingAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameHeatingAttributesModel.yaml'
  waterAttributes:
    description: MeterFrameWaterAttributes.
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameWaterAttributesModel.yaml'
  geographicalLocation:
    description: Set of geographical location properties - describing Meter Frame geo location.
    nullable: true
    type: object
    oneOf:
      - $ref: './GeographicalLocationModel.yaml'
  gisPropertiesElectricity:
    description: MeterFrameGisPropertiesElectricity
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameGisPropertiesElectricityModel.yaml'
  gisPropertiesHeating:
    description: MeterFrameGisPropertiesHeating
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameGisPropertiesHeatingModel.yaml'
  gisPropertiesWater:
    description: MeterFrameGisPropertiesWater
    nullable: true
    type: object
    oneOf:
      - $ref: './MeterFrameGisPropertiesWaterModel.yaml'
  mainBranchLineElectricity:
    description: MainBranchLineElectricity
    nullable: true
    type: object
    oneOf:
      - $ref: './MainBranchLineElectricityCreateUpdateModel.yaml'
  reserveBranchLineElectricity:
    description: ReserveBranchLineElectricity
    nullable: true
    type: object
    oneOf:
      - $ref: './ReserveBranchLineElectricityCreateUpdateModel.yaml'
  mainBranchLineWater:
    description: MainBranchLineWater
    nullable: true
    type: object
    oneOf:
      - $ref: './MainBranchLineWaterModel.yaml'
  reserveBranchLineWater:
    description: ReserveBranchLineWater
    nullable: true
    type: object
    oneOf:
        - $ref: './ReserveBranchLineWaterModel.yaml'
  addressId:
    type: string
    description: An UUID reference to a master data address.
    format: uuid
    nullable: true

  tagAssignments:
    type: array
    description: Tags.
    maxItems: 1000000
    items:
      $ref: './AddTagAssignmentModel.yaml'
  supplyStatus:
    $ref: './MeterFrameSupplyStatusModel.yaml'
  meterReadingType:
    allOf:
      - $ref: '../../_simple-type/GuidNullable.yaml'
    description: 'A list of different reading methods a meter can have.'
  supplyDisconnectType:
    $ref: './MeterFrameSupplyDisconnectTypeModel.yaml'
  noMeter:
    type: boolean
    description: 'DK: Målerfri.'
  commonReading:
    type: boolean
    nullable: false
    description: 'Common reading flag.'
  collectiveReading:
    type: boolean
    nullable: false
    description: 'Collective reading flag.'
  supplyDisconnectedComment:
    description: Supply Disconnected Comment.
    nullable: true
    allOf:
      - $ref: '../../_simple-type/MediumStringNullable.yaml'
