openapi: 3.0.3
info:
  title: KMD.Elements.MDM.UI
  x-maintainers: Team-UI-2
  description: |-
    # KMD.Elements.MDM.MasterData UI OpenAPI Specification

    ---
    Stability level: STABLE<br/>
    <br/>
    The **KMD.Elements.MDM.MasterData UI** is part of the KMD Element product.<br/>

    ## Capabilities
    The API allows to:
    - search meter in MDM
    - get register details
    ---

  termsOfService: "https://www.kmd.net/terms-of-use"

  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>

  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"

  version: "1.21"

servers:
  - url: "https://localhost:5001"
    description: Localhost
  - url: "https://mdm.dev.kmdelements.com/api/kmd-elements-mdm-bff"
    description: Dev env
security:
  - Jwt: []

tags:
  - name: ConnectionPoints
    description: API for connection points
  - name: MeterFrames
    description: API for meter frames
  - name: MeteringPoints
    description: API for metering points
  - name: MeteringPointTimeSeries
    description: API for metering point time series
  - name: Meters
    description: API for meters
  - name: Registers
    description: API for registers
  - name: TechnicalMeteringPoints
    description: API for technical metering points

paths:
  /v1/ui/meters/commands/search:
    post:
      tags:
        - Meters
      operationId: searchMeters
      summary: Endpoint to fetch meters
      description: |-
        - Endpoint to fetch meters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMetersRequest"
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMetersResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/meters/{meterId}:
    get:
      tags:
        - Meters
      operationId: getMeterDetails
      summary: Endpoint to fetch meter by identifier
      description: |-
        - Endpoint to fetch meter details
      parameters:
        - $ref: "#/components/parameters/MeterIdInPath"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetMeterDetailsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/metering-points/{meteringPointNumber}:
    get:
      tags:
        - MeteringPoints
      operationId: getMeteringPointDetails
      summary: Endpoint to fetch metering point by version identifier
      description: |-
        - Endpoint to fetch metering point by version identifier
      parameters:
        - $ref: "#/components/parameters/MeteringPointNumberInPath"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetMeteringPointDetailsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/technical-metering-points/{meteringPointNumber}:
    get:
      tags:
        - TechnicalMeteringPoints
      operationId: getTechnicalMeteringPointDetails
      summary: Endpoint to fetch technical metering point by metering point number
      description: |-
        - Endpoint to fetch technical metering point by metering point number
      parameters:
        - $ref: "#/components/parameters/MeteringPointNumberInPath"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetTechnicalMeteringPointDetailsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/registers/{registerId}:
    get:
      tags:
        - Registers
      operationId: getRegisterDetails
      summary: Endpoint to fetch register details
      description: |-
        - Endpoint to fetch register details
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/RegisterId"
      responses:
        "200":
          description: 200 Process initialized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetRegisterResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/metering-point-time-series/{meteringPointNumber}:
    get:
      tags:
        - MeteringPointTimeSeries
      operationId: getMeteringPointTimeSeries
      summary: Endpoint to fetch metering point time series by metering point Identifier
      description: |-
        - Endpoint to fetch metering point time series by metering point Identifier
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/MeteringPointNumberInPath"
      responses:
        "200":
          description: 200 Process initialized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetMeteringPointTimeSeriesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/meter-frames/{meterFrameId}:
    get:
      tags:
        - MeterFrames
      operationId: getMeterFrameDetails
      summary: Endpoint to fetch meter frame by identifier
      description: |-
        - Endpoint to fetch meter frame details
      parameters:
        - $ref: "#/components/parameters/MeterFrameIdInPath"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetMeterFrameDetailsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/meter-frames/commands/search:
    post:
      tags:
        - MeterFrames
      operationId: searchMeterFrames
      summary: Endpoint to fetch meter frames
      description: |-
        - Endpoint to fetch meter frames
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeterFramesRequest"
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMeterFramesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/metering-points/commands/search:
    post:
      tags:
        - MeteringPoints
      operationId: getMeteringPoints
      summary: Endpoint to fetch metering points
      description: |-
        - Endpoint to fetch meters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchMeteringPointsRequest"
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: 200 Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchMeteringPointsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/technical-metering-points/commands/search:
    post:
      tags:
        - TechnicalMeteringPoints
      operationId: getTechnicalMeteringPoints
      summary: Endpoint to fetch technical metering points
      description: |-
        - Endpoint to fetch technical metering points
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchTechnicalMeteringPointsRequest"
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: 200 Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchTechnicalMeteringPointsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/connection-points/commands/search:
    post:
      tags:
        - ConnectionPoints
      operationId: searchConnectionPoints
      summary: Endpoint to search connection points
      description: |-
        - Endpoint to search connection points
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SearchConnectionPointsRequest"
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchConnectionPointsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/ui/connection-points/{connectionPointId}:
    get:
      tags:
        - ConnectionPoints
      operationId: getConnectionPointDetails
      summary: Endpoint to fetch connection point details by identifier
      description: |-
        - Endpoint to fetch connection point details by identifier
      parameters:
        - $ref: "#/components/parameters/ConnectionPointIdInPath"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetConnectionPointDetailsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

components:
  parameters:
    EsMessageIdInHeader:
      name: es-message-id
      description: Unique message ID. The same message id is used when resending the message.
      in: header
      schema:
        type: string
        format: uuid
      required: true
      example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
    EsCorrelationIdInHeader:
      name: es-correlation-id
      description: |
        This is used to "link" messages together.
        The server will place the incoming es-correlation-id value as the es-correlation-id
        on the outgoing reply. If not supplied on the request, the es-correlation-id of the
        reply should be set to the value of the es-message-id that was used on the request, if present.
        Given that the es-correlation-id is used to "link" messages together, it may be reused on more than one message.
      in: header
      schema:
        type: string
        format: uuid
      required: true
      example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d
    MeterIdInPath:
      name: meterId
      description: Meter identifier.
      in: path
      schema:
        type: string
        pattern: ""
        maxLength: 100
      required: true
      example: "*********"
    MeteringPointNumberInPath:
      name: meteringPointNumber
      description: Metering point number.
      in: path
      schema:
        type: string
        pattern: ""
        maxLength: 100
      required: true
      example: "571000000000701339"
    RegisterId:
      in: path
      name: registerId
      schema:
        type: string
        pattern: ""
        maxLength: 100
      description: Identifier of meter register
      required: true
      example: "123421"
    MeterFrameIdInPath:
      name: meterFrameId
      description: Meter frame identifier.
      in: path
      schema:
        type: string
        pattern: ""
        maxLength: 100
      required: true
      example: "*********"
    ConnectionPointIdInPath:
      description: Connection point identifier.
      name: connectionPointId
      in: path
      required: true
      schema:
        type: string
        pattern: ""
        maxLength: 100
      example: "*********"

  headers:
    RetryAfter:
      description: Number of seconds until you should try again.
      schema:
        type: integer
        format: int32
        minimum: 1
        maximum: 2678400 # 31 days
      required: true
      example: 3600 # 1 hour

  schemas:
    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
          example: "https://errors.kmdelements.com/500"
        title:
          description: "A short, human-readable summary of the problem type."
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
          example: Error short description
        status:
          description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
          type: integer
          format: int32
          minimum: 400
          maximum: 599
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          maxLength: 2048
          pattern: "^.*$"
          nullable: true
          example: Description what exactly happened
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          maxLength: 32779
          pattern: "^.*$"
          nullable: true
          example: /resources/1

    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1000
              additionalProperties:
                type: array
                description: Array of validation error messages.
                maxItems: 1000
                items:
                  type: string
                  maxLength: 2048
                  pattern: "^.*$"
              nullable: true

    GetTechnicalMeteringPointDetailsResponse:
      description: Get technical metering point details response
      title: GetTechnicalMeteringPointDetailsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meteringPoints
      properties:
        meteringPoints:
          type: array
          description: Array of Metering point details
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/GetTechnicalMeteringPointDetailsResponseMeteringPointVersion"
    GetTechnicalMeteringPointDetailsResponseMeteringPointVersion:
      description: Technical metering point details
      title: GetTechnicalMeteringPointDetailsResponseMeteringPointVersion
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meteringPointNumber
        - validFrom
        - validTo
        - supplyType
        - typeOfMeteringPoint
        - subTypeOfMeteringPoint
        - readingFrequency
        - connectionStatus
        - connectionPointNumber
        - unitType
      properties:
        meteringPointNumber:
          description: Metering Point Number (GSRN number).
          type: string
          pattern: ^\d{18}$
          minLength: 18
          maxLength: 18
          example: "571313190000020132"
        validFrom:
          description: Metering point version valid from.
          type: string
          format: date-time
          example: "2019-11-14T23:00:00.000Z"
        validTo:
          description: Metering point version valid to.
          type: string
          format: date-time
          example: "2019-11-14T23:00:00.000Z"
        supplyType:
          maxLength: 11
          example: Heating
          type: string
          description: System name of supply type.
          pattern: ^(Electricity|Heating|Water)$
        typeOfMeteringPoint:
          description: |-
            Type of measuring point, e.g. production, consumption.
            | Code | Description                         | DanishTranslation                   |
            | ---- | ----------------------------------- | ----------------------------------- |
            | D01  | VE production                       | VE produktion                       |
            | D02  | Analysis                            | Analysemålepunkt                    |
            | D03  | Not used                            | Anvendes ikke                       |
            | D04  | Surplus production group 6          | Overskudsproduktion gruppe 6        |
            | D05  | Net production                      | Nettoproduktion                     |
            | D06  | Supply to grid                      | Leveret til net                     |
            | D07  | Consumption from grid               | Forbrugt fra net                    |
            | D08  | Whole sale services / information   | Afregningsgrundlag/Information      |
            | D09  | Own production                      | Egenproduktion                      |
            | D10  | Net from grid                       | Netto fra net                       |
            | D11  | Net to grid                         | Netto til net                       |
            | D12  | Total consumption                   | Brutto forbrug                      |
            | D13  | Net loss correction                 | Nettabskorrektion                   |
            | D14  | Electrical heating                  | Elvarme                             |
            | D15  | Net consumption                     | Nettoforbrug                        |
            | D16  | Reserved for later use              | Reserveret til senere brug          |
            | D17  | Other consumption                   | Øvrigt forbrug                      |
            | D18  | Other production                    | Øvrig produktion                    |
            | D19  | Reserved for later use              | Reserveret til senere brug          |
            | D20  | Exchange - Reactive energy          | Udveksling – Reaktiv energi         |
            | D21  | Reserved for later use              | Reserveret til senere brug          |
            | D22  | Reserved for later use              | Reserveret til senere brug          |
            | D23  | Reserved for later use              | Reserveret til senere brug          |
            | D24  | Reserved for later use              | Reserveret til senere brug          |
            | D25  | Reserved for later use              | Reserveret til senere brug          |
            | D26  | Reserved for later use              | Reserveret til senere brug          |
            | D27  | Reserved for later use              | Reserveret til senere brug          |
            | D28  | Reserved for later use              | Reserveret til senere brug          |
            | D29  | Reserved for later use              | Reserveret til senere brug          |
            | D30  | Reserved for later use              | Reserveret til senere brug          |
            | D99  | Internal use                        | Intern brug                         |
            | E17  | Consumption                         | Forbrug                             |
            | E18  | Production                          | Produktion                          |
            | E20  | Exchange                            | Udveksling                          |
          type: string
          minLength: 3
          maxLength: 3
          pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D21|D22|D23|D24|D25|D26|D27|D28|D29|D30|D99|E17|E18|E20)$
          example: E17
          nullable: true
        subTypeOfMeteringPoint:
          description: |-
            Sub-type of Metering Point.
            | Code | Description | DanishTranslation |
            | ---- | ----------- | ----------------- |
            | D01  | Physical    | Fysisk            |
            | D02  | Virtual     | Virtuel           |
            | D03  | Calculated  | Beregnet          |
          type: string
          pattern: ^(D01|D02|D03)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        readingFrequency:
          type: string
          description: |-
            Reading Frequency of Technical Metering Point.

            | CodeListName                                        | DisplayName    | Code | Name   | Translation |
            |---------------------------------------------------  |--------------- |------|--------|-------------|
            | System-TechnicalMeteringPoint-ReadingFrequency      | PT5M           | PT5M | 5 min  | 5 mins      |
            | System-TechnicalMeteringPoint-ReadingFrequency      | PT1H           | PT1H | Time   | Hour        |
            | System-TechnicalMeteringPoint-ReadingFrequency      | PT15M          | PT15M| Kvarter| 15 mins     |
            | System-TechnicalMeteringPoint-ReadingFrequency      | P1D            | P1D  | Dag    | Day         |

          minLength: 3
          maxLength: 5
          pattern: "^(PT1H|PT5M|PT15M|P1D)$"
          example: PT1H
          nullable: true
        connectionStatus:
          description: |-
            Connection status of Metering Point also known in DH as PhysicalStatusOfMeteringPoint.
            | Code | Description   | DanishTranslation |
            | ---- | ------------- | ----------------- |
            | D01  | Not used      | Anvendes ikke     |
            | D02  | Closed down   | Nedlæg            |
            | D03  | New           | Ny                |
            | E22  | Connected     | Tilsluttet        |
          type: string
          pattern: ^(D01|D02|D03|E22)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        unitType:
          description: |-
            Indicates unit Type.
            | Code  | Description              | DanishTranslation               |
            | ----  | ------------------------ | --------------------------------|
            | AMP   | Ampere                   | Ampere                          |
            | K3    | kVArh                    | KiloVolt-Ampere reactive hour   |
            | KWH   | kWh                      | Kilowatt-hour                   |
            | KWT   | kW                       | Kilowatt                        |
            | MAW   | MW                       | Megawatt                        |
            | MWH   | MWh                      | Megawatt-hour                   |
            | TNE   | Tonne                    | metric ton                      |
            | Z03   | MVAr                     | MegaVolt-Ampere reactive power  |
            | Z14   | Danish Tariff code       | KT Tarifkode                    |
            | H87   | STK                      | Antal styk                      |
          type: string
          pattern: ^(AMP|K3|KWH|KWT|MAW|MWH|TNE|Z03|Z14|H87)$
          minLength: 2
          maxLength: 3
          example: AMP
          nullable: true
        connectionPointNumber:
          description: The ConnectionPoint Number.
          type: string
          maxLength: 64
          pattern: "^.*$"
          nullable: true

    GetMeteringPointDetailsResponse:
      description: Get metering point details response
      title: GetMeteringPointDetailsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meteringPoints
      properties:
        meteringPoints:
          type: array
          description: Array of Metering point details
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/GetMeteringPointDetailsResponseMeteringPointVersion"
    GetMeteringPointDetailsResponseMeteringPointVersion:
      description: Metering point details
      title: GetMeteringPointDetailsResponseMeteringPointVersion
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meteringPointNumber
        - validFrom
        - validTo
        - supplyType
        - parentMeteringPointNumber
        - typeOfMeteringPoint
        - subTypeOfMeteringPoint
        - meterReadingOccurrence
        - connectionStatus
        - settlementMethod
        - maximumPower
        - meteringGridAreaId
        - locationDescription
        - productId
        - unitType
        - fromNet
        - toNet
        - netSettlementGroup
        - powerSupplierGsrn
        - productionCapacityInKiloWatts
        - connectionType
        - assetType
        - meterMeterNumber
        - meterMeterReadingType
        - meterNumberOfDigits
        - meterUnitType
        - meterConversionFactor
        - disconnectionType
      properties:
        meteringPointNumber:
          description: Metering point number (GSRN number).
          type: string
          pattern: ^\d{18}$
          minLength: 18
          maxLength: 18
          example: "571313190000020132"
        validFrom:
          description: Metering point version valid from.
          type: string
          format: date-time
          example: "2019-11-14T23:00:00.000Z"
        validTo:
          description: Metering point version valid to.
          type: string
          format: date-time
          example: "2019-11-14T23:00:00.000Z"
        supplyType:
          maxLength: 11
          example: Heating
          type: string
          description: System name of supply type.
          pattern: ^(Electricity|Heating|Water)$
        parentMeteringPointNumber:
          description: Parent Metering Point Number.
          type: string
          pattern: ^\d{18}$
          minLength: 18
          maxLength: 18
          example: "571313190000020132"
          x-reference:
            entityFamily: ElectricityMeteringPoint
            entityType: ElectricityMeteringPoint
          nullable: true
        typeOfMeteringPoint:
          description: |-
            Type of measuring point, e.g. production, consumption.
            | Code | Description                         | DanishTranslation                   |
            | ---- | ----------------------------------- | ----------------------------------- |
            | D01  | VE production                       | VE produktion                       |
            | D02  | Analysis                            | Analysemålepunkt                    |
            | D03  | Not used                            | Anvendes ikke                       |
            | D04  | Surplus production group 6          | Overskudsproduktion gruppe 6        |
            | D05  | Net production                      | Nettoproduktion                     |
            | D06  | Supply to grid                      | Leveret til net                     |
            | D07  | Consumption from grid               | Forbrugt fra net                    |
            | D08  | Whole sale services / information   | Afregningsgrundlag/Information      |
            | D09  | Own production                      | Egenproduktion                      |
            | D10  | Net from grid                       | Netto fra net                       |
            | D11  | Net to grid                         | Netto til net                       |
            | D12  | Total consumption                   | Brutto forbrug                      |
            | D13  | Net loss correction                 | Nettabskorrektion                   |
            | D14  | Electrical heating                  | Elvarme                             |
            | D15  | Net consumption                     | Nettoforbrug                        |
            | D16  | Reserved for later use              | Reserveret til senere brug          |
            | D17  | Other consumption                   | Øvrigt forbrug                      |
            | D18  | Other production                    | Øvrig produktion                    |
            | D19  | Reserved for later use              | Reserveret til senere brug          |
            | D20  | Exchange - Reactive energy          | Udveksling – Reaktiv energi         |
            | D21  | Reserved for later use              | Reserveret til senere brug          |
            | D22  | Reserved for later use              | Reserveret til senere brug          |
            | D23  | Reserved for later use              | Reserveret til senere brug          |
            | D24  | Reserved for later use              | Reserveret til senere brug          |
            | D25  | Reserved for later use              | Reserveret til senere brug          |
            | D26  | Reserved for later use              | Reserveret til senere brug          |
            | D27  | Reserved for later use              | Reserveret til senere brug          |
            | D28  | Reserved for later use              | Reserveret til senere brug          |
            | D29  | Reserved for later use              | Reserveret til senere brug          |
            | D30  | Reserved for later use              | Reserveret til senere brug          |
            | D99  | Internal use                        | Intern brug                         |
            | E17  | Consumption                         | Forbrug                             |
            | E18  | Production                          | Produktion                          |
            | E20  | Exchange                            | Udveksling                          |
          type: string
          minLength: 3
          maxLength: 3
          pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D21|D22|D23|D24|D25|D26|D27|D28|D29|D30|D99|E17|E18|E20)$
          example: E17
          nullable: true
        subTypeOfMeteringPoint:
          description: |-
            Sub-type of Metering Point.
            | Code | Description | DanishTranslation |
            | ---- | ----------- | ----------------- |
            | D01  | Physical    | Fysisk            |
            | D02  | Virtual     | Virtuel           |
            | D03  | Calculated  | Beregnet          |
          type: string
          pattern: ^(D01|D02|D03)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        meterReadingOccurrence:
          description: |-
            ISO standard, ISO 8601 is used to express
            resolution
            Either format: PnYnMnDTnHnMnS, where nY
            expresses the number of years and so on to nM et
            number of minutes and nS a number of seconds.
            Or the text "OTHER" / "ANDET".
            Values:
              P1M, PT1H, PT15M, ANDET
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 100
          example: ANDET
          nullable: true
        connectionStatus:
          description: |-
            Connection status of Metering Point also known in DH as PhysicalStatusOfMeteringPoint.
            | Code | Description   | DanishTranslation |
            | ---- | ------------- | ----------------- |
            | D01  | Not used      | Anvendes ikke     |
            | D02  | Closed down   | Nedlæg            |
            | D03  | New           | Ny                |
            | E22  | Connected     | Tilsluttet        |
          type: string
          pattern: ^(D01|D02|D03|E22)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        settlementMethod:
          description: |-
            The Metering Point form of Settlement.
            | Code | Description   | DanishTranslation |
            | ---- | ------------- | ----------------- |
            | D01  | Flex settled  | Flex afregnet     |
            | E01  | Profiled      | Profileret        |
            | E02  | Non profiled  | Ikke-profileret   |
          type: string
          pattern: ^(D01|E01|E02)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        maximumPower:
          description: Power limit in kW.
          type: number
          format: decimal
          minimum: 0
          maximum: 99999999
          example: 100
          nullable: true
        meteringGridAreaId:
          description: Grid area Identifier - 3 digits.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "900"
          nullable: true
        locationDescription:
          description: Possible description of Meter Location.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 256
          example: Building No. 2
          nullable: true
        productId:
          description: Product identification. The product can e.g. be energy or power. GLN number is used to specify the product.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 100
          example: "8716867000030"
          nullable: true
        unitType:
          description: |-
            Indicates unit Type.
            | Code  | Description              | DanishTranslation               |
            | ----  | ------------------------ | --------------------------------|
            | AMP   | Ampere                   | Ampere                          |
            | K3    | kVArh                    | KiloVolt-Ampere reactive hour   |
            | KWH   | kWh                      | Kilowatt-hour                   |
            | KWT   | kW                       | Kilowatt                        |
            | MAW   | MW                       | Megawatt                        |
            | MWH   | MWh                      | Megawatt-hour                   |
            | TNE   | Tonne                    | metric ton                      |
            | Z03   | MVAr                     | MegaVolt-Ampere reactive power  |
            | Z14   | Danish Tariff code       | KT Tarifkode                    |
            | H87   | STK                      | Antal styk                      |
          type: string
          pattern: ^(AMP|K3|KWH|KWT|MAW|MWH|TNE|Z03|Z14|H87)$
          minLength: 2
          maxLength: 3
          example: AMP
          nullable: true
        fromNet:
          description: |-
            FromNet also known as "FromGrid". Network area is a term for a network which managed by a network company.
            Danish Energy's code is used (DE no.)
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "125"
          nullable: true
        toNet:
          description: |-
            Network area is a term for a network which managed by a network company. Danish Energy's code is used (DE no.).
            In DataHub known as ToGrid.
            3 digits.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "123"
          nullable: true
        netSettlementGroup:
          description: |-
            The value 0 is specified for metering points which are not settled net.
            Possible values: 0,1,2,3,4,5,6,7,99. 7 is not used.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 100
          example: "2"
          nullable: true
        powerSupplierGsrn:
          description: Power Plant GSRN - 18 digits.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "579000133055400001"
          nullable: true
        productionCapacityInKiloWatts:
          type: number
          description: The plant's capacity in kW. 8 digits total.
          format: decimal
          minimum: 0
          maximum: 99999999
          example: 1.1
          nullable: true
        connectionType:
          description: |-
            Describes whether a (net settled) Metering Point is directly or installation connected.
            | Code | Description            | DanishTranslation       |
            | ---- | --------------------   | ----------------------  |
            | D01  | Direct connected       | Direkte tilsluttet      |
            | D02  | Installation connected | Installationstilsluttet |
          type: string
          pattern: ^(D01|D02)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        assetType:
          description: |-
            Indicates which technology a Metering Point uses.
            Available codes in value field:
            | Code | Description                              | DanishTranslation               |
            | ---- | ---------------------------------------- | ------------------------------- |
            | D01  | Steam turbine with back-pressure mode    | Dampturbine med modtryksdrift   |
            | D02  | Gasturbine                               | Gasturbine                      |
            | D03  | Combined cycle                           | Combined cycle                  |
            | D04  | Combustion engine gas                    | Forbrændingsmotor Gas           |
            | D05  | Steam turbine with condensation / steam  | Dampturbine med kondens/damp    |
            | D06  | Boiler                                   | Kedel                           |
            | D07  | Stirling engine                          | Stirlingmotor                   |
            | D08  | Reserved for later use                   | Reserveret til senere brug      |
            | D09  | Reserved for later use                   | Reserveret til senere brug      |
            | D10  | Fuel cells                               | Brændselsceller                 |
            | D11  | Photovoltaic cells                       | Solceller                       |
            | D12  | Wind turbines                            | Vindmøller                      |
            | D13  | Hydroelectric power                      | Vandkraft                       |
            | D14  | Wave power                               | Bølgekraft                      |
            | D15  | Reserved for later use                   | Reserveret til senere brug      |
            | D16  | Reserved for later use                   | Reserveret til senere brug      |
            | D17  | Dispatchable wind turbines               | Regulerbare Vindmøller          |
            | D18  | Reserved for later use                   | Reserveret til senere brug      |
            | D19  | Combustion engine – diesel               | Forbrændingsmotor Dieselmotor   |
            | D20  | Combustion engine - bio                  | Bioforbrændingsmotor            |
            | D99  | Unknown technology                       | Ukendt teknologi                |
          type: string
          pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D99)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        meterMeterNumber:
          description: Meter number.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "110048"
          x-nestedObjectName: Meter
          nullable: true
        meterMeterReadingType:
          description: |-
            Meter Reading Type.
            | Code | Description   | DanishTranslation |
            | ---- | ------------- | ----------------- |
            | D01  | Accumulated   | Akkumulerende     |
            | D02  | Balanced      | Salderende        |
          type: string
          pattern: ^(D01|D02)$
          minLength: 3
          maxLength: 3
          example: D01
          x-nestedObjectName: Meter
          nullable: true
        meterNumberOfDigits:
          description: Number of digits.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "8"
          x-nestedObjectName: Meter
          nullable: true
        meterUnitType:
          description: |-
            Indicates unit Type.
            | Code  | Description              | DanishTranslation               |
            | ----  | ------------------------ | --------------------------------|
            | AMP   | Ampere                   | Ampere                          |
            | K3    | kVArh                    | KiloVolt-Ampere reactive hour   |
            | KWH   | kWh                      | Kilowatt-hour                   |
            | KWT   | kW                       | Kilowatt                        |
            | MAW   | MW                       | Megawatt                        |
            | MWH   | MWh                      | Megawatt-hour                   |
            | TNE   | Tonne                    | metric ton                      |
            | Z03   | MVAr                     | MegaVolt-Ampere reactive power  |
            | Z14   | Danish Tariff code       | KT Tarifkode                    |
            | H87   | STK                      | Antal styk                      |
          type: string
          pattern: ^(AMP|K3|KWH|KWT|MAW|MWH|TNE|Z03|Z14|H87)$
          minLength: 2
          maxLength: 3
          example: AMP
          x-nestedObjectName: Meter
          nullable: true
        meterConversionFactor:
          description: Conversion factor.
          type: number
          format: decimal
          example: 1.02
          x-nestedObjectName: Meter
          nullable: true
        disconnectionType:
          description: |-
            Can a Metering Point be disconnected automatically from system.
            | Code | Description          | DanishTranslation      |
            | ---- | -------------------- | ---------------------- |
            | D01  | Remote disconnection | Fjern afbrydelig       |
            | D02  | Manual disconnection | Manual afbrydelig      |
          type: string
          pattern: ^(D01|D02)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true

    SearchMetersRequest:
      description: Search meters request
      title: SearchMeterRequest
      type: object
      additionalProperties: false
      required:
        - offset
        - limit
      properties:
        offset:
          description: Results offset
          type: integer
          format: int32
          maximum: 2147483647
          minimum: 0
        limit:
          description: Results limit
          type: integer
          format: int32
          maximum: 2147483647
          minimum: 0
        sortOrder:
          description: Sorting order
          type: string
          nullable: true
          pattern: "^(ASC|DESC)$"
          maxLength: 16
        sortBy:
          description: Sorting property
          type: string
          nullable: true
          pattern: "^(serialNumber|fabricationNumber)$"
          maxLength: 64
        searchMeterFilters:
          description: Filters
          type: object
          additionalProperties: false
          properties:
            meterNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            meterFrameNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            serialNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            fabricationNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            alternativeMeterNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            productionYear:
              $ref: "#/components/schemas/SearchDateFilter"
            remoteReadable:
              $ref: "#/components/schemas/SearchBoolFilter"
            remoteDisconnectable:
              $ref: "#/components/schemas/SearchBoolFilter"
    GetMeterDetailsResponse:
      description: Get meter details response
      title: GetMeterDetailsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - supplyType
        - periodFrom
        - periodTo
        - fabricationType
        - meterType
        - meterNumber
        - alternativeMeterNumber
        - meterFrameNumber
        - serialNumber
        - fabricationNumber
        - barCodeEanNumber
        - manufacturer
        - productionYear
        - hesSystem
        - communicationMethod
        - remoteReadable
        - remoteDisconnectable
        - accuracyClass
        - numberOfPhases
        - current
        - voltage
        - ratioVt
        - ratioCt
        - technicalCurrent
        - technicalVoltage
        - connectionType
        - connectionRemark
        - meteringPrinciple
        - mainControlMeterRole
        - relatedMeter
        - temporary
        - meterConfiguration
        - meterRegisters
      properties:
        periodFrom:
          type: string
          description: Period from
          format: date-time
        periodTo:
          type: string
          description: Period to
          format: date-time
        supplyType:
          maxLength: 11
          example: Heating
          type: string
          description: System name of supply type.
          pattern: ^(Electricity|Heating|Water)$
        fabricationType:
          x-reference:
            entityCollectionId: 68199f9c-bf84-4eef-b254-a90c2270aca7
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Fabrication Type value list display value.
        meterType:
          pattern: ""
          maxLength: 100
          type: string
          example: Meter typeb
          x-reference:
            entityCollectionId: 80a64f06-e6ac-44cf-a450-ece1d34f1e27
            entityType: ValueListValue
            entityFamily: ReferenceData
          description: Meter type value list display value.
        meterNumber:
          maxLength: 50
          type: string
          pattern: ^.*$
          example: ""
          description: Meter number.
        meterFrameNumber:
          example: "1234"
          pattern: ^.*$
          minLength: 0
          type: string
          description: |-
            DK: Målerrammenummer.
            Number on the Meter Frame.
          maxLength: 50
        serialNumber:
          nullable: true
          pattern: ""
          minLength: 1
          example: SerialNumber
          type: string
          description: Serial Number.
          maxLength: 50
        fabricationNumber:
          nullable: true
          pattern: ""
          minLength: 1
          example: FabricationNumber
          type: string
          description: Fabrication Number.
          maxLength: 50
        barCodeEanNumber:
          nullable: true
          pattern: ""
          minLength: 1
          example: Bar Code
          type: string
          description: Bar Code Ean Number.
          maxLength: 50
        manufacturer:
          x-reference:
            entityCollectionId: 1eee884f-a3e1-41bd-8641-c4bd16cb901d
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Manufacturer
          pattern: ""
          maxLength: 100
          type: string
          description: Manufacturer value list display value.
        alternativeMeterNumber:
          nullable: true
          pattern: ""
          minLength: 0
          example: Alternative
          type: string
          description: Alternative Meter Number.
          maxLength: 50
        productionYear:
          nullable: true
          example: 2019-11-14
          format: date
          type: string
          description: Production Year.
        hesSystem:
          x-reference:
            entityCollectionId: 9622ac54-3ff0-4c8a-ba28-7395400c2c94
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: HES system a
          pattern: ""
          maxLength: 100
          type: string
          description: Hes System value list display value.
        communicationMethod:
          nullable: true
          x-reference:
            entityCollectionId: ef95bf39-e943-4142-aed2-5832c2dc7fc4
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Communication Method value list display value.
        remoteReadable:
          type: boolean
          example: true
          description: Remote Disconnectable flag.
        remoteDisconnectable:
          type: boolean
          example: true
          description: Remote Disconnectable flag.
        accuracyClass:
          x-reference:
            entityCollectionId: 9e1a6427-a755-4e51-a0be-41771814dd31
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Accuracy Class value list display value.
        numberOfPhases:
          x-reference:
            entityCollectionId: 2ccd3a41-25ac-4159-83ce-3de95197c6e1
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Example number of phases
          pattern: ""
          maxLength: 100
          type: string
          description: Number Of Phases value list value.
        current:
          x-reference:
            entityCollectionId: 398f5574-d9e8-4d3c-9aa1-649c378fb577
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Current value list display value.
        voltage:
          x-reference:
            entityCollectionId: a5e6e4df-0189-4ae6-8bf8-97e3b05e2bc5
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Voltage value list display value.
        ratioVt:
          nullable: true
          x-reference:
            entityCollectionId: 261068d1-ea70-4958-ad8f-21cb5802d01f
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: RatioVT1
          pattern: ""
          maxLength: 100
          type: string
          description: Ratio Vt value list display value.
        ratioCt:
          nullable: true
          x-reference:
            entityCollectionId: 7f005c8a-eb8d-44aa-a42c-a044eb8baffa
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: 1200/5
          pattern: ""
          maxLength: 100
          type: string
          description: Ratio Ct value list display value.
        technicalCurrent:
          nullable: true
          x-reference:
            entityCollectionId: 4b2035a8-37bb-425b-b823-32542a03ab19
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Technical current value
          pattern: ""
          maxLength: 100
          type: string
          description: Technical Current value list display value.
        technicalVoltage:
          nullable: true
          x-reference:
            entityCollectionId: 1fbddff2-793c-446a-bdeb-0a1ce27ecb85
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Technical voltage value
          pattern: ""
          maxLength: 100
          type: string
          description: Technical Voltage value list display value.
        connectionType:
          x-reference:
            entityCollectionId: 2ac3d273-b494-4794-b1e5-a7dadb925dec
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: connection type value
          pattern: ""
          maxLength: 100
          type: string
          description: Connection Type value list value.
        connectionRemark:
          nullable: true
          x-reference:
            entityCollectionId: 9c659c97-8f76-497b-9f8d-3623231e8ca0
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: WireSquare X
          pattern: ""
          maxLength: 100
          type: string
          description: Connection Remark value list display value.
        meteringPrinciple:
          nullable: true
          x-reference:
            entityCollectionId: b7ffad6f-864e-4b61-8220-66719c4233e7
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Phase
          pattern: ""
          maxLength: 100
          type: string
          description: Metering Principle value list display value.
        mainControlMeterRole:
          nullable: true
          pattern: ""
          example: Control Type
          type: string
          description: Meter Control Type
          maxLength: 25
        relatedMeter:
          nullable: true
          pattern: ""
          example: Related Meter
          type: string
          description: Related Meter.
          maxLength: 25
        temporary:
          type: boolean
          example: true
          description: Temporary flag.
        meterConfiguration:
          $ref: "#/components/schemas/MeterDetailsMeterConfigurationItem"
        meterRegisters:
          type: array
          description: Array of Meter registers
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/MeterDetailsMeterRegisterItem"
    SearchMetersResponse:
      description: Search meters response
      title: SearchMetersResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - totalRecords
        - items
      properties:
        totalRecords:
          description: Total records
          type: integer
          format: int32
          maximum: 2147483647
          minimum: 0
        items:
          type: array
          description: Array of meters
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/SearchMeterItem"
    SearchMeterItem:
      description: Search meter item
      title: SearchMetersResponse
      type: object
      nullable: false
      required:
        - meterId
        - supplyType
        - fabricationType
        - meterType
        - meterNumber
        - meterFrameNumber
        - serialNumber
        - fabricationNumber
        - barCodeEanNumber
        - manufacturer
        - alternativeMeterNumber
        - productionYear
        - hesSystem
        - communicationMethod
        - remoteReadable
        - remoteDisconnectable
        - accuracyClass
        - numberOfPhases
        - current
        - voltage
        - ratioVt
        - ratioCt
        - technicalCurrent
        - technicalVoltage
        - connectionType
        - connectionRemark
        - meteringPrinciple
        - mainControlMeterRole
        - relatedMeter
        - temporary
      properties:
        meterId:
          type: string
          pattern: ""
          maxLength: 100
          description: Meter identifier
        supplyType:
          maxLength: 11
          example: Heating
          type: string
          description: System name of supply type.
          pattern: ^(Electricity|Heating|Water)$
        fabricationType:
          x-reference:
            entityCollectionId: 68199f9c-bf84-4eef-b254-a90c2270aca7
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Fabrication Type value list display value.
        meterType:
          pattern: ""
          maxLength: 100
          type: string
          example: Meter typeb
          x-reference:
            entityCollectionId: 80a64f06-e6ac-44cf-a450-ece1d34f1e27
            entityType: ValueListValue
            entityFamily: ReferenceData
          description: Meter type value list display value.
        meterNumber:
          maxLength: 50
          type: string
          pattern: ^.*$
          example: ""
          description: Meter number.
        meterFrameNumber:
          example: "1234"
          pattern: ^.*$
          minLength: 0
          type: string
          description: |-
            DK: Målerrammenummer.
            Number on the Meter Frame.
          maxLength: 50
        serialNumber:
          nullable: true
          pattern: ""
          minLength: 1
          example: SerialNumber
          type: string
          description: Serial Number.
          maxLength: 50
        fabricationNumber:
          nullable: true
          pattern: ""
          minLength: 1
          example: FabricationNumber
          type: string
          description: Fabrication Number.
          maxLength: 50
        barCodeEanNumber:
          nullable: true
          pattern: ""
          minLength: 1
          example: Bar Code
          type: string
          description: Bar Code Ean Number.
          maxLength: 50
        manufacturer:
          x-reference:
            entityCollectionId: 1eee884f-a3e1-41bd-8641-c4bd16cb901d
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Manufacturer
          pattern: ""
          maxLength: 100
          type: string
          description: Manufacturer value list display value.
        alternativeMeterNumber:
          nullable: true
          pattern: ""
          minLength: 0
          example: Alternative
          type: string
          description: Alternative Meter Number.
          maxLength: 50
        productionYear:
          nullable: true
          example: 2019-11-14
          format: date
          type: string
          description: Production Year.
        hesSystem:
          x-reference:
            entityCollectionId: 9622ac54-3ff0-4c8a-ba28-7395400c2c94
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: HES system a
          pattern: ""
          maxLength: 100
          type: string
          description: Hes System value list display value.
        communicationMethod:
          nullable: true
          x-reference:
            entityCollectionId: ef95bf39-e943-4142-aed2-5832c2dc7fc4
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Communication Method value list display value.
        remoteReadable:
          type: boolean
          example: true
          description: Remote Disconnectable flag.
        remoteDisconnectable:
          type: boolean
          example: true
          description: Remote Disconnectable flag.
        accuracyClass:
          x-reference:
            entityCollectionId: 9e1a6427-a755-4e51-a0be-41771814dd31
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Accuracy Class value list display value.
        numberOfPhases:
          x-reference:
            entityCollectionId: 2ccd3a41-25ac-4159-83ce-3de95197c6e1
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Example number of phases
          pattern: ""
          maxLength: 100
          type: string
          description: Number Of Phases value list value.
        current:
          x-reference:
            entityCollectionId: 398f5574-d9e8-4d3c-9aa1-649c378fb577
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Current value list display value.
        voltage:
          x-reference:
            entityCollectionId: a5e6e4df-0189-4ae6-8bf8-97e3b05e2bc5
            entityType: ValueListValue
            entityFamily: ReferenceData
          pattern: ""
          maxLength: 100
          type: string
          description: Voltage value list display value.
        ratioVt:
          nullable: true
          x-reference:
            entityCollectionId: 261068d1-ea70-4958-ad8f-21cb5802d01f
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: RatioVT1
          pattern: ""
          maxLength: 100
          type: string
          description: Ratio Vt value list display value.
        ratioCt:
          nullable: true
          x-reference:
            entityCollectionId: 7f005c8a-eb8d-44aa-a42c-a044eb8baffa
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: 1200/5
          pattern: ""
          maxLength: 100
          type: string
          description: Ratio Ct value list display value.
        technicalCurrent:
          nullable: true
          x-reference:
            entityCollectionId: 4b2035a8-37bb-425b-b823-32542a03ab19
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Technical current value
          pattern: ""
          maxLength: 100
          type: string
          description: Technical Current value list display value.
        technicalVoltage:
          nullable: true
          x-reference:
            entityCollectionId: 1fbddff2-793c-446a-bdeb-0a1ce27ecb85
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Technical voltage value
          pattern: ""
          maxLength: 100
          type: string
          description: Technical Voltage value list display value.
        connectionType:
          x-reference:
            entityCollectionId: 2ac3d273-b494-4794-b1e5-a7dadb925dec
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: connection type value
          pattern: ""
          maxLength: 100
          type: string
          description: Connection Type value list value.
        connectionRemark:
          nullable: true
          x-reference:
            entityCollectionId: 9c659c97-8f76-497b-9f8d-3623231e8ca0
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: WireSquare X
          pattern: ""
          maxLength: 100
          type: string
          description: Connection Remark value list display value.
        meteringPrinciple:
          nullable: true
          x-reference:
            entityCollectionId: b7ffad6f-864e-4b61-8220-66719c4233e7
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Phase
          pattern: ""
          maxLength: 100
          type: string
          description: Metering Principle value list display value.
        mainControlMeterRole:
          nullable: true
          pattern: ""
          example: Control Type
          type: string
          description: Meter Control Type
          maxLength: 25
        relatedMeter:
          nullable: true
          pattern: ""
          example: Related Meter
          type: string
          description: Related Meter.
          maxLength: 25
        temporary:
          type: boolean
          example: true
          description: Temporary flag.

    MeterDetailsMeterConfigurationItem:
      description: Meter details meter configuration item
      title: MeterDetailsMeterConfigurationItem
      type: object
      nullable: true
      additionalProperties: false
      required:
        - name
        - description
      properties:
        name:
          nullable: true
          pattern: ""
          example: Meter Configuration Name
          type: string
          description: Meter Configuration Name.
          maxLength: 256
        description:
          nullable: true
          pattern: ""
          example: Meter Configuration Description
          type: string
          description: Meter Configuration Description.
          maxLength: 1024

    MeterDetailsMeterRegisterItem:
      description: Meter details Meter register item
      title: MeterDetailsMeterRegisterItem
      type: object
      nullable: false
      additionalProperties: false
      required:
        - registerId
        - inputNumber
        - inputType
        - registerNumber
        - registerName
        - meterReadingType
        - measuringUnit
        - meteringComponent
      properties:
        registerId:
          nullable: false
          pattern: ""
          example: Register identifier
          type: string
          description: Register identifier.
          maxLength: 100
        inputNumber:
          nullable: false
          pattern: ""
          example: Input Number
          type: string
          description: Input Number.
          maxLength: 256
        inputType:
          nullable: false
          pattern: ""
          example: Input Type
          type: string
          description: Input Type.
          maxLength: 256
        registerNumber:
          nullable: false
          pattern: ""
          example: Register Number
          type: string
          description: Register Number.
          maxLength: 256
        registerName:
          nullable: false
          pattern: ""
          example: Register Name
          type: string
          description: Register Name.
          maxLength: 256
        meterReadingType:
          nullable: false
          pattern: ""
          example: Meter Reading Type
          type: string
          description: Meter Reading Type.
          maxLength: 256
        measuringUnit:
          nullable: false
          pattern: ""
          example: Measuring Unit
          type: string
          description: Measuring Unit.
          maxLength: 256
        meteringComponent:
          nullable: true
          x-reference:
            entityCollectionId: 0ECD6378-B64A-419A-B0DF-770EFFE0D006
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: A- Produktion
          pattern: ""
          maxLength: 100
          type: string
          description: Metering Component Id.
    SearchTextFilter:
      description: Text filtering object
      title: SearchTextFilter
      type: object
      nullable: true
      additionalProperties: false
      required:
        - value
        - condition
      properties:
        value:
          description: Filtering value
          type: string
          maxLength: 256
          pattern: ""
        condition:
          description: Filtering condition
          type: string
          pattern: "^(contains|equals)$"
          maxLength: 100
    SearchDateFilter:
      description: Date filtering object
      title: SearchDateFilter
      type: object
      nullable: true
      additionalProperties: false
      required:
        - value
        - condition
      properties:
        value:
          description: Filtering value
          type: string
          format: date-time
        condition:
          description: Filtering condition
          type: string
          pattern: "^(lt|lte|gt|gte|equals)$"
          maxLength: 100
    SearchBoolFilter:
      description: Bool filtering object
      title: SearchBoolFilter
      type: object
      nullable: true
      additionalProperties: false
      required:
        - value
        - condition
      properties:
        value:
          description: Filtering value
          type: boolean
        condition:
          description: Filtering condition
          type: string
          pattern: "^(equals)$"
          maxLength: 100
    GetRegisterResponse:
      description: Search meters response
      title: GetRegisterResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - register
      properties:
        register:
          $ref: "#/components/schemas/GetRegisterResponseRegister"
        meter:
          $ref: "#/components/schemas/GetRegisterResponseMeter"

    GetRegisterResponseRegister:
      description: Register data for get register details response
      title: GetRegisterResponseRegister
      type: object
      nullable: false
      additionalProperties: false
      required:
        - registerId
        - registerNumber
        - measurementType
        - timeSeriesType
        - unit
        - maxCounterValue
        - validFrom
        - validTo
        - displayFactor
        - numberOfOverflows
      properties:
        registerId:
          type: string
          description: Register identifier
          maxLength: 100
          pattern: ""
        registerNumber:
          type: integer
          description: Register number
          maximum: 2147483647
          minimum: 1
          format: int32
        measurementType:
          type: string
          pattern: ""
          description: Metering component
          maxLength: 100
          example: A+ Forbrug
          x-reference:
            entityFamily: "ReferenceData"
            entityType: "ValueListValue"
            entityCollectionId: "0ecd6378-b64a-419a-b0df-770effe0d006"
        timeSeriesType:
          type: string
          description: Meter reading type
          maxLength: 256
          pattern: "^(Accumulating|Interval)$"
          example: "Accumulating"
        unit:
          type: string
          description: Measuring unit
          maxLength: 256
          pattern: "^(Wh|kWh|MWh|VArh|kVArh|MVArh|kW|m3|TemperatureCelsiusWater|TemperatureCelsiusRoom)$"
          example: "kWh"
        maxCounterValue:
          type: number
          format: decimal
          description: Maximum counter value
          example: 999999.999
        validFrom:
          description: Valid from
          pattern: "^.*$"
          type: string
          maxLength: 100
        validTo:
          description: Valid to
          pattern: "^.*$"
          type: string
          maxLength: 100
        displayFactor:
          description: Factor
          pattern: "^.*$"
          type: string
          maxLength: 100
        numberOfOverflows:
          description: Overflow
          pattern: "^.*$"
          type: string
          maxLength: 100

    GetRegisterResponseMeter:
      description: Meter data for get register details response
      title: GetRegisterResponseMeter
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meterId
        - meterNumber
        - installationPeriodFrom
        - installationPeriodTo
        - hesSystem
      properties:
        meterId:
          type: string
          pattern: ""
          maxLength: 100
          description: Meter identifier
        meterNumber:
          type: string
          description: Meter number
          pattern: ""
          maxLength: 256
          example: "951753660"
        installationPeriodFrom:
          type: string
          description: Installation period from
          format: date-time
          example: "2023-01-01T00:00:00Z"
        installationPeriodTo:
          type: string
          nullable: true
          description: Installation period to
          format: date-time
          example: "2023-12-31T23:59:59Z"
        hesSystem:
          type: string
          pattern: ""
          maxLength: 100
          example: HES system a
          description: HES system
          x-reference:
            entityFamily: "ReferenceData"
            entityType: "ValueListValue"
            entityCollectionId: "9622ac54-3ff0-4c8a-ba28-7395400c2c94"

    GetMeteringPointTimeSeriesResponse:
      description: Meter xx data for get register details response
      title: GetMeteringPointTimeSeriesResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meteringPoint
        - timeSeries
      properties:
        meteringPoint:
          $ref: "#/components/schemas/MeteringPoint"
        timeSeries:
          $ref: "#/components/schemas/TimeSeries"

    MeteringPoint:
      description: Metering point details response
      title: MeteringPoint
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meteringPointNumber
        - validityFrom
        - validityTo
        - type
        - code
        - formula
        - meterFrameNumber
      properties:
        meteringPointNumber:
          type: string
          pattern: ""
          maxLength: 100
          description: Metering point number
          example: "571313190000010454"
        validityFrom:
          type: string
          description: Valid from date
          format: date-time
          example: "2023-01-01T00:00:00Z"
        validityTo:
          type: string
          description: Valid to date
          format: date-time
          example: "2023-01-01T00:00:00Z"
        type:
          type: string
          description: Time series type
          maxLength: 256
          pattern: "^(Additive|Accumulative|Constant|Instantaneous)$"
          example: "Additive"
        code:
          type: string
          pattern: ""
          maxLength: 100
          description: Code
          example: "571313190000010454"
        formula:
          type: string
          pattern: ""
          description: Formula
          maxLength: 256
        meterFrameNumber:
          type: string
          pattern: ""
          maxLength: 100
          description: Number identifier of meter frame
          example: "14658_3_4_06/04/2025 08:00:00"
        meterFrameId:
          description: Meter frame identifier.
          type: string
          pattern: ""
          maxLength: 100
          nullable: true
          example: "*********"

    TimeSeries:
      description: Time Series  Response
      title: TimeSeries
      type: object
      nullable: false
      additionalProperties: false
      required:
        - type
        - measurementType
        - unit
        - dataResolution
      properties:
        type:
          type: string
          description: Time series type
          maxLength: 256
          pattern: "^(Additive|Accumulative|Constant|Instantaneous)$"
          example: "Additive"
        measurementType:
          type: string
          pattern: ""
          description: Metering component
          maxLength: 256
        unit:
          type: string
          description: Unit type
          maxLength: 256
          pattern: "^(A|kVArh|kW|kWh|MW|MWh|t|MVAr|pcs|N|m|J|m3|DKK|EUR|NOK|SEK|DKKPerkWh|DKKPerMWh|EURPerkWh|EURPerMWh|NOKPerkWh|NOKPerMWh|SEKPerkWh|SEKPerMWh|NoUnit|kg|kgPerNm3|kWhPerNm3|MJ|Nm3|MJPerNm3|Nm3Perh|ONm3|kWhPerh|GJ|kgPerGJ|DKKPerNm3|EURPerNm3|NOKPerNm3|SEKPerNm3|GBP|GBPPerkWh|GBPPerMWh|GBPPerNm3|USD|USDPerkWh|USDPerMWh|USDPerNm3|GW|GWh|MVArh|DKKPerpcs|EURPerpcs|NOKPerpcs|SEKPerpcs|GBPPerpcs|USDPerpcs|EuroCent|EuroCentPerkWh|EuroCentPerMWh|EuroCentPerNm3|Ore|OrePerkWh|OrePerMWh|OrePerNm3|DkkPerEuro|EuroPerDkk|DKKPerkW|DKKPerA|kVA|DKKPerkVA|m2|DKKPerm|DKKPerm2|DKKPerm3|PLN|PLNPerA|PLNPerkVA|PLNPerkW|PLNPerkWh|PLNPerm|PLNPerm2|PLNPerm3|PLNPerMWh|PLNPerNm3|PLNPerpcs|Grosz|GroszPerkWh|GroszPerMWh|GroszPerNm3|Celsius)$"
          example: "kWh"
        dataResolution:
          type: string
          description: Meter reading occurrence
          maxLength: 256
          pattern: "^(PT1M|PT5M|PT10M|PT15M|PT30M|PT1H|P1D|P7D|P1M|P3M|P1Y)$"
          example: "PT15M"

    GetMeterFrameDetailsResponse:
      description: Get meter frame details response
      title: GetMeterFrameDetailsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meterFrameId
        - meterFrameNumber
        - meterFrameVersions
        - registerRequirements
      properties:
        meterFrameId:
          description: Meter frame identifier.
          type: string
          pattern: ""
          maxLength: 100
          example: "*********"
        meterFrameNumber:
          pattern: ^.*$
          minLength: 0
          maxLength: 50
          type: string
          description: |-
            DK: Målerrammenummer.
            Number on the Meter Frame.
          example: "123421"
        meterFrameVersions:
          type: array
          description: Array of meter frame versions
          nullable: false
          minItems: 1
          maxItems: 1024
          items:
            $ref: "#/components/schemas/GetMeterFrameDetailsMeterFrameVersion"
        registerRequirements:
          type: array
          description: Array of register requirements
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/GetMeterFrameDetailsResponseRegisterRequirementItem"

    GetMeterFrameDetailsMeterFrameVersion:
      description: Get meter frame details meter frame version
      title: GetMeterFrameDetailsResponseMeterFrameVersion
      type: object
      nullable: false
      additionalProperties: false
      required:
        - validFrom
        - validTo
        - generalInfo
        - additionalSupplyTypeInfo
      properties:
        validFrom:
          description: Meter frame version valid from.
          type: string
          format: date-time
          example: "2019-11-14T23:00:00.000Z"
        validTo:
          description: Meter frame version valid to.
          type: string
          format: date-time
          example: "2019-11-14T23:00:00.000Z"
        generalInfo:
          $ref: "#/components/schemas/GetMeterFrameDetailsResponseGeneralInfo"
        additionalSupplyTypeInfo:
          $ref: "#/components/schemas/GetMeterFrameDetailsResponseAdditionalSupplyTypeInfo"
        meterInfo:
          $ref: "#/components/schemas/GetMeterFrameDetailsResponseMeterInfo"

    GetMeterFrameDetailsResponseGeneralInfo:
      description: Get meter frame details response general info
      title: GetMeterFrameDetailsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - supplyType
        - created
        - placementCode
        - placementSpecification
        - connectionStatus
        - noMeter
        - decommissioned
        - gisId
        - geographicalLocationLatitude
        - geographicalLocationLongitude
      properties:
        supplyType:
          maxLength: 11
          example: Heating
          type: string
          description: System name of supply type.
          pattern: ^(Electricity|Heating|Water)$
        created:
          type: string
          format: date-time
          example: 2019-11-14T00:00:00Z
          description: Created timestamp.
        placementCode:
          x-reference:
            entityCollectionId: 152EF9AC-D751-4331-AE9B-B938CF33BC98
            entityType: ValueListValue
            entityFamily: ReferenceData
          type: string
          pattern: ""
          maxLength: 100
          description: Placement code from value list.
        placementSpecification:
          description: |-
            Placement specification
            DK: Placeringsbeskrivelse.
          nullable: true
          pattern: "^.*$"
          type: string
          minLength: 0
          maxLength: 50
          example: "Spec1"
        connectionStatus:
          x-reference:
            entityCollectionId: 8695B6D3-CA87-455C-B5E2-F130DEB0C50C
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Nyoprettet
          type: string
          pattern: ""
          maxLength: 100
          description: Connection statusId from value list.
        noMeter:
          description: Filter by No meter.
          type: boolean
          nullable: true
          example: true
        decommissioned:
          type: string
          format: date-time
          description: Indicates when the meter frame is decomissioned.
          nullable: true
        gisId:
          type: string
          nullable: true
          description: "Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system."
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        geographicalLocationLatitude:
          description: Latitude - Y in most spatial file formats.
          type: number
          format: decimal
          example: 5.33
          nullable: true
        geographicalLocationLongitude:
          description: Longitude - X in most spatial file formats.
          type: number
          format: decimal
          example: 23.4
          nullable: true

    GetMeterFrameDetailsResponseAdditionalSupplyTypeInfo:
      description: Get meter frame details response additional supply type info
      title: GetMeterFrameDetailsResponseAdditionalSupplyTypeInfo
      type: object
      nullable: false
      additionalProperties: false
      required:
        - connectionType
        - lossFactor
        - meterFrameFuse
        - powerLimitKw
        - productionCapacity
        - connectionRemark
        - purpose
        - tariffConnectionPoint
        - ratioVt
        - ratioCt
      properties:
        connectionType:
          x-reference:
            entityCollectionId: 2ac3d273-b494-4794-b1e5-a7dadb925dec
            entityType: ValueListValue
            entityFamily: ReferenceData
          type: string
          example: CT-VT
          nullable: true
          pattern: ""
          maxLength: 100
          description: Connection Type display value.
        lossFactor:
          type: number
          description: |-
            DK: NettabsFaktor.
            Nettabsfaktor som bruges på målerrammen i MDM. Vi skal lige gennemgå den fra MDM siden. Skal indgå både på fuldttidserie niveau så faktoren ganges på både 15/60 forbrug samt
            tællerstande.
            Mains loss factor used on the measuring frame in MDM. We just need to review it from the MDM page. Must be included both at full-time series level so the factor is multiplied by
            both 15/60 consumption and meter readings.
          format: decimal
          nullable: true
          example: 10.1
        meterFrameFuse:
          type: integer
          description: |-
            DK: MålerRammeForsikring.
            Forsikring (tarifsikring, T-ret sikring etc) som ikke er stikledningssikringen eller tilslutningsrettigheden. Kunden kan selv bestemme størrelsen på denne.
            Hvis kunden ikke har oplyst en forsikring, så er stikledningssikringen kundens forsikring.
            Kunden kan selv sikrer op eller ned (stikledningssikringen begrænser ham selvfølgelig).
            Vi har den kun i systemet for at kunne rumme data fra blanketten.
            EN:
            Insurance (tariff fuse, T-right fuse etc) which is not the branch line fuse or the connection right. The customer can decide the size of this.
            If the customer has not stated an insurance, then the branch line insurance is the customer's insurance.
            The customer can secure up or down himself (the branch line protection limits him of course).
            We only have it in the system to be able to hold data from the form.
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          nullable: true
          example: 1234
        powerLimitKw:
          type: number
          description: |-
            Power limit in kW
            DK: EffektgrænseKW.
          format: decimal
          nullable: true
          example: 1234
        productionCapacity:
          type: number
          description: |-
            Production capacity
            DK: Anlægskapacitet.
          format: decimal
          nullable: true
          example: 1234
        connectionRemark:
          nullable: true
          x-reference:
            entityCollectionId: 9c659c97-8f76-497b-9f8d-3623231e8ca0
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Stikbensadapter X
          pattern: ""
          maxLength: 100
          type: string
          description: Connection Remark display value.
        purpose:
          x-reference:
            entityCollectionId: 9FEEDC56-EC29-4B8D-A15E-C65992CD8DCD
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Kontrol måling
          type: string
          pattern: ""
          maxLength: 100
          nullable: true
          description: Purpose display value.
        tariffConnectionPoint:
          x-reference:
            entityCollectionId: 439646F1-53AD-4DA0-926A-80E7ECFF3EEC
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: B høj >= 10 kV
          pattern: ""
          maxLength: 100
          nullable: true
          type: string
          description: Tarif connection point display value.
        ratioVt:
          x-reference:
            entityCollectionId: 261068d1-ea70-4958-ad8f-21cb5802d01f
            entityType: ValueListValue
            entityFamily: ReferenceData
          nullable: true
          example: RatioVT1
          pattern: ""
          maxLength: 100
          type: string
          description: Ratio Vt display value.
        ratioCt:
          nullable: true
          x-reference:
            entityCollectionId: 7f005c8a-eb8d-44aa-a42c-a044eb8baffa
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: 1200/5
          pattern: ""
          maxLength: 100
          type: string
          description: Ratio Ct Id.

    GetMeterFrameDetailsResponseMeterInfo:
      description: Get meter frame details response meter info
      title: GetMeterFrameDetailsResponseMeterInfo
      type: object
      nullable: true
      additionalProperties: false
      required:
        - meterNumber
        - meterType
        - meterInputNumber
        - meterInputType
        - validFrom
        - validTo
      properties:
        meterNumber:
          description: Meter number.
          type: string
          maxLength: 50
          example: ""
          pattern: ^.*$
        meterType:
          description: Meter type display value.
          x-reference:
            entityFamily: ReferenceData
            entityType: ValueListValue
            entityCollectionId: 80a64f06-e6ac-44cf-a450-ece1d34f1e27
          type: string
          pattern: ""
          maxLength: 100
          example: Meter typec 1
        meterInputNumber:
          description: This field is part of the MeterConfiguration in the Meter domain.
          type: integer
          format: int32
          minimum: 0
          maximum: 2147483647
          example: 111
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          type: string
          pattern: ""
          example: Internal
          maxLength: 9999999
        validFrom:
          description: Valid from date.
          type: string
          format: date-time
          example: 2019-11-14T00:00:00Z
        validTo:
          description: Valid to date.
          type: string
          format: date-time
          example: 2019-11-14T00:00:00Z

    GetMeterFrameDetailsResponseRegisterRequirementItem:
      description: Get meter frame details response register requirement item
      title: GetMeterFrameDetailsResponseRegisterRequirementItem
      type: object
      nullable: false
      additionalProperties: false
      required:
        - requirementId
        - meteringComponent
        - validFrom
        - validTo
      properties:
        requirementId:
          description: Register requirement id.
          type: string
          pattern: ""
          maxLength: 100
          example: "*********"
        meteringComponent:
          description: |-
            Display value of the Metering Component value list.
            DK: MålingsKomponentId.
            Shared value list with Meter domain.
            The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
            It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
          type: string
          pattern: ""
          maxLength: 100
          example: Produktion A-
          x-reference:
            entityFamily: ReferenceData
            entityType: ValueListValue
            entityId: 0ecd6378-b64a-419a-b0df-770effe0d006
        validFrom:
          description: Valid from date.
          type: string
          format: date-time
          example: 2019-11-14T00:00:00Z
        validTo:
          description: Valid to date.
          type: string
          format: date-time
          example: 2019-11-14T00:00:00Z

    SearchMeterFramesRequest:
      description: Search meter frames request
      title: SearchMeterFramesRequest
      type: object
      additionalProperties: false
      required:
        - limit
        - offset
      properties:
        offset:
          description: Results offset
          type: integer
          format: int32
          maximum: 10000000
          minimum: 0
        limit:
          description: Results limit
          type: integer
          format: int32
          maximum: 1000
          minimum: 0
        sortOrder:
          description: Sorting order
          type: string
          nullable: true
          pattern: "^(ASC|DESC)$"
          maxLength: 16
        sortBy:
          description: Sorting property
          type: string
          nullable: true
          pattern: "^(connectionType|supplyType|connectionStatus)$"
          maxLength: 64
        meterFramesFilters:
          description: Filters
          nullable: true
          type: object
          additionalProperties: false
          properties:
            supplyType:
              $ref: "#/components/schemas/SearchTextFilter"
            meterFrameNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            connectionStatus:
              $ref: "#/components/schemas/SearchTextFilter"
            connectionType:
              $ref: "#/components/schemas/SearchTextFilter"
            connectionPointNumber:
              $ref: "#/components/schemas/SearchTextFilter"
    SearchMeterFramesResponse:
      description: Search meter frames response
      title: SearchMeterFramesResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - totalRecords
        - items
      properties:
        totalRecords:
          description: Total records
          type: integer
          format: int32
          maximum: 2147483647
          minimum: 0
        items:
          type: array
          description: Array of meters
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/SearchMeterFrameItem"
    SearchMeterFrameItem:
      description: Search meter frame item
      title: SearchMeterFrameItem
      type: object
      required:
        - meterFrameId
        - meterFrameNumber
        - supplyType
        - created
        - placementCode
        - placementSpecification
        - noMeter
        - decommissioned
        - gisId
        - geographicalLocationLatitude
        - geographicalLocationLongitude
        - connectionType
        - meterFrameFuse
        - powerLimitKw
        - lossFactor
        - productionCapacity
        - ratioCt
        - ratioVt
        - tariffConnectionPoint
        - purpose
        - connectionRemark
        - connectionPointNumber
      properties:
        meterFrameId:
          description: Meter frame identifier.
          type: string
          pattern: ""
          maxLength: 100
          example: "*********"
        meterFrameNumber:
          pattern: ^.*$
          minLength: 0
          maxLength: 50
          type: string
          description: |-
            DK: Målerrammenummer.
            Number on the Meter Frame.
          example: "1234"
        supplyType:
          maxLength: 11
          example: Heating
          type: string
          description: System name of supply type.
          pattern: ^(Electricity|Heating|Water)$
        created:
          example: 2019-11-14
          format: date
          type: string
          description: Created timestamp.
        placementCode:
          x-reference:
            entityCollectionId: 152EF9AC-D751-4331-AE9B-B938CF33BC98
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: IFA - indvendig FRI adgang
          type: string
          pattern: ""
          maxLength: 100
          description: Placement code display value.
        placementSpecification:
          description: |-
            Placement specification
            DK: Placeringsbeskrivelse.
          nullable: true
          pattern: "^.*$"
          type: string
          minLength: 0
          maxLength: 50
          example: "Spec1"
        connectionStatus:
          x-reference:
            entityCollectionId: 8695B6D3-CA87-455C-B5E2-F130DEB0C50C
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Nyoprettet
          type: string
          pattern: ""
          maxLength: 100
          description: Connection status display value.
        noMeter:
          description: Filter by No meter.
          type: boolean
          nullable: true
          example: true
        decommissioned:
          type: string
          format: date-time
          description: Indicates when the meter frame is decomissioned.
          nullable: true
        gisId:
          type: string
          description: "Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system."
          format: uuid
          nullable: true
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        geographicalLocationLatitude:
          description: Latitude - Y in most spatial file formats.
          type: number
          format: decimal
          example: 5.33
          nullable: true
        geographicalLocationLongitude:
          description: Longitude - X in most spatial file formats.
          type: number
          format: decimal
          example: 23.4
          nullable: true
        connectionPointNumber:
          type: string
          pattern: "^.*$"
          minLength: 0
          maxLength: 64
          description: Number of the ConnectionPoint.
          example: "20000001"
        connectionType:
          x-reference:
            entityCollectionId: 2ac3d273-b494-4794-b1e5-a7dadb925dec
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: CT-VT
          nullable: true
          pattern: ""
          maxLength: 100
          type: string
          description: Connection Type display value.
        lossFactor:
          type: number
          description: |-
            DK: NettabsFaktor.
            Nettabsfaktor som bruges på målerrammen i MDM. Vi skal lige gennemgå den fra MDM siden. Skal indgå både på fuldttidserie niveau så faktoren ganges på både 15/60 forbrug samt
            tællerstande.
            Mains loss factor used on the measuring frame in MDM. We just need to review it from the MDM page. Must be included both at full-time series level so the factor is multiplied by
            both 15/60 consumption and meter readings.
          format: decimal
          nullable: true
          example: 10.1
        meterFrameFuse:
          type: integer
          description: |-
            DK: MålerRammeForsikring.
            Forsikring (tarifsikring, T-ret sikring etc) som ikke er stikledningssikringen eller tilslutningsrettigheden. Kunden kan selv bestemme størrelsen på denne.
            Hvis kunden ikke har oplyst en forsikring, så er stikledningssikringen kundens forsikring.
            Kunden kan selv sikrer op eller ned (stikledningssikringen begrænser ham selvfølgelig).
            Vi har den kun i systemet for at kunne rumme data fra blanketten.
            EN:
            Insurance (tariff fuse, T-right fuse etc) which is not the branch line fuse or the connection right. The customer can decide the size of this.
            If the customer has not stated an insurance, then the branch line insurance is the customer's insurance.
            The customer can secure up or down himself (the branch line protection limits him of course).
            We only have it in the system to be able to hold data from the form.
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          nullable: true
          example: 1234
        powerLimitKw:
          type: number
          description: |-
            Power limit in kW
            DK: EffektgrænseKW.
          format: decimal
          nullable: true
          example: 1234
        productionCapacity:
          type: number
          description: |-
            Production capacity
            DK: Anlægskapacitet.
          format: decimal
          nullable: true
          example: 1234
        connectionRemark:
          nullable: true
          x-reference:
            entityCollectionId: 9c659c97-8f76-497b-9f8d-3623231e8ca0
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Stikbensadapter X
          pattern: ""
          maxLength: 100
          type: string
          description: Connection Remark display value.
        purpose:
          x-reference:
            entityCollectionId: 9FEEDC56-EC29-4B8D-A15E-C65992CD8DCD
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: Kontrol måling
          pattern: ""
          maxLength: 100
          type: string
          nullable: true
          description: Purpose display value.
        tariffConnectionPoint:
          x-reference:
            entityCollectionId: 439646F1-53AD-4DA0-926A-80E7ECFF3EEC
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: B høj >= 10 kV
          pattern: ""
          maxLength: 100
          nullable: true
          type: string
          description: Tarif connection point display value.
        ratioVt:
          nullable: true
          x-reference:
            entityCollectionId: 261068d1-ea70-4958-ad8f-21cb5802d01f
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: RatioVT1
          pattern: ""
          maxLength: 100
          type: string
          description: Ratio Vt display value.
        ratioCt:
          nullable: true
          x-reference:
            entityCollectionId: 7f005c8a-eb8d-44aa-a42c-a044eb8baffa
            entityType: ValueListValue
            entityFamily: ReferenceData
          example: 1200/5
          pattern: ""
          maxLength: 100
          type: string
          description: Ratio Ct display value.
    SearchMeteringPointsRequest:
      description: Search metering point request
      title: SearchMeteringPoints
      type: object
      additionalProperties: false
      required:
        - limit
        - offset
      properties:
        sortOrder:
          description: Sorting order
          type: string
          nullable: true
          pattern: "^(ASC|DESC)$"
          maxLength: 16
        sortBy:
          description: Sorting property
          type: string
          nullable: true
          pattern: "^(validFrom|supplyType|typeOfMeteringPoint|subTypeOfMeteringPoint|meterReadingOccurrence|connectionStatus|netSettlementGroup)$"
          maxLength: 64
        offset:
          description: Results offset
          type: integer
          format: int32
          maximum: 10000000
          minimum: 0
        limit:
          description: Results limit
          type: integer
          format: int32
          maximum: 1000
          minimum: 0
        searchMeteringPointsFilters:
          description: Filters
          nullable: true
          type: object
          additionalProperties: false
          properties:
            meteringPointNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            validFrom:
              $ref: "#/components/schemas/SearchDateFilter"
            supplyType:
              $ref: "#/components/schemas/SearchTextFilter"
            parentMeteringPointNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            typeOfMeteringPoint:
              $ref: "#/components/schemas/SearchTextFilter"
            subTypeOfMeteringPoint:
              $ref: "#/components/schemas/SearchTextFilter"
            meterReadingOccurrence:
              $ref: "#/components/schemas/SearchTextFilter"
            connectionStatus:
              $ref: "#/components/schemas/SearchTextFilter"
            netSettlementGroup:
              $ref: "#/components/schemas/SearchTextFilter"
            meterMeterNumber:
              $ref: "#/components/schemas/SearchTextFilter"

    SearchTechnicalMeteringPointsRequest:
      description: Search technical metering point request
      title: SearchTechnicalMeteringPoints
      type: object
      additionalProperties: false
      required:
        - limit
        - offset
      properties:
        sortOrder:
          description: Sorting order
          type: string
          nullable: true
          pattern: "^(ASC|DESC)$"
          maxLength: 16
        sortBy:
          description: Sorting property
          type: string
          nullable: true
          pattern: "^(validFrom|supplyType|typeOfMeteringPoint|subTypeOfMeteringPoint|readingFrequency|connectionStatus)$"
          maxLength: 64
        offset:
          description: Results offset
          type: integer
          format: int32
          maximum: 10000000
          minimum: 0
        limit:
          description: Results limit
          type: integer
          format: int32
          maximum: 1000
          minimum: 0
        searchTechnicalMeteringPointsFilters:
          description: Filters
          nullable: true
          type: object
          additionalProperties: false
          properties:
            meteringPointNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            validFrom:
              $ref: "#/components/schemas/SearchDateFilter"
            supplyType:
              $ref: "#/components/schemas/SearchTextFilter"
            connectionPointNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            connectionStatus:
              $ref: "#/components/schemas/SearchTextFilter"
            typeOfMeteringPoint:
              $ref: "#/components/schemas/SearchTextFilter"
            subTypeOfMeteringPoint:
              $ref: "#/components/schemas/SearchTextFilter"
            readingFrequency:
              $ref: "#/components/schemas/SearchTextFilter"
            unitType:
              $ref: "#/components/schemas/SearchTextFilter"

    SearchMeteringPointsResponse:
      description: Search metering points response
      title: SearchMeteringPointsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - totalRecords
        - items
      properties:
        totalRecords:
          description: Total records
          type: integer
          format: int32
          maximum: 2147483647
          minimum: 0
        items:
          type: array
          description: Items
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/SearchMeteringPointItem"
    SearchMeteringPointItem:
      description: Search meter item
      title: SearchMetersResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meteringPointNumber
        - validFrom
        - supplyType
        - parentMeteringPointNumber
        - typeOfMeteringPoint
        - subTypeOfMeteringPoint
        - meterReadingOccurrence
        - connectionStatus
        - settlementMethod
        - maximumPower
        - meteringGridAreaId
        - locationDescription
        - productId
        - unitType
        - fromNet
        - toNet
        - netSettlementGroup
        - powerSupplierGsrn
        - productionCapacityInKiloWatts
        - connectionType
        - assetType
        - meterMeterNumber
        - meterMeterReadingType
        - meterNumberOfDigits
        - meterUnitType
        - meterConversionFactor
        - disconnectionType
      properties:
        meteringPointNumber:
          description: Electricity Metering Point Number.
          type: string
          pattern: ^.*$
          minLength: 1
          maxLength: 50
          example: "571313190000020132"
        validFrom:
          description: Metering point version validity start
          type: string
          format: date-time
          example: "2019-11-14T22:00:00.000Z"
        supplyType:
          maxLength: 11
          example: Heating
          type: string
          description: System name of supply type.
          pattern: ^(Electricity|Heating|Water)$
        parentMeteringPointNumber:
          description: Parent Metering Point Number.
          type: string
          pattern: ^\d{18}$
          minLength: 18
          maxLength: 18
          example: "571313190000020132"
          x-reference:
            entityFamily: ElectricityMeteringPoint
            entityType: ElectricityMeteringPoint
          nullable: true
        typeOfMeteringPoint:
          description: |-
            Type of measuring point, e.g. production, consumption.
            | Code | Description                         | DanishTranslation                   |
            | ---- | ----------------------------------- | ----------------------------------- |
            | D01  | VE production                       | VE produktion                       |
            | D02  | Analysis                            | Analysemålepunkt                    |
            | D03  | Not used                            | Anvendes ikke                       |
            | D04  | Surplus production group 6          | Overskudsproduktion gruppe 6        |
            | D05  | Net production                      | Nettoproduktion                     |
            | D06  | Supply to grid                      | Leveret til net                     |
            | D07  | Consumption from grid               | Forbrugt fra net                    |
            | D08  | Whole sale services / information   | Afregningsgrundlag/Information      |
            | D09  | Own production                      | Egenproduktion                      |
            | D10  | Net from grid                       | Netto fra net                       |
            | D11  | Net to grid                         | Netto til net                       |
            | D12  | Total consumption                   | Brutto forbrug                      |
            | D13  | Net loss correction                 | Nettabskorrektion                   |
            | D14  | Electrical heating                  | Elvarme                             |
            | D15  | Net consumption                     | Nettoforbrug                        |
            | D16  | Reserved for later use              | Reserveret til senere brug          |
            | D17  | Other consumption                   | Øvrigt forbrug                      |
            | D18  | Other production                    | Øvrig produktion                    |
            | D19  | Reserved for later use              | Reserveret til senere brug          |
            | D20  | Exchange - Reactive energy          | Udveksling – Reaktiv energi         |
            | D21  | Reserved for later use              | Reserveret til senere brug          |
            | D22  | Reserved for later use              | Reserveret til senere brug          |
            | D23  | Reserved for later use              | Reserveret til senere brug          |
            | D24  | Reserved for later use              | Reserveret til senere brug          |
            | D25  | Reserved for later use              | Reserveret til senere brug          |
            | D26  | Reserved for later use              | Reserveret til senere brug          |
            | D27  | Reserved for later use              | Reserveret til senere brug          |
            | D28  | Reserved for later use              | Reserveret til senere brug          |
            | D29  | Reserved for later use              | Reserveret til senere brug          |
            | D30  | Reserved for later use              | Reserveret til senere brug          |
            | D99  | Internal use                        | Intern brug                         |
            | E17  | Consumption                         | Forbrug                             |
            | E18  | Production                          | Produktion                          |
            | E20  | Exchange                            | Udveksling                          |
          type: string
          minLength: 3
          maxLength: 3
          pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D21|D22|D23|D24|D25|D26|D27|D28|D29|D30|D99|E17|E18|E20)$
          example: E17
          nullable: true
        subTypeOfMeteringPoint:
          description: |-
            Sub-type of Metering Point.
            | Code | Description | DanishTranslation |
            | ---- | ----------- | ----------------- |
            | D01  | Physical    | Fysisk            |
            | D02  | Virtual     | Virtuel           |
            | D03  | Calculated  | Beregnet          |
          type: string
          pattern: ^(D01|D02|D03)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        meterReadingOccurrence:
          description: |-
            ISO standard, ISO 8601 is used to express
            resolution
            Either format: PnYnMnDTnHnMnS, where nY
            expresses the number of years and so on to nM et
            number of minutes and nS a number of seconds.
            Or the text "OTHER" / "ANDET".
            Values:
              P1M, PT1H, PT15M, ANDET
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 100
          example: ANDET
          nullable: true
        connectionStatus:
          description: |-
            Connection status of Metering Point also known in DH as PhysicalStatusOfMeteringPoint.
            | Code | Description   | DanishTranslation |
            | ---- | ------------- | ----------------- |
            | D01  | Not used      | Anvendes ikke     |
            | D02  | Closed down   | Nedlæg            |
            | D03  | New           | Ny                |
            | E22  | Connected     | Tilsluttet        |
          type: string
          pattern: ^(D01|D02|D03|E22)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        settlementMethod:
          description: |-
            The Metering Point form of Settlement.
            | Code | Description   | DanishTranslation |
            | ---- | ------------- | ----------------- |
            | D01  | Flex settled  | Flex afregnet     |
            | E01  | Profiled      | Profileret        |
            | E02  | Non profiled  | Ikke-profileret   |
          type: string
          pattern: ^(D01|E01|E02)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        maximumPower:
          description: Power limit in kW.
          type: number
          format: decimal
          minimum: 0
          maximum: 99999999
          example: 100
          nullable: true
        meteringGridAreaId:
          description: Grid area Identifier - 3 digits.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "900"
          nullable: true
        locationDescription:
          description: Possible description of Meter Location.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 256
          example: Building No. 2
          nullable: true
        productId:
          description: Product identification. The product can e.g. be energy or power. GLN number is used to specify the product.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 100
          example: "8716867000030"
          nullable: true
        unitType:
          description: |-
            Indicates unit Type.
            | Code  | Description              | DanishTranslation               |
            | ----  | ------------------------ | --------------------------------|
            | AMP   | Ampere                   | Ampere                          |
            | K3    | kVArh                    | KiloVolt-Ampere reactive hour   |
            | KWH   | kWh                      | Kilowatt-hour                   |
            | KWT   | kW                       | Kilowatt                        |
            | MAW   | MW                       | Megawatt                        |
            | MWH   | MWh                      | Megawatt-hour                   |
            | TNE   | Tonne                    | metric ton                      |
            | Z03   | MVAr                     | MegaVolt-Ampere reactive power  |
            | Z14   | Danish Tariff code       | KT Tarifkode                    |
            | H87   | STK                      | Antal styk                      |
          type: string
          pattern: ^(AMP|K3|KWH|KWT|MAW|MWH|TNE|Z03|Z14|H87)$
          minLength: 2
          maxLength: 3
          example: AMP
          nullable: true
        fromNet:
          description: |-
            FromNet also known as "FromGrid". Network area is a term for a network which managed by a network company.
            Danish Energy's code is used (DE no.)
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "125"
          nullable: true
        toNet:
          description: |-
            Network area is a term for a network which managed by a network company. Danish Energy's code is used (DE no.).
            In DataHub known as ToGrid.
            3 digits.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "123"
          nullable: true
        netSettlementGroup:
          description: |-
            The value 0 is specified for metering points which are not settled net.
            Possible values: 0,1,2,3,4,5,6,7,99. 7 is not used.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 100
          example: "2"
          nullable: true
        powerSupplierGsrn:
          description: Power Plant GSRN - 18 digits.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "579000133055400001"
          nullable: true
        productionCapacityInKiloWatts:
          type: number
          description: The plant's capacity in kW. 8 digits total.
          format: decimal
          minimum: 0
          maximum: 99999999
          example: 1.1
          nullable: true
        connectionType:
          description: |-
            Describes whether a (net settled) Metering Point is directly or installation connected.
            | Code | Description            | DanishTranslation       |
            | ---- | --------------------   | ----------------------  |
            | D01  | Direct connected       | Direkte tilsluttet      |
            | D02  | Installation connected | Installationstilsluttet |
          type: string
          pattern: ^(D01|D02)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        assetType:
          description: |-
            Indicates which technology a Metering Point uses.
            Available codes in value field:
            | Code | Description                              | DanishTranslation               |
            | ---- | ---------------------------------------- | ------------------------------- |
            | D01  | Steam turbine with back-pressure mode    | Dampturbine med modtryksdrift   |
            | D02  | Gasturbine                               | Gasturbine                      |
            | D03  | Combined cycle                           | Combined cycle                  |
            | D04  | Combustion engine gas                    | Forbrændingsmotor Gas           |
            | D05  | Steam turbine with condensation / steam  | Dampturbine med kondens/damp    |
            | D06  | Boiler                                   | Kedel                           |
            | D07  | Stirling engine                          | Stirlingmotor                   |
            | D08  | Reserved for later use                   | Reserveret til senere brug      |
            | D09  | Reserved for later use                   | Reserveret til senere brug      |
            | D10  | Fuel cells                               | Brændselsceller                 |
            | D11  | Photovoltaic cells                       | Solceller                       |
            | D12  | Wind turbines                            | Vindmøller                      |
            | D13  | Hydroelectric power                      | Vandkraft                       |
            | D14  | Wave power                               | Bølgekraft                      |
            | D15  | Reserved for later use                   | Reserveret til senere brug      |
            | D16  | Reserved for later use                   | Reserveret til senere brug      |
            | D17  | Dispatchable wind turbines               | Regulerbare Vindmøller          |
            | D18  | Reserved for later use                   | Reserveret til senere brug      |
            | D19  | Combustion engine – diesel               | Forbrændingsmotor Dieselmotor   |
            | D20  | Combustion engine - bio                  | Bioforbrændingsmotor            |
            | D99  | Unknown technology                       | Ukendt teknologi                |
          type: string
          pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D99)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        meterMeterNumber:
          description: Meter number.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "110048"
          x-nestedObjectName: Meter
          nullable: true
        meterMeterReadingType:
          description: |-
            Meter Reading Type.
            | Code | Description   | DanishTranslation |
            | ---- | ------------- | ----------------- |
            | D01  | Accumulated   | Akkumulerende     |
            | D02  | Balanced      | Salderende        |
          type: string
          pattern: ^(D01|D02)$
          minLength: 3
          maxLength: 3
          example: D01
          x-nestedObjectName: Meter
          nullable: true
        meterNumberOfDigits:
          description: Number of digits.
          type: string
          pattern: ^.*$
          minLength: 0
          maxLength: 25
          example: "8"
          x-nestedObjectName: Meter
          nullable: true
        meterUnitType:
          description: |-
            Indicates unit Type.
            | Code  | Description              | DanishTranslation               |
            | ----  | ------------------------ | --------------------------------|
            | AMP   | Ampere                   | Ampere                          |
            | K3    | kVArh                    | KiloVolt-Ampere reactive hour   |
            | KWH   | kWh                      | Kilowatt-hour                   |
            | KWT   | kW                       | Kilowatt                        |
            | MAW   | MW                       | Megawatt                        |
            | MWH   | MWh                      | Megawatt-hour                   |
            | TNE   | Tonne                    | metric ton                      |
            | Z03   | MVAr                     | MegaVolt-Ampere reactive power  |
            | Z14   | Danish Tariff code       | KT Tarifkode                    |
            | H87   | STK                      | Antal styk                      |
          type: string
          pattern: ^(AMP|K3|KWH|KWT|MAW|MWH|TNE|Z03|Z14|H87)$
          minLength: 2
          maxLength: 3
          example: AMP
          x-nestedObjectName: Meter
          nullable: true
        meterConversionFactor:
          description: Conversion factor.
          type: number
          format: decimal
          example: 1.02
          x-nestedObjectName: Meter
          nullable: true
        disconnectionType:
          description: |-
            Can a Metering Point be disconnected automatically from system.
            | Code | Description          | DanishTranslation      |
            | ---- | -------------------- | ---------------------- |
            | D01  | Remote disconnection | Fjern afbrydelig       |
            | D02  | Manual disconnection | Manual afbrydelig      |
          type: string
          pattern: ^(D01|D02)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true

    SearchTechnicalMeteringPointsResponse:
      description: Search technical metering points response
      title: SearchTechnicalMeteringPointsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - totalRecords
        - items
      properties:
        totalRecords:
          description: Total records
          type: integer
          format: int32
          maximum: 2147483647
          minimum: 0
        items:
          type: array
          description: Items
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/SearchTechnicalMeteringPointItem"
    SearchTechnicalMeteringPointItem:
      description: Search technical metering point item
      title: SearchTechnicalMeteringPointItem
      type: object
      nullable: false
      additionalProperties: false
      required:
        - meteringPointNumber
        - validFrom
        - supplyType
        - typeOfMeteringPoint
        - subTypeOfMeteringPoint
        - readingFrequency
        - connectionStatus
        - connectionPointNumber
        - unitType
      properties:
        meteringPointNumber:
          description: Electricity Metering Point Number.
          type: string
          pattern: ^.*$
          minLength: 1
          maxLength: 50
          example: "571313190000020132"
        validFrom:
          description: Metering point version validity start
          type: string
          format: date-time
          example: "2019-11-14T22:00:00.000Z"
        supplyType:
          maxLength: 11
          example: Heating
          type: string
          description: System name of supply type.
          pattern: ^(Electricity|Heating|Water)$
        typeOfMeteringPoint:
          description: |-
            Type of measuring point, e.g. production, consumption.
            | Code | Description                         | DanishTranslation                   |
            | ---- | ----------------------------------- | ----------------------------------- |
            | D01  | VE production                       | VE produktion                       |
            | D02  | Analysis                            | Analysemålepunkt                    |
            | D03  | Not used                            | Anvendes ikke                       |
            | D04  | Surplus production group 6          | Overskudsproduktion gruppe 6        |
            | D05  | Net production                      | Nettoproduktion                     |
            | D06  | Supply to grid                      | Leveret til net                     |
            | D07  | Consumption from grid               | Forbrugt fra net                    |
            | D08  | Whole sale services / information   | Afregningsgrundlag/Information      |
            | D09  | Own production                      | Egenproduktion                      |
            | D10  | Net from grid                       | Netto fra net                       |
            | D11  | Net to grid                         | Netto til net                       |
            | D12  | Total consumption                   | Brutto forbrug                      |
            | D13  | Net loss correction                 | Nettabskorrektion                   |
            | D14  | Electrical heating                  | Elvarme                             |
            | D15  | Net consumption                     | Nettoforbrug                        |
            | D16  | Reserved for later use              | Reserveret til senere brug          |
            | D17  | Other consumption                   | Øvrigt forbrug                      |
            | D18  | Other production                    | Øvrig produktion                    |
            | D19  | Reserved for later use              | Reserveret til senere brug          |
            | D20  | Exchange - Reactive energy          | Udveksling – Reaktiv energi         |
            | D21  | Reserved for later use              | Reserveret til senere brug          |
            | D22  | Reserved for later use              | Reserveret til senere brug          |
            | D23  | Reserved for later use              | Reserveret til senere brug          |
            | D24  | Reserved for later use              | Reserveret til senere brug          |
            | D25  | Reserved for later use              | Reserveret til senere brug          |
            | D26  | Reserved for later use              | Reserveret til senere brug          |
            | D27  | Reserved for later use              | Reserveret til senere brug          |
            | D28  | Reserved for later use              | Reserveret til senere brug          |
            | D29  | Reserved for later use              | Reserveret til senere brug          |
            | D30  | Reserved for later use              | Reserveret til senere brug          |
            | D99  | Internal use                        | Intern brug                         |
            | E17  | Consumption                         | Forbrug                             |
            | E18  | Production                          | Produktion                          |
            | E20  | Exchange                            | Udveksling                          |
          type: string
          minLength: 3
          maxLength: 3
          pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D21|D22|D23|D24|D25|D26|D27|D28|D29|D30|D99|E17|E18|E20)$
          example: E17
          nullable: true
        subTypeOfMeteringPoint:
          description: |-
            Sub-type of Metering Point.
            | Code | Description | DanishTranslation |
            | ---- | ----------- | ----------------- |
            | D01  | Physical    | Fysisk            |
            | D02  | Virtual     | Virtuel           |
            | D03  | Calculated  | Beregnet          |
          type: string
          pattern: ^(D01|D02|D03)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        readingFrequency:
          type: string
          description: |-
            Reading Frequency of Technical Metering Point.

            | CodeListName                                        | DisplayName    | Code | Name   | Translation |
            |---------------------------------------------------  |--------------- |------|--------|-------------|
            | System-TechnicalMeteringPoint-ReadingFrequency      | PT5M           | PT5M | 5 min  | 5 mins      |
            | System-TechnicalMeteringPoint-ReadingFrequency      | PT1H           | PT1H | Time   | Hour        |
            | System-TechnicalMeteringPoint-ReadingFrequency      | PT15M          | PT15M| Kvarter| 15 mins     |
            | System-TechnicalMeteringPoint-ReadingFrequency      | P1D            | P1D  | Dag    | Day         |

          minLength: 3
          maxLength: 5
          pattern: "^(PT1H|PT5M|PT15M|P1D)$"
          example: PT1H
          nullable: true
        connectionStatus:
          description: |-
            Connection status of Metering Point also known in DH as PhysicalStatusOfMeteringPoint.
            | Code | Description   | DanishTranslation |
            | ---- | ------------- | ----------------- |
            | D01  | Not used      | Anvendes ikke     |
            | D02  | Closed down   | Nedlæg            |
            | D03  | New           | Ny                |
            | E22  | Connected     | Tilsluttet        |
          type: string
          pattern: ^(D01|D02|D03|E22)$
          minLength: 3
          maxLength: 3
          example: D01
          nullable: true
        unitType:
          description: |-
            Indicates unit Type.
            | Code  | Description              | DanishTranslation               |
            | ----  | ------------------------ | --------------------------------|
            | AMP   | Ampere                   | Ampere                          |
            | K3    | kVArh                    | KiloVolt-Ampere reactive hour   |
            | KWH   | kWh                      | Kilowatt-hour                   |
            | KWT   | kW                       | Kilowatt                        |
            | MAW   | MW                       | Megawatt                        |
            | MWH   | MWh                      | Megawatt-hour                   |
            | TNE   | Tonne                    | metric ton                      |
            | Z03   | MVAr                     | MegaVolt-Ampere reactive power  |
            | Z14   | Danish Tariff code       | KT Tarifkode                    |
            | H87   | STK                      | Antal styk                      |
          type: string
          pattern: ^(AMP|K3|KWH|KWT|MAW|MWH|TNE|Z03|Z14|H87)$
          minLength: 2
          maxLength: 3
          example: AMP
          nullable: true
        connectionPointNumber:
          description: The ConnectionPoint Number.
          type: string
          maxLength: 64
          pattern: "^.*$"
          nullable: true

    SearchConnectionPointsRequest:
      description: Search connection points request
      title: SearchConnectionPointsRequest
      type: object
      additionalProperties: false
      required:
        - limit
        - offset
      properties:
        offset:
          description: Results offset
          type: integer
          format: int32
          maximum: 10000000
          minimum: 0
        limit:
          description: Results limit
          type: integer
          format: int32
          maximum: 1000
          minimum: 0
        sortOrder:
          description: Sorting order
          type: string
          nullable: true
          pattern: "^(ASC|DESC)$"
          maxLength: 16
        sortBy:
          description: Sorting property
          type: string
          nullable: true
          maxLength: 100
          pattern: "^(connectionPointNumber|supplyType|temporary|connectionStatus|connectionPointCategoryValue|installationTypeValue|netSettlementGroup|consumerCategory)$"
        searchConnectionPointFilters:
          description: Filters
          type: object
          nullable: true
          additionalProperties: false
          properties:
            connectionPointNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            supplyType:
              $ref: "#/components/schemas/SearchTextFilter"
            installationNumber:
              $ref: "#/components/schemas/SearchTextFilter"
            temporary:
              $ref: "#/components/schemas/SearchBoolFilter"
            connectionStatus:
              $ref: "#/components/schemas/SearchTextFilter"
            connectionPointCategoryValue:
              $ref: "#/components/schemas/SearchTextFilter"
            installationTypeValue:
              $ref: "#/components/schemas/SearchTextFilter"
            netSettlementGroup:
              $ref: "#/components/schemas/SearchTextFilter"
            consumerCategory:
              $ref: "#/components/schemas/SearchTextFilter"
            gridAreaId:
              $ref: "#/components/schemas/SearchTextFilter"
            decommissioned:
              $ref: "#/components/schemas/SearchDateFilter"

    SearchConnectionPointsResponse:
      description: Search connection points response
      title: SearchConnectionPointsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - totalRecords
        - items
      properties:
        totalRecords:
          description: Total records
          type: integer
          format: int32
          maximum: 2147483647
          minimum: 0
        items:
          type: array
          description: Array of connection points
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/SearchConnectionPointItem"

    SearchConnectionPointItem:
      description: Search connection point item
      title: SearchConnectionPointItem
      type: object
      nullable: false
      required:
        - connectionPointId
        - connectionPointNumber
        - supplyType
        - installationNumber
        - description
        - temporary
        - connectionStatus
        - connectionPointCategoryValue
        - installationTypeValue
        - netSettlementGroup
        - consumerCategory
        - gridAreaId
        - decommissioned
      properties:
        connectionPointId:
          description: Connection Point Entity Id.
          type: string
          maxLength: 50
          pattern: "^.*$"
        connectionPointNumber:
          description: The ConnectionPoint Number.
          type: string
          maxLength: 64
          pattern: "^.*$"
        supplyType:
          description: List of possible supply types.
          type: string
          maxLength: 50
          pattern: "^.*$"
        installationNumber:
          description: Installation number.
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
        description:
          description: Description field for a connection point.
          type: string
          maxLength: 1000
          pattern: "^.*$"
          nullable: true
        temporary:
          description: Set to true, if the Connection Point is temporary.
          type: boolean
          nullable: true
        connectionStatus:
          description: Connection status on the connection point.
          type: string
          example: Tilsluttet
          pattern: ""
          maxLength: 100
          nullable: true
        connectionPointCategoryValue:
          description: Categorization of a Connection Point.
          type: string
          example: STD-Villa
          pattern: ""
          maxLength: 100
          nullable: true
        installationTypeValue:
          description: Defines type of Installation.
          type: string
          example: UDV-Udvidet husholdning
          pattern: ""
          maxLength: 100
          nullable: true
        netSettlementGroup:
          description: Net settlement group.
          type: string
          example: 1 - Group 1
          pattern: ""
          maxLength: 100
          nullable: true
        consumerCategory:
          description: Consumer category.
          type: string
          example: 447-Offentlig administration
          pattern: ""
          maxLength: 100
          nullable: true
        gridAreaId:
          description: Grid area id.
          type: string
          maxLength: 25
          pattern: "^.*$"
          nullable: true
        decommissioned:
          description: Decommissioned date.
          type: string
          format: date-time
          nullable: true

    GetConnectionPointDetailsResponse:
      description: Get connection point details response
      title: GetConnectionPointDetailsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - connectionPointId
        - versions
      properties:
        connectionPointId:
          description: Connection Point Entity Id.
          type: string
          maxLength: 50
          pattern: "^.*$"
        versions:
          type: array
          description: Array of connection point versions
          nullable: false
          maxItems: 1024
          items:
            $ref: "#/components/schemas/GetConnectionPointDetailsResponseVersion"

    GetConnectionPointDetailsResponseVersion:
      description: Get connection point details response
      title: GetConnectionPointDetailsResponse
      type: object
      nullable: false
      additionalProperties: false
      required:
        - connectionPointNumber
        - validFrom
        - validTo
        - supplyType
        - installationNumber
        - description
        - temporary
        - connectionStatus
        - connectionPointCategoryValue
        - installationTypeValue
        - netSettlementGroup
        - consumerCategory
        - gridAreaId
        - decommissioned
      properties:
        connectionPointNumber:
          description: The ConnectionPoint Number.
          type: string
          maxLength: 64
          pattern: "^.*$"
        validFrom:
          description: Connection point version valid from.
          type: string
          format: date-time
          example: "2019-11-14T23:00:00.000Z"
        validTo:
          description: Connection point version valid to.
          type: string
          format: date-time
          example: "2019-11-14T23:00:00.000Z"
        supplyType:
          description: List of possible supply types.
          type: string
          maxLength: 50
          pattern: "^.*$"
        installationNumber:
          description: Installation number.
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
        description:
          description: Description field for a connection point.
          type: string
          maxLength: 1000
          pattern: "^.*$"
          nullable: true
        temporary:
          description: Set to true, if the Connection Point is temporary.
          type: boolean
          nullable: true
        connectionStatus:
          description: Connection status on the connection point.
          type: string
          example: Tilsluttet
          pattern: ""
          maxLength: 100
          nullable: true
        connectionPointCategoryValue:
          description: Categorization of a Connection Point.
          type: string
          example: STD-Villa
          pattern: ""
          maxLength: 100
          nullable: true
        installationTypeValue:
          description: Defines type of Installation.
          type: string
          example: UDV-Udvidet husholdning
          pattern: ""
          maxLength: 100
          nullable: true
        netSettlementGroup:
          description: Net settlement group.
          type: string
          example: 1 - Group 1
          pattern: ""
          maxLength: 100
          nullable: true
        consumerCategory:
          description: Consumer category.
          type: string
          example: 447-Offentlig administration
          pattern: ""
          maxLength: 100
          nullable: true
        gridAreaId:
          description: Grid area id.
          type: string
          maxLength: 25
          pattern: "^.*$"
          nullable: true
        decommissioned:
          description: Decommissioned date.
          type: string
          format: date-time
          nullable: true

  responses:
    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ValidationProblemDetails"
          examples:
            BadRequestExample:
              value:
                type: "https://errors.kmdelements.com/400"
                title: Bad Request
                status: 400
                detail: "Invalid request"
                instance: "/resources/1"
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            UnauthorizedExample:
              value:
                type: "https://errors.kmdelements.com/401"
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: "/resources/1"
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ForbiddenExample:
              value:
                type: "https://errors.kmdelements.com/403"
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: "/resources/1"
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/404"
                title: Not Found
                status: 404
                detail: Not Found
                instance: "/resources/1"
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          $ref: "#/components/headers/RetryAfter"
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            TooManyRequestsExample:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 429
                detail: Rate limit is exceeded.
                instance: "/resources/1"
    "499":
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ClientClosedRequestExample:
              value:
                type: "https://errors.kmdelements.com/499"
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: "/resources/1"
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            InternalServerErrorExample:
              value:
                type: "https://errors.kmdelements.com/500"
                title: Internal Server Error
                status: 500
                detail: "body.0.age: Value `Not Int` does not match format `int32`"
                instance: "/resources/1"
    "502":
      description: 502 Bad Gateway.
    "503":
      description: 503 Service Unavailable.
    "504":
      description: 504 Gateway Timeout.

  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
      type: http
      scheme: bearer
      bearerFormat: JWT
