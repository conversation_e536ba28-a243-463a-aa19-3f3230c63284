type: object
description: Model used to get Technical Metering Point Default Value Set details.
additionalProperties: false
required:
  - name
  - supplyType
  - typeOfMeteringPoint
properties:
  id:
    description: Technical Metering Point Default Value Set identifier.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  supplyType:
    $ref: '../../SupplyType.yaml'
    description: Supply type of the default value set.
  name:
    description: |-
      DK: Navn.
      Name of Default Value Set (typically from MeteringComponent value list).
    allOf:
      - $ref: '../../DataTypes/ShortString.yaml'
  typeOfMeteringPoint:
    description: Type of metering point value list reference, can refer 3 different VLs depending on supply type.
    allOf:
      - $ref: '../../DataTypes/Guid.yaml'
  subTypeOfMeteringPoint:
    type: string
    description: |-
      Must be specified for all metering points. Specifies the nature of the metering point.
      - A physical metering point is a metering point with a physical meter.
      - A virtual metering point is a metering point where the amount of energy is calculated by the utility company.

      | CodeListName                                        | DisplayName        | Code | Name    | Translation |
      |---------------------------------------------------  |------------------- |------|---------|-------------|
      | System-TechnicalMeteringPoint-SubTypeOfMeteringPoint| D01 – Fysisk       | D01  | Fysisk  | Physical    |
      | System-TechnicalMeteringPoint-SubTypeOfMeteringPoint| D02 – Virtuel      | D02  | Virtuel | Virtual     |
    nullable: true
    minLength: 3
    maxLength: 3
    pattern: "^(D01|D02)$"
    example: D02
  meterReadingOccurrence:
    type: string
    description: |-
      Reading Frequency of Technical Metering Point.

      | CodeListName                                        | DisplayName    | Code | Name   | Translation |
      |---------------------------------------------------  |--------------- |------|--------|-------------|
      | System-TechnicalMeteringPoint-ReadingFrequency      | PT5M           | PT5M | 5 min  | 5 mins      |
      | System-TechnicalMeteringPoint-ReadingFrequency      | PT1H           | PT1H | Time   | Hour        |
      | System-TechnicalMeteringPoint-ReadingFrequency      | PT15M          | PT15M| Kvarter| 15 mins     |
      | System-TechnicalMeteringPoint-ReadingFrequency      | P1D            | P1D  | Dag    | Day         |

    nullable: true
    minLength: 3
    maxLength: 5
    pattern: "^(PT1H|PT5M|PT15M|P1D)$"
    example: PT1H
  unitType:
    type: string
    description: |-
      Unit Type of Technical Metering Point.

      | CodeListName                                | DisplayName    | Code    | Name                       | Translation                    |
      |-------------------------------------------  |--------------- |-------- |--------------------------- |------------------------------- |
      | System-TechnicalMeteringPoint-UnitType      | m3             | M3      | Cubic meter                | Cubic Meter                    |
      | System-TechnicalMeteringPoint-UnitType      | C              | C       | Celsius                    | Celsius                        |
      | System-TechnicalMeteringPoint-UnitType      | kVARh          | KVARH   | kilo volt ampere reactive  | kilo volt ampere reactive      |
      | System-TechnicalMeteringPoint-UnitType      | MVARh          | MVARH   | mega volt ampere reactive  | mega volt ampere reactive      |
      | System-TechnicalMeteringPoint-UnitType      | VARh           | VARH    | volt ampere reactive       | volt ampere reactive           |
      | System-TechnicalMeteringPoint-UnitType      | kW             | KW      | kilo watt                  | kilo Watt                      |
      | System-TechnicalMeteringPoint-UnitType      | kWh            | KWH     | kilowatt-hour              | kilo Watt-hour                 |
      | System-TechnicalMeteringPoint-UnitType      | MWh            | MWH     | megawatt-hour              | mega Watt-hour                 |
      | System-TechnicalMeteringPoint-UnitType      | WH             | WH      | watt-hour                  | Watt-hour                      |

    nullable: true
    minLength: 1
    maxLength: 5
    pattern: "^(M3|C|KVARH|MVARH|VARH|KW|KWH|MWH|WH)$"
    example: AMP
  formulaVersion:
    description: |
      The formula that describes how data for the measuring point's time series is calculated using data
      from the individual formula parameters.
    allOf:
      - $ref: './TechnicalMeteringPointDefaultValueSetFormulaVersion.yaml'
    nullable: true
  rowVersion:
    description: Row version.
    allOf:
      - $ref: '../../DataTypes/RowVersion.yaml'
    nullable: true
