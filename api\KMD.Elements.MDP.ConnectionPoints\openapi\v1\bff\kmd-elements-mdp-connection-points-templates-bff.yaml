openapi: 3.0.3

info:
  title: KMD.Elements.ConnectionPoints.MasterDataTemplates.BFF
  x-maintainers: Team-MD-3
  description: |-
    # KMD Elements - Master Data Templates BFF
    Stability level: V1
    <br/>
    <br/>
    The **KMD Elements Master Data Templates BFF** is part of the KMD Element product.
    <br/>

    ## Capabilities
    The API allows to:
    - Get details of Connection Point Templates, Metering Points Templates and Meter Frame Templates
    ---

  termsOfService: https://www.kmd.net/terms-of-use

  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>

  license:
    name: License
    url: https://www.kmd.net/terms-of-use

  version: "1.7"

servers:
  - url: "https://localhost:5061"
    description: Localhost

security:
  - Jwt: []

tags:
  - name: Configurations
    description: API allows to get data from application tenant configurations.
  - name: ConnectionPointTemplatesBff
    description: Allows to create, get and manage connection point templates.
  - name: DefaultValueSetsBff
    description: Allows to get default value sets.
  - name: MasterDataTemplatesBff
    description: Section with direct access to all Templates.
  - name: MeterFrameTemplatesBff
    description: Allows to create, get and manage meter frame templates.
  - name: MeteringPointsTemplatesBff
    description: Allows to create, get and manage metering points templates.

paths:
  /bff/mdp-connection-points/templates:
    post:
      x-authorization: DefaultValueSets.Read
      tags:
        - MasterDataTemplatesBff
      summary: Gets master data templates
      operationId: getMasterDataTemplates
      description: Gets basic data of templates of all types
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetMasterDataTemplatesRequest"
        required: true
      responses:
        "200":
          description: Returns templates with minimal properties
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetMasterDataTemplatesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/templates/{templateId}:
    delete:
      x-authorization: DefaultValueSets.Write
      tags:
        - MasterDataTemplatesBff
      summary: Deletes master data template by template id
      operationId: deleteMasterDataTemplateById
      description: Deletes master data template based on template Id.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/TemplateId"
      responses:
        "204":
          description: Master data template was deleted successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/mdp-connection-points/templates/meter-frame-templates:
    post:
      x-authorization: DefaultValueSets.Write
      tags:
        - MeterFrameTemplatesBff
      summary: Create meter frame template
      operationId: createMeterFrameTemplate
      description: Creates a new meter frame template.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "./schemas/MasterDataTemplates/MeterFrameTemplate/CreateMeterFrameTemplateRequest.yaml"
        required: true
      responses:
        "201":
          $ref: "#/components/responses/TemplateCreatedResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/templates/meter-frame-templates/{templateId}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - MeterFrameTemplatesBff
      summary: Get meter frame template by template id
      operationId: getMeterFrameTemplateById
      description: Return meter frame template based on template Id.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/TemplateId"
      responses:
        "200":
          $ref: "#/components/responses/MeterFrameTemplateResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    patch:
      x-authorization: DefaultValueSets.Write
      tags:
        - MeterFrameTemplatesBff
      summary: Upsert meter frame template by template id
      operationId: upsertMeterFrameTemplateById
      description: Upsert meter frame template based on template Id.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/TemplateId"
      requestBody:
        description: The model includes characteristics describing meter frame template.
        content:
          application/json:
            schema:
              $ref: "./schemas/MasterDataTemplates/MeterFrameTemplate/PatchMeterFrameTemplateRequest.yaml"
        required: true
      responses:
        "204":
          description: The meter frame template was updated successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/templates/meter-frame-templates-by-supply-type/{supplyTypeInPath}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - MeterFrameTemplatesBff
      summary: Get meter frame templates by supply type
      operationId: getMeterFrameTemplatesBySupplyType
      description: Return meter frame templates based on supply type.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyTypeInPath"
      responses:
        "200":
          $ref: "#/components/responses/MeterFrameTemplatesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/mdp-connection-points/templates/metering-points-templates:
    post:
      x-authorization: DefaultValueSets.Write
      tags:
        - MeteringPointsTemplatesBff
      summary: Create metering points template
      operationId: createMeteringPointsTemplate
      description: Creates a new metering points template.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "./schemas/MasterDataTemplates/MeteringPointsTemplate/CreateMeteringPointsTemplateRequest.yaml"
        required: true
      responses:
        "201":
          $ref: "#/components/responses/TemplateCreatedResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/templates/metering-points-templates/{templateId}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - MeteringPointsTemplatesBff
      summary: Get metering points template by template id
      operationId: getMeteringPointsTemplateById
      description: Get metering points template based on template id.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/TemplateId"
      responses:
        "200":
          $ref: "#/components/responses/MeteringPointsTemplateResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    patch:
      x-authorization: DefaultValueSets.Write
      tags:
        - MeteringPointsTemplatesBff
      summary: Upsert metering points template by template id
      operationId: upsertMeteringPointsTemplateById
      description: Upsert metering points template based on template Id.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/TemplateId"
      requestBody:
        description: The model includes characteristics describing metering points template.
        content:
          application/json:
            schema:
              $ref: "./schemas/MasterDataTemplates/MeteringPointsTemplate/PatchMeteringPointsTemplateRequest.yaml"
        required: true
      responses:
        "204":
          description: The metering points template was updated successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/templates/metering-points-templates-by-supply-type/{supplyTypeInPath}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - MeteringPointsTemplatesBff
      summary: Get metering points templates by supply type
      operationId: getMeteringPointsTemplatesBySupplyType
      description: Return metering points templates based on supply type.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyTypeInPath"
      responses:
        "200":
          $ref: "#/components/responses/MeteringPointsTemplatesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/mdp-connection-points/templates/connection-point-templates:
    post:
      x-authorization: DefaultValueSets.Write
      tags:
        - ConnectionPointTemplatesBff
      summary: Create connection point template
      operationId: createConnectionPointTemplate
      description: Creates a new connection point template.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "./schemas/MasterDataTemplates/ConnectionPointTemplate/CreateConnectionPointTemplateRequest.yaml"
        required: true
      responses:
        "201":
          $ref: "#/components/responses/TemplateCreatedResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/templates/connection-point-templates/{templateId}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - ConnectionPointTemplatesBff
      summary: Get connection point template by template id
      operationId: getConnectionPointTemplateById
      description: Get connection point template based on template id.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/TemplateId"
      responses:
        "200":
          $ref: "#/components/responses/ConnectionPointTemplateResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
    patch:
      x-authorization: DefaultValueSets.Write
      tags:
        - ConnectionPointTemplatesBff
      summary: Upsert connection point template by template id
      operationId: upsertConnectionPointTemplateById
      description: Upsert connection point template based on template Id.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/TemplateId"
      requestBody:
        description: The model includes characteristics describing connection point template.
        content:
          application/json:
            schema:
              $ref: "./schemas/MasterDataTemplates/ConnectionPointTemplate/PatchConnectionPointTemplateRequest.yaml"
        required: true
      responses:
        "204":
          description: The connection point template was updated successfully.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/templates/connection-point-templates-by-supply-type/{supplyTypeInPath}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - ConnectionPointTemplatesBff
      summary: Get connection point templates by supply type
      operationId: getConnectionPointTemplatesBySupplyType
      description: Return connection point templates based on supply type.
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyTypeInPath"
      responses:
        "200":
          $ref: "#/components/responses/ConnectionPointTemplatesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/mdp-connection-points/default-value-sets/metering-points/{supplyTypeInPath}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - DefaultValueSetsBff
      summary: Gets metering points default value sets
      operationId: getMeteringPointsDefaultValueSets
      description: Gets basic data of metering points default value sets
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyTypeInPath"
      responses:
        "200":
          description: Returns metering points default value sets
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetMeteringPointsDefaultValueSetsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/default-value-sets/meter-frames/{supplyTypeInPath}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - DefaultValueSetsBff
      summary: Gets meter frame default value sets
      operationId: getMeterFrameDefaultValueSets
      description: Gets basic data of meter frame default value sets
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyTypeInPath"
      responses:
        "200":
          description: Returns meter frame default value sets
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDefaultValueSetsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/default-value-sets/register-requirements/{supplyTypeInPath}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - DefaultValueSetsBff
      summary: Gets register requirements default value sets
      operationId: getRegisterRequirementsDefaultValueSets
      description: Gets basic data of register requirements default value sets
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyTypeInPath"
      responses:
        "200":
          description: Returns register requirements default value sets
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetRegisterRequirementsDefaultValueSetsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /bff/mdp-connection-points/default-value-sets/connection-points/{supplyTypeInPath}:
    get:
      x-authorization: DefaultValueSets.Read
      tags:
        - DefaultValueSetsBff
      summary: Gets connection point default value sets
      operationId: getConnectionPointDefaultValueSets
      description: Gets basic data of connection point default value sets
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyTypeInPath"
        - $ref: "#/components/parameters/Search"
      responses:
        "200":
          description: Returns connection point default value sets
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDefaultValueSetsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /bff/configurations/supply-types:
    get:
      tags:
        - Configurations
      summary: Returns list of available supply types.
      description: Retrieves list of available supply types from system management, basing on current tenant.
      operationId: getAvailableSupplyTypes
      x-authorization:
      responses:
        "200":
          $ref: "#/components/responses/GetSupplyTypesResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      security:
        - Jwt: []

components:
  headers:
    RetryAfter:
      description: Number of seconds until you should try again.
      schema:
        type: integer
        format: int32
        minimum: 1
        maximum: 2678400
      required: true
      example: 3600
    Location:
      description: A relative url to newly created resource.
      schema:
        type: string
        format: uri
        maxLength: 256
      required: true
  schemas:
    #-------------------------------
    # Reusable simple types
    #-------------------------------
    GetMasterDataTemplatesRequest:
      type: object
      description: Request model to get master data templates.
      additionalProperties: false
      properties:
        templateType:
          type: integer
          format: int32
          minimum: 1
          maximum: 3
          description: Type of template
          x-enumNames:
            - ConnectionPoint
            - MeteringPoint
            - MeterFrame
          enum:
            - 1
            - 2
            - 3
          nullable: true
          oneOf:
            - $ref: "./schemas/TemplateType.yaml"
        name:
          $ref: "./schemas/DataTypes/ShortStringNullable.yaml"
        supplyType:
          type: integer
          format: int32
          minimum: 1
          maximum: 3
          description: Possible supply types "Electricity", "Water", "Heating"
          x-enumNames:
            - Electricity
            - Water
            - Heating
          enum:
            - 1
            - 2
            - 3
          nullable: true
          oneOf:
            - $ref: "./schemas/SupplyType.yaml"

    GetMasterDataTemplatesResponse:
      type: array
      maxItems: 1000
      description: Response model to get master data templates.
      additionalProperties: false
      items:
        $ref: "#/components/schemas/MasterDataTemplateDetails"

    MasterDataTemplateDetails:
      type: object
      description: Master data template model
      additionalProperties: false
      properties:
        templateId:
          $ref: "./schemas/DataTypes/Guid.yaml"
        templateType:
          $ref: "./schemas/TemplateType.yaml"
        name:
          $ref: "./schemas/DataTypes/ShortString.yaml"
        supplyType:
          $ref: "./schemas/SupplyType.yaml"

    CreatedResponseModel:
      title: CreatedResponseModel
      description: Response model for newly created entity.
      type: object
      additionalProperties: false
      properties:
        templateId:
          $ref: "./schemas/DataTypes/Guid.yaml"

    GetMeteringPointsDefaultValueSetsResponse:
      type: array
      maxItems: 1000
      description: Response model to get metering points default value sets.
      additionalProperties: false
      items:
        $ref: "./schemas/MasterDataTemplates/MeteringPointsTemplate/MeteringPointDefaultValueSetDetails.yaml"

    GetRegisterRequirementsDefaultValueSetsResponse:
      type: array
      maxItems: 1000
      description: Response model to get register requirements default value sets.
      additionalProperties: false
      items:
        $ref: "./schemas/MasterDataTemplates/MeterFrameTemplate/RegisterRequirementDefaultValueSetDetails.yaml"

    GetDefaultValueSetsResponse:
      type: array
      maxItems: 1000
      description: Response model to get default value sets.
      additionalProperties: false
      items:
        $ref: "./schemas/MasterDataTemplates/DefaultValueSet.yaml"

    GetMeterFrameTemplatesDetailsModel:
      type: object
      description: Meter frame template details.
      additionalProperties: false
      properties:
        meterFrameTemplatesDetails:
          type: array
          maxItems: 200
          items:
            $ref: "./schemas/MasterDataTemplates/MeterFrameTemplate/MeterFrameTemplateDetails.yaml"
          description: List of meter frame templates with details.

    GetMeteringPointsTemplatesDetailsModel:
      type: object
      description: Metering points template details.
      additionalProperties: false
      properties:
        meteringPointsTemplatesDetails:
          type: array
          maxItems: 200
          items:
            $ref: "./schemas/MasterDataTemplates/MeteringPointsTemplate/MeteringPointsTemplateDetails.yaml"
          description: List of metering points templates with details.

    GetConnectionPointTemplatesDetailsModel:
      type: object
      description: Connection point template details.
      additionalProperties: false
      properties:
        connectionPointTemplatesDetails:
          type: array
          maxItems: 200
          items:
            $ref: "./schemas/MasterDataTemplates/ConnectionPointTemplate/ConnectionPointTemplateDetails.yaml"
          description: List of connection point templates with details.

    SupplyTypesResult:
      description: List of available supply types
      type: array
      additionalProperties: false
      items:
        $ref: "./schemas/SupplyType.yaml"
      maxItems: 3

    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          maxLength: 50
          nullable: true
          format: uri
          example: "https://errors.kmdelements.com/500"
        title:
          description: "A short, human-readable summary of the problem type."
          type: string
          nullable: true
          maxLength: 100
          pattern: "^.*$"
          example: "Error short description"
        status:
          description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
          type: integer
          format: int32
          nullable: true
          minimum: 400
          maximum: 599
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          nullable: true
          pattern: "^.*$"
          example: "Description what exactly happened"
          maxLength: 256
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          nullable: true
          format: uri
          example: "/resources/1"
          maxLength: 256

  parameters:
    EsMessageIdInHeader:
      $ref: "./parameters/EsMessageIdInHeader.yaml"
    EsCorrelationIdInHeader:
      $ref: "./parameters/EsCorrelationIdInHeader.yaml"
    SupplyTypeInPath:
      $ref: "./parameters/SupplyTypeCodeInPath.yaml"
    TemplateId:
      name: templateId
      description: Unique id of a master data template
      in: path
      required: true
      schema:
        $ref: "./schemas/DataTypes/Guid.yaml"
      example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
    Search:
      $ref: "./parameters/Search.yaml"

  responses:
    "MeterFrameTemplateResponse":
      description: Contains Get meter frame template response object.
      content:
        application/json:
          schema:
            $ref: "./schemas/MasterDataTemplates/MeterFrameTemplate/MeterFrameTemplateDetails.yaml"
    "MeterFrameTemplatesResponse":
      description: Contains Get meter frame templates response object.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/GetMeterFrameTemplatesDetailsModel"
    "MeteringPointsTemplateResponse":
      description: Contains get metering points template by id response object.
      content:
        application/json:
          schema:
            $ref: "./schemas/MasterDataTemplates/MeteringPointsTemplate/MeteringPointsTemplateDetails.yaml"
    "MeteringPointsTemplatesResponse":
      description: Contains Get metering points templates response object.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/GetMeteringPointsTemplatesDetailsModel"
    "ConnectionPointTemplateResponse":
      description: Contains get connection point template by id response object.
      content:
        application/json:
          schema:
            $ref: "./schemas/MasterDataTemplates/ConnectionPointTemplate/ConnectionPointTemplateDetails.yaml"
    "ConnectionPointTemplatesResponse":
      description: Contains Get connection point templates response object.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/GetConnectionPointTemplatesDetailsModel"
    "TemplateCreatedResponse":
      description: New template was created.
      headers:
        Location:
          $ref: "#/components/headers/Location"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/CreatedResponseModel"
    "GetSupplyTypesResponse":
      description: Supply types available for current tenant.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/SupplyTypesResult"
    "400":
      description: 400 Bad Request."
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            UnauthorizedExample:
              value:
                type: "https://errors.kmdelements.com/401"
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: "/resources/1"
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ForbiddenExample:
              value:
                type: "https://errors.kmdelements.com/403"
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: "/resources/1"
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            NotFoundExample:
              value:
                type: "https://errors.kmdelements.com/404"
                title: Not Found
                status: 404
                detail: Not Found
                instance: "/resources/1"
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            InternalServerErrorExample:
              value:
                type: "https://errors.kmdelements.com/500"
                title: Internal Server Error
                status: 500
                detail: "body.0.age: Value `Not Int` does not match format `int32`"
                instance: "/resources/1"
    "503":
      description: 503 Service Unavailable.
      headers:
        Retry-After:
          $ref: "#/components/headers/RetryAfter"
    "504":
      description: 504 Gateway Timeout.

  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
      type: http
      scheme: bearer
      bearerFormat: JWT
