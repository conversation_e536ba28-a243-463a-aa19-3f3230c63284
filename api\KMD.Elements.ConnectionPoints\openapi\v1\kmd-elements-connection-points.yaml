openapi: 3.0.3

info:
  title: KMD.Elements.ConnectionPoints.Api
  x-maintainers: Team-MD-2
  description: KMD Elements - Connection Points Api API
  termsOfService: https://www.kmd.net/terms-of-use
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: License
    url: https://www.kmd.net/terms-of-use
  version: '1.43'

servers:
  - url: "/"
  - url: "/api/connection-points-api"

security:
  - Jwt: [ ]

paths:
  /v1/connection-rights/total-value:
    post:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Calculate Total Connection Rights.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: calculateTotalConnectionRights
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionRightsTotalValueBody'
        required: true
      responses:
        '200':
          description: Successfully calculated.
          content:
            application/json:
              schema:
                description: Connection Rights returned successfully.
                type: array
                maxItems: 1000
                items:
                  $ref: '#/components/schemas/ConnectionRightsTotalValueModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-rights/command/import:
    post:
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Migrations.Write
      tags:
        - ConnectionRights
      summary: Imports Connection Rights.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: importConnectionRights
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ImportConnectionRightsModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-rights:
    get:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Fetches Connection Rights list by meter frame
      description: |-
        ### Result
        Returns top connection rights according to query parameters.
      operationId: getCollectionOfConnectionRights
      parameters:
        - name: meterFrameId
          in: query
          description: Id of the Meter Frame.
          required: true
          schema:
            $ref: '#/components/schemas/GuidField'
          example: 4d14817c-b622-493b-a515-284c0872d15d
        - name: installationFormId
          in: query
          description: Id of installation form.
          required: false
          schema:
            $ref: '#/components/schemas/GuidFieldNullable'
          example: 4d14817c-b622-493b-a515-284c0872d15d
        - name: onlyWithoutInvoice
          in: query
          description: Should return only connection rights without invoice.
          required: false
          schema:
            $ref: '#/components/schemas/BooleanFieldNullable'
          example: true
      responses:
        '200':
          description: Connection Rights returned successfully.
          content:
            application/json:
              schema:
                description: Connection Rights returned successfully.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/ConnectionRightModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Create Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: createConnectionRight
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConnectionRightModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-rights/{id}:
    put:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Update Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: updateConnectionRight
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
        - $ref: '#/components/parameters/Id'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConnectionRightModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Allows to delete Connection Rights.
      description: Deletes Connection Right.
      operationId: deleteConnectionRight
      parameters:
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    get:
      x-authorization: ConnectionRights.Read
      tags:
        - ConnectionRights
      summary: Gets specific Connection Right.
      operationId: getConnectionRightById
      description: Returns specific Connection Right by technical GUID identifier.
      parameters:
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Connection Right.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionRightModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-rights/commands/stop:
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Stop Connection Right.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: stopConnectionRight
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StopConnectionRightModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-rights/commands/transfer:
    post:
      x-authorization: ConnectionRights.Write
      tags:
        - ConnectionRights
      summary: Transfers one connection right into many, many into one or one to one. Transfer deactives (sets valid to date) on transfered connection right.
      operationId: transferConnectionRights
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      description: |-
        Transfers connection rights.
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionRightTransfer'
        required: true
      responses:
        '204':
          description: Successfully transfered.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/code-lists:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - CodeList
      summary: Fetches list of Code Lists by Code List Area / Code List Type or all when no parameters are passed.
      description: |-
        ### Result
        Returns top codelist according to query parameters.
      operationId: getCollectionOfCodeLists
      parameters:
        - $ref: '#/components/parameters/CodeListUsageArea'
        - $ref: '#/components/parameters/CodeListTypeKind'
      responses:
        '200':
          description: Code Lists returned successfully.
          content:
            application/json:
              schema:
                description: Code Lists returned successfully.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/CodeListModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/special-agreements/commands/search:
    post:
      deprecated: true
      x-authorization: ConnectionPoints.Read
      tags:
        - SpecialAgreement
      summary:  |-
        [Obsolete] use filtered-search. Fetches list of Special Agreements.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Special Agreements according to passed criteria.
      operationId: searchSpecialAgreements
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchListQueryModel'
        required: true
      responses:
        '200':
          description: Special Agreements returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetSpecialAgreementsSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/special-agreements/commands/filtered-search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - SpecialAgreement
      summary: Fetches list of Special Agreements.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Special Agreements according to passed criteria.
      operationId: specialAgreementsFilteredSearch
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SpecialAgreementsSearchListQueryModel'
        required: true
      responses:
        '200':
          description: Special Agreements returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetSpecialAgreementsSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/special-agreements/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - SpecialAgreement
      summary: Gets specific Special Agreement.
      operationId: getSpecialAgreementById
      description: Returns specific Special Agreement by technical GUID identifier.
      parameters:
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Special Agreement.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSpecialAgreementByIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - SpecialAgreement
      summary: Allows to update Special Agreement.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putSpecialAgreement
      parameters:
        - $ref: '#/components/parameters/Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSpecialAgreementModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - SpecialAgreement
      summary: Allows to delete Special Agreement.
      description: Deletes Special Agreement.
      operationId: deleteSpecialAgreement
      parameters:
        - $ref: '#/components/parameters/Id'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/special-agreements:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - SpecialAgreement
      summary: Creates Special Agreement.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postSpecialAgreement
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSpecialAgreementModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/special-agreements/{specialAgreementId}/notes:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - SpecialAgreementNote
      summary: Gets all notes for specific Special Agreement.
      description: Gets all notes for specific Special Agreement.
      operationId: getAllSpecialAgreementNotes
      parameters:
        - $ref: '#/components/parameters/SpecialAgreementId'
      responses:
        '200':
          description: Special Agreement Notes.
          content:
            application/json:
              schema:
                description: Special Agreement Notes.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/GetSpecialAgreementNoteBySpecialAgreementIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - SpecialAgreementNote
      summary: Creates Special Agreement Note.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postSpecialAgreementNote
      parameters:
        - $ref: '#/components/parameters/SpecialAgreementId'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSpecialAgreementNoteModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/special-agreements/{specialAgreementId}/notes/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - SpecialAgreementNote
      summary: Gets specific Special Agreement Note.
      description: Gets specific Special Agreement Note.
      operationId: getSpecialAgreementNoteById
      parameters:
        - $ref: '#/components/parameters/SpecialAgreementId'
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Special Agreement Note.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSpecialAgreementNoteByIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - SpecialAgreementNote
      summary: Allows to update Note for Special Agreement.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putSpecialAgreementNote
      parameters:
        - $ref: '#/components/parameters/SpecialAgreementId'
        - $ref: '#/components/parameters/Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSpecialAgreementNoteModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - SpecialAgreementNote
      summary: Allows to delete Note from Special Agreement.
      description: Allows to delete Note from Special Agreement.
      operationId: deleteSpecialAgreementNote
      parameters:
        - $ref: '#/components/parameters/SpecialAgreementId'
        - $ref: '#/components/parameters/Id'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Fetches list of Meter Frames.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Meter Frames according to passed criteria.
      operationId: searchMeterFrames
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFramesListSearchQueryModel'
        required: true
      responses:
        '200':
          description: Meter Frames returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResponseOfPostMeterFrameSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-frames/commands/get-meterframe-by-metering-point-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Fetches list of Meter Frames based on related meteringpoint id.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Meter Frames according to passed metering point id.
      operationId: getMeterFrameByMeteringPointId
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetMeterFramesListByMeteringPointIdQueryModel'
        required: true
      responses:
        '200':
          description: Meter Frames returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResponseOfPostMeterFrameSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-frames:
    get:
      deprecated: true
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets paged Meter Frames for passed query parameters.
      description: Gets paged Meter Frames for passed query parameters.
      operationId: getPagedMeterFrames
      parameters:
        - $ref: '#/components/parameters/ConnectionPointIdInQueryGuid'
        - $ref: '#/components/parameters/MeterFrameIdInQueryGuid'
        - $ref: '#/components/parameters/ConnectionPointBusinessId'
        - $ref: '#/components/parameters/SupplyType'
        - $ref: '#/components/parameters/MeterFrameNumber'
        - $ref: '#/components/parameters/SupplyStatus'
        - $ref: '#/components/parameters/SupplyDisconnectType'
        - $ref: '#/components/parameters/CommonReading'
        - $ref: '#/components/parameters/NoMeter'
        - $ref: '#/components/parameters/MeterMissing'
        - $ref: '#/components/parameters/MeterReadingType'
        - $ref: '#/components/parameters/AddressLine1'
        - $ref: '#/components/parameters/AddressLine2'
        - $ref: '#/components/parameters/AddressStatus'
        - $ref: '#/components/parameters/AddressType'
        - $ref: '#/components/parameters/LifeCycleStatus'
        - $ref: '#/components/parameters/DarStatus'
        - $ref: '#/components/parameters/CabinetNumber'
        - $ref: '#/components/parameters/CableNumber'
        - $ref: '#/components/parameters/StationNumber'
        - $ref: '#/components/parameters/TransformerNumber'
        - $ref: '#/components/parameters/ShinNumber'
        - $ref: '#/components/parameters/HeatingBranchLineNumber'
        - $ref: '#/components/parameters/HeatingPlantId'
        - $ref: '#/components/parameters/HeatingPlantName'
        - $ref: '#/components/parameters/HeatingPlantPipeName'
        - $ref: '#/components/parameters/HeatingPlantPipeNumber'
        - $ref: '#/components/parameters/WaterBranchLineNumber'
        - $ref: '#/components/parameters/SectionId'
        - $ref: '#/components/parameters/SectionName'
        - $ref: '#/components/parameters/WaterPlantId'
        - $ref: '#/components/parameters/WaterPlantName'
        - $ref: '#/components/parameters/WaterPlantPipeId'
        - $ref: '#/components/parameters/WaterPlantPipeName'
        - $ref: '#/components/parameters/MainZone'
        - $ref: '#/components/parameters/SuperZone'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
        - $ref: '#/components/parameters/SortBy'
        - $ref: '#/components/parameters/SortOrder'
      responses:
        '200':
          description: Meter Frames collection.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResponseOfGetMeterFrameSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      deprecated: true
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Creates Meter Frame. DEPRECATED. Please use v2 instead.
      description: |-
        DEPRECATED. Please use v2 instead.
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postMeterFrame
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddMeterFrameModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets specific Meter Frame.
      description: Gets specific Meter Frame by technical identifier.
      operationId: getMeterFrameById
      parameters:
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Meter Frame.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMeterFrameModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      deprecated: true
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrame
      summary: Allows to update Meter Frame. DEPRECATED. Please use v2 instead.
      description: |-
        DEPRECATED. Please use v2 instead.
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrame
      parameters:
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMeterFrameModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter/commands/get-meter-by-meter-frame-register-requirement:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - Meter
      summary: Get meter information by Meter frame register requirement id (Electricity).
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: getMeterInformationByMeterFrameRegisterRequirement
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetMeterByMeterFrameRegisterRequirementModel'
        required: true
      responses:
        '200':
          description: Found Meter Information.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMeterInformationModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/commands/get-meter-frame-by-number:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: getMeterFrameModelByNumber
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFrameNumberInSchema'
        required: true
      responses:
        '200':
          description: Found Meter Frame.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMeterFrameModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/commands/history-as-of:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame history entry as of date.
      description: Gets Meter Frame history entry as of date - historical data by temporal table.
      operationId: getMeterFrameAsOfHistory
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HistoryAsOf'
        required: true
      responses:
        '200':
          description: Meter Frame history entry for given parameters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MeterFrameWithValidityPeriodModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/commands/exists-by-number:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Checks if Meter Frame exists.
      description: Check if Meter Frame with exact meter frame number exists within connection point.
      operationId: checkIfMeterFrameExistsByNumber
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFrameExistByNumberModel'
        required: true
      responses:
        '200':
          description: Meter Frame exists.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMeterFrameExistsResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/commands/obtain-meter-frames-by-meter-frames-ids:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: >-
        Get Meter Frames collection by id collection passed in request body.
        Each response model contains metadata with information whether given meter frame was found.
        Maximum 1000 ids in body.
      description: >-
        Get Meter Frames collection by id collection passed in request body.
        Each response model contains metadata with information whether given meter frame was found.
        Maximum 1000 ids in body.
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFrameIdsModel'
        required: true
      operationId: getMeterFramesByMeterFramesIds
      responses:
        '200':
          description: Meter Frames collection with metadata. If meter frame searched by given ID does not exist, sub meterFrame property will be null and isFound marked as false.
          content:
            application/json:
              schema:
                description: Meter Frames collection with metadata. If meter frame searched by given ID does not exist, sub meterFrame property will be null and isFound marked as false.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/MeterFrameWithMetadata'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/commands/get-meter-frames-with-unrelated-branch-line:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - Returns meter frames with meter frame numbers containing passed parameter. Returned MFs have filled out branch lines and no connected meter frame id.
      operationId: getMeterFramesWithUnrelatedBranchLine
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchByMeterFrameNumberPagedModel'
        required: true
      responses:
        '200':
          description: Found Meter Frames.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResponseOfGetMeterFramesBasicModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/commands/get-meter-frames-with-branch-line-related-to-meter-frame:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrame
      summary: Gets Meter Frame.
      description: |-
        ### Remarks
        - Returns meter frames with meter frame numbers containing passed parameter. Returned MFs have filled out branch lines and no connected meter frame id.
      operationId: getMeterFramesWithRelatedBranchLines
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterFrameIdWithSupplyTypeInSchema'
        required: true
      responses:
        '200':
          description: Found Meter Frames.
          content:
            application/json:
              schema:
                type: array
                description: Results array.
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/GetMeterFrameBasicModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/{meterFrameId}/external-equipment-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameExternalEquipmentAssignments
      summary: Gets All External Equipments assigned to the Meter Frame.
      description: Gets All External Equipments assigned to the Meter Frame.
      operationId: getAllMeterFrameExternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Meter Frames collection.
          content:
            application/json:
              schema:
                description: Meter Frames collection.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/ExternalEquipmentAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameExternalEquipmentAssignments
      summary: Creates External Equipment assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postMeterFrameExternalEquipmentAssignment
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExternalEquipmentAssignmentUpsertModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/{meterFrameId}/external-equipment-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameExternalEquipmentAssignments
      summary: Gets specific External Equipment Assignment.
      description: Gets specific External Equipment Assignment.
      operationId: getMeterFrameExternalEquipmentAssignmentById
      parameters:
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Found External Equipment Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalEquipmentAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameExternalEquipmentAssignments
      summary: Allows to update External Equipment Assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrameExternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExternalEquipmentAssignmentUpsertModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/{meterFrameId}/internal-equipment-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameInternalEquipmentAssignments
      summary: Gets All Internal Equipments assigned to the Meter Frame.
      description: Gets All Internal Equipments assigned to the Meter Frame.
      operationId: getAllMeterFrameInternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: All Internal Equipments assigned to the Meter Frame.
          content:
            application/json:
              schema:
                description: All Internal Equipments assigned to the Meter Frame.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/InternalEquipmentAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameInternalEquipmentAssignments
      summary: Creates Internal Equipment assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postMeterFrameInternalEquipmentAssignment
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalEquipmentAssignmentUpsertModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/{meterFrameId}/internal-equipment-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameInternalEquipmentAssignments
      summary: Gets specific Internal Equipment Assignment.
      description: Gets specific Meter Frame's Internal Equipment Assignment by technical identifiers.
      operationId: getMeterFrameInternalEquipmentAssignmentById
      parameters:
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Found Internal Equipment Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalEquipmentAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameInternalEquipmentAssignments
      summary: Allows to update Internal Equipment Assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrameInternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalEquipmentAssignmentUpsertModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/{meterFrameId}/tag-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameTagAssignments
      summary: Gets All Tags assigned to the Meter Frame.
      description: Gets All Tags assigned to the Meter Frame by technical identifier.
      operationId: getAllMeterFrameTagAssignments
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Tags collection.
          content:
            application/json:
              schema:
                description: Tags collection.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/TagAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameTagAssignments
      summary: Creates Tag assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postMeterFrameTagAssignments
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TagAssignmentUpsertModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/{meterFrameId}/tag-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameTagAssignments
      summary: Gets specific Tag Assignment.
      description: Gets specific Tag Assignment by technical identifier.
      operationId: getMeterFrameTagAssignmentById
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Tag Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameTagAssignments
      summary: Allows to update Tag Assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putMeterFrameTagAssignments
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TagAssignmentUpsertModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameTagAssignments
      summary: Allows to delete Tag Assignment from Meter Frame.
      description: Allows to delete Tag Assignment from Meter Frame.
      operationId: deleteMeterFrameTagAssignments
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/internal-equipments/commands/batch-upsert:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipment
      summary: Batch creates / updates internal equipments.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: batchUpsertInternalEquipments
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchUpsertInternalEquipmentsModel'
        required: true
      responses:
        '204':
          description: 'Batch upsert performed successfully.'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/internal-equipments/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipment
      summary: Fetches list of Internal Equipments.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Internal Equipments according to passed criteria.
      operationId: searchInternalEquipment
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchListQueryModel'
        required: true
      responses:
        '200':
          description: Internal Equipments returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetInternalEquipmentSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/internal-equipments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipment
      summary: Fetches list of Internal Equipments by equipment number.
      description: |-
        ### Result
        Returns top 100 internal equipments.
      operationId: searchNonAssignedInternalEquipmentsByEquipmentNumber
      parameters:
        - $ref: '#/components/parameters/EquipmentNumberNullable'
        - $ref: '#/components/parameters/InstallationPossibility'
      responses:
        '200':
          description: Internal Equipments returned successfully.
          content:
            application/json:
              schema:
                description: Internal Equipments.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/GetInternalEquipmentSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    head:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipment
      summary: Checks if Internal Equipment with equipment number exists.
      description: Checks if Internal Equipment with equipment number exists.
      operationId: checkIfInternalEquipmentExistsByEquipmentNumber
      parameters:
        - $ref: '#/components/parameters/EquipmentNumber'
      responses:
        '200':
          description: Equipment exists.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipment
      summary: Creates Internal Equipment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postInternalEquipment
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddInternalEquipmentModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/internal-equipments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipment
      summary: Gets specific Internal Equipment.
      description: Gets specific Internal Equipment.
      operationId: getInternalEquipmentById
      parameters:
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Internal Equipment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetInternalEquipmentByIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipment
      summary: Allows to update Internal Equipment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putInternalEquipment
      parameters:
        - $ref: '#/components/parameters/Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInternalEquipmentModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipment
      summary: Allows to delete Internal Equipment.
      description: Allows to delete Internal Equipment.
      operationId: deleteInternalEquipment
      parameters:
        - $ref: '#/components/parameters/Id'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/internal-equipments/{internalEquipmentId}/notes:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipmentNote
      summary: Gets all notes for specific Internal Equipment.
      description: Gets all notes for specific Internal Equipment by technical identifier.
      operationId: getAllInternalEquipmentNotes
      parameters:
        - $ref: '#/components/parameters/InternalEquipmentId'
      responses:
        '200':
          description: Found Internal Equipment.
          content:
            application/json:
              schema:
                description: Internal Equipments.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/GetInternalEquipmentNoteByIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipmentNote
      summary: Creates Internal Equipment Note.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postInternalEquipmentNote
      parameters:
        - $ref: '#/components/parameters/InternalEquipmentId'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddInternalEquipmentNoteModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/internal-equipments/{internalEquipmentId}/notes/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - InternalEquipmentNote
      summary: Gets specific Internal Equipment Note.
      description: Gets specific Internal Equipment Note by technical identifier.
      operationId: getInternalEquipmentNoteById
      parameters:
        - $ref: '#/components/parameters/InternalEquipmentId'
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Internal Equipment Note.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetInternalEquipmentNoteByIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipmentNote
      summary: Allows to update Note for Internal Equipment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putInternalEquipmentNote
      parameters:
        - $ref: '#/components/parameters/InternalEquipmentId'
        - $ref: '#/components/parameters/Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInternalEquipmentNoteModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - InternalEquipmentNote
      summary: Allows to delete Note from Internal Equipment.
      description: Allows to delete Note from Internal Equipment by technical identifier.
      operationId: deleteInternalEquipmentNote
      parameters:
        - $ref: '#/components/parameters/InternalEquipmentId'
        - $ref: '#/components/parameters/Id'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/commands/search-obsolete:
    post:
      deprecated: true
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Fetches list of Connection Points.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Connection Points according to passed criteria.
      operationId: searchConnectionPointsObsolete
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchListQueryModel'
        required: true
      responses:
        '200':
          description: Connection Points returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetConnectionPointsSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/connection-points/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Fetches list of Connection Points.
      description: |-
        ### Result
        Returns filtered, sorted and paged list of Connection Points according to passed criteria.
      operationId: searchConnectionPoints
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionPointsListSearchQueryModel'
        required: true
      responses:
        '200':
          description: Connection Points returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetConnectionPointsSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/command/import:
    post:
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Migrations.Write
      tags:
        - ConnectionPoint
      summary: Imports Connection Point.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: importConnectionPoint
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ImportConnectionPointModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
  /v1/connection-points:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Gets paged ConnectionPoints for passed query parameters.
      description: Gets paged ConnectionPoints for passed query parameters.
      operationId: getPagedConnectionPoints
      parameters:
        - $ref: '#/components/parameters/ConnectionPointNumberInQueryStringRequired'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: Connection Points collection.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResponseOfGetPagedConnectionPointsMasterDataModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      deprecated: true
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: 'Creates Connection Point. DEPRECATED. Please use v2 instead.'
      description: |-
        DEPRECATED. Please use v2 instead.
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postConnectionPoint
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddConnectionPointModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
  /v1/connection-points/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Gets specific Connection Point.
      description: Gets specific Connection Point by technical identifier.
      operationId: getConnectionPointById
      parameters:
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Connection Point.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionPointModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      deprecated: true
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: 'Allows to update Connection Point. DEPRECATED. Please use v2 instead.'
      description: |-
        DEPRECATED. Please use v2 instead.
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putConnectionPoint
      parameters:
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConnectionPointModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPoint
      summary: Allows to delete Connection Point.
      description: Allows to delete Connection Point.
      operationId: deleteConnectionPoint
      parameters:
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/commands/by-business-id:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Gets specific by Connection Point Number.
      description: Gets specific by Connection Point Number.
      operationId: getConnectionPointByConnectionPointNumber
      parameters:
        - $ref: '#/components/parameters/ConnectionPointNumberInQueryStringRequired'
      responses:
        '200':
          description: Found Connection Point.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionPointModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/commands/history-as-of:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPoint
      summary: Gets Connection Point history entry as of date.
      description: Gets Connection Point history entry as of date.
      operationId: getConnectionPointFromToHistory
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HistoryAsOf'
        required: true
      responses:
        '200':
          description: Connection Point history entry for given parameters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionPointWithValidityPeriodModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/{connectionPointId}/external-equipment-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointExternalEquipmentAssignments
      summary: Gets All External Equipments assigned to the Connection Point.
      description: Gets All External Equipments assigned to the Connection Point.
      operationId: getAllConnectionPointExternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
      responses:
        '200':
          description: Connection Points collection.
          content:
            application/json:
              schema:
                description: Connection Points collection.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/ExternalEquipmentAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointExternalEquipmentAssignments
      summary: Creates External Equipment assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postConnectionPointExternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExternalEquipmentAssignmentUpsertModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/{connectionPointId}/external-equipment-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointExternalEquipmentAssignments
      summary: Gets specific External Equipment Assignment.
      description: Gets specific External Equipment Assignment.
      operationId: getConnectionPointExternalEquipmentAssignmentById
      parameters:
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/ConnectionPointId'
      responses:
        '200':
          description: Found External Equipment Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalEquipmentAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointExternalEquipmentAssignments
      summary: Allows to update External Equipment Assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putConnectionPointExternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExternalEquipmentAssignmentUpsertModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/{connectionPointId}/internal-equipment-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointInternalEquipmentAssignments
      summary: Gets All Internal Equipments assigned to the Connection Point.
      description: Gets All Internal Equipments assigned to the Connection Point.
      operationId: getAllConnectionPointInternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
      responses:
        '200':
          description: Connection Points collection.
          content:
            application/json:
              schema:
                description: Connection Points collection.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/InternalEquipmentAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointInternalEquipmentAssignments
      summary: Creates Internal Equipment assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postConnectionPointInternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalEquipmentAssignmentUpsertModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '422':
          $ref: '#/components/responses/422'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/{connectionPointId}/internal-equipment-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointInternalEquipmentAssignments
      summary: Gets specific Internal Equipment Assignment.
      description: Gets specific Internal Equipment Assignment.
      operationId: getConnectionPointInternalEquipmentAssignmentById
      parameters:
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/ConnectionPointId'
      responses:
        '200':
          description: Found Internal Equipment Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalEquipmentAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointInternalEquipmentAssignments
      summary: Allows to update Internal Equipment Assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putConnectionPointInternalEquipmentAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalEquipmentAssignmentUpsertModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/{connectionPointId}/notes:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointNote
      summary: Gets all notes for specific connection point.
      description: Gets all notes for specific connection point.
      operationId: getAllConnectionPointNotes
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
      responses:
        '200':
          description: Found Internal Equipment.
          content:
            application/json:
              schema:
                description: Found Internal Equipment.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/GetConnectionPointNoteByConnectionPointIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointNote
      summary: Create Connection Point Note.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postConnectionPointNote
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddConnectionPointNoteModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/{connectionPointId}/notes/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointNote
      summary: Gets specific Connection Point Note.
      description: Gets specific Connection Point Note by technical identifier.
      operationId: getConnectionPointNoteById
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Internal Equipment Note.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionPointNoteByIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointNote
      summary: Allows to update Connection Point Note.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putConnectionPointNote
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConnectionPointNoteModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointNote
      summary: Allows to delete Note from Connection Point.
      description: Allows to delete Note from Connection Point.
      operationId: deleteConnectionPointNote
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/{connectionPointId}/tag-assignments:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointTagAssignments
      summary: Gets All Tags assigned to the Connection Point.
      description: Gets All Tags assigned to the Connection Point by technical identifier.
      operationId: getAllConnectionPointTagAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
      responses:
        '200':
          description: Tags collection.
          content:
            application/json:
              schema:
                description: Tags collection.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/TagAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointTagAssignments
      summary: Creates Tag assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postConnectionPointTagAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TagAssignmentUpsertModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/connection-points/{connectionPointId}/tag-assignments/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - ConnectionPointTagAssignments
      summary: Gets specific Tag Assignment.
      description: Gets specific Tag Assignment by technical identifier.
      operationId: getConnectionPointTagAssignmentById
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Tag Assignment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagAssignmentModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointTagAssignments
      summary: Allows to update Tag Assignment.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putConnectionPointTagAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TagAssignmentUpsertModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - ConnectionPointTagAssignments
      summary: Allows to delete Tag Assignment from Connection Point.
      description: Allows to delete Tag Assignment from Connection Point.
      operationId: deleteConnectionPointTagAssignments
      parameters:
        - $ref: '#/components/parameters/ConnectionPointId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/command/import:
    post:
      x-authorization: ConnectionPoints.Write
      x-authorization-1: Migrations.Write
      tags:
        - MeterFrame
      summary: Imports Meter Frames.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: importMeterFrame
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ImportMeterFrameModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
  /v1/meter-frames/{meterFrameId}/notes:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameNote
      summary: Gets all notes for specific Meter Frame.
      description: Gets all notes for specific Meter Frame.
      operationId: getAllMeterFrameNotes
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Found Meter Frame Notes.
          content:
            application/json:
              schema:
                description: Meter Frame Notes.
                type: array
                maxItems: 1000000
                items:
                  $ref: '#/components/schemas/GetMeterFrameNoteByMeterFrameIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameNote
      summary: Create Meter Frame Note.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: createMeterFrameNote
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddMeterFrameNoteModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/{meterFrameId}/notes/{id}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterFrameNote
      summary: Gets specific Meter Frame Note.
      description: Gets specific Meter Frame Note by technical identifier.
      operationId: getMeterFrameNoteById
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/Id'
      responses:
        '200':
          description: Found Meter Frame Note.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMeterFrameNoteByIdModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameNote
      summary: Allows to update Meter Frame Note.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: updateMeterFrameNote
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMeterFrameNoteModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterFrameNote
      summary: Allows to delete Note from Meter Frame.
      description: Allows to delete Note from Meter Frame.
      operationId: deleteMeterFrameNote
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
        - $ref: '#/components/parameters/Id'
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/register-requirements:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirement
      summary: Gets paged Register Requirements for passed query parameters.
      description: Gets paged Register Requirements for passed query parameters.
      operationId: getPagedRegisterRequirements
      parameters:
        - $ref: '#/components/parameters/MeterFrameIdInQueryGuid'
        - $ref: '#/components/parameters/RegisterRequirementIdStartsWithInQueryString'
        - $ref: '#/components/parameters/MeterFrameNumberInQueryString'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: Register Requirements collection.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRegisterRequirementResponseBody'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - RegisterRequirement
      summary: Creates Register Requirement.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: postRegisterRequirement
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddRegisterRequirementModel'
        required: true
      responses:
        '201':
          description: Successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntIdResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/register-requirements/{registerRequirementId}:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirement
      summary: Gets specific Register Requirement.
      description: Gets specific Register Requirement by technical identifier.
      operationId: getRegisterRequirementById
      parameters:
        - $ref: '#/components/parameters/RegisterRequirementIdInPath'
      responses:
        '200':
          description: Found Register Requirement.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterRequirementModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    put:
      x-authorization: ConnectionPoints.Write
      tags:
        - RegisterRequirement
      summary: Allows to update Register Requirement.
      description: |-
        ### Remarks
        - passed model has to meet validation requirements described below.
      operationId: putRegisterRequirement
      parameters:
        - $ref: '#/components/parameters/RegisterRequirementIdInPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRegisterRequirementModel'
        required: true
      responses:
        '204':
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
    delete:
      x-authorization: ConnectionPoints.Write
      tags:
        - RegisterRequirement
      summary: Allows to delete Register Requirement.
      description: Allows to delete Register Requirement by technical identifier.
      operationId: deleteRegisterRequirement
      parameters:
        - $ref: '#/components/parameters/RegisterRequirementIdInPath'
      responses:
        '204':
          description: Successfully deleted.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/register-requirements/commands/calculate-default-valid-from:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - RegisterRequirement
      summary: Calculate default valid from for specific meter frame.
      description: |-
        ### Result
        Returns valid from date.
      operationId: calculateDefaultValidFromForSpecificMeterFrame
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateDefaultValidFromBody'
        required: true
      responses:
        '200':
          description: Default valid from returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateDefaultValidFromResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-frames/{meterFrameId}/meter-input-connections:
    get:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Gets all Meter Input Connections assigned to Meter Frame.
      description: Gets a list of "Meter Input Connection" objects on a Meter Frame.
      operationId: getMeterInputConnectionsByMeterFrameId
      parameters:
        - $ref: '#/components/parameters/MeterFrameId'
      responses:
        '200':
          description: Meter Input Connections.
          content:
            application/json:
              schema:
                description: Meter Input Connections.
                type: array
                maxItems: 1000
                items:
                  $ref: '#/components/schemas/MeterInputConnectionModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/meter-frames/commands/change-meter-on-meter-frame:
    post:
      x-authorization: ConnectionPoints.Write
      tags:
        - MeterInputConnections
      summary: Change Meter on Meter Frame.
      description: Change Meter on Meter Frame.
      operationId: changeMeter
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeMeterInputConnection'
        required: true
      responses:
        '200':
          description: Successfully created/updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChangeMeterOnMeterFrameResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-input-connections/commands/search:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Fetches list of Meter Input Connections.
      description: |-
        ### Result
        Returns filtered and paged list of Meter Input Connections according to passed criteria.
      operationId: searchMeterInputConnections
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeterInputConnectionsListSearchQueryModel'
        required: true
      responses:
        '200':
          description: Meter Input Connections returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedResultOfGetMeterInputConnectionsSearchModel'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-input-connections/commands/validate-meter-on-meter-frame-register-requirements:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Validates meter on meter frame register requirements.
      description: |-
        ### Result
        Returns validation result.
      operationId: validateMeterOnMeterFrameRegisterRequirements
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeMeterInputConnection'
        required: true
      responses:
        '200':
          description: Validation returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateMeterOnMeterFrameResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-input-connections/commands/validate-active-meter-on-meter-frame-register-requirements:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Validates active meter on meter frame register requirements.
      description: Validates active meter on meter frame register requirements.
      operationId: validateActiveMeterOnMeterFrameRegisterRequirements
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameRegisterRequirements'
        required: true
      responses:
        "204":
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-input-connections/commands/validate-active-meter-on-meter-frame-attributes-requirements:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Validates active meter on meter frame attributes requirements.
      description: Validates active meter on meter frame attributes requirements.
      operationId: validateActiveMeterOnMeterFrameAttributesRequirements
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameAttributesRequirements'
        required: true
      responses:
        "204":
          description: Successfully updated.
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-input-connections/commands/validate-meter-on-meter-frame:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Validates meter on meter frame.
      description: |-
        ### Result
        Returns validation result.
      operationId: validateMeterOnMeterFrame
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeMeterInputConnection'
        required: true
      responses:
        '200':
          description: Validation returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateMeterOnMeterFrameResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-input-connections/commands/validate-meter-on-meter-attributes-requirements:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Validates meter on meter attributes requirements.
      description: |-
        ### Result
        Returns validation result.
      operationId: validateMeterOnMeterAttributesRequirements
      parameters:
        - $ref: '#/components/parameters/EsCorrelationIdInHeader'
        - $ref: '#/components/parameters/EsMessageIdInHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateMeterOnMeterAttributesRequirements'
        required: true
      responses:
        '200':
          description: Validation returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateMeterOnMeterAttributesRequirementsResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-input-connections/commands/validate-active-meter-on-meter-frame-connection-type-and-ct-vt:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Validates active meter on meter frame connection type and CT/VT.
      description: Validates active meter on meter frame connection type and CT/VT.
      operationId: validateActiveMeterOnMeterFrameConnectionTypeAndCtVt
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameConnectionTypeAndCtVt'
        required: true
      responses:
        '200':
          description: Validation returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameConnectionTypeAndCtVtValidationResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'

  /v1/meter-input-connections/commands/validate-active-meter-on-meter-frame-full-validation:
    post:
      x-authorization: ConnectionPoints.Read
      tags:
        - MeterInputConnections
      summary: Validates active meter on meter frame register requirements, atribute requirements, connection type and CT/VT.
      description: Validates active meter on meter frame register requirements, atribute requirements, connection type and CT/VT.
      operationId: validateActiveMeterOnMeterFrameFullValidation
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameFullValidation'
        required: true
      responses:
        '200':
          description: Validation returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameFullValidationResult'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '500':
          $ref: '#/components/responses/500'
        '502':
          $ref: '#/components/responses/502'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
components:
  parameters:
    MeterFrameId:
      name: meterFrameId
      in: path
      description: Id of the Meter Frame.
      required: true
      schema:
        $ref: '#/components/schemas/GuidField'
      example: 4d14817c-b622-493b-a515-284c0872d15d
    MeterFrameIdInQueryGuid:
      name: meterFrameId
      in: query
      description: Id of the Meter Frame.
      schema:
        $ref: '#/components/schemas/GuidFieldNullable'
      example: 4d14817c-b622-493b-a515-284c0872d15d
    ConnectionPointId:
      name: connectionPointId
      in: path
      description: Connection Point technical identifier.
      required: true
      schema:
        $ref: '#/components/schemas/GuidField'
      example: 4d14817c-b622-493b-a515-284c0872d15e
    ConnectionPointNumberInQueryStringRequired:
      name: connectionPointNumber
      in: query
      description: Connection Point Number.
      required: true
      schema:
        $ref: '#/components/schemas/ShortStringObsolete'
      example: "20000001"
    ConnectionPointIdInQueryGuid:
      name: connectionPointId
      in: query
      description: Connection Point technical identifier.
      schema:
        $ref: '#/components/schemas/GuidFieldNullable'
      example: 4d14817c-b622-493b-a515-284c0872d15e
    Id:
      name: id
      in: path
      description: Technical identifier.
      required: true
      schema:
        $ref: '#/components/schemas/GuidField'
      example: 4d14817d-b622-493b-a515-284c0872d15e
    RegisterRequirementIdInPath:
      name: registerRequirementId
      in: path
      description: Technical identifier.
      required: true
      schema:
        $ref: '#/components/schemas/Integer'
      example: 1234
    CodeListUsageArea:
      name: codeListUsageArea
      in: query
      description: Defines aggregate root area usage (ex. codelists used in Meter Frames).
      schema:
        $ref: '#/components/schemas/CodeListUsageAreasModel'
      example: 1
    CodeListTypeKind:
      name: codeListTypeKind
      in: query
      description: CodeList enum identifier.
      schema:
        $ref: '#/components/schemas/CodeListTypeKindModel'
      example: 1
    SpecialAgreementId:
      name: specialAgreementId
      in: path
      description: Special Agreement Id.
      required: true
      schema:
        $ref: '#/components/schemas/GuidField'
      example: 4a14817d-b622-493b-a515-284c0872d15e
    InternalEquipmentId:
      name: internalEquipmentId
      in: path
      description: Internal Equipment id.
      required: true
      schema:
        $ref: '#/components/schemas/GuidField'
      example: 4f14817d-b622-493b-a515-284c0872d15e
    SupplyType:
      name: supplyType
      in: query
      description: Type of supply as described by object Supply Types DTO.
      schema:
        description: Type of supply as described by object Supply Types DTO.
        nullable: true
        allOf:
          - $ref: '#/components/schemas/SupplyTypesModel'
      example: 1
    SupplyStatus:
      name: supplyStatus
      in: query
      description: List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
      schema:
        description: List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
        nullable: true
        allOf:
          - $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
      example: 1
    SupplyDisconnectType:
      name: supplyDisconnectType
      in: query
      description: List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation", "Connected", "Unknown".
      schema:
        $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
      example: 1
    AddressStatus:
      name: addressStatus
      in: query
      description: List of possible statuses that a master data address can have "Active", "Inactive".
      schema:
        $ref: '#/components/schemas/AddressStatus'
      example: 1
    AddressType:
      name: addressType
      in: query
      description: List of possible address types that a MasterDataAddressDetails object can hold. Eg. "Address", "AccessAddress".
      schema:
        $ref: '#/components/schemas/AddressType'
      example: 1
    LifeCycleStatus:
      name: lifeCycleStatus
      in: query
      description: List of possible life cycle states that the MDR system can put the address into.
      schema:
        $ref: '#/components/schemas/LifeCycleStatus'
      example: 1
    DarStatus:
      name: darStatus
      in: query
      description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated".
      schema:
        $ref: '#/components/schemas/DarStatus'
      example: 1
    InstallationPossibility:
      name: installationPossibility
      in: query
      description: Installation possibility enum value as described in reference DTO.
      required: true
      schema:
        description: Installation possibility enum value as described in reference DTO.
        nullable: true
        allOf:
          - $ref: '#/components/schemas/InstallationPossibilityModel'
      example: 1
    MeterFrameNumber:
      name: meterFrameNumber
      in: query
      description: Meter frame number.
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: aa123
    ConnectionPointBusinessId:
      name: connectionPointBusinessId
      in: query
      description: Connection point business identifier.
      schema:
        description: Connection point business identifier.
        nullable: true
        allOf:
          - $ref: '#/components/schemas/ShortStringObsoleteNullable'
      example: aa123
    CommonReading:
      name: commonReading
      in: query
      description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
      schema:
        $ref: '#/components/schemas/GuidFieldNullable'
      example: 'f56d5e72-d9f1-4d4d-95ac-ca06079dfc0a'
    NoMeter:
      name: noMeter
      in: query
      description: No meter.
      schema:
        description: No Meter.
        nullable: true
        type: boolean
      example: true
    MeterMissing:
      name: hasMeterMissing
      in: query
      description: Indicates if meter is missing.
      schema:
        description: Indicates if meter is missing.
        nullable: true
        type: boolean
      example: true
    MeterReadingType:
      name: meterReadingType
      in: query
      description: A list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".
      schema:
        $ref: '#/components/schemas/GuidFieldNullable'
      example: 'd988b796-225b-4eef-8ec4-191ee69a3a80'
    AddressLine1:
      name: addressLine1
      in: query
      description: AddressLine1 from Central Address Registry.
      schema:
        $ref: '#/components/schemas/ShortStringObsoleteNullable'
      example: Tagensvej
    AddressLine2:
      name: addressLine2
      in: query
      description: AddressLine2 from Central Address Registry.
      schema:
        $ref: '#/components/schemas/ShortStringObsoleteNullable'
      example: 2200 København, Dennmark
    EquipmentNumber:
      name: equipmentNumber
      in: query
      description: Equipment number.
      required: true
      schema:
        $ref: '#/components/schemas/ShortStringObsolete'
      example: aa123
    EquipmentNumberNullable:
      name: equipmentNumber
      in: query
      description: Equipment number.
      schema:
        $ref: '#/components/schemas/ShortStringObsoleteNullable'
      example: aa123
    CabinetNumber:
      name: cabinetNumber
      in: query
      description: Number of the cable box where the plug is connected. Ex. 123658.
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 123658
    CableNumber:
      name: cableNumber
      in: query
      description: Number on execution in station. Ex. 1, 2, 3 etc.
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 3
    StationNumber:
      name: stationNumber
      in: query
      description: The station from which the branch line is supplied. Ex. 98756.
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 98756
    TransformerNumber:
      name: transformerNumber
      in: query
      description: Transformer number, for several transformers in a station. Ex. 1, 2, 3 etc.
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 2
    ShinNumber:
      name: shinNumber
      in: query
      description: Number on the rail in the locker.
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 15
    HeatingBranchLineNumber:
      name: heatingBranchLineNumber
      in: query
      description: Number on branch line. Ex. 246810.
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 246810
    HeatingPlantId:
      name: heatingPlantId
      in: query
      description: Heating plant Id - Heater "kID".
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 3
    HeatingPlantName:
      name: heatingPlantName
      in: query
      description: Heating plant name.
      required: false
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: Max. 50 characters long string with spaces.
    HeatingPlantPipeName:
      name: heatingPlantPipeName
      in: query
      description: Pipe name / outlet marking.
      required: false
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: Max. 50 characters long string with spaces.
    HeatingPlantPipeNumber:
      name: heatingPlantPipeNumber
      in: query
      description: Outlet marking number.
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 4
    WaterBranchLineNumber:
      name: waterBranchLineNumber
      in: query
      description: Number on branch line. Ex. XF2500.
      required: false
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: XF2500
    SectionId:
      name: sectionId
      in: query
      description: Indicates an area (polygon) in which the water meter frame is located (for checking water balance).
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 2
    SectionName:
      name: sectionName
      in: query
      description: Section name.
      required: false
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: Max. 50 characters long string with spaces.
    WaterPlantId:
      name: waterPlantId
      in: query
      description: Water Plant Id.
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 3
    WaterPlantName:
      name: waterPlantName
      in: query
      description: Water Plant Name.
      required: false
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: Max. 50 characters long string with spaces.
    WaterPlantPipeId:
      name: waterPlantPipeId
      in: query
      description: Water Plant Pipe Id.
      required: false
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 4
    WaterPlantPipeName:
      name: waterPlantPipeName
      in: query
      description: Water Plant Pipe name.
      required: false
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: Max. 50 characters long string with spaces.
    MainZone:
      name: mainZone
      in: query
      description: Indicates an area (higher level than section) = an operating area (polygon).
      required: false
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: Max. 50 characters long string with spaces.
    SuperZone:
      name: superZone
      in: query
      description: Indicates an area <Section> (higher level than MainZone) = e.g. a city (polygon).
      required: false
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: Max. 50 characters long string with spaces.
    PageNumber:
      name: pageNumber
      in: query
      description: Page number for pagination - defaults to 1.
      required: true
      schema:
        $ref: '#/components/schemas/NonNegativeIntegerNullable'
      example: 1
    PageSize:
      name: pageSize
      in: query
      description: Page size for pagination - limited by application if not passed.
      required: true
      schema:
        $ref: '#/components/schemas/NonNegativeIntegerNullable'
      example: 1
    SortBy:
      name: sortBy
      in: query
      description: Name of the property to sort by.
      schema:
        $ref: '#/components/schemas/SortByNullable'
      example: 'connectionPointNumber'
    SortOrder:
      name: sortOrder
      in: query
      description: Direction of sorting. Can only be 'asc' for ascending or 'desc' for descending.
      schema:
        $ref: '#/components/schemas/SortOrderNullable'
      example: 'asc'
    RegisterRequirementIdStartsWithInQueryString:
      name: registerRequirementIdStartsWithInQueryString
      in: query
      description: Starts with technical identifier.
      schema:
        $ref: '#/components/schemas/IntegerNullable'
      example: 1234
    MeterFrameNumberInQueryString:
      name: meterFrameNumber
      in: query
      description: Meter Frame number.
      schema:
        $ref: '#/components/schemas/ShortStringNullable'
      example: '4268761_01'
    EsCorrelationIdInHeader:
      name: es-correlation-id
      description: |
        This is used to "link" messages together. This can be supplied on a request, so
        that the client can correlate a corresponding reply message.
        The server will place the incoming es-correlation-id value as the es-correlation-id
        on the outgoing reply. If not supplied on the request, the es-correlation-id of the
        reply should be set to the value of the es-message-id that was used on the request, if present.
        Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
      in: header
      schema:
        $ref: '#/components/schemas/GuidField'
      required: false
      example: '8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d'
    EsMessageIdInHeader:
      name: es-message-id
      description: Unique message ID. The same message id is used when resending the
        message.
      in: header
      schema:
        $ref: '#/components/schemas/GuidField'
      example: '3773907e-45a2-11ee-be56-0242ac120003'
      required: false
  schemas:
    MeterFrameNumberInSchema:
      title: MeterFrameNumberInSchema
      type: object
      description: |-
        Meter Frame Number property.
      additionalProperties: false
      required:
        - meterFrameNumber
      properties:
        meterFrameNumber:
          description: Meter Frame number.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/ShortString'
          example: 'aa123'

    GetMeterByMeterFrameRegisterRequirementModel:
      title: GetMeterByMeterFrameRegisterRequirementModel
      type: object
      description: |-
        Meter Frame Id and Meter register requirement id.
      additionalProperties: false
      required:
        - meterFrameId
        - registerRequirementId
      properties:
        meterFrameId:
          description: Meter Frame Id.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/GuidId'
        registerRequirementId:
          description: Register Requirement Id.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/Integer'

    SearchByMeterFrameNumberPagedModel:
      title: SearchByMeterFrameNumberPagedModel
      type: object
      description: |-
        Meter Frame Number property.
      additionalProperties: false
      required:
        - meterFrameNumber
        - supplyType
      properties:
        meterFrameNumber:
          description: Meter Frame number.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/ShortString'
          example: 'aa123'
        supplyType:
          description: Type of supply as described by object Supply Types DTO.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
          example: 1
        pageNumber:
          description: Page number for pagination - defaults to 1.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/NonNegativeIntegerNullable'
          example: 1
        pageSize:
          description: Page size for pagination - limited by application if not passed.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/NonNegativeIntegerNullable'
          example: 1

    MeterFrameIdWithSupplyTypeInSchema:
      title: MeterFrameIdWithSupplyTypeInSchema
      type: object
      description: |-
        Meter Frame Id property.
      additionalProperties: false
      required:
        - meterFrameId
        - supplyType
      properties:
        meterFrameId:
          description: Meter Frame Id.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/GuidId'
        supplyType:
          description: Type of supply as described by object Supply Types DTO.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
          example: 1

    MeterFrameGisProperties:
      title: MeterFrameGisProperties
      type: object
      description: |-
        Common properties for GIS in MeterFrame.
      additionalProperties: false
      required:
        - gisId
        - gisDescription
      properties:
        gisId:
          description: 'Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.'
          allOf:
            - $ref: '#/components/schemas/GuidField'
        gisDescription:
          description: 'The name of the connection point in the physical topology. The name is set when the gis id is selected from the GIS system.'
          allOf:
            - $ref: '#/components/schemas/DescriptionString'
          example: 'GisDescription is any 1000 characters long string with spaces allowed.'

    MeterFrameGisPropertiesElectricityModel:
      title: MeterFrameGisPropertiesElectricityModel
      description: |-
        GIS properties for electricity supply type in Meter Frame.
      additionalProperties: false
      allOf:
        - $ref: '#/components/schemas/MeterFrameGisProperties'
        - type: object
          additionalProperties: false
          properties:
            equipmentContainerId:
              description: 'Top level (Equipment container) topology item identifier. Top level parent of topology node specified in gisId.'
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'
            branchLineFuseAmps:
              description: "Indicates the size of, or setting of the maximum circuit breaker in front of the branch line on the network company's side. Ex. 125 Amperes."
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 125
            branchLineFuseType:
              nullable: true
              description: 'Indicates the type of branch line fuse present. Ex. Fuse, Maximum switch, HSP fuse.'
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              example: 'Max. 50 characters long string with spaces.'
            cabinetNumber:
              description: 'Number of the cable box where the plug is connected. Ex. 123658.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 123658
            cableNumber:
              description: 'Number on execution in station. Ex. 1, 2, 3 etc.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 3
            connectionPointLevel:
              description: 'Indicates at which level the branch line is connected in the network. Ex. C, B1, B2, A1, A2 and A0.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              example: 'B1'
            shinNumber:
              description: 'Number on the rail in the locker.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 15
            stationNumber:
              description: 'The station from which the branch line is supplied. Ex. 98756.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 98756
            transformerNumber:
              description: 'Transformer number, for several transformers in a station. Ex. 1, 2, 3 etc.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 2

    MeterFrameGisPropertiesHeatingModel:
      title: MeterFrameGisPropertiesHeatingModel
      description: |-
        GIS properties for heating supply type in Meter Frame.
      required:
        - branchLineSize
      allOf:
        - $ref: '#/components/schemas/MeterFrameGisProperties'
        - type: object
          additionalProperties: false
          properties:
            branchLineSize:
              description: 'Indicates how large a branch line is laid in the ground from the main line to the meter.'
              nullable: false
              allOf:
                - $ref: '#/components/schemas/OneWordString'
              example: 'SomeStringWithoutSpaces'
            branchLineNumber:
              description: 'Number on branch line. Ex. 246810.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 246810
            expectedForwardFlowTemp:
              description: 'Calculated value of forward-flow temperature.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 2
            expectedPressure:
              description: 'Calculated value of expected pressure - max/min/delta.'
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            heatingPlantId:
              description: 'Heating plant Id - Heater "kID".'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 3
            heatingPlantName:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Heating plant name.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            heatingPlantPipeName:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Pipe name / outlet marking.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            heatingPlantPipeNumber:
              description: 'Outlet marking number.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 4
            hydraulicZone:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Indicates an area, e.g. a city (polygon).'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true

    MeterFrameGisPropertiesWaterModel:
      title: MeterFrameGisPropertiesWaterModel
      description: |-
        GIS properties for water supply type in Meter Frame.
      allOf:
        - $ref: '#/components/schemas/MeterFrameGisProperties'
        - type: object
          additionalProperties: false
          properties:
            branchLineSizeSquare:
              allOf:
                - $ref: '#/components/schemas/OneWordStringNullable'
              description: 'Indicates how large a branch line is laid in the ground from Main line to Area.'
              example: 'ShortStringWithoutSpaces'
              nullable: true
            branchLineNumber:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Number on branch line. Ex. XF2500.'
              example: 'XF2500'
              nullable: true
            hardness:
              description: 'Hardness in dH (german unit for water hardness).'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 1
            mainZone:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Indicates an area (higher level than section) = an operating area (polygon).'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            pressure:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Section/elevation/conduction loss (calculated value).'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            sectionId:
              description: 'Indicates an area (polygon) in which the water meter frame is located (for checking water balance).'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 2
            sectionName:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Section name.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            superZone:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Indicates an area <Section> (higher level than MainZone) = e.g. a city (polygon).'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            waterPlantId:
              description: 'Water Plant Id.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 3
            waterPlantName:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Water Plant Name.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true
            waterPlantPipeId:
              description: 'Water Plant Pipe Id.'
              nullable: true
              allOf:
                - $ref: '#/components/schemas/IntegerNullable'
              example: 4
            waterPlantPipeName:
              allOf:
                - $ref: '#/components/schemas/ShortStringNullable'
              description: 'Water Plant Pipe name.'
              example: 'Max. 50 characters long string with spaces.'
              nullable: true

    MainBranchLineElectricityModel:
      title: MainBranchLineElectricityModel
      description: MainBranchLineElectricityModel
      type: object
      additionalProperties: false
      properties:
        connectedMeterFrameId:
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          description: Id of related meterFrameId, when connected via other branchline
        branchLineNumber:
          allOf:
            - $ref: '#/components/schemas/ShortString'
          description: 'Number on branch line. Ex. XF2500.'
          example: 'XF2500'
          nullable: false
        branchLineType:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Branch line type.'
          example: 'ShortStringWithoutSpaces'
          nullable: true
        materialCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Material code.'
          example: 'Material code'
          nullable: true
        neutralSquare:
          allOf:
            - $ref: '#/components/schemas/DecimalNullable'
          description: 'Neutral square.'
          example: 1
          nullable: true
        numberOfCables:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of cables.'
          example: 1
          nullable: true
        numberOfConductors:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of conductors.'
          example: 1
          nullable: true
        systemGrounding:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'System Grounding.'
          example: 'SystemGrounding'
          nullable: true

    MainBranchLineElectricityCreateUpdateModel:
      title: MainBranchLineElectricityCreateUpdateModel
      description: MainBranchLineElectricityCreateUpdateModel
      type: object
      additionalProperties: false
      properties:
        connectedMeterFrameId:
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          description: Id of related meterFrameId, when connected via other branchline
        branchLineType:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Branch line type.'
          example: 'ShortStringWithoutSpaces'
          nullable: true
        materialCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Material code.'
          example: 'Material code'
          nullable: true
        neutralSquare:
          allOf:
            - $ref: '#/components/schemas/DecimalNullable'
          description: 'Neutral square.'
          example: 1
          nullable: true
        numberOfCables:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of cables.'
          example: 1
          nullable: true
        numberOfConductors:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of conductors.'
          example: 1
          nullable: true
        systemGrounding:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'System Grounding.'
          example: 'SystemGrounding'
          nullable: true

    ReserveBranchLineElectricityModel:
      title: ReserveBranchLineElectricityModel
      description: ReserveBranchLineElectricityModel
      type: object
      additionalProperties: false
      properties:
        branchLineNumber:
          allOf:
            - $ref: '#/components/schemas/ShortString'
          description: 'Number on branch line. Ex. XF2500.'
          example: 'XF2500'
          nullable: false
        branchLineType:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Branch line type.'
          example: 'ShortStringWithoutSpaces'
          nullable: true
        materialCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Material code.'
          example: 'Material code'
          nullable: true
        neutralSquare:
          allOf:
            - $ref: '#/components/schemas/DecimalNullable'
          description: 'Neutral square.'
          example: 1
          nullable: true
        numberOfCables:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of cables.'
          example: 1
          nullable: true
        numberOfConductors:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of conductors.'
          example: 1
          nullable: true
        systemGrounding:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'System Grounding.'
          example: 'SystemGrounding'
          nullable: true

    ReserveBranchLineElectricityCreateUpdateModel:
      title: ReserveBranchLineElectricityCreateUpdateModel
      description: ReserveBranchLineElectricityCreateUpdateModel
      type: object
      additionalProperties: false
      properties:
        branchLineType:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Branch line type.'
          example: 'ShortStringWithoutSpaces'
          nullable: true
        materialCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'Material code.'
          example: 'Material code'
          nullable: true
        neutralSquare:
          allOf:
            - $ref: '#/components/schemas/DecimalNullable'
          description: 'Neutral square.'
          example: 1
          nullable: true
        numberOfCables:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of cables.'
          example: 1
          nullable: true
        numberOfConductors:
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          description: 'Number of conductors.'
          example: 1
          nullable: true
        systemGrounding:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: 'System Grounding.'
          example: 'SystemGrounding'
          nullable: true

    MainBranchLineWaterModel:
      title: MainBranchLineWaterModel
      description: MainBranchLineWaterModel
      type: object
      additionalProperties: false
      properties:
        connectedMeterFrameId:
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          description: Id of related meterFrameId, when connected via other branchline
        branchLineSize:
          allOf:
            - $ref: '#/components/schemas/OneWordString'
          description: 'Number on branch line. Ex. XF2500.'
          example: 'XF2500'
          nullable: false

    ReserveBranchLineWaterModel:
      title: ReserveBranchLineWaterModel
      description: ReserveBranchLineWaterModel
      type: object
      additionalProperties: false
      properties:
        branchLineSize:
          allOf:
            - $ref: '#/components/schemas/OneWordString'
          description: 'Number on branch line. Ex. XF2500.'
          example: 'XF2500'
          nullable: false

    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
          example: "https://errors.kmdelements.com/500"
        title:
          description: "A short, human-readable summary of the problem type."
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
          example: Error short description
        status:
          description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
          type: integer
          format: int32
          minimum: 400
          maximum: 599
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          maxLength: 2048
          pattern: "^.*$"
          nullable: true
          example: Description what exactly happened
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          maxLength: 32779
          pattern: "^.*$"
          nullable: true
          example: /resources/1
      additionalProperties: true
    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1000000
              additionalProperties:
                type: array
                maxItems: 1000000
                description: Array of validation error messages.
                items:
                  $ref: '#/components/schemas/DescriptionString'
              nullable: false

    ConnectionRightModel:
      type: object
      description: Connection Right model
      additionalProperties: false
      required:
        - id
        - changeType
        - validFrom
        - validTo
        - value
        - unit
        - createdAt
        - electricityCategory
        - invoices
        - rightType
        - branchLineType
        - meterFrameId
        - rowVersion
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        changeType:
          $ref: '#/components/schemas/ConnectionRightChangeType'
        validFrom:
          type: string
          format: date-time
          nullable: true
          description: Valid from date.
          example: '2023-01-01T06:50:30.870Z'
        validTo:
          type: string
          format: date-time
          nullable: true
          description: Valid until date.
          example: '2023-01-01T06:50:30.870Z'
        value:
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
          description: Value of measurument of given unit.
        unit:
          $ref: '#/components/schemas/ConnectionRightElectricityUnitType'
        createdAt:
          type: string
          format: date-time
          description: When Connection Right was created.
          example: '2023-01-01T06:50:30.870Z'
        electricityCategory:
          $ref: '#/components/schemas/ConnectionRightElectricityCategory'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        invoices:
          type: array
          maxItems: 1000
          description: Connection Right Invoices
          items:
            $ref: '#/components/schemas/ConnectionRightInvoiceModel'
        rightType:
          $ref: '#/components/schemas/ConnectionRightType'
        branchLineType:
          $ref: '#/components/schemas/BranchLineType'
        comment:
          allOf:
            - $ref: '#/components/schemas/LongStringNullable'
          description: Comment.
          example: 'comment'
        connectionRightReferences:
          type: array
          maxItems: 1000
          description: List of Connection Right reference ids.
          items:
            $ref: '#/components/schemas/ConnectionRightReference'
        formReference:
          $ref: '#/components/schemas/FormReference'
        meterFrameId:
          $ref: '#/components/schemas/GuidId'

    ConnectionRightTransfer:
      type: object
      description: Code list model.
      additionalProperties: false
      required:
        - fromConnectionRights
        - toConnectionRights
      properties:
        fromConnectionRights:
          type: array
          maxItems: 1000
          description: List of Connection Right Transfer Items. Those will be transfered to connection rights in "to" collection.
          items:
            $ref: '#/components/schemas/ConnectionRightTransferItem'
        toConnectionRights:
          type: array
          maxItems: 1000
          description: List of Connection Right Transfer Items. The ones from 'from' Collection will be trasfered into these connection rights.
          items:
            $ref: '#/components/schemas/ConnectionRightTransferItem'

    ConnectionRightReference:
      type: object
      description: Code list header model.
      additionalProperties: false
      required:
        - id
        - meterFrameId
        - category
        - unit
        - value
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        meterFrameId:
          $ref: '#/components/schemas/GuidId'
        category:
          $ref: '#/components/schemas/ConnectionRightElectricityCategory'
        unit:
          $ref: '#/components/schemas/ConnectionRightElectricityUnitType'
        value:
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
          description: Value of measurement of a given unit.
          example: 1

    ConnectionRightTransferItem:
      type: object
      description: Code list header model.
      additionalProperties: false
      required:
        - id
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'

    CodeListModel:
      type: object
      description: Code list model.
      additionalProperties: false
      required:
        - id
        - name
        - from
        - to
        - codeListTypeKind
        - codeListValueModels
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        name:
          description: Name of the Code List.
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
        systemIdentifier:
          type: string
          description: System identifier of the Code List
          nullable: true
          maxLength: 100
          pattern: '^.*$'
        parentId:
          type: string
          description: Value list parent ID (GUID).
          format: uuid
          nullable: true
        from:
          type: string
          description: Value list form.
          format: date-time
        to:
          type: string
          description: Value list to.
          format: date-time
        codeListTypeKind:
          $ref: '#/components/schemas/CodeListTypeKindModel'
        codeListValueModels:
          type: array
          maxItems: 1000000
          description: Values - elements of a single Code List.
          items:
            $ref: '#/components/schemas/CodeListValueModel'
        headers:
          type: array
          maxItems: 1000000
          description: Headers of a Code List - available only if Code List contains any Attributes.
          items:
            $ref: '#/components/schemas/CodeListHeaderModel'
    CodeListHeaderModel:
      type: object
      description: Code list header model.
      additionalProperties: false
      required:
        - id
        - headerText
        - headerOrder
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        headerText:
          description: Header display text.
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
        headerOrder:
          allOf:
            - $ref: '#/components/schemas/Integer'
          nullable: false
          description: Column order - lowest should come first.

    ConnectionRightType:
      type: string
      description: 'Connection Right Type'
      x-enumNames:
        - Consumption
        - Production
      enum:
        - Consumption
        - Production

    BranchLineType:
      type: string
      description: 'Branch Line Type'
      enum:
        - Main
        - Reserve

    ConnectionRightChangeType:
      type: string
      description: 'Connection Right Change Type'
      x-enumNames:
        - Creation
        - Extension
      enum:
        - Creation
        - Extension

    ConnectionRightElectricityCategory:
      type: string
      description: 'Connection Right Electricity category'
      x-enumNames:
        - Parcel
        - TerracedHouse
        - SmallApartment
        - LargeApartment
        - Allotment
        - YoungOldAndNursery
        - SmallInstallation
        - Other
        - ExtensionAHigh
        - ExtensionALow
        - ExtensionBHigh
        - ExtensionBLow
        - ExtensionCLevel
        - kWMaxBHigh
        - kWMaxBLow
        - kWMaxCLevel
        - None
        - LimitedNetAccess
      enum:
        - Parcel
        - TerracedHouse
        - SmallApartment
        - LargeApartment
        - Allotment
        - YoungOldAndNursery
        - SmallInstallation
        - Other
        - ExtensionAHigh
        - ExtensionALow
        - ExtensionBHigh
        - ExtensionBLow
        - ExtensionCLevel
        - kWMaxBHigh
        - kWMaxBLow
        - kWMaxCLevel
        - None
        - LimitedNetAccess

    ConnectionRightElectricityUnitType:
      type: string
      description: 'Connection Right Electricity Unit Type'
      x-enumNames:
        - A
        - kVA
        - kW
        - SharedBranchLineConnectionRight
        - None
      enum:
        - A
        - kVA
        - kW
        - SharedBranchLineConnectionRight
        - None

    CodeListTypeKindModel:
      type: integer
      format: int32
      minimum: 1
      maximum: 292
      description: 'Enum identifying Code List usage purpose.'
      x-enumNames:
        - ConnectionPointElectricityConnectionPointCategoryValue
        - ConnectionPointElectricityInstallationType
        - ConnectionPointElectricityConnectionStatus
        - ConnectionPointElectricityNetSettlementGroup
        - ConnectionPointElectricityConsumerCategory
        - ConnectionPointHeatingConnectionPointCategoryValue
        - ConnectionPointHeatingInstallationType
        - ConnectionPointHeatingConnectionStatus
        - ConnectionPointHeatingHeatWaterHeater
        - ConnectionPointHeatingWaterHeaterType
        - ConnectionPointHeatingHeatPlantType
        - ConnectionPointHeatingHeatExchange
        - ConnectionPointWaterConnectionPointCategoryValue
        - ConnectionPointWaterInstallationType
        - ConnectionPointWaterConnectionStatus
        - ConnectionPointTag
        - SpecialAgreementZone
        - MeterFrameElectricityConnectionType
        - MeterFrameElectricityMeterDisplaySetting
        - MeterFrameElectricityConnectionRemark
        - MeterFrameElectricityTarifConnectionPoint
        - MeterFrameElectricityPurpose
        - MeterFrameElectricityRatioCT
        - MeterFrameElectricityRatioVT
        - MeterFrameElectricityConnectionStatus
        - MeterFrameHeatPlantModelType
        - MeterFrameHeatMeterFrameConnectionStatus
        - MeterFrameHeatCustomerCriticalityCategory
        - MeterFrameHeatHeatMeterConnectionType
        - MeterFrameHeatMeterDisplaySetting
        - MeterFrameWaterCustomerCriticalityCategory
        - MeterFrameWaterMeterFrameConnectionStatus
        - MeterFrameWaterMediumCategory
        - MeterFrameWaterDriveByType
        - MeterFrameWaterMeterDisplaySetting
        - MeterFrameTag
        - MeterFramePlacementCode
        - MeterFrameRegisterRequirementMeteringComponent
        - MeterFrameCommonReading
        - MeterFrameMeterReadingType
        - MeterManagementGeneric
        - MeterDisplayConfiguration
        - MeterRegisterConfiguration
        - ElectricityMeteringPointType
        - HeatingMeteringPointType
        - WaterMeteringPointType
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 30
        - 31
        - 32
        - 33
        - 34
        - 35
        - 36
        - 60
        - 61
        - 62
        - 90
        - 120
        - 231
        - 232
        - 233
        - 234
        - 235
        - 236
        - 237
        - 238
        - 252
        - 253
        - 254
        - 255
        - 256
        - 281
        - 282
        - 283
        - 284
        - 285
        - 290
        - 291
        - 292
        - 293
        - 294
        - 300
        - 301
        - 302
        - 330
        - 331
        - 332
    CodeListValueModel:
      type: object
      description: Code list value model.
      additionalProperties: false
      required:
        - id
        - displayValue
        - attributes
        - from
        - to
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        displayValue:
          description: Code List Value Display Value.
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
          nullable: false
        attributes:
          type: array
          maxItems: 1000000
          description: Attributes collection.
          nullable: false
          items:
            $ref: '#/components/schemas/CodeListAttributeModel'
        parentId:
          type: string
          description: Value list parent ID (GUID).
          format: uuid
          nullable: true
        from:
          type: string
          description: Value list form.
          format: date-time
        to:
          type: string
          description: Value list to.
          format: date-time
    CodeListAttributeModel:
      type: object
      description: Code list attribute model.
      additionalProperties: false
      required:
        - id
        - codeListHeaderId
        - codeListValueId
        - value
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        codeListHeaderId:
          description: Code List Header id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        codeListValueId:
          description: Code List Value id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        value:
          description: Code List Attribute value.
          allOf:
            - $ref: '#/components/schemas/DescriptionString'
    CodeListUsageAreasModel:
      type: integer
      format: int32
      minimum: 1
      maximum: 9
      description: 'Filter for contextual usage area - ex. ConnectionPoints.'
      x-enumNames:
        - ConnectionPoints
        - SpecialAgreements
        - MeterFrames
        - MeterAttributeRequirements
      enum:
        - 1
        - 2
        - 3
        - 8
    PagedResultOfGetSpecialAgreementsSearchModel:
      description: Paged result of special agreements search model.
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          additionalProperties: false
          properties:
            results:
              description: Array.
              type: array
              nullable: false
              maxItems: 1000000
              items:
                $ref: '#/components/schemas/GetSpecialAgreementsSearchModel'
    GetSpecialAgreementsSearchModel:
      type: object
      description: Get special agreements search model.
      additionalProperties: false
      required:
        - id
        - description
        - validFrom
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        description:
          description: Description.
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
        validFrom:
          type: string
          format: date-time
          description: Valid from date.
        validUntil:
          type: string
          format: date-time
          nullable: true
          description: Valid until date.
        zoneCodeListValueId:
          description: Zone code list value identifier.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        zoneCodeListValueDisplayValue:
          nullable: true
          description: Zone code list value display value.
          allOf:
            - $ref: '#/components/schemas/LongStringObsoleteNullable'

    PagedResultBase:
      type: object
      additionalProperties: false
      description: Paged result.
      required:
        - currentPage
        - pageCount
        - pageSize
        - rowCount
      properties:
        currentPage:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Current page.
        pageCount:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Page count.
        pageSize:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Page size.
        rowCount:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Row count.
        maxCount:
          allOf:
            - $ref: '#/components/schemas/NonNegativeIntegerNullable'
          description: Max count.

    MeterInputConnectionsListSearchFilterModel:
      description: Properties for filtration of Meter Input Connections.
      type: object
      additionalProperties: false
      properties:
        meterId:
          description: Filter by Meter Id - integer value.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MeterId'
          example: 360
        supplyType:
          description: Filter by Supply Type Enum Flag - integer value.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
          example: 1

    ConnectionPointsListSearchFilterModel:
      description: Properties for filtration of Connection Points.
      type: object
      additionalProperties: false
      properties:
        supplyType:
          description: Filter by Supply Type Enum Flag - integer value.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
        connectionPointId:
          description: Filter by Connection Point Identifier - must be valid GUID.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        connectionPointNumber:
          description: Filter by Connection Point Number (business identifier) - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        installationNumber:
          description: Filter by Installation Number - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        alternativeInstallationNumber:
          description: Filter by Alternative Installation Number - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        address:
          description: Filter by Address (CentralAddressRegistry Address Id) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        addressLine1:
          description: Filter by Address Line 1 - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        addressLine2:
          description: Filter by Address Line 2 - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
        addressStatus:
          description: Filter by Address Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          description: Filter by Address Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressType'
        darStatus:
          description: Filter by Address DAR Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          description: Filter by Address Life Cycle Status (MDR) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        electricityAttributesInstallationTypeValue:
          description: Filter by Electricity Attributes Installation Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        electricityAttributesConnectionPointCategoryValue:
          description: Filter by Electricity Attributes Connection Point Category - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        electricityAttributesConnectionStatus:
          description: Filter by Electricity Attributes Connection Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        electricityAttributesConsumerCategory:
          description: Filter by Electricity Attributes Consumer Category - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        electricityAttributesNetSettlementGroup:
          description: Filter by Electricity Attributes Net Settlement Group - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        electricityAttributesTemporary:
          description: Filter by Electricity Attributes Temporary Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributesStartPeriod:
          description: Filter by Electricity Attributes Temporary Until Date (TemporaryUntil <= StartPeriod) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        electricityAttributesEndPeriod:
          description: Filter by Electricity Attributes Temporary Until Date (TemporaryUntil < EndPeriod) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        electricityAttributesGridAreaId:
          description: Grid area id.
          allOf:
            - $ref: '#/components/schemas/OneWordStringNullable'
        heatingAttributesConnectionPointCategoryValue:
          description: Filter by Heating Attributes Connection Point Category - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        heatingAttributesInstallationTypeValue:
          description: Filter by Heating Attributes Installation Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        heatingAttributesConnectionStatus:
          description: Filter by Heating Attributes Connection Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        heatingAttributesHeatExchange:
          description: Filter by Heating Attributes Heat Exchange Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        heatingAttributesHeatPlantType:
          description: Filter by Heating Attributes Heat Plant Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        waterAttributesConnectionPointCategoryValue:
          description: Filter by Water Attributes Connection Point Category - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        waterAttributesInstallationTypeValue:
          description: Filter by Water Attributes Installation Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        waterAttributesConnectionStatus:
          description: Filter by Water Attributes Connection Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        waterAttributesTemporary:
          description: Filter by Water Attributes Temporary Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        waterAttributesStartPeriod:
          description: Filter by Water Attributes Temporary Until Date (TemporaryUntil <= StartPeriod) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        waterAttributesEndPeriod:
          description: Filter by Water Attributes Temporary Until Date (TemporaryUntil < EndPeriod) - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'

    SpecialAgreementsListSearchFilterModel:
      description: Properties for filtration of Connection Points.
      type: object
      additionalProperties: false
      properties:
        connectionPointId:
          description: Filter by Connection Point Identifier - must be valid GUID.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        zoneCodeListValueId:
          description: Filter by ZoneCodeListValueId - must be valid GUID.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        validFrom:
          description: Filter by ValidFrom.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        validUntil:
          description: Filter by ValidFrom.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'

    MeterFramesListSearchFilterModel:
      description: Properties for filtration of Connection Points.
      type: object
      additionalProperties: false
      properties:
        supplyType:
          description: Filter by Type of supply as described by object Supply Types DTO.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
          example: 1
        connectionPointId:
          description: Filter by Connection Point Identifier - must be valid GUID.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        meterFrameId:
          description: Filter by Id of the Meter Frame.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        connectionPointBusinessId:
          description: Filter by Connection point business identifier.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
          example: 1100000016
        meterFrameNumber:
          description: Filter by Meter frame number.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 1100000016-1
        supplyStatus:
          description: Filter by List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
          example: 1
        supplyDisconnectType:
          description: Filter by List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation", "Connected", "Unknown".
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
          example: 1
        commonReading:
          description: Filter by Common Reading
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        noMeter:
          description: Filter by No meter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: true
        meterMissing:
          description: Filter by meter is missing.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: false
        meterReadingType:
          description: Filter by a list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        addressLine1:
          description: Filter by Address Line 1 - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
          example: 'Trælastgade 21'
        addressLine2:
          description: Filter by Address Line 2 - startsWith (like) search query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/StartsWithStringFilterParameter'
          example: '8000 Aarhus C'
        addressStatus:
          description: Filter by Address Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressStatus'
          example: 1
        addressType:
          description: Filter by Address Type - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressType'
          example: 1
        lifeCycleStatus:
          description: Filter by LifeCycleStatus.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
          example: 1
        darStatus:
          description: Filter by Address DAR Status - exact query parameter.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DarStatus'
          example: 1
        cabinetNumber:
          description: Filter by Number of the cable box where the plug is connected. Ex. 123658.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        cableNumber:
          description: Filter by Number on execution in station. Ex. 1, 2, 3 etc.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        stationNumber:
          description: Filter by The station from which the branch line is supplied. Ex. 98756.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        transformerNumber:
          description: Filter by Transformer number, for several transformers in a station. Ex. 1, 2, 3 etc.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        shinNumber:
          description: Number on the rail in the locker.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        heatingBranchLineNumber:
          description: Filter by Number on branch line. Ex. 246810.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        heatingPlantId:
          description: Filter by Heating plant Id - Heater "kID".
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        heatingPlantName:
          description: Filter by Heating plant name.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'plant'
        heatingPlantPipeName:
          description: Filter by Pipe name / outlet marking.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'heating plan name'
        heatingPlantPipeNumber:
          description: Filter by Outlet marking number.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        waterBranchLineNumber:
          description: Filter by Number on branch line. Ex. XF2500.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 111
        sectionId:
          description: Filter by an area (polygon) in which the water meter frame is located (for checking water balance).
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        sectionName:
          description: Filter by Section name.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'section name'
        waterPlantId:
          description: Filter by Water Plant Id.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        waterPlantName:
          description: Filter by Water Plant Name.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'water plant name'
        waterPlantPipeId:
          description: Filter by Water Plant Pipe Id.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 111
        waterPlantPipeName:
          description: Filter by Water Plant Pipe name.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'water plant pipe name'
        mainZone:
          description: Filter by an area (higher level than section) = an operating area (polygon).
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'main zone'
        superZone:
          description: Filter by an area <Section> (higher level than MainZone) = e.g. a city (polygon).
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'super zone'
        hasMainBranchLine:
          description: Filter by Main Branch Line.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: true
        hasReserveBranchLine:
          description: Filter by Reserve Branch Line.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: true
        connectionStatus:
          description: Filter by Connection Status.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        branchLineNumber:
          description: Filter by Branch Line Number.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: '1234a'
        branchLineType:
          description: Filter by Branch Line Type.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'type c'
        branchLineSize:
          description: Filter by Branch Line Size.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'branch line size'
        reserveBranchLineNumber:
          description: Filter by Reserve Branch Line Number.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: '1234a'
        reserveBranchLineType:
          description: Filter by Reserve Branch Line Type.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'branch line type'
        reserveBranchLineSize:
          description: Filter by Reserve Branch Line Size.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 'branch line size'
        connectedThroughOtherMeterFrame:
          description: Filter by Connected Through Other Meter Frame.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
          example: true
        connectedThroughOtherMeterFrameNumber:
          description: Filter by Connected Through Other Meter Frame NUmber.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: 1100000016-1
        meterNumber:
          description: 'Filter by Meter Number.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        registerRequirementValidation:
          description: 'Filter by Register Requirement Validation.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'
        attributeRequirementValidation:
          description: 'Filter by Attribute Requirement Validation.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BooleanFieldNullable'

    MeterInputConnectionsListSearchQueryModel:
      type: object
      additionalProperties: false
      description: Meter Input Connections list search query model.
      required:
        - filter
      properties:
        page:
          nullable: true
          type: object
          description: Page object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Page'
        filter:
          description: Filter object.
          type: object
          nullable: false
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/MeterInputConnectionsListSearchFilterModel'

    ConnectionPointsListSearchQueryModel:
      type: object
      additionalProperties: false
      description: Connection Points list search query model.
      required:
        - filter
      properties:
        page:
          nullable: true
          type: object
          description: Page object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Page'
        sort:
          nullable: true
          type: object
          description: Sort object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Sort'
        filter:
          description: Filter object.
          type: object
          nullable: false
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/ConnectionPointsListSearchFilterModel'

    SpecialAgreementsSearchListQueryModel:
      type: object
      additionalProperties: false
      description: Connection Points list search query model.
      required:
        - filter
      properties:
        page:
          nullable: true
          type: object
          description: Page object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Page'
        sort:
          nullable: true
          type: object
          description: Sort object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Sort'
        filter:
          description: Filter object.
          type: object
          nullable: false
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/SpecialAgreementsListSearchFilterModel'

    MeterFramesListSearchQueryModel:
      type: object
      additionalProperties: false
      description: Meter Frames list search query model.
      required:
        - filter
      properties:
        page:
          nullable: true
          type: object
          description: Page object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Page'
        sort:
          nullable: true
          type: object
          description: Sort object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Sort'
        filter:
          description: Filter object.
          type: object
          nullable: false
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/MeterFramesListSearchFilterModel'

    GetMeterFramesListByMeteringPointIdQueryModel:
      type: object
      additionalProperties: false
      description: Meter Frames list search query model.
      required:
        - meteringPointId
      properties:
        page:
          nullable: true
          type: object
          description: Page object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Page'
        sort:
          nullable: true
          type: object
          description: Sort object.
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/Sort'
        meteringPointId:
          description: Metering Point Id.
          nullable: false
          allOf:
            - $ref: '#/components/schemas/ShortString'
          example: 571313100000003974

    SearchListQueryModel:
      type: object
      additionalProperties: false
      description: Search list query model.
      required:
        - filter
      properties:
        page:
          nullable: true
          type: object
          description: Page object.
          oneOf:
            - $ref: '#/components/schemas/Page'
        sort:
          nullable: true
          type: object
          description: Sort object.
          oneOf:
            - $ref: '#/components/schemas/Sort'
        filter:
          description: Filter object.
          type: object
          oneOf:
            - $ref: '#/components/schemas/Filter'
    Page:
      type: object
      additionalProperties: false
      description: Page.
      required:
        - number
        - size
      properties:
        number:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Page number.
        size:
          type: integer
          format: int32
          minimum: 0
          maximum: 10000
          description: Page size.
    Sort:
      description: Sort object.
      additionalProperties: false
      properties:
        direction:
          type: string
          nullable: true
          maxLength: 4
          minLength: 3
          description: Direction of sorting. Can only be 'asc' for ascending or 'desc' for descending.
          example: asc
          pattern: '^(asc|desc)$'
        propertyName:
          type: string
          pattern: "^.*$"
          nullable: true
          description: Name of the property to sort by.
          minLength: 1
          maxLength: 200
    Filter:
      description: Filter.
      allOf:
        - $ref: '#/components/schemas/FilterAbstraction'
        - type: object
          additionalProperties: false
    FilterAbstraction:
      type: object
      additionalProperties: false
      description: Filter abstraction.
      properties:
        properties:
          type: array
          nullable: true
          description: Array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/PropertyFilter'
        propertiesForNestedCollections:
          type: array
          nullable: true
          description: Properties for nested collections.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/NestedCollectionItemFilter'
    PropertyFilter:
      type: object
      additionalProperties: false
      description: Property filter.
      required:
        - condition
      properties:
        name:
          description: Property name.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DescriptionStringNullable'
        condition:
          $ref: '#/components/schemas/FilterCondition'
        value:
          nullable: true
          type: object
          description: Property value.
    FilterCondition:
      type: integer
      format: int32
      minimum: 0
      maximum: 7
      description: Filter condition.
      x-enumNames:
        - Like
        - Equal
        - In
        - LessThan
        - LessThanOrEqual
        - GreaterThan
        - GreaterThanOrEqual
        - StartsWith
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
    NestedCollectionItemFilter:
      description: Nested collection item filter.
      allOf:
        - $ref: '#/components/schemas/FilterAbstraction'
        - type: object
          additionalProperties: false
          properties:
            name:
              description: Name.
              allOf:
                - $ref: '#/components/schemas/DescriptionString'
    GetSpecialAgreementByIdModel:
      type: object
      additionalProperties: false
      description: Get special agreement by id model.
      required:
        - id
        - description
        - validFrom
        - specialAgreementConnectionPointAssignments
        - rowVersion
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        description:
          description: Description.
          allOf:
            - $ref: '#/components/schemas/LongStringObsoleteNullable'
        validFrom:
          type: string
          format: date-time
          description: Valid from date.
        validUntil:
          type: string
          format: date-time
          nullable: true
          description: Valid until date.
        zoneCodeListValueId:
          description: Zone code list value identifier.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        specialAgreementConnectionPointAssignments:
          type: array
          description: Special agreement connection point assignments array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/SpecialAgreementConnectionPointAssignmentReadModel'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
    SpecialAgreementConnectionPointAssignmentReadModel:
      type: object
      additionalProperties: false
      description: Special agreement connection point assignment read model.
      required:
        - assignmentId
        - connectionPointId
        - connectionPointBusinessId
      properties:
        assignmentId:
          description: Assignment identifier.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        connectionPointId:
          description: Connection point identifier.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        connectionPointBusinessId:
          description: Connection point business identifier.
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
        connectionPointAddressId:
          description: Connection point address identifier.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        connectionPointAddressLine1:
          description: Connection point address line 1.
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
        connectionPointAddressLine2:
          description: Connection point address line 2.
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
    IdResult:
      type: object
      additionalProperties: false
      description: Id result.
      required:
        - id
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
    ChangeMeterOnMeterFrameResult:
      type: object
      additionalProperties: false
      description: ChangeMeterOnMeterFrameResult.
      required:
        - status
      properties:
        meterInputConnectionId:
          type: string
          format: uuid
          description: MeterInputConnection id.
        status:
          description: Validation status for Meter Input Connection.
          allOf:
            - $ref: "#/components/schemas/ChangeMeterOnMeterFrameResultStatus"
          example: 'Success'
        validationResult:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/ChangeOfMeterErrorDescription'
          description: Status of validation result, will be provided only with Failure and Warning status.
          example: Meter is currently not associated to the Meter Frame
    ValidateMeterOnMeterFrameResult:
      type: object
      additionalProperties: false
      description: ValidateMeterOnMeterFrameResult.
      required:
        - status
      properties:
        status:
          description: Validation status for Meter Input Connection.
          allOf:
            - $ref: "#/components/schemas/ChangeMeterOnMeterFrameResultStatus"
          example: 'Success'
        validationResult:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/ChangeOfMeterErrorDescription'
          description: Status of validation result, will be provided only with Failure and Warning status.
          example: Meter is currently not associated to the Meter Frame

    ValidateMeterOnMeterAttributesRequirementsResult:
      type: object
      additionalProperties: false
      description: ValidateMeterOnMeterAttributesRequirementsResult.
      required:
        - validationPassed
      properties:
        validationPassed:
          description: Validation result for Meter on Meter Attributes Requirements.
          type: boolean
          example: true
        validationErrors:
          allOf:
            - $ref: '#/components/schemas/DescriptionStringNullable'
          description: Status of validation result, will be provided only when ValidationPassed is false.
          example: 'The following attributes do not met the requirements: ManufacturerId, CableLength.'

    ValidateActiveMeterOnMeterFrameFullValidationResult:
      type: object
      additionalProperties: false
      description: ValidateActiveMeterOnMeterFrameFullValidationResult.
      required:
        - status
      properties:
        status:
          description: Validation status for Meter Input Connection.
          allOf:
            - $ref: "#/components/schemas/ValidateActiveMeterOnMeterFrameFullValidationResultStatus"
          example: 'Success'
        validationResults:
          description: Validation error codes. Only filled when Error in status.
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameErrorCodes'
        configurationDetails:
          description: Configuration properties details.
          type: object
          additionalProperties: false
          properties:
            displayConfiguration:
              description: |-
                Display configuration. Possible values:
                de23f9df-a4f0-4591-b743-1f7d1b00a351 - Not Possible
                79f3ec96-3bd4-41f8-bbc2-aa73846c86a7 - Optical Eye
                098a905e-dfe0-4842-b7ce-c58a756d8212 - Direct Control
              example: de23f9df-a4f0-4591-b743-1f7d1b00a351
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'
            registerConfiguration:
              description: |-
                Remote configuration. Possible values:
                bf4040ad-e590-4ec8-9237-7c9f3ef6580f - Not Possible
                f0322843-8742-41ea-91e1-970634c84bb1 - Optical Eye
                1edf42bd-28aa-451f-a27c-d0798ae52768 - Direct Control
              example: bf4040ad-e590-4ec8-9237-7c9f3ef6580f
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'


    ValidateActiveMeterOnMeterFrameConnectionTypeAndCtVtValidationResult:
      type: object
      additionalProperties: false
      description: ValidateActiveMeterOnMeterFrameConnectionTypeAndCtVtValidationResult.
      required:
        - status
      properties:
        status:
          description: Validation status for Meter Input Connection.
          allOf:
            - $ref: "#/components/schemas/ValidateActiveMeterOnMeterFrameConnectionTypeAndCtVtResultStatus"
          example: 'Success'
        validationResults:
          description: Validation error codes. Only filled when Error in status.
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/ValidateActiveMeterOnMeterFrameErrorCodes'

    IntIdResult:
      type: object
      additionalProperties: false
      description: Integer id result.
      required:
        - id
      properties:
        id:
          description: Id.
          example: 1234
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
    ChangeOfMeterErrorDescription:
      type: object
      additionalProperties: false
      description: Error description.
      required:
        - errorCode
        - defaultMessage
      properties:
        errorCode:
          type: integer
          format: int32
          minimum: 1
          maximum: 4
          x-enumNames:
            - MeterCannotBeAssignedToMoreThanOneMeterFrameAtATime
            - MeterIsNotFulfillingMeterFrameRegisterRequirement
            - MeterIsNotCurrentlyAssociatedToMeterFrame
            - ActiveMeterIsCurrentlyAssociatedToMeterFrame
            - InvalidChangeOfMeterDate
            - MeterIsNotFulfillingMeterFrameFutureRegisterRequirement
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
          description: Type of change of meter error code.
        defaultMessage:
          description: Default message.
          allOf:
            - $ref: '#/components/schemas/DescriptionString'
    ValidateActiveMeterOnMeterFrameErrorCodes:
      type: object
      additionalProperties: false
      description: Error description.
      required:
        - result
      properties:
        result:
          type: string
          nullable: true
          description: Active meter on meter frame full validation error codes.
          minLength: 16
          maxLength: 42
          pattern: "^(RegisterRequirementValidationFailed|MeterAttributeRequirementsValidationFailed|ConnectionTypeValidationFailed|CtValidationFailed|VtValidationFailed|MeterComponentValidationFailed|RegisterRequirementValidationPassed|MeterAttributeRequirementsValidationPassed|ConnectionTypeValidationPassed|CtValidationPassed|VtValidationPassed|MeterComponentValidationPassed)$"
          example: RegisterRequirementValidationFailed

    ErrorDescription:
      type: object
      additionalProperties: false
      description: Error description.
      required:
        - errorCode
        - defaultMessage
      properties:
        errorCode:
          description: Error code.
          allOf:
            - $ref: '#/components/schemas/DescriptionString'
        defaultMessage:
          description: Default message.
          allOf:
            - $ref: '#/components/schemas/DescriptionString'

    ConnectionRightsTotalValueBody:
      type: object
      additionalProperties: false
      description: Request to ask for connection rights total value.
      properties:
        meterFrameId:
          $ref: '#/components/schemas/GuidField'

    ConnectionRightsTotalValueModel:
      type: object
      description: ConnectionRightsTotalValueModel
      required:
        - unit
        - totalValue
      properties:
        unit:
          $ref: '#/components/schemas/ConnectionRightElectricityUnitType'
        totalValue:
          $ref: '#/components/schemas/PositiveInteger'
      additionalProperties: false

    AddConnectionRightModel:
      type: object
      additionalProperties: false
      description: Add connection right model.
      required:
        - meterFrameId
        - supplyType
        - changeType
        - rightType
        - value
        - electricityCategory
      properties:
        meterFrameId:
          $ref: '#/components/schemas/GuidId'
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        changeType:
          $ref: '#/components/schemas/ConnectionRightChangeType'
        rightType:
          $ref: '#/components/schemas/ConnectionRightType'
        validFrom:
          type: string
          format: date-time
          nullable: true
          description: Valid from date.
          example: '2023-01-01T06:50:30.870Z'
        validTo:
          type: string
          format: date-time
          nullable: true
          description: Valid until date.
          example: '2023-01-01T06:50:30.870Z'
        value:
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
          description: Value of measurument of given unit.
        electricityCategory:
          $ref: '#/components/schemas/ConnectionRightElectricityCategory'

    CreateConnectionRightModel:
      type: object
      additionalProperties: false
      description: Add or edit connection right.
      required:
        - meterFrameId
        - changeType
        - rightType
        - branchLineType
        - electricityCategory
        - value
      properties:
        meterFrameId:
          $ref: '#/components/schemas/GuidId'
        changeType:
          $ref: '#/components/schemas/ConnectionRightChangeType'
        rightType:
          $ref: '#/components/schemas/ConnectionRightType'
        branchLineType:
          $ref: '#/components/schemas/BranchLineType'
        electricityCategory:
          $ref: '#/components/schemas/ConnectionRightElectricityCategory'
        value:
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
          description: Value of measurument of given unit.
        comment:
          allOf:
            - $ref: '#/components/schemas/LongStringNullable'
          description: Comment.
          example: 'comment'
        connectionRightReferences:
          type: array
          maxItems: 1000
          description: List of Connection Right reference ids.
          items:
            $ref: '#/components/schemas/GuidId'
        formReference:
          $ref: '#/components/schemas/FormReference'

    UpdateConnectionRightModel:
      type: object
      additionalProperties: false
      description: Add or edit connection right.
      required:
        - changeType
        - rightType
        - electricityCategory
        - value
        - rowVersion
      properties:
        changeType:
          $ref: '#/components/schemas/ConnectionRightChangeType'
        rightType:
          $ref: '#/components/schemas/ConnectionRightType'
        electricityCategory:
          $ref: '#/components/schemas/ConnectionRightElectricityCategory'
        value:
          type: integer
          format: int32
          minimum: 1
          maximum: 10000
          description: Value of measurument of given unit.
        comment:
          allOf:
            - $ref: '#/components/schemas/LongStringNullable'
          description: Comment.
          example: 'comment'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        connectionRightReferences:
          type: array
          maxItems: 1000
          description: List of Connection Right reference ids.
          items:
            $ref: '#/components/schemas/GuidId'
        formReference:
          $ref: '#/components/schemas/FormReference'

    FormReference:
      type: object
      additionalProperties: false
      description: Form reference.
      nullable: true
      required:
        - formId
        - formNumber
        - formType
      properties:
        formId:
          $ref: '#/components/schemas/GuidId'
        formNumber:
          $ref: '#/components/schemas/FormNumber'
        formType:
          $ref: '#/components/schemas/FormType'

    FormType:
      type: string
      description: A form category
      x-enumNames:
        - NewInstallation
        - Extension
        - EnergyProduction
      enum:
        - NewInstallation
        - Extension
        - EnergyProduction

    FormNumber:
      type: string
      description: Form number
      minLength: 1
      maxLength: 10
      pattern: "^.*$"
      example: "S24007058"

    StopConnectionRightModel:
      type: object
      additionalProperties: false
      description: Add or edit connection right.
      required:
        - connectionRightId
        - validTo
        - rowVersion
      properties:
        connectionRightId:
          $ref: '#/components/schemas/GuidId'
        validTo:
          type: string
          format: date-time
          nullable: false
          description: Valid until date.
          example: '2023-01-01T06:50:30.870Z'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'

    AddSpecialAgreementModel:
      type: object
      additionalProperties: false
      description: Add special agreement model.
      required:
        - description
        - validFrom
        - specialAgreementConnectionPointAssignments
      properties:
        description:
          description: Description.
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
        zoneCodeListValueId:
          type: string
          format: uuid
          nullable: true
          description: Zone code list value identifier.
        validFrom:
          type: string
          format: date-time
          description: Valid from date.
        validUntil:
          type: string
          format: date-time
          nullable: true
          description: Valid until date.
        specialAgreementConnectionPointAssignments:
          type: array
          description: Special agreement connection point assignments.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/SpecialAgreementAssignmentAddUpdateModel'
    SpecialAgreementAssignmentAddUpdateModel:
      type: object
      additionalProperties: false
      description: Special agreement assignment add and update model.
      required:
        - connectionPointId
      properties:
        assignmentId:
          type: string
          format: uuid
          nullable: true
          description: Assignment identifier.
        connectionPointId:
          type: string
          format: uuid
          description: Connection point identifier.
    UpdateSpecialAgreementModel:
      type: object
      additionalProperties: false
      description: Update special agreement model.
      required:
        - description
        - validFrom
        - specialAgreementConnectionPointAssignments
        - rowVersion
      properties:
        description:
          description: Description.
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
        zoneCodeListValueId:
          type: string
          format: uuid
          nullable: true
          description: Zone code list value id.
        validFrom:
          type: string
          format: date-time
          description: Valid from date.
        validUntil:
          type: string
          format: date-time
          nullable: true
          description: Valid until date.
        specialAgreementConnectionPointAssignments:
          type: array
          description: Special agreement connection point assignments array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/SpecialAgreementAssignmentAddUpdateModel'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
    GetSpecialAgreementNoteBySpecialAgreementIdModel:
      description: Get special agreement note by special agreement id model.
      allOf:
        - $ref: '#/components/schemas/GetSpecialAgreementNoteByIdModel'
        - type: object
          additionalProperties: false
    GetSpecialAgreementNoteByIdModel:
      type: object
      additionalProperties: false
      description: Get special agreement note by id model.
      required:
        - id
        - note
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        note:
          description: Note.
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
    AddSpecialAgreementNoteModel:
      description: Add special agreement note model.
      allOf:
        - $ref: '#/components/schemas/SpecialAgreementNoteModel'
        - type: object
          additionalProperties: false
    SpecialAgreementNoteModel:
      type: object
      additionalProperties: false
      description: Special agreement note model.
      required:
        - note
      properties:
        note:
          description: Note.
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
    UpdateSpecialAgreementNoteModel:
      description: Update Special Agreement note model.
      allOf:
        - $ref: '#/components/schemas/SpecialAgreementNoteModel'
        - type: object
          additionalProperties: false
          properties:
            rowVersion:
              $ref: '#/components/schemas/RowVersion'
    PagedResponseOfGetMeterFrameSearchModel:
      type: object
      additionalProperties: false
      description: Paged response of get meter frame search model.
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Current page.
        pageSize:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Page size.
        totalPages:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Total pages.
        totalRows:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Total rows.
        results:
          type: array
          description: Results array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/GetMeterFrameSearchModel'

    PagedResponseOfPostMeterFrameSearchModel:
      description: Paged response of post command meter frame search model.
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          additionalProperties: false
          properties:
            results:
              type: array
              nullable: true
              description: Results array.
              maxItems: 1000000
              items:
                $ref: '#/components/schemas/GetMeterFrameSearchModel'


    PagedResponseOfGetMeterFramesBasicModel:
      type: object
      additionalProperties: false
      description: Paged response of get meter frame basic model.
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Current page.
        pageSize:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Page size.
        totalPages:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Total pages.
        totalRows:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Total rows.
        results:
          type: array
          description: Results array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/GetMeterFrameBasicModel'
    GetMeterFrameSearchModel:
      type: object
      additionalProperties: false
      description: Get meter frame search model.
      required:
        - id
        - meterFrameNumber
        - supplyType
        - connectionPointId
        - commonReading
        - meterMissing
        - meterSealed
        - meterWorkConsumerBilled
        - supplyStatus
        - placementCode
        - hasActiveBailiffCase
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        meterFrameNumber:
          description: |-
            DK: Målerrammenummer.
            Number on the Meter Frame.
          allOf:
            - $ref: '#/components/schemas/ShortString'
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        connectionPointId:
          type: string
          description: ConnectionPointId.
          format: uuid
        connectionPointBusinessId:
          description: ConnectionPointBusinessId.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
        addressId:
          type: string
          description: An UUID reference to a master data address.
          format: uuid
          nullable: true
        addressName:
          description: AddressName.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine1:
          description: AddressLine1.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine2:
          description: AddressLine2.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressStatus:
          description: AddressStatus.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          description: AddressType.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AddressType'
        lifeCycleStatus:
          description: List of possible life cycle states that the MDR system can put the address into.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        darStatus:
          description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated"..
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DarStatus'
        commonReading:
          allOf:
            - $ref: '#/components/schemas/GuidField'
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
        noMeter:
          type: boolean
          description: 'DK: Målerfri.'
        meterMissing:
          type: boolean
          description: 'DK: MålerVæk.'
        placementSpecification:
          description: 'DK: Placeringsbeskrivelse.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        meterSealDate:
          type: string
          description: |-
            DK: Målerplomberingsdato.
            Indicates the date when the meter was sealed.
          format: date-time
          nullable: true
        meterSealed:
          type: boolean
          description: |-
            DK: MålerPlomberet.
            Indicates whether the meter is sealed.
        meterWorkConsumerBilled:
          type: boolean
          description: |-
            DK: MålerYdelseFaktureresEjer.
            Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
        supplyStatus:
          $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        meterReadingType:
          allOf:
            - $ref: '#/components/schemas/GuidField'
          nullable: true
          description: 'Description: A list of different reading methods a meter can have. Eg. "RemoteRf", "RemoteGprs", "ManualCard", "Estimated", "SlaveReading".'
        supplyDisconnectType:
          $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        gisPropertiesElectricity:
          description: MeterFrameGisPropertiesElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesElectricityModel'
        gisPropertiesHeating:
          description: MeterFrameGisPropertiesHeating
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesHeatingModel'
        gisPropertiesWater:
          description: MeterFrameGisPropertiesWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesWaterModel'
        mainBranchLineElectricity:
          description: MainBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineElectricityModel'
        reserveBranchLineElectricity:
          description: ReserveBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineElectricityModel'
        mainBranchLineWater:
          description: MainBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineWaterModel'
        reserveBranchLineWater:
          description: ReserveBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineWaterModel'
        placementCode:
          type: string
          description: 'Placement code CodeList value id.'
          format: uuid
          nullable: false
        connectionStatus:
          description: 'DK: Tilslutningsstatus.'
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        decommissioned:
          description: 'Decommissioned date.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        statusChanged:
          description: 'Latest status change date.'
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        mainBranchLineNumber:
          description: 'Main Branch Line Number.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        mainBranchLineSize:
          description: 'Main Branch Line Size.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        mainBranchLineType:
          description: 'Main Branch Line Type.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        connectedThroughMeterFrameNumber:
          description: 'DK: Placeringsbeskrivelse.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        meterNumber:
          description: 'Meter number.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        meterId:
          description: 'Meter Id.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/MeterIdNullable'
        meterInputConnectionStartDate:
          description: 'Meter input connection start date.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        hasActiveBailiffCase:
          type: boolean
          description: 'Indicates whether there is a bailiff case in Created or Active state associated with the frame.'

    SupplyTypesModel:
      type: integer
      format: int32
      minimum: 1
      maximum: 7
      description: List of possible supply types "Electricity", "Heating", "Water"
      x-enumFlags: true
      x-enumNames:
        - Electricity
        - Heating
        - Water
      enum:
        - 1
        - 2
        - 4
    AddressStatus:
      nullable: true
      type: integer
      format: int32
      minimum: 1
      maximum: 2
      description: List of possible statuses that a master data address can have "Active", "Inactive".
      x-enumNames:
        - Active
        - Inactive
      enum:
        - 1
        - 2
    AddressType:
      nullable: true
      type: integer
      format: int32
      minimum: 1
      maximum: 3
      description: List of possible address types that a MasterDataAddressDetails object can hold. Eg. "Address", "AccessAddress".
      x-enumNames:
        - Primary
        - Temporary
        - AccessAddress
      enum:
        - 1
        - 2
        - 3
    MeterFrameSupplyStatusModel:
      type: integer
      format: int32
      minimum: 1
      maximum: 2
      description: List of possible statuses that Meter Frame power supply can have "Connected", "Disconnected".
      x-enumNames:
        - Connected
        - Disconnected
      enum:
        - 1
        - 2
    MeterFrameSupplyDisconnectTypeModel:
      type: integer
      nullable: true
      format: int32
      minimum: 1
      maximum: 8
      description: List of possible types of supply disconnection. Eg. "MeterNoVoltage", "DisconnectedWithBreakerInMeter", "DisconnectedBeforeMeter", "DisconnectedInKabinet", "DisconnectedAfterMeter", "DisconnectedStation".
      x-enumNames:
        - MeterNoVoltage
        - DisconnectedWithBreakerInMeter
        - DisconnectedBeforeMeter
        - DisconnectedInKabinet
        - DisconnectedAfterMeter
        - DisconnectedStation
        - Connected
        - Unknown
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
    MeterFrameId:
      description: Technical Id of the Meter Frame.
      allOf:
        - $ref: '#/components/schemas/GuidField'
    MeterId:
      allOf:
        - $ref: '#/components/schemas/PositiveInteger'
      example: 360
      description: The technical key that uniquely identifies the Meter
    MeterIdNullable:
      allOf:
        - $ref: '#/components/schemas/PositiveIntegerNullable'
      example: 360
      description: The technical key that uniquely identifies the Meter
    ConnectionPointId:
      description: Connection Point technical identifier.
      type: string
      format: uuid
      example: 4d14817c-b622-493b-a515-284c0872d15e
    MeterFrameIdsModel:
      description: Wrapping model for collection of Meter Frame Ids.
      nullable: false
      type: object
      additionalProperties: false
      required:
        - meterFrameIds
      properties:
        meterFrameIds:
          type: array
          minItems: 1
          maxItems: 1000
          uniqueItems: true
          description: Meter frame id array.
          items:
            $ref: '#/components/schemas/MeterFrameId'
    MeterFrameWithMetadata:
      type: object
      additionalProperties: false
      description: Meter frame with metadata.
      required:
        - id
        - isFound
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        isFound:
          type: boolean
          description: Is returned for given ID.
          nullable: false
        meterFrame:
          type: object
          nullable: true
          additionalProperties: false
          description: Meter frame object.
          oneOf:
            - $ref: '#/components/schemas/GetMeterFrameModel'
    GetMeterFrameModel:
      type: object
      additionalProperties: false
      description: Get meter frame model.
      required:
        - id
        - changedByUserId
        - created
        - meterFrameNumber
        - supplyType
        - commonReading
        - meterMissing
        - meterSealed
        - meterWorkConsumerBilled
        - connectionStatus
        - rowVersion
        - connectionPointId
        - supplyStatus
        - tagAssignments
        - placementCode
        - accessInformation
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        changedByUserId:
          type: string
          description: Last change user id.
          format: uuid
        created:
          type: string
          description: |-
            DK: Oprettet.
            Creation time stamp of the Meter Frame.
          format: date-time
        meterFrameNumber:
          description: |-
            DK: Målerrammenummer.
            Number on the Meter Frame.
          allOf:
            - $ref: '#/components/schemas/ShortString'
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        commonReading:
          allOf:
            - $ref: '#/components/schemas/GuidField'
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
        meterMissing:
          type: boolean
          description: 'DK: MålerVæk.'
        placementSpecification:
          description: 'DK: Placeringsbeskrivelse.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        meterSealDate:
          type: string
          description: |-
            DK: Målerplomberingsdato.
            Indicates the date when the meter was sealed.
          format: date-time
          nullable: true
        meterSealed:
          type: boolean
          description: |-
            DK: MålerPlomberet.
            Indicates whether the meter is sealed.
        meterWorkConsumerBilled:
          type: boolean
          description: |-
            DK: MålerYdelseFaktureresEjer.
            Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
        connectionStatus:
          type: string
          description: 'DK: Tilslutningsstatus.'
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
        statusChanged:
          type: string
          description: Latest status change date.
          format: date-time
          nullable: true
        electricityAttributes:
          description: ElectricityAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameElectricityAttributesModel'
        heatingAttributes:
          description: HeatingAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameHeatingAttributesModel'
        waterAttributes:
          description: MeterFrameWaterAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameWaterAttributesModel'
        geographicalLocation:
          description: Set of geographical location properties - describing Meter Frame geo location.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/GeographicalLocationModel'
        gisPropertiesElectricity:
          description: MeterFrameGisPropertiesElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesElectricityModel'
        gisPropertiesHeating:
          description: MeterFrameGisPropertiesHeating
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesHeatingModel'
        gisPropertiesWater:
          description: MeterFrameGisPropertiesWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesWaterModel'
        mainBranchLineElectricity:
          description: Main branch line on electricity type of meter frame
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineElectricityModel'
        reserveBranchLineElectricity:
          description: Reserve branch line on electricity type of meter frame
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineElectricityModel'
        mainBranchLineWater:
          description: Main branch line on water type of meter frame
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineWaterModel'
        reserveBranchLineWater:
          description: Reserve branch line on water type of meter frame
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineWaterModel'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        connectionPointId:
          type: string
          description: ConnectionPointId.
          format: uuid
        addressId:
          type: string
          description: An UUID reference to a master data address.
          format: uuid
          nullable: true
        addressName:
          description: AddressName.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine1:
          description: AddressLine1.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine2:
          description: AddressLine2.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressStatus:
          $ref: '#/components/schemas/AddressStatus'
        addressType:
          $ref: '#/components/schemas/AddressType'
        darStatus:
          $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          $ref: '#/components/schemas/LifeCycleStatus'
        supplyStatus:
          $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/TagAssignmentModel'
        meterReadingType:
          $ref: '#/components/schemas/GuidFieldNullable'
        supplyDisconnectType:
          $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        placementCode:
          type: string
          description: 'Placement code CodeList value id.'
          format: uuid
          nullable: false
        noMeter:
          type: boolean
          description: 'DK: Målerfri.'
          nullable: false
        accessInformation:
          type: array
          description: Access information.
          maxItems: 1000
          items:
            $ref: '#/components/schemas/AccessInformationModel'
    GetMeterFrameBasicModel:
      type: object
      additionalProperties: false
      description: Get meter frame basic model.
      required:
        - id
        - meterFrameNumber
        - supplyType
        - connectionPointId
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        meterFrameNumber:
          description: |-
            DK: Målerrammenummer.
            Number on the Meter Frame.
          allOf:
            - $ref: '#/components/schemas/ShortString'
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        connectionPointId:
          type: string
          description: ConnectionPointId.
          format: uuid
    MeterFrameExistByNumberModel:
      type: object
      additionalProperties: false
      required:
        - connectionPointId
        - meterFrameNumber
      description: Model to check if a meter frame exists by its number.
      properties:
        connectionPointId:
          type: string
          description: An UUID reference to the connection point.
          format: uuid
        meterFrameNumber:
          description: Number on the Meter Frame.
          allOf:
            - $ref: '#/components/schemas/ShortString'
          example: '4268761_01'
    HistoryAsOf:
      type: object
      additionalProperties: false
      required:
        - id
        - validAsOfUtc
      description: Historical information about Meter Frame as of a specific date.
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        validAsOfUtc:
          description: UTC Date at the point when the Meter Frame was active.
          type: string
          format: date-time
          example: 2017-07-21T17:32:28Z
    GetMeterFrameExistsResult:
      type: object
      additionalProperties: false
      required:
        - exists
      description: Result indicating whether the meter frame exists.
      properties:
        exists:
          type: boolean
          description: 'Flag that indicates if the meter frame exists.'
    GetMeterInformationModel:
      type: object
      additionalProperties: false
      description: Get meter frame model.
      required:
        - meter
        - disconnectionType
      properties:
        meter:
          type: object
          nullable: false
          additionalProperties: false
          description: Meter information object.
          oneOf:
            - $ref: '#/components/schemas/MeterInformationModel'
        disconnectionType:
          description: Disconnection type.
          allOf:
            - $ref: '#/components/schemas/ShortString'
    MeterInformationModel:
      type: object
      additionalProperties: false
      description: Meter Information model.
      required:
        - meterNumber
        - numberOfDigits
        - conversionFactor
        - unitType
        - meterReadingType
      properties:
        meterNumber:
          description: 'Meter Number.'
          allOf:
            - $ref: '#/components/schemas/ShortString'
        numberOfDigits:
          description: 'Number of digits.'
          allOf:
            - $ref: '#/components/schemas/ShortString'
        conversionFactor:
          type: number
          description: 'Conversion factor.'
          format: decimal
          nullable: false
        unitType:
          description: 'Unit type.'
          allOf:
            - $ref: '#/components/schemas/ShortString'
        meterReadingType:
          description: 'Meter reading type.'
          allOf:
            - $ref: '#/components/schemas/ShortString'

    MeterFrameElectricityAttributesModel:
      type: object
      additionalProperties: false
      description: Meter frame electricity attributes model.
      required:
        - connectionType
        - breakerBeforeMeter
      properties:
        connectionType:
          type: string
          description: 'DK: Tilslutningstype.'
          format: uuid
        breakerBeforeMeter:
          type: boolean
          description: 'DK: AfbryderFørMåler.'
        powerLimitA:
          type: number
          description: 'DK: EffektgrænseA.'
          format: decimal
          nullable: true
        powerLimitKw:
          type: number
          description: 'DK: EffektgrænseKW.'
          format: decimal
          nullable: true
        productionCapacity:
          type: number
          description: 'DK: Anlægskapacitet.'
          format: decimal
          nullable: true
        purpose:
          type: string
          description: 'DK: Formål.'
          format: uuid
          nullable: true
        ratioCt:
          description: 'DK: OmsætningsforholdCT.'
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        ratioVt:
          description: 'DK: OmsætningsforholdVT.'
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        tarifConnectionPoint:
          type: string
          description: 'DK: TarifTilslutningspunkt.'
          format: uuid
          nullable: true
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
        lossFactor:
          type: number
          description: |-
            DK: NettabsFaktor.
            Nettabsfaktor som bruges på målerrammen i MDM. Vi skal lige gennemgå den fra MDM siden. Skal indgå både på fuldttidserie niveau så faktoren ganges på både 15/60 forbrug samt
            tællerstande.
            Mains loss factor used on the measuring frame in MDM. We just need to review it from the MDM page. Must be included both at full-time series level so the factor is multiplied by
            both 15/60 consumption and meter readings.
          format: decimal
          nullable: true
        meterFrameFuse:
          description: |-
            DK: MålerRammeForsikring.
            Forsikring (tarifsikring, T-ret sikring etc) som ikke er stikledningssikringen eller tilslutningsrettigheden. Kunden kan selv bestemme størrelsen på denne.
            Hvis kunden ikke har oplyst en forsikring, så er stikledningssikringen kundens forsikring.
            Kunden kan selv sikrer op eller ned (stikledningssikringen begrænser ham selvfølgelig).
            Vi har den kun i systemet for at kunne rumme data fra blanketten.
            EN:
            Insurance (tariff fuse, T-right fuse etc) which is not the branch line fuse or the connection right. The customer can decide the size of this.
            If the customer has not stated an insurance, then the branch line insurance is the customer's insurance.
            The customer can secure up or down himself (the branch line protection limits him of course).
            We only have it in the system to be able to hold data from the form.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          nullable: true
        meterDisplaySettings:
          type: string
          description: |-
            DK: VisningMåler.
            En bruger skal kunne stille krav til visning af alle målerer som installeres i målerrammen.
            Skal medfører målerrammekrav på målerammen som gennemtvinger specifik display visning.
            EN:
            A user must be able to set requirements for displaying all meters that are installed in the meter frame.
            Must entail meter frame requirements on the meter frame which enforces specific display.
          format: uuid
          nullable: true
        connectionRemark:
          type: string
          description: |-
            DK: TilslutningsBemærkning.
            Specielle forhold omkring tilslutning, f.eks. adapter, kvadrat, klemmetype etc.
            EN:
            Special conditions regarding connection, e.g. adapter, square, terminal type etc.
          format: uuid
          nullable: true
    MeterFrameHeatingAttributesModel:
      type: object
      additionalProperties: false
      description: Meter frame heating attributes model (MFPropertySetHeat).
      required:
        - calculateCooling
      properties:
        calculateCooling:
          type: boolean
          description: 'DK: BeregnAfkøling.'
        coolingLimit:
          type: number
          description: 'DK: Afkølingsgrænse.'
          format: decimal
          nullable: true
        criticalCustomer:
          type: string
          description: 'DK: KritiskKundekategori.'
          format: uuid
          nullable: true
        planEffect:
          description: 'DK: Anlægsydelse.'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        plantType:
          description: 'DK: Anlægstype.'
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        counterPlacement:
          description: Counter Placement.
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        heatMeterConnectionType:
          description: Heat Meter Connection Type.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        heatMeterDisplaySettings:
          description: Heat Meter Display Settings.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: '1975CFF1-8FC6-46ED-8D6C-FA05F950CDA9'
        returnHeatingConnected:
          type: boolean
          description: 'DK: Returvarme.'
          nullable: true
        totalAllowedAreaM2:
          description: 'Allowed total area m2'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        allowedBusinessAreaM2:
          description: 'Allowed business area m2'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        allowedStorageAreaM2:
          description: 'Allowed storage area m2'
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
    MeterFrameWaterAttributesModel:
      type: object
      additionalProperties: false
      description: Meter frame water attributes model.
      required:
        - directSprinkling
        - driveBy
      properties:
        directSprinkling:
          type: boolean
          description: DK:DirekteSprinkling.
        driveBy:
          type: string
          description: DK:DriveBy.
          format: uuid
        criticalCustomer:
          type: string
          description: DK:KritiskKundekategori.
          format: uuid
          nullable: true
        mediumCategory:
          type: string
          description: DK:MediumKategori.
          format: uuid
          nullable: true
        ownPump:
          type: boolean
          description: DK:EgenPumpe.
          nullable: true
        pressureEnhancer:
          type: boolean
          description: DK:TrykForøger.
          nullable: true
        qvkSensor:
          type: boolean
          description: DK:QvkSensor.
          nullable: true
        waterMeterDisplaySettings:
          description: Water Meter Display Settings.
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          example: '6D18E71C-49B0-4C7F-BDDB-C7BC234D183B'
        waterFlowLimit:
          type: string
          nullable: true
          description: Flow limit
          minLength: 1
          maxLength: 3
          pattern: "^(2,5|6|10|15|25|40|100|150)$"
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
    GeographicalLocationModel:
      description: This object contains information about a point on the globe, including information about the projection, source accuracy, etc.
      type: object
      additionalProperties: false
      required:
        - longitude
        - latitude
      properties:
        accuracy:
          description: The accuracy class of the point.
          allOf:
            - $ref: '#/components/schemas/OneWordStringNullable'
        name:
          description: The name of the point, e.g. Waypoint, Access Point.
          allOf:
            - $ref: '#/components/schemas/OneWordStringNullable'
        source:
          description: Source of the geographical location point.
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        technicalStandard:
          description: Technical standard.
          allOf:
            - $ref: '#/components/schemas/OneWordStringNullable'
        longitude:
          type: number
          description: Longitude - X in most spatial file formats.
          format: decimal
          nullable: false
        latitude:
          type: number
          description: Latitude - Y in most spatial file formats.
          format: decimal
          nullable: false
        elevation:
          type: number
          description: Z - elevation in some spatial formats - number of meters above the surface of the water.
          format: decimal
    DarStatus:
      nullable: true
      type: integer
      format: int32
      minimum: 1
      maximum: 3
      description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated".
      x-enumNames:
        - 'Yes'
        - 'No'
        - Temporary
      enum:
        - 1
        - 2
        - 3
    LifeCycleStatus:
      nullable: true
      type: integer
      format: int32
      minimum: 1
      maximum: 3
      description: List of possible life cycle states that the MDR system can put the address into.
      x-enumNames:
        - ToBeDeleted
        - Valid
        - UnderInvestigation
      enum:
        - 1
        - 2
        - 3
    MeterFrameWithValidityPeriodModel:
      description: Meter frame with validity period model.
      allOf:
        - $ref: '#/components/schemas/GetMeterFrameModel'
        - type: object
          additionalProperties: false
          description: Result with Meter Frames valid in selected period of time.
          properties:
            validFrom:
              description: Valid from.
              type: string
              format: date-time
            validTo:
              description: Valid to.
              type: string
              format: date-time
    AddMeterFrameModel:
      description: Add meter frame model.
      allOf:
        - $ref: '#/components/schemas/MeterFrameCommonModel'
        - type: object
          additionalProperties: false
          properties:
            supplyType:
              $ref: '#/components/schemas/SupplyTypesModel'
    MeterFrameCommonModel:
      type: object
      additionalProperties: false
      description: Meter Frame model.
      required:
        - connectionPointId
        - commonReading
        - meterMissing
        - meterSealed
        - meterWorkConsumerBilled
        - supplyStatus
        - tagAssignments
        - placementCode
        - accessInformation
      properties:
        created:
          type: string
          description: Meter Frame created date.
          format: date-time
          nullable: true
        placementCode:
          type: string
          description: 'Placement code CodeList value id.'
          format: uuid
          nullable: false
        connectionPointId:
          type: string
          format: uuid
          minLength: 1
          description: Connection point identifier.
        commonReading:
          allOf:
            - $ref: '#/components/schemas/GuidField'
          description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
        meterMissing:
          type: boolean
          description: 'DK: MålerVæk.'
        placementSpecification:
          description: 'DK: Placeringsbeskrivelse.'
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        meterSealDate:
          type: string
          description: |-
            DK: Målerplomberingsdato.
            Indicates the date when the meter was sealed.
          format: date-time
          nullable: true
        meterSealed:
          type: boolean
          description: |-
            DK: MålerPlomberet.
            Indicates whether the meter is sealed.
        meterWorkConsumerBilled:
          type: boolean
          description: |-
            DK: MålerYdelseFaktureresEjer.
            Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
        connectionStatus:
          type: string
          description: Deprecated for upsert operations. This status is calculated based on related metering point connection statuses for the meter frame. If provided, it will be ignored.
          deprecated: true
          nullable: true
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
        statusChanged:
          type: string
          description: Latest status change date.
          format: date-time
          nullable: true
        electricityAttributes:
          description: ElectricityAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameElectricityAttributesModel'
        heatingAttributes:
          description: HeatingAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameHeatingAttributesModel'
        waterAttributes:
          description: MeterFrameWaterAttributes.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameWaterAttributesModel'
        geographicalLocation:
          description: Set of geographical location properties - describing Meter Frame geo location.
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/GeographicalLocationModel'
        gisPropertiesElectricity:
          description: MeterFrameGisPropertiesElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesElectricityModel'
        gisPropertiesHeating:
          description: MeterFrameGisPropertiesHeating
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesHeatingModel'
        gisPropertiesWater:
          description: MeterFrameGisPropertiesWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MeterFrameGisPropertiesWaterModel'
        mainBranchLineElectricity:
          description: MainBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineElectricityCreateUpdateModel'
        reserveBranchLineElectricity:
          description: ReserveBranchLineElectricity
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineElectricityCreateUpdateModel'
        mainBranchLineWater:
          description: MainBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/MainBranchLineWaterModel'
        reserveBranchLineWater:
          description: ReserveBranchLineWater
          nullable: true
          type: object
          oneOf:
            - $ref: '#/components/schemas/ReserveBranchLineWaterModel'
        addressId:
          type: string
          description: An UUID reference to a master data address.
          format: uuid
          nullable: true
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/AddTagAssignmentModel'
        supplyStatus:
          $ref: '#/components/schemas/MeterFrameSupplyStatusModel'
        meterReadingType:
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
          description: 'A list of different reading methods a meter can have.'
        supplyDisconnectType:
          $ref: '#/components/schemas/MeterFrameSupplyDisconnectTypeModel'
        noMeter:
          type: boolean
          description: 'DK: Målerfri.'
        accessInformation:
          type: array
          description: Access information.
          maxItems: 1000
          items:
            $ref: '#/components/schemas/AccessInformationModel'
    UpdateMeterFrameModel:
      description: Update meter frame model.
      allOf:
        - $ref: '#/components/schemas/MeterFrameCommonModel'
        - type: object
          description: Model for update Meter Frame.
          additionalProperties: false
          properties:
            rowVersion:
              $ref: '#/components/schemas/RowVersion'
    ExternalEquipmentAssignmentModel:
      type: object
      additionalProperties: false
      description: Assignment of external equipment model.
      required:
        - id
        - installedDate
        - equipment
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        installedDate:
          type: string
          format: date-time
          description: Date of installation.
        removedDate:
          type: string
          format: date-time
          nullable: true
          description: Date of removal.
        equipment:
          description: Equipment.
          type: object
          oneOf:
            - $ref: '#/components/schemas/EquipmentModel'
    EquipmentModel:
      type: object
      additionalProperties: false
      description: Equipment model.
      required:
        - installationPossibility
        - equipmentNumber
        - equipmentType
        - vendor
      properties:
        installationPossibility:
          $ref: '#/components/schemas/InstallationPossibilityModel'
        equipmentNumber:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Equipment number.
        equipmentType:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Equipment type.
        vendor:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Vendor.
        description:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
          nullable: true
          description: Description.
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
    InstallationPossibilityModel:
      type: integer
      format: int32
      minimum: 0
      maximum: 2
      description: Installation possibility that equipment can have "All", "ConnectionPoint", "MeterFrame".
      x-enumNames:
        - All
        - ConnectionPoint
        - MeterFrame
      enum:
        - 0
        - 1
        - 2
    ExternalEquipmentAssignmentUpsertModel:
      type: object
      additionalProperties: false
      description: Model for insert and update external equipment.
      required:
        - rowVersion
        - installedDate
        - equipment
      properties:
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        installedDate:
          type: string
          format: date-time
          description: Date of installation.
        removedDate:
          type: string
          format: date-time
          nullable: true
          description: Date of removal.
        equipment:
          type: object
          description: Equipment model.
          oneOf:
            - $ref: '#/components/schemas/EquipmentModel'
    InternalEquipmentAssignmentModel:
      type: object
      additionalProperties: false
      description: Assignment of internal equipment model.
      required:
        - id
        - internalEquipmentId
        - installedDate
        - equipment
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        internalEquipmentId:
          description: Internal equipment id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        installedDate:
          type: string
          format: date-time
          description: Date of installation.
        removedDate:
          type: string
          format: date-time
          nullable: true
          description: Date of removal.
        equipment:
          type: object
          description: Equipment model.
          oneOf:
            - $ref: '#/components/schemas/EquipmentModel'
    InternalEquipmentAssignmentUpsertModel:
      type: object
      additionalProperties: false
      description: Model for insert and update internal equipment.
      required:
        - internalEquipmentId
        - rowVersion
        - installedDate
      properties:
        internalEquipmentId:
          description: Internal equipment id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        installedDate:
          type: string
          format: date-time
          description: Date of installation.
        removedDate:
          type: string
          format: date-time
          nullable: true
          description: Date of removal.
    BatchUpsertInternalEquipmentsModel:
      type: object
      additionalProperties: false
      description: Model for batch insert and update internal equipment.
      required:
        - internalEquipments
      properties:
        internalEquipments:
          type: array
          description: Internal equipments array.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/AddInternalEquipmentModel'
    AddInternalEquipmentModel:
      description: Add internal equipment model.
      allOf:
        - $ref: '#/components/schemas/InternalEquipmentCommonModel'
        - type: object
          additionalProperties: false
          description: Add internal equipment model.
          properties:
            installationPossibility:
              $ref: '#/components/schemas/InstallationPossibilityModel'
            equipmentNumber:
              allOf:
                - $ref: '#/components/schemas/ShortStringObsolete'
              description: Equipment serial number from the manufacture.
    InternalEquipmentCommonModel:
      type: object
      additionalProperties: false
      description: Internal equipment common model.
      required:
        - equipmentType
        - vendor
      properties:
        equipmentType:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Equipment type.
        vendor:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Vendor.
        description:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
          nullable: true
          description: Description.
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
    PagedResultOfGetInternalEquipmentSearchModel:
      description: Paged results of get internal equipment search model.
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          additionalProperties: false
          properties:
            results:
              type: array
              nullable: true
              description: Results.
              maxItems: 1000000
              items:
                $ref: '#/components/schemas/GetInternalEquipmentSearchModel'
    GetInternalEquipmentSearchModel:
      description: Get internal equipment search model.
      allOf:
        - $ref: '#/components/schemas/GetInternalEquipmentByIdModel'
        - type: object
          additionalProperties: false
    GetInternalEquipmentByIdModel:
      type: object
      additionalProperties: false
      description: Get Internal Equipment by id result model.
      required:
        - id
        - installationPossibility
        - equipmentNumber
        - equipmentType
        - vendor
        - rowVersion
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        installationPossibility:
          $ref: '#/components/schemas/InstallationPossibilityModel'
        equipmentNumber:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Equipment number.
        equipmentType:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Equipment type.
        vendor:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Vendor.
        description:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
          nullable: true
          description: Description.
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
    UpdateInternalEquipmentModel:
      description: Update internal equipment model.
      allOf:
        - $ref: '#/components/schemas/InternalEquipmentCommonModel'
        - type: object
          additionalProperties: false
          properties:
            rowVersion:
              $ref: '#/components/schemas/RowVersion'
    AddInternalEquipmentNoteModel:
      description: Add internal equipment note model.
      allOf:
        - $ref: '#/components/schemas/InternalEquipmentNoteModel'
        - type: object
          additionalProperties: false
    InternalEquipmentNoteModel:
      type: object
      additionalProperties: false
      description: Internal Equipment note model.
      required:
        - note
      properties:
        note:
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
          description: Note.
    UpdateInternalEquipmentNoteModel:
      description: Update internal equipment note model.
      allOf:
        - $ref: '#/components/schemas/InternalEquipmentNoteModel'
        - type: object
          additionalProperties: false
          description: Update model for Internal Equipment.
          properties:
            rowVersion:
              $ref: '#/components/schemas/RowVersion'
    PagedResultOfGetConnectionPointsSearchModel:
      description: Paged result of get connection points search model.
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          additionalProperties: false
          properties:
            results:
              type: array
              nullable: true
              description: Results.
              maxItems: 1000000
              items:
                $ref: '#/components/schemas/GetConnectionPointsSearchModel'

    PagedResultOfGetMeterInputConnectionsSearchModel:
      description: Paged result of get meter input connections search model.
      allOf:
        - $ref: '#/components/schemas/PagedResultBase'
        - type: object
          additionalProperties: false
          properties:
            results:
              type: array
              nullable: true
              description: Results.
              maxItems: 1000000
              items:
                $ref: '#/components/schemas/GetMeterInputConnectionsSearchModel'

    GetInternalEquipmentNoteByIdModel:
      description: Get internal equipment note by id model.
      allOf:
        - $ref: '#/components/schemas/InternalEquipmentNoteModel'
        - type: object
          additionalProperties: false
          properties:
            id:
              $ref: '#/components/schemas/GuidId'

    GetMeterInputConnectionsSearchModel:
      description: Get connection points search model.
      allOf:
        - $ref: '#/components/schemas/MeterInputConnectionModel'
        - type: object
          additionalProperties: false
          properties:
            connectionPointId:
              $ref: '#/components/schemas/ConnectionPointId'
            connectionPointNumber:
              allOf:
                - $ref: '#/components/schemas/ShortStringObsolete'
              description: Number of the ConnectionPoint.
              example: "20000001"
            meterFrameNumber:
              allOf:
                - $ref: '#/components/schemas/ShortString'
              description: Meter Frame number.
              nullable: false
              example: 'aa123'

    GetConnectionPointsSearchModel:
      description: Get connection points search model.
      allOf:
        - $ref: '#/components/schemas/GetConnectionPointsSearchQueryResultBase'
        - type: object
          additionalProperties: false
          properties:
            supplyType:
              $ref: '#/components/schemas/SupplyTypesModel'
            electricityAttributesConnectionPointCategoryValue:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
              nullable: true
            electricityAttributesInstallationTypeValue:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
              nullable: true
            electricityAttributesConnectionStatus:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
              nullable: true
            electricityAttributesConsumerCategory:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.
              nullable: true
            electricityAttributesNetSettlementGroup:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: This field register the net settlement group, which is also used in the market communication (DataHub).
              nullable: true
            electricityAttributesGridAreaId:
              description: Grid area id.
              allOf:
                - $ref: '#/components/schemas/OneWordStringNullable'
            heatingAttributesConnectionStatus:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
              nullable: true
            heatingAttributesConnectionPointCategoryValue:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific codelist.
              nullable: true
            heatingAttributesInstallationTypeValue:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Defines type of installation type. Eg. For apartments, single households, Industrial, Agricultural.
              nullable: true
            heatingAttributesHeatWaterHeater:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: List of different hot water heating controls that can be installed.
              nullable: true
            heatingAttributesWaterHeaterType:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: List of different water heating types that can be installed.
              nullable: true
            heatingAttributesHeatPlantType:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Lists the different plant types.
              nullable: true
            heatingAttributesHeatExchange:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: List of different heat exchanger options.
              nullable: true
            waterAttributesConnectionStatus:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
              nullable: true
            waterAttributesConnectionPointCategoryValue:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
              nullable: true
            waterAttributesInstallationTypeValue:
              allOf:
                - $ref: '#/components/schemas/LongStringObsoleteNullable'
              description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
              nullable: true
    GetConnectionPointsSearchQueryResultBase:
      type: object
      additionalProperties: false
      description: Get connection points search query result base.
      required:
        - id
        - connectionPointNumber
        - created
        - rowVersion
        - changedByUserId
        - createdWithoutInstallationForm
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        connectionPointNumber:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Number of the ConnectionPoint.
          example: "20000001"
        created:
          type: string
          description: Creation time stamp of the ConnectionPoint.
          format: date-time
        description:
          allOf:
            - $ref: '#/components/schemas/LongStringObsoleteNullable'
          description: Custom description.
          nullable: true
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        changedByUserId:
          description: ID of a user who changed the entity last time.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        address:
          description: An UUID reference to a master data address.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        addressLine1:
          description: AddressLine1.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine2:
          description: AddressLine2.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressStatus:
          $ref: '#/components/schemas/AddressStatus'
        addressType:
          $ref: '#/components/schemas/AddressType'
        darStatus:
          $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          $ref: '#/components/schemas/LifeCycleStatus'
        alternativeInstallationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        installationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Installation number.
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributesDeMasterDataForms:
          description: DEMasterDataForm that comes from the settlement calculation.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/Integer'
        electricityAttributesInstallationDescription:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        electricityAttributesTemporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
          nullable: true
        electricityAttributesTemporaryUntil:
          type: string
          description: |-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
            grid company.
          format: date-time
          nullable: true
        electricityAttributesFlexAttributeObject:
          description: |-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        heatingAttributesInstallationDescription:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        heatingAttributesNumberOfWaterHeater:
          description: The number of Water heaters.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/Integer'
        heatingAttributesFlexAttributeObject:
          description: |-
            An UUID reference to a settlement object that is used to register flexible attributes about the connection point.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        waterAttributesTemporaryUntil:
          type: string
          description: |-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
            grid company.
          format: date-time
          nullable: true
        waterAttributesInstallationDescription:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        waterAttributesTemporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
          nullable: true
        waterAttributesFlexAttributeObject:
          description: |-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form.
          type: boolean
          nullable: false
    PagedResponseOfGetPagedConnectionPointsMasterDataModel:
      type: object
      additionalProperties: false
      description: Paged response of get paged connection points master data model.
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Current page.
        pageSize:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Page size.
        totalPages:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Total pages.
        totalRows:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          description: Total rows.
        results:
          type: array
          description: Results.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/GetPagedConnectionPointsMasterDataModel'
    GetPagedConnectionPointsMasterDataModel:
      type: object
      additionalProperties: false
      description: Get paged connection points master data model.
      required:
        - id
        - connectionPointNumber
        - created
        - supplyType
        - createdWithoutInstallationForm
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        connectionPointNumber:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Number of the ConnectionPoint.
          example: "20000001"
        created:
          type: string
          description: Creation time stamp of the ConnectionPoint.
          format: date-time
        description:
          allOf:
            - $ref: '#/components/schemas/LongStringObsoleteNullable'
          description: Custom description.
          nullable: true
        address:
          description: An UUID reference to a master data address.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        addressLine1:
          description: AddressLine1.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine2:
          description: AddressLine2.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        alternativeInstallationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        installationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Installation number.
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form.
          type: boolean
          nullable: false
    GetConnectionPointModel:
      type: object
      additionalProperties: false
      description: Get connection point model.
      required:
        - id
        - connectionPointNumber
        - created
        - rowVersion
        - changedByUserId
        - supplyType
        - tagAssignments
        - priceGroupExcluded
        - address
        - addressLine1
        - addressLine2
        - createdWithoutInstallationForm
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        connectionPointNumber:
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          description: Number of the ConnectionPoint.
          example: "20000001"
        created:
          type: string
          description: Creation time stamp of the ConnectionPoint.
          format: date-time
        description:
          allOf:
            - $ref: '#/components/schemas/LongStringObsoleteNullable'
          description: Custom description.
          nullable: true
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
        changedByUserId:
          description: ID of a user who changed the entity last time.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        address:
          description: An UUID reference to a master data address.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        addressName:
          description: AddressName.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine1:
          description: AddressLine1.
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressLine2:
          description: AddressLine2.
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
        addressStatus:
          $ref: '#/components/schemas/AddressStatus'
        addressType:
          $ref: '#/components/schemas/AddressType'
        darStatus:
          $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          $ref: '#/components/schemas/LifeCycleStatus'
        alternativeInstallationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        installationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Installation number.
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/TagAssignmentModel'
        priceGroupExcluded:
          description: Used to indicate if a connectiont point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributes:
          nullable: true
          description: Electricity attributes.
          allOf:
            - $ref: '#/components/schemas/ElectricityAttributesGetModel'
        heatingAttributes:
          nullable: true
          description: Heating attributes.
          allOf:
            - $ref: '#/components/schemas/HeatingAttributesGetModel'
        waterAttributes:
          nullable: true
          description: Water attributes.
          allOf:
            - $ref: '#/components/schemas/WaterAttributesGetModel'
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form.
          type: boolean
          nullable: false
    TagAssignmentModel:
      type: object
      additionalProperties: false
      description: Tag assignment model.
      required:
        - id
        - assigneeId
        - tagCodeListValueId
        - tagCodeListValue
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        assigneeId:
          description: Assignee id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        tagCodeListValueId:
          description: Tag code list value id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        tagCodeListValue:
          allOf:
            - $ref: '#/components/schemas/LongStringObsoleteNullable'
          description: Tag code list value.
    ElectricityAttributesGetModel:
      type: object
      additionalProperties: false
      description: Model used for returning connection points electricity attributes.
      allOf:
        - $ref: '#/components/schemas/ElectricityAttributesBaseModel'
        - type: object
          additionalProperties: false
          description: Additional properties relevant only for response information about connection point electricity attributes.
          required:
            - connectionStatus
          properties:
            connectionStatus:
              description: This status is calculated based on the status of the parent metering point for the connection point.
              allOf:
                - $ref: '#/components/schemas/GuidField'

    ElectricityAttributesUpsertModel:
      type: object
      additionalProperties: false
      description: Electricity attributes upsert model.
      allOf:
        - $ref: '#/components/schemas/ElectricityAttributesBaseModel'
        - type: object
          description: Additional properties relevant only for create or update connection point electricity attributes.
          additionalProperties: false
          properties:
            connectionStatus:
              description: Deprecated for upsert operations. This status is calculated based on the status of the parent metering point for the connection point. If provided, it will be ignored.
              deprecated: true
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'

    ElectricityAttributesBaseModel:
      type: object
      additionalProperties: false
      description: Electricity attributes model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - temporary
      properties:
        connectionPointCategoryValue:
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        installationTypeValue:
          description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        consumerCategory:
          type: string
          description: Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.
          format: uuid
          nullable: true
        deMasterDataForms:
          description: DEMasterDataForm that comes from the settlement calculation.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/Integer'
        installationDescription:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        netSettlementGroup:
          description: This field register the net settlement group, which is also used in the market communication (DataHub).
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        gridAreaId:
          description: Grid area id.
          allOf:
            - $ref: '#/components/schemas/OneWordStringNullable'
        temporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
        temporaryUntil:
          type: string
          description: |-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
            grid company.
          format: date-time
          nullable: true
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    HeatingAttributesGetModel:
      type: object
      additionalProperties: false
      description: Heating attributes get model.
      allOf:
        - $ref: '#/components/schemas/HeatingAttributesBaseModel'
        - type: object
          description: Additional properties relevant for getting connection point heating attributes.
          additionalProperties: false
          required:
            - connectionStatus
          properties:
            connectionStatus:
              description: Connection status.
              allOf:
                - $ref: '#/components/schemas/GuidField'

    HeatingAttributesUpsertModel:
      type: object
      additionalProperties: false
      description: Heating attributes upsert model.
      allOf:
        - $ref: '#/components/schemas/HeatingAttributesBaseModel'
        - type: object
          description: Additional properties relevant for creating or updating connection point heating attributes.
          additionalProperties: false
          properties:
            connectionStatus:
              description: Deprecated for upsert operations. This status is calculated based on the status of the parent metering point for the connection point. If provided, it will be ignored.
              deprecated: true
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'

    HeatingAttributesBaseModel:
      type: object
      additionalProperties: false
      description: Heating attributes model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - heatWaterHeater
      properties:
        connectionPointCategoryValue:
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific codelist.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        installationTypeValue:
          description: Defines type of installation type. Eg. For apartments, single households, Industrial, Agricultural.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        installationDescription:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        numberOfWaterHeater:
          description: The number of Water heaters.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          nullable: true
        heatWaterHeater:
          description: List of different hot water heating controls that can be installed.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        waterHeaterType:
          description: List of different water heating types that can be installed.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        heatPlantType:
          description: Lists the different plant types.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        heatExchange:
          description: List of different heat exchanger options.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true

    WaterAttributesGetModel:
      type: object
      additionalProperties: false
      description: Water attributes get model.
      allOf:
        - $ref: '#/components/schemas/WaterAttributesBaseModel'
        - type: object
          description: Additional properties relevant for getting connection point water attributes.
          additionalProperties: false
          required:
            - connectionStatus
          properties:
            connectionStatus:
              description: Connection status.
              allOf:
                - $ref: '#/components/schemas/GuidField'

    WaterAttributesUpsertModel:
      type: object
      additionalProperties: false
      description: Water attributes upsert model.
      allOf:
        - $ref: '#/components/schemas/WaterAttributesBaseModel'
        - type: object
          description: Additional properties relevant for creating or updating connection point water attributes.
          additionalProperties: false
          properties:
            connectionStatus:
              description: Deprecated for upsert operations. This status is calculated based on the status of the parent metering point for the connection point. If provided, it will be ignored.
              deprecated: true
              allOf:
                - $ref: '#/components/schemas/GuidFieldNullable'

    WaterAttributesBaseModel:
      type: object
      additionalProperties: false
      description: Water attributes base model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - temporary
      properties:
        connectionPointCategoryValue:
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific code list.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        installationTypeValue:
          description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        temporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
        temporaryUntil:
          type: string
          description: |-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
            grid company.
          format: date-time
          nullable: true
        installationDescription:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
        flexAttributeObject:
          $ref: '#/components/schemas/FlexAttributeObject'
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    GetConnectionPointWithValidityPeriodModel:
      description: Get connection point with validity period model.
      allOf:
        - $ref: '#/components/schemas/GetConnectionPointModel'
        - type: object
          additionalProperties: false
          properties:
            validFrom:
              type: string
              format: date-time
              description: Valid from date.
            validTo:
              type: string
              format: date-time
              description: Valid until date.
    AddConnectionPointModel:
      type: object
      additionalProperties: false
      description: Add connection point model.
      required:
        - supplyType
        - tagAssignments
        - priceGroupExcluded
        - address
      properties:
        alternativeInstallationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        installationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Installation number.
        description:
          allOf:
            - $ref: '#/components/schemas/LongStringObsoleteNullable'
          description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.
          nullable: true
        address:
          description: An UUID reference to a master data address.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/AddTagAssignmentModel'
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributes:
          nullable: true
          type: object
          description: Electricity attributes.
          oneOf:
            - $ref: '#/components/schemas/ElectricityAttributesUpsertModel'
        heatingAttributes:
          nullable: true
          type: object
          description: Heating attributes.
          oneOf:
            - $ref: '#/components/schemas/HeatingAttributesUpsertModel'
        waterAttributes:
          nullable: true
          type: object
          description: Water attributes.
          oneOf:
            - $ref: '#/components/schemas/WaterAttributesUpsertModel'
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form. Defaults to true if left empty.
          type: boolean
          nullable: true
    ImportConnectionPointModel:
      description: Import ConnectionPoint model.
      allOf:
        - $ref: '#/components/schemas/AddConnectionPointModel'
        - type: object
          additionalProperties: false
          properties:
            connectionPointNumber:
              allOf:
                - $ref: '#/components/schemas/ShortStringObsolete'
              description: The ConnectionPoint Number.
              example: "2000001"
            created:
              type: string
              nullable: true
              description: |-
                DK: Oprettet.
                Creation time stamp of the Connection Point.
              format: date-time

    ImportConnectionRightsModel:
      description: Import MeterFrame model.
      allOf:
        - $ref: "#/components/schemas/AddConnectionRightModel"

    ImportMeterFrameModel:
      description: Import MeterFrame model.
      allOf:
        - $ref: '#/components/schemas/AddMeterFrameModel'
        - type: object
          additionalProperties: false
          properties:
            meterFrameNumber:
              allOf:
                - $ref: '#/components/schemas/ShortString'
              description: The MeterFrame Number.
              example: "2000003-4"
            created:
              type: string
              nullable: true
              description: |-
                DK: Oprettet.
                Creation time stamp of the Meter Frame.
              format: date-time
    AddTagAssignmentModel:
      type: object
      additionalProperties: false
      description: Add tag assignment model.
      required:
        - tagCodeListValueId
      properties:
        tagCodeListValueId:
          description: Tag code list value identifier.
          allOf:
            - $ref: '#/components/schemas/GuidField'
    UpdateConnectionPointModel:
      type: object
      additionalProperties: false
      description: Update connection point model.
      required:
        - supplyType
        - tagAssignments
        - priceGroupExcluded
        - address
        - rowVersion
      properties:
        alternativeInstallationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
        installationNumber:
          allOf:
            - $ref: '#/components/schemas/MediumStringObsoleteNullable'
          description: Installation number.
        description:
          allOf:
            - $ref: '#/components/schemas/LongStringObsoleteNullable'
          description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.
          nullable: true
        address:
          description: An UUID reference to a master data address.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        supplyType:
          $ref: '#/components/schemas/SupplyTypesModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/UpdateTagAssignmentModel'
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        electricityAttributes:
          nullable: true
          type: object
          description: Electricity attributes.
          oneOf:
            - $ref: '#/components/schemas/ElectricityAttributesUpsertModel'
        heatingAttributes:
          nullable: true
          type: object
          description: Heating attributes.
          oneOf:
            - $ref: '#/components/schemas/HeatingAttributesUpsertModel'
        waterAttributes:
          nullable: true
          type: object
          description: Water attributes.
          oneOf:
            - $ref: '#/components/schemas/WaterAttributesUpsertModel'
        createdWithoutInstallationForm:
          description: >-
            Indicates whether the connection point was created without an installation form. Defaults to true if left empty.
          type: boolean
          nullable: true
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
    UpdateTagAssignmentModel:
      type: object
      additionalProperties: false
      description: Udpate tag assignment model.
      required:
        - tagCodeListValueId
      properties:
        id:
          description: UUID Id of the Resource.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidId'
        tagCodeListValueId:
          description: Tag code list value id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
    GetConnectionPointNoteByConnectionPointIdModel:
      description: Get connection point note by connection point id model.
      allOf:
        - $ref: '#/components/schemas/GetConnectionPointNoteByIdModel'
        - type: object
          additionalProperties: false
    GetConnectionPointNoteByIdModel:
      type: object
      additionalProperties: false
      description: Get connection point note by id model.
      required:
        - id
        - note
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        note:
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
          description: Note.
    AddConnectionPointNoteModel:
      description: Add connection point node model.
      allOf:
        - $ref: '#/components/schemas/ConnectionPointNoteModel'
        - type: object
          additionalProperties: false
    ConnectionPointNoteModel:
      type: object
      additionalProperties: false
      description: Connection point note model.
      required:
        - note
      properties:
        note:
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
          description: Note.
    UpdateConnectionPointNoteModel:
      description: Update connection point note model.
      allOf:
        - $ref: '#/components/schemas/ConnectionPointNoteModel'
        - type: object
          additionalProperties: false
          properties:
            rowVersion:
              $ref: '#/components/schemas/RowVersion'
    TagAssignmentUpsertModel:
      type: object
      additionalProperties: false
      description: Tag assignment upsert model.
      required:
        - tagId
        - rowVersion
      properties:
        tagId:
          description: Tag identifier.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
    GetMeterFrameNoteByMeterFrameIdModel:
      description: Get meter frame note by meter frame id model.
      allOf:
        - $ref: '#/components/schemas/GetMeterFrameNoteByIdModel'
        - type: object
          additionalProperties: false
    GetMeterFrameNoteByIdModel:
      type: object
      additionalProperties: false
      description: Get meter frame note by id model.
      required:
        - id
        - note
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        note:
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
          description: Note.
    AddMeterFrameNoteModel:
      description: Add meter frame model.
      allOf:
        - $ref: '#/components/schemas/MeterFrameNoteModel'
        - type: object
          additionalProperties: false
    MeterFrameNoteModel:
      type: object
      additionalProperties: false
      description: Meter frame note model.
      required:
        - note
      properties:
        note:
          allOf:
            - $ref: '#/components/schemas/LongStringObsolete'
          description: Note.
    UpdateMeterFrameNoteModel:
      description: Update meter frame model.
      allOf:
        - $ref: '#/components/schemas/MeterFrameNoteModel'
        - type: object
          additionalProperties: false
          properties:
            rowVersion:
              $ref: '#/components/schemas/RowVersion'
    GetRegisterRequirementResponseBody:
      type: object
      additionalProperties: false
      description: Paged result of register requirement collection.
      required:
        - currentPage
        - pageSize
        - totalPages
        - totalRows
        - results
      properties:
        currentPage:
          description: Current page number.
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
        pageSize:
          description: Page size.
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
        totalPages:
          description: Number of pages.
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
        totalRows:
          description: Number of total rows.
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
        results:
          description: Array with result models.
          type: array
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/RegisterRequirementModel'
    RegisterRequirementModel:
      type: object
      additionalProperties: false
      description: Register Requirement model.
      required:
        - id
        - meterFrameId
        - registerRequirementType
        - name
        - meteringComponentId
      properties:
        id:
          description: Register Requirement internal identifier.
          example: 1234
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
        meterFrameId:
          description: Meter Frame id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        registerRequirementType:
          $ref: '#/components/schemas/RegisterRequirementTypeModel'
        name:
          description: |-
            DK: Navn.
            Name of requirement (typically from MeteringComponent value list).
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          nullable: false
        meteringComponentId:
          description: |-
            DK: MålingsKomponentId.
            Shared value list with Meter domain.
            The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
            It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          nullable: true
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        mainMeterFrameId:
          description: Reference to the main Meter Frame
          allOf:
            - $ref: "#/components/schemas/GuidFieldNullable"
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        mainMeterFrameNumber:
          description: Extended reference to the main Meter Frame - Meter Frame Number
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          example: MF-001122
        inputNumber:
          description: Input number
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 1
        mainMeterFrameRegisterRequirementId:
          description: |-
            DK: HovedMalerrammeRegisterKravId.
            Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          nullable: true
        mainMeterFrameRegisterRequirementName:
          description: Used to refer to the main meter register requirement.
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
          nullable: true
        outsourcedToMeterFrameRegisterRequirementId:
          description: |-
            DK: HjemtagesAfMalerrammeRegisterKravId.
            Can refer to another meter frame which is actually the one that receives data through an external input.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          nullable: true
        outsourcedToMeterFrameRegisterRequirementName:
          description: Used to refer to another meter frame which is actually the one that receives data through an external input.
          allOf:
            - $ref: '#/components/schemas/ShortStringObsoleteNullable'
          nullable: true
        futureRequirement:
          description: |-
            DK: FremtidigtRegisterKra
            Indicates whether the register requirement should only be a future requirement. Default false.
            If set to true Validity period will be ignored.
          type: boolean
          nullable: false
        meterReadingRequired:
          description: |-
            DK: AflæsningPåkrævet
            Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
          type: boolean
          nullable: false
        requiredFrom:
          description: |-
            DK: GyldigFra.
            Indicates when the meter must meet the requirement.
            format: date-time
          type: string
          format: date-time
          nullable: true
        requiredUntil:
          description: |-
            DK: GyldigTil.
            Indicates from when the meter does not have to meet the requirement.
          type: string
          format: date-time
          nullable: true
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
    AddRegisterRequirementModel:
      type: object
      additionalProperties: false
      description: Model for adding Register Requirement.
      required:
        - registerRequirementType
        - name
        - meteringComponentId
        - futureRequirement
        - meterReadingRequired
        - meterFrameId
      properties:
        registerRequirementType:
          $ref: '#/components/schemas/RegisterRequirementTypeModel'
        name:
          description: |-
            DK: Navn.
            Name of requirement (typically from MeteringComponent value list).
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          nullable: false
        meteringComponentId:
          description: |-
            DK: MålingsKomponentId.
            Shared value list with Meter domain.
            The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
            It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          nullable: true
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        inputNumber:
          description: Input number
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 1
        mainMeterFrameId:
          description: Reference to the main Meter Frame
          allOf:
            - $ref: "#/components/schemas/GuidFieldNullable"
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        mainMeterFrameRegisterRequirementId:
          description: |-
            DK: HovedMalerrammeRegisterKravId.
            Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          nullable: true
        outsourcedToMeterFrameRegisterRequirementId:
          description: |-
            DK: HjemtagesAfMalerrammeRegisterKravId.
            Can refer to another meter frame which is actually the one that receives data through an external input.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          nullable: true
        futureRequirement:
          description: |-
            DK: FremtidigtRegisterKra
            Indicates whether the register requirement should only be a future requirement. Default false.
            If set to true Validity period will be ignored.
          type: boolean
          nullable: false
        meterReadingRequired:
          description: |-
            DK: AflæsningPåkrævet
            Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
          type: boolean
          nullable: false
        requiredFrom:
          description: |-
            DK: GyldigFra.
            Indicates when the meter must meet the requirement.
          type: string
          format: date-time
          nullable: true
        requiredUntil:
          description: |-
            DK: GyldigTil.
            Indicates from when the meter does not have to meet the requirement.
          type: string
          format: date-time
          nullable: true
        meterFrameId:
          description: |-
            Meter Frame id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
    UpdateRegisterRequirementModel:
      type: object
      additionalProperties: false
      description: Model for updating Register Requirement.
      required:
        - name
        - futureRequirement
        - meterReadingRequired
        - registerRequirementType
      properties:
        name:
          description: |-
            DK: Navn.
            Name of requirement (typically from MeteringComponent value list).
          allOf:
            - $ref: '#/components/schemas/ShortStringObsolete'
          nullable: false
        registerRequirementType:
          $ref: '#/components/schemas/RegisterRequirementTypeModel'
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          nullable: true
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        inputNumber:
          description: Input number
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          example: 1
        mainMeterFrameId:
          description: Reference to the main Meter Frame
          allOf:
            - $ref: "#/components/schemas/GuidFieldNullable"
          example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8
        mainMeterFrameRegisterRequirementId:
          description: |-
            DK: HovedMalerrammeRegisterKravId.
            Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          nullable: true
        outsourcedToMeterFrameRegisterRequirementId:
          description: |-
            DK: HjemtagesAfMalerrammeRegisterKravId.
            Can refer to another meter frame which is actually the one that receives data through an external input.
          allOf:
            - $ref: '#/components/schemas/IntegerNullable'
          nullable: true
        futureRequirement:
          description: |-
            DK: FremtidigtRegisterKra
            Indicates whether the register requirement should only be a future requirement. Default false.
            If set to true Validity period will be ignored.
          type: boolean
          nullable: false
        meterReadingRequired:
          description: |-
            DK: AflæsningPåkrævet
            Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
          type: boolean
          nullable: false
        requiredFrom:
          description: |-
            DK: GyldigFra.
            Indicates when the meter must meet the requirement.
          type: string
          format: date-time
          nullable: true
        requiredUntil:
          description: |-
            DK: GyldigTil.
            Indicates from when the meter does not have to meet the requirement.
          type: string
          format: date-time
          nullable: true
        rowVersion:
          $ref: '#/components/schemas/RowVersion'
    RegisterRequirementTypeModel:
      nullable: false
      description: List of possible register requirement "ControlRequirement", "MarketRequirement".
      type: integer
      format: int32
      minimum: 1
      maximum: 2
      x-enumFlags: false
      x-enumNames:
        - ControlRequirement
        - MarketRequirement
      enum:
        - 1
        - 2

    MeterInputConnectionModel:
      type: object
      additionalProperties: false
      description: Model of Meter Input Connection.
      required:
        - id
        - meterFrameId
        - meterId
        - meterInputNumber
        - meterInputType
        - meterNumber
        - meterType
        - fulfillsRequirements
        - validFrom
      properties:
        id:
          $ref: '#/components/schemas/GuidId'
        meterFrameId:
          $ref: '#/components/schemas/MeterFrameId'
        meterId:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          example: 360
          description: The technical key that uniquely identifies the Meter
        meterInputNumber:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          example: 360
          description: This field is part of the MeterConfiguration in the Meter domain.
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        meterNumber:
          description: |-
            This is the meter number that is communicated to the DataHub.
            The meter number can deviate from the meter number that can be seen on the physical meter,
            though according to guidelines they should match.
          allOf:
            - $ref: "#/components/schemas/OneWordString"
          example: "8975439870435"
        meterType:
          allOf:
            - $ref: "#/components/schemas/ShortString"
          example: "meter type"
          description: Values comes from the Meter entity in the Meter domain. The field defines the type of Meter assigned.
        fulfillsRequirements:
          type: boolean
          example: true
          description: Boolean updated by validation method that indicates whether Meter fulfils the register requirements.
        lastChecked:
          type: string
          format: date-time
          nullable: true
          example: "2022-09-07T09:50:30.870Z"
          description: Indicates time of the last validation check.
        validationStatus:
          allOf:
            - $ref: "#/components/schemas/ShortStringNullable"
          description: Status text on the latest validation check.
        validFrom:
          type: string
          format: date-time
          nullable: false
          example: "2022-09-07T09:50:30.870Z"
          description: Defines the validity period, when the meter is assigned to the meter frame.
        validUntil:
          type: string
          format: date-time
          nullable: true
          example: "2022-09-07T09:50:30.870Z"
          description: Defines the validity period, when the meter is assigned to the meter frame.
        meterAttributeRequirementsValidationPassed:
          description: Validation result for Meter on Meter Attributes Requirements.
          type: boolean
          example: true
          nullable: true
        meterAttributeRequirementsInvalidAttributes:
          type: array
          description: Collection of properties failing to fulfill meter attribute requirements.
          nullable: true
          maxItems: 1000
          items:
            type: string
            pattern: "^.*$"
            maxLength: 100
            description: Name of Meter property not meeting meter attribute requirements.
            nullable: false
            example: CableLength
        meterAttributeRequirementsLastValidationDate:
          type: string
          format: date-time
          nullable: true
          example: "2022-09-07T09:50:30.870Z"
          description: Last performed meter attribute requirements validation date.
        displayConfiguration:
          description: |-
            Display configuration. Possible values:
            de23f9df-a4f0-4591-b743-1f7d1b00a351 - Not Possible
            79f3ec96-3bd4-41f8-bbc2-aa73846c86a7 - Optical Eye
            098a905e-dfe0-4842-b7ce-c58a756d8212 - Direct Control
          example: de23f9df-a4f0-4591-b743-1f7d1b00a351
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        registerConfiguration:
          description: |-
            Remote configuration. Possible values:
            bf4040ad-e590-4ec8-9237-7c9f3ef6580f - Not Possible
            f0322843-8742-41ea-91e1-970634c84bb1 - Optical Eye
            1edf42bd-28aa-451f-a27c-d0798ae52768 - Direct Control
          example: bf4040ad-e590-4ec8-9237-7c9f3ef6580f
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        meterComponentRequirementsValidationPassed:
          description: Boolean updated by validation method that indicates whether Meter fulfils the component requirements.
          example: true
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        meterComponentRequirementsLastValidationDate:
          description: Indicates time of the last meter component validation check.
          example: "2024-06-03T22:00:00.000Z"
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        meterComponentInvalidRequirements:
          description: Collection of meter components which do not fulfill meter component requirements.
          type: array
          minItems: 0
          maxItems: 100
          items:
            $ref: "#/components/schemas/MeterComponentInvalidRequirement"

    MeterComponentInvalidRequirement:
      type: object
      additionalProperties: false
      description: Meter component which do not fulfill meter component requirements model.
      required:
        - componentId
        - invalidRequirements
      properties:
        componentId:
          description: Component identifier.
          example: 1234
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
        invalidRequirements:
          description: Collection of requirements which do not fulfill meter components requirements.
          type: array
          minItems: 0
          maxItems: 10
          items:
            description: Requirements which do not fulfill meter components requirements.
            example: 'Manufacturer'
            allOf:
              - $ref: '#/components/schemas/MediumString'

    MeterInputType:
      type: string
      enum:
        - Internal
        - External
        - WiredMbus
        - MBus
        - Pulse
      description: Shared list of enums between Meter Frame and Meter domain
    ChangeMeterOnMeterFrameResultStatus:
      type: string
      enum:
        - Success
        - Failure
        - Warning
      description: Validation status for Meter Input Connection
      example: 'Success'

    ValidateActiveMeterOnMeterFrameFullValidationResultStatus:
      type: string
      enum:
        - Success
        - Error
      description: Validation status for Meter Input Connection
      example: 'Success'

    ValidateActiveMeterOnMeterFrameConnectionTypeAndCtVtResultStatus:
      type: string
      enum:
        - Success
        - Error
      description: Validation status for Meter Input Connection
      example: 'Success'

    ValidateActiveMeterOnMeterFrameRegisterRequirements:
      type: object
      description: Model for validation currently assigned active Meter on Meter Frame Register Requirement.
      additionalProperties: false
      required:
        - meterFrameId
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
    ValidateActiveMeterOnMeterFrameAttributesRequirements:
      type: object
      description: Model for validation currently assigned active Meter on Meter Frame Attributes Requirement.
      additionalProperties: false
      required:
        - meterFrameId
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
    ValidateActiveMeterOnMeterFrameFullValidation:
      type: object
      description: Model for validation currently assigned active Meter on Meter Frame Register Requirement, atribute requirements, connection type and CT/VT.
      additionalProperties: false
      required:
        - meterFrameId
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
    ValidateActiveMeterOnMeterFrameConnectionTypeAndCtVt:
      type: object
      description: Model for validation currently assigned active Meter on Meter Frame connection type and CT/VT.
      additionalProperties: false
      required:
        - meterFrameId
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
    ChangeMeterInputConnection:
      type: object
      additionalProperties: false
      description: Model for changing Meter on Meter Frame.
      required:
        - meterFrameId
        - meterId
        - supplyType
        - meterNumber
        - meterType
        - changeOfMeterDate
        - context
        - meterInputId
        - inputNumber
        - meterInputType
        - meterRegisters
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          allOf:
            - $ref: '#/components/schemas/GuidField'
        meterId:
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
          description: Id of a meter.
          example: 1
        supplyType:
          type: integer
          format: int32
          minimum: 1
          maximum: 3
          description: List of possible supply types "Electricity", "Heating", "Water"
          x-enumNames:
            - Electricity
            - Heating
            - Water
          enum:
            - 1
            - 2
            - 3
          example: 3
        meterNumber:
          description: |-
            This is the meter number that is communicated to the DataHub.
            The meter number can deviate from the meter number that can be seen on the physical meter,
            though according to guidelines they should match.
          example: "8975439870435"
          allOf:
            - $ref: "#/components/schemas/OneWordString"
        meterType:
          allOf:
            - $ref: "#/components/schemas/ShortString"
          example: "meter type"
          description: Values comes from the Meter entity in the Meter domain. The field defines the type of Meter assigned.
        meterInputId:
          description: Meter input id
          allOf:
            - $ref: '#/components/schemas/GuidField'
        inputNumber:
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          example: 360
          description: Meter input number.
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          allOf:
            - $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        meterRegisters:
          description: List of meter registry.
          type: array
          minItems: 1
          maxItems: 10000
          items:
            $ref: "#/components/schemas/MeterRegisterModel"
        changeOfMeterDate:
          type: string
          format: date-time
          description: Date of changing meter.
        context:
          type: string
          enum:
            - InstallMeter
            - ChangeMeter
            - RemoveMeter
          description: Type of operation.
        displayConfiguration:
          description: |-
            Display configuration. Possible values:
            de23f9df-a4f0-4591-b743-1f7d1b00a351 - Not Possible
            79f3ec96-3bd4-41f8-bbc2-aa73846c86a7 - Optical Eye
            098a905e-dfe0-4842-b7ce-c58a756d8212 - Direct Control
          example: de23f9df-a4f0-4591-b743-1f7d1b00a351
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'
        registerConfiguration:
          description: |-
            Remote configuration. Possible values:
            bf4040ad-e590-4ec8-9237-7c9f3ef6580f - Not Possible
            f0322843-8742-41ea-91e1-970634c84bb1 - Optical Eye
            1edf42bd-28aa-451f-a27c-d0798ae52768 - Direct Control
          example: bf4040ad-e590-4ec8-9237-7c9f3ef6580f
          allOf:
            - $ref: '#/components/schemas/GuidFieldNullable'

    ValidateMeterOnMeterAttributesRequirements:
      type: object
      additionalProperties: false
      description: Model for validation meter on meter attributes requirements.
      required:
        - meterFrameId
        - meterId
        - supplyType
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          example: 58acc521-de2c-49ff-b233-47a77f697517
          allOf:
            - $ref: '#/components/schemas/GuidField'
        meterId:
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
          description: Id of a meter.
          example: 1
        supplyType:
          type: string
          description: Supply type of meter.
          minLength: 5
          maxLength: 11
          pattern: "^(Electricity|Heating|Water)$"
          example: Water

    CalculateDefaultValidFromBody:
      type: object
      additionalProperties: false
      description: Model for calculating default valid from date for specific meter frame.
      required:
        - meterFrameId
      properties:
        meterFrameId:
          description: |-
            Meter Frame id.
          example: 58acc521-de2c-49ff-b233-47a77f697517
          allOf:
            - $ref: '#/components/schemas/GuidField'

    CalculateDefaultValidFromResult:
      type: object
      additionalProperties: false
      description: Model for returning calculated default valid from date for specific meter frame.
      required:
        - defaultValidFrom
      properties:
        defaultValidFrom:
          description: Default valid from date
          example: "2023-07-01T22:00:00Z"
          allOf:
            - $ref: '#/components/schemas/DateTime'

    MeterRegisterModel:
      type: object
      description: Model of meter registry.
      additionalProperties: false
      required:
        - meterRegisterId
        - constant
        - decimals
        - digits
        - fixedZeroes
        - registerName
        - registerNumber
        - meterReadingType
        - measuringUnit
      properties:
        meterRegisterId:
          description: Meter register id
          allOf:
            - $ref: '#/components/schemas/GuidField'
        constant:
          type: number
          format: decimal
          description: "Must the meter register be multiplied by a constant when data is retrieved"
          minimum: -9999999999.9999
          maximum: 9999999999.9999
          example: 13.3
        decimals:
          description: "Number of decimal places after the decimal"
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          example: 1
        description:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Description
          example: "some description"
          nullable: true
        digits:
          description: Number of digits in meter register. We agree that digits are exclusive of decimals
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          example: 1
        fixedZeroes:
          description: Is there a fixed zero on the counter in display view, i.e. multiplier in display
          allOf:
            - $ref: '#/components/schemas/NonNegativeInteger'
          example: 1
        registerName:
          allOf:
            - $ref: '#/components/schemas/MediumString'
          description: Name of registry
          example: "registerName"
        registerNumber:
          allOf:
            - $ref: '#/components/schemas/PositiveInteger'
          description: Number of meter register
          example: 1
        meterReadingType:
          type: integer
          format: int32
          minimum: 1
          maximum: 2
          description: List of possible meter reading type "Accumulating", "Interval"
          x-enumNames:
            - Accumulating
            - Interval
          enum:
            - 1
            - 2
          example: 2
        measuringUnit:
          description: |-
            Measurement units that the meters can measure in.
            Electricity: Wh, kWh, MWh, VArh, kVArh, MVArh, kW
            Heating: MWh, m3, TemperatureCelsiusWater, TemperatureCelsiusRoom,
            Water: m3, TemperatureCelsiusWater, TemperatureCelsiusRoom
          pattern: "^(Wh|kWh|MWh|VArh|kVArh|MVArh|kW|m3|TemperatureCelsiusWater|TemperatureCelsiusRoom)$"
          minLength: 1
          maxLength: 23
          type: string
          nullable: false
          example: "TemperatureCelsiusWater"
        meteringComponentId:
          description: |-
            DK: MålingsKomponentId.
            Shared value list with Meter domain.
            The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
            It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
          nullable: true
          allOf:
            - $ref: '#/components/schemas/GuidField'
    AccessInformationModel:
      type: object
      description: Indicates the registered access information that the utility has regarding the address, including contact information, key information, codes, etc.
      additionalProperties: false
      required:
        - type
      properties:
        type:
          allOf:
            - $ref: '#/components/schemas/ShortString'
          description: Indicates where the information is accessed. Ex. "Locked basement door".
          example: 'type'
        contactPerson:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Indicates the name of the contact person who can be contacted to gain access.
          example: 'contact person'
        keyNumber:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: Indicates a possible key number when there are multiple keys.
          example: 'key number'
        keyStorageLocation:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Describes storage location, e.g. '60 / 10 SKB.
          example: 60 / 10 SKB
        keyPlacement:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Describes the location of the key, e.g. key cabinet.
          example: key cabinet
        keyComment:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Various remarks.
          example: 'key comment'
        doorCode:
          allOf:
            - $ref: '#/components/schemas/ShortStringNullable'
          description: Indicates any code for the door.
          example: 'door code'
        doorCodeComment:
          allOf:
            - $ref: '#/components/schemas/MediumStringNullable'
          description: Various remarks on the door code.
          example: 'door code comment'
    ConnectionRightInvoiceModel:
      type: object
      description: Connection Right Invoice
      additionalProperties: false
      required:
        - invoiceId
      properties:
        invoiceId:
          type: string
          format: uuid
          description: Invoice Id
          example: '00000000-0000-0000-0000-000000000000'
        invoiceNumber:
          type: string
          description: Invoice number returned from ERP.
          maxLength: 25
          pattern: '^.*$'
          nullable: true
          example: "1234567890123456789012345"

    # --------------------------------------------------------- SHARED FIELDS IN OBJECTS -------------------------------------------------------------------
    GuidId:
      description: UUID Id of the Resource.
      allOf:
        - $ref: '#/components/schemas/GuidField'

    FlexAttributeObject:
      description: |-
        An UUID reference to a Settlement Object that is used to register flexible attributes about the entity.
        In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an entity.
      nullable: true
      type: string
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8

    RowVersion:
      type: string
      description: RowVersion.
      format: byte
      example: "AAAAAAAAB+I="

    # --------------------------------------------------------- COMMON DATA TYPES --------------------------------------------------------------------------
    OneWordString:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 25
      description: 'Max. 25 characters long string with spaces.'
      example: 'Max. 25 chars. w. spaces.'

    OneWordStringNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 0
      maxLength: 25
      description: 'Max. 25 characters long string with spaces.'
      example: 'Max. 25 chars. w. spaces.'

    ShortString:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 50
      description: 'Max. 50 characters long string with spaces.'
      example: 'Max. 50 characters long string with spaces.'

    ShortStringNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 0
      maxLength: 50
      description: 'Max. 50 characters long string with spaces.'
      example: 'Max. 50 characters long string with spaces.'

    SortByNullable:
      pattern: '^(id|connectionPointId|connectionPointNumber|meterframeNumber|supplyType|supplyStatus|supplyDisconnectType|meterMissing|addressLine1|addressLine2|addressStatus|addressType|darStatus|lifeCycleStatus)$'
      type: string
      nullable: true
      maxLength: 21
      minLength: 2
      description: Name of the property to sort by.
      example: connectionPointNumber

    SortOrderNullable:
      pattern: '^(asc|desc)$'
      type: string
      nullable: true
      maxLength: 4
      minLength: 3
      description: Direction of sorting. Can only be 'asc' for ascending or 'desc' for descending.
      example: asc

    MediumString:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 100
      description: 'Max. 100 characters long string with spaces.'
      example: 'Max. 100 characters long string with spaces.'

    MediumStringNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 0
      maxLength: 100
      description: 'Max. 100 characters long string with spaces.'
      example: 'Max. 100 characters long string with spaces.'

    DescriptionString:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 1000
      description: 'Max. 1000 characters long string with spaces.'
      example: 'Max. 1000 characters long string with spaces.'

    DescriptionStringNullable:
      pattern: "^.*$"
      type: string
      nullable: true
      minLength: 0
      maxLength: 1000
      description: 'Max. 1000 characters long string with spaces.'
      example: 'Max. 1000 characters long string with spaces.'

    DateTimeNullable:
      type: string
      nullable: true
      description: DateTime Nullable.
      format: date-time
      example: "2023-07-01T22:00:00Z"

    DateTime:
      type: string
      description: DateTime.
      format: date-time
      example: "2023-07-01T22:00:00Z"

    ShortStringObsolete:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 128
      description: 'Max. 128 characters long string with spaces.'
      example: 'Max. 128 characters long string with spaces.'

    ShortStringObsoleteNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 0
      maxLength: 128
      description: 'Max. 128 characters long string with spaces.'
      example: 'Max. 128 characters long string with spaces.'

    MediumStringObsoleteNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 0
      maxLength: 256
      description: 'Max. 256 characters long string with spaces.'
      example: 'Max. 256 characters long string with spaces.'

    LongStringObsolete:
      pattern: "^.*$"
      nullable: false
      type: string
      minLength: 1
      maxLength: 1024
      description: 'Max. 1024 characters long string with spaces.'
      example: 'Max. 1024 characters long string with spaces.'

    LongStringObsoleteNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 0
      maxLength: 1024
      description: 'Max. 1024 characters long string with spaces.'
      example: 'Max. 1024 characters long string with spaces.'


    LongStringNullable:
      pattern: "^.*$"
      nullable: true
      type: string
      minLength: 0
      maxLength: 256
      description: 'Max. 256 characters long string with spaces.'
      example: 'Max. 256 characters long string with spaces.'

    StartsWithStringFilterParameter:
      type: string
      pattern: "^.*$"
      description: StartsWith (like) search query parameter.
      nullable: true
      minLength: 1
      maxLength: 1000
      example: AnyStringWhichWillBeUsedForLikeConditionInQuery

    GuidField:
      type: string
      description: GUID field.
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8

    GuidFieldNullable:
      nullable: true
      type: string
      description: GUID field.
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8

    BooleanField:
      type: boolean
      description: Boolean field.
      example: true

    BooleanFieldNullable:
      nullable: true
      type: boolean
      description: Boolean field nullable.
      example: true

    Integer:
      type: integer
      nullable: false
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Integer type in <-2147483648,2147483647> range.'
      example: 111

    IntegerNullable:
      type: integer
      nullable: true
      format: int32
      minimum: -2147483648
      maximum: 2147483647
      description: 'Integer type in <-2147483648,2147483647> range.'
      example: 111

    DecimalNullable:
      type: number
      description: 'Decimal'
      format: decimal
      nullable: true

    NonNegativeInteger:
      type: integer
      nullable: false
      format: int32
      minimum: 0
      maximum: 2147483647
      description: 'Integer type in <0,2147483647> range.'
      example: 111

    NonNegativeIntegerNullable:
      type: integer
      nullable: true
      format: int32
      minimum: 0
      maximum: 2147483647
      description: 'Integer type in <0,2147483647> range.'
      example: 111

    PositiveInteger:
      type: integer
      nullable: false
      format: int32
      minimum: 1
      maximum: 2147483647
      description: 'Integer type in <1,2147483647> range.'
      example: 111

    PositiveIntegerNullable:
      type: integer
      nullable: false
      format: int32
      minimum: 1
      maximum: 2147483647
      description: 'Integer type in <1,2147483647> range.'
      example: 111

  responses:
    '400':
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ValidationProblemDetails'
          examples:
            ItIsABadRequest:
              value:
                type: 'https://errors.kmdelements.com/400'
                title: Bad Request
                status: 400
                detail: 'Invalid request'
                instance: /resources-path/1
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    '401':
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            YouShallNotPass:
              value:
                type: 'https://errors.kmdelements.com/401'
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources-path/1
    '403':
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            CannotTouchThis:
              value:
                type: 'https://errors.kmdelements.com/403'
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources-path/1
    '404':
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ItWasHere:
              value:
                type: 'https://errors.kmdelements.com/404'
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources-path/1
    '409':
      description: Conflict - entity updated concurrently and/or incorrect rowversion passed and/or resource is conflicting with unique constraint.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ItsConflictingOrSomethingElseChangedIt:
              value:
                type: 'https://errors.kmdelements.com/409'
                title: Conflict
                status: 409
                detail: Conflict
                instance: /resources-path/1
    '422':
      description: 422 Unprocessable.
      content:
        application/problem+json:
          schema:
            description: 422 Unprocessable.
            type: array
            maxItems: 1000000
            items:
              $ref: '#/components/schemas/ErrorDescription'
          examples:
            CustomValidationsIncorrect:
              value:
                - errorCode: 'Translatable error code.'
                  defaultMessage: 'Default error description in english.'
                - errorCode: 'Other translatable error code.'
                  defaultMessage: 'Other error description in english.'
    '429':
      description: 429 Too Many Requests
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            $ref: '#/components/schemas/PositiveInteger'
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            TooFastTooQuicklyTooSoon:
              value:
                type: 'https://errors.kmdelements.com/429'
                title: Too Many Requests
                status: 360
                detail: Rate limit is exceeded.
                instance: /resources-path/1
    '499':
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            NotFoundExample:
              value:
                type: 'https://errors.kmdelements.com/499'
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: /resources-path/1
    '500':
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            Thisshouldnothappen:
              value:
                type: 'https://errors.kmdelements.com/500'
                title: Internal Server Error
                status: 500
                detail: 'body.0.age: Value `Not Int` does not match format `int32`'
                instance: /resources-path/1
    "502":
      description: 502 Bad Gateway.
    "503":
      description: 503 Service Unavailable.
    "504":
      description: 504 Gateway Timeout.

  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT

tags:
  - name: CodeList
    description: API for Code Lists management.
  - name: ConnectionPoint
    description: API for Connection Points management.
  - name: ConnectionPointExternalEquipmentAssignments
    description: API for managing External Equipments assignments of Connection Point.
  - name: ConnectionPointInternalEquipmentAssignments
    description: API for managing Internal Equipments assignments of Connection Point.
  - name: ConnectionPointNote
    description: API for Connection Point Notes management.
  - name: ConnectionPointTagAssignments
    description: API for managing Tag assignments of Connection Point.
  - name: InternalEquipment
    description: API for Internal Equipments management.
  - name: InternalEquipmentNote
    description: API for Internal Equipment Notes management.
  - name: MeterFrame
    description: API for Meter Frames management.
  - name: MeterFrameExternalEquipmentAssignments
    description: API for managing External Equipments assignments of Meter Frame.
  - name: MeterFrameInternalEquipmentAssignments
    description: API for managing Internal Equipments assignments of Meter Frame.
  - name: MeterFrameNote
    description: API for Meter Frame Notes management.
  - name: MeterFrameTagAssignments
    description: API for managing Tag assignments of Meter Frame.
  - name: MeterInputConnections
    description: API for managing information about information about assigning Meter to a Meter Frame in certain period of time.
  - name: RegisterRequirement
    description: API for Register Requirement management.
  - name: SpecialAgreement
    description: API for Special Agreements management.
  - name: SpecialAgreementNote
    description: API for Special Agreement Notes management.
  - name: ConnectionRights
    description: API for Connection Rights management
  - name: Meter
    description: API for Meter related to Meter Frame and Meter Input Connection
