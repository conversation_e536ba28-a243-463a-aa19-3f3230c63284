asyncapi: 2.6.0
id: https://async.api.kmdelements.com/hes-meter-command-command/
info:
  title: HES Meter Command Command
  x-maintainers: Team-DP-2
  version: "0.0.5-preview"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    Schema for HES Meter Commands.
    The commands will be consumed by Meter Data Reader component which is responsible for communicating with the meters.

tags:
  - name: Team-DP-2
    description: Maintained by Team-DP-2

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.command.hes.meter-command.v1:
    description: Topic with messages containing meter commands
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Meter Commands
      description: Contains all data required to process meter command, like credentials for meter connection, meterId and meter command details
      operationId: meterCommandCommand
      message:
        $ref: "#/components/messages/MeterCommandCommand"

components:
  messages:
    MeterCommandCommand:
      name: Meter Command
      title: Meter Command to be performed on a meter
      summary: Command contains all data required to perform, like command type and details, IP address, port and other credentials for meter connection
      contentType: application/json
      payload:
        $ref: "#/components/schemas/MeterCommandPayload"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    MeterCommandPayload:
      title: MeterCommandPayload
      name: MeterCommandPayload
      type: object
      additionalProperties: false
      required:
        - meterId
        - connectionData
        - meterCommand
      properties:
        meterId:
          type: integer
          format: int64
          description: Meter ID
        connectionData:
          type: object
          description: Connection data for the meter
          $ref: "#/components/schemas/MeterConnectionData"
        meterCommand:
          type: object
          description: Meter command details
          $ref: "#/components/schemas/MeterCommand"

    MeterConnectionData:
      title: MeterConnectionData
      name: MeterConnectionData
      type: object
      additionalProperties: false
      required:
        - ipAddress
        - port
        - password
        - clientAddress
        - serverAddress
        - addressSize
        - logicalAddress
        - interfaceType
        - authenticationLevel
        - useLogicalNameReferencing
      properties:
        ipAddress:
          type: string
          description: IP Address of the meter
        port:
          type: integer
          description: Port used to connect to the meter
        password:
          type: string
          description: Meter password used to connect to the meter
        clientAddress:
          type: integer
          description: Client address
        serverAddress:
          type: integer
          description: Server address
        addressSize:
          type: integer
          description: Address size
        logicalAddress:
          type: integer
          description: Logical address
        interfaceType:
          type: string
          description: Interface type
          enum:
            - HDLC
        authenticationLevel:
          type: string
          description: Authentication level
          enum:
            - LOW
        useLogicalNameReferencing:
          type: boolean
          description: Use logical name referencing

    MeterCommand:
      title: MeterCommand
      name: MeterCommand
      type: object
      additionalProperties: false
      required:
        - commandType
      properties:
        commandType:
          type: string
          description: Meter command type
        getMeterReadingsCommand:
          $ref: "#/components/schemas/GetMeterReadingsCommand"
        getMeterInstantValuesCommand:
          $ref: "#/components/schemas/GetMeterInstantValuesCommand"

    GetMeterReadingsCommand:
      title: Get meter readings command
      name: Get meter readings command
      type: object
      additionalProperties: false
      required:
        - readingTypes
        - timeSchedule
      properties:
        readingTypes:
          type: array
          description: Set of reading types (OBIS names of registers)
          items:
            type: string
        timeSchedule:
          $ref: "#/components/schemas/TimeSchedule"

    GetMeterInstantValuesCommand:
      title: Get meter instant values command
      name: Get meter instant values command
      type: object
      additionalProperties: false
      required:
        - readingTypes
      properties:
        readingTypes:
          type: array
          description: Set of reading types (OBIS names of registers)
          items:
            type: string

    TimeSchedule:
      title: Time schedule
      name: Time schedule
      type: object
      additionalProperties: false
      required:
        - start
      properties:
        start:
          format: date-time
          type: string
        end:
          format: date-time
          type: string

  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
