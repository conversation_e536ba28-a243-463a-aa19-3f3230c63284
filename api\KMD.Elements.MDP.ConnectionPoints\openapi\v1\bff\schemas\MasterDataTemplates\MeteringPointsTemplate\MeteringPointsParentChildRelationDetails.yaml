title: MeteringPointsParentChildRelationDetails
type: object
description: Represents metering point parent-child relation details.
additionalProperties: false
properties:
  relationId:
    $ref: "../../DataTypes/Guid.yaml"
  parent:
    type: object
    description: "Parent metering point default value set"
    nullable: true
    oneOf:
      - $ref: "./MeteringPointDefaultValueSetDetails.yaml"
  child:
    type: object
    description: "Child metering point default value set"
    nullable: true
    oneOf:
      - $ref: "./MeteringPointDefaultValueSetDetails.yaml"
