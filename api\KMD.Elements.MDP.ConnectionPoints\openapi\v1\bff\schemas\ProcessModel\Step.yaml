type: object
description: Process step.
additionalProperties: false
required:
  - isExpandable
  - stepId
properties:
  type:
    $ref: "./StepType.yaml"
  timeStamp:
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
    description: Date time when the process step was last updated.
    nullable: true
  status:
    $ref: "./StepStatus.yaml"
  infrastructureElementId:
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
    description: Identifier of the entity for which the process is executed.
    example: "20000001"
  errorMessage:
    allOf:
      - $ref: '../DataTypes/LongStringNullable.yaml'
    description: Error message for subprocess.
    example: "Error message"
  domainProcessId:
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    description: Identifier of the Subprocess in domain.
    example: "da85baa6-a66a-11ea-bb37-0242ac130002"
  isExpandable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: Property for setting accordion as Expandable or not.
    nullable: false
  stepId:
    $ref: "../DataTypes/Guid.yaml"
  stepAttributes:
    type: array
    description: Array of step attributes objects.
    items:
      $ref: './StepAttribute.yaml'
    maxItems: 256
  history:
    type: array
    description: History of all status changes
    maxItems: 100
    items:
      $ref : "./HistoryEntry.yaml"
