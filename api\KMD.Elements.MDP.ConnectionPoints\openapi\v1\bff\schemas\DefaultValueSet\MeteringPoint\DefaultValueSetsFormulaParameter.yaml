type: object
description: Metering Point Version Formula Parameter.
additionalProperties: false
required:
  - name
  - formulaParameterType
properties:
  name:
    description: The name of the formula parameter.
    allOf:
      - $ref: '../../DataTypes/MediumStringNullable.yaml'
  formulaParameterType:
    $ref: './DefaultValueSetsFormulaParameterType.yaml'
  description:
    description: A description of what data the formula parameter represents.
    allOf:
      - $ref: '../../DataTypes/DescriptionString.yaml'
    nullable: true
  meteringPointTypeCode:
    description: Indicates which Metering point type code is to be retrieved.
    type: string
    maxLength: 4
    pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D21|D22|D23|D24|D25|D26|D27|D28|D29|D30|D99|E17|E18|E20|VA17|VD07|FV17|FV18|FD06|FD07|FD14|FD10|FD11)$
