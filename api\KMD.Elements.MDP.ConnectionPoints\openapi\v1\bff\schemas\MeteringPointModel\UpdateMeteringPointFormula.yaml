type: object
description: |
  Extended metering point version.
additionalProperties: false
properties:
  occurrence:
    description: Occurrence date.
    allOf:
      - $ref: "../DataTypes/DateTimeIso8601.yaml"
    nullable: true
  formula:
    description: |
      The formula that describes how data for the metering point's time series is calculated using data
      from the individual formula parameters. The formula is sent to MDM, and all the parameters' data
      must be found in MDM in order for MDM to calculate the result profile.
    allOf:
      - $ref: './FormulaVersionNullable.yaml'
  meterReadingOccurrence:
    type: string
    description: Meter reading occurrence attribute.
    nullable: true
    minLength: 3
    maxLength: 5
    pattern: ^(P1M|PT1H|PT15M|ANDET)$
    example: PT1H
  unitType:
    nullable: true
    type: string
    maxLength: 3
    description: Measurement unit
    pattern: ^(K3|KWH|KWT|MAW|MWH|TNE|Z03)$
    example: KWH
  typeOfMeteringPoint:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `TypeOfMeteringPoint`
    pattern: ^(D01|D02|D03|D04|D05|D06|D07|D08|D09|D10|D11|D12|D13|D14|D15|D16|D17|D18|D19|D20|D21|D22|D23|D24|D25|D26|D27|D28|D29|D30|D99|E17|E18|E20)$
  subTypeOfMeteringPoint:
    nullable: true
    type: string
    maxLength: 3
    pattern: ^(D01|D02|D03)$
    description: Is mapped to `SubTypeOfMeteringPoint`
  physicalStatusOfMeteringPoint:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `PhysicalStatusOfMeteringPoint`
    pattern: ^(D02|D03|E22|E23)$
  meteringPointDomainLocationId:
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    nullable: true
    description: Metering Point Domain Location Identifier
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
