openapi: 3.0.3
info:
  title: Public API for Connectors VVS
  x-maintainers: 'Team-MC-1'
  description: >-
    This API should be used by Utilihive SMILE Water and Heating API (IN).
  termsOfService: https://www.kmd.net/terms-of-use
  contact:
    name: KMD Elements
    url: >-
      https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: License
    url: https://www.kmd.net/terms-of-use
  version: '1.20'
servers:
  - url: https://localhost:8080
    description: Localhost
  - url: '{scheme}://{baseUrl}'
    description: Production API settings
    variables:
      baseUrl:
        default: localhost:8080
      scheme:
        enum:
          - https
        default: https
        description: Scheme used for the connection
security:
  - Jwt: []
tags:
  - name: MeteringPoints
    description: Collection of all endpoints related to metering point domain.
  - name: Prices
    description: Collection of all endpoints related to prices domain.
  - name: PriceLinks
    description: Collection of all endpoints related to price links domain.
  - name: ServiceRequests
    description: Collection of all endpoints related to Service Requests domain.
  - name: TimeSeries
    description: Collection of all endpoints related to TimeSeries domain.
paths:
  /v1/{supplyType}/metering-points/{meteringPointId}:
    get:
      tags:
        - MeteringPoints
      operationId: getMeteringPoint
      x-authorization: Water.All
      x-authorization-1: Heating.All
      summary: Get metering point versions between two dates.
      description: |
        <li>If no period is defined, today's date occurrence is returned.
        <li>If only start date is defined, current occurrence from that data and onward.
        <li>If start and end date are defined, current occurrence from start date and until current occurrence during end date.
      parameters:
        - $ref: "#/components/parameters/MessageIdInHeader"
        - $ref: "#/components/parameters/CorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyTypeInPath"
        - $ref: "#/components/parameters/MeteringPointIdInPath"
        - $ref: "#/components/parameters/DateFromInQuery"
        - $ref: "#/components/parameters/DateToInQuery"
      responses:
        "200":
          description: MeteringPoint versions
          content:
            application/json:
              schema:
                type: array
                description: List of metering point versions
                maxItems: 1000
                items:
                  $ref: "#/components/schemas/GetMeteringPointResponseBody"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
  /v1/{supplyType}/metering-points/{meteringPointId}/service-request/cancel:
    post:
      tags:
        - ServiceRequests
      operationId: cancelServiceRequest
      x-authorization: Water.All
      x-authorization-1: Heating.All
      summary: VV039
      description: VV039
      parameters:
        - $ref: '#/components/parameters/MessageIdInHeader'
        - $ref: '#/components/parameters/CorrelationIdInHeader'
        - $ref: "#/components/parameters/SupplyTypeInPath"
        - $ref: '#/components/parameters/MeteringPointIdInPath'
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelServiceRequestRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/202'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/{supplyType}/metering-points/{meteringPointId}/service-request/create:
    post:
      tags:
        - ServiceRequests
      operationId: requestServiceRequest
      x-authorization: Water.All
      x-authorization-1: Heating.All
      summary: VV039
      description: VV039
      parameters:
        - $ref: '#/components/parameters/MessageIdInHeader'
        - $ref: '#/components/parameters/CorrelationIdInHeader'
        - $ref: "#/components/parameters/SupplyTypeInPath"
        - $ref: '#/components/parameters/MeteringPointIdInPath'
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequestServiceRequestRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/202'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/{supplyType}/metering-points/{meteringPointId}/timeseries/request:
    post:
      tags:
        - TimeSeries
      operationId: requestMeteredData
      x-authorization: Water.All
      x-authorization-1: Heating.All
      summary: VV025
      description: VV025
      parameters:
        - $ref: '#/components/parameters/MessageIdInHeader'
        - $ref: '#/components/parameters/CorrelationIdInHeader'
        - $ref: "#/components/parameters/SupplyTypeInPath"
        - $ref: '#/components/parameters/MeteringPointIdInPath'
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequestMeteredDataRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/202'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/{supplyType}/metering-points/{meteringPointId}/customer:
    put:
      tags:
        - MeteringPoints
      operationId: upsertCustomer
      x-authorization: Water.All
      x-authorization-1: Heating.All
      summary: VV015
      description: VV015
      parameters:
        - $ref: '#/components/parameters/MessageIdInHeader'
        - $ref: '#/components/parameters/CorrelationIdInHeader'
        - $ref: "#/components/parameters/SupplyTypeInPath"
        - $ref: '#/components/parameters/MeteringPointIdInPath'
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertCustomerContactRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/202'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/{supplyType}/metering-points/{meteringPointId}/price-elements:
    get:
      tags:
        - PriceLinks
      operationId: requestPriceElementsByMeteringPoint
      x-authorization: Water.All
      x-authorization-1: Heating.All
      summary: Get price elements related to a metering point
      description: |
        <li>If no period is defined, today's date occurrence is returned.</li>
        <li>If only start date defined, current occurrence from that date and onward.</li>
        <li>If start and end date are defined, current occurrence from start date and until current occurrence during end date.</li>
      parameters:
        - $ref: '#/components/parameters/MessageIdInHeader'
        - $ref: '#/components/parameters/CorrelationIdInHeader'
        - $ref: '#/components/parameters/SupplyTypeInPath'
        - $ref: '#/components/parameters/MeteringPointIdInPath'
        - $ref: '#/components/parameters/DateFromInQuery'
        - $ref: '#/components/parameters/DateToInQuery'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                description: List of price elements
                maxItems: 1000
                items:
                  $ref: '#/components/schemas/PriceRelation'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '499':
          $ref: '#/components/responses/499'
        '500':
          $ref: '#/components/responses/500'
        '503':
          $ref: '#/components/responses/503'
        '504':
          $ref: '#/components/responses/504'
  /v1/{supplyType}/price-elements/{owner}/{priceType}/{priceId}:
    get:
      tags:
        - Prices
      operationId: getPrices
      x-authorization: Water.All
      x-authorization-1: Heating.All
      summary: Get price occurrences
      description: |
        - If no period is defined, today's date occurrence is returned.
        - If only start date is defined, current occurrence from that date and onward.
        - If start and end date are defined, current occurrence from start date and until current occurrence during end date.
      parameters:
        - $ref: "#/components/parameters/MessageIdInHeader"
        - $ref: "#/components/parameters/CorrelationIdInHeader"
        - $ref: '#/components/parameters/SupplyTypeInPath'
        - $ref: "#/components/parameters/OwnerInPath"
        - $ref: "#/components/parameters/PriceTypeInPath"
        - $ref: "#/components/parameters/PriceIdInPath"
        - $ref: "#/components/parameters/DateFromInQuery"
        - $ref: "#/components/parameters/DateToInQuery"
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                description: List of price elements
                maxItems: 1000
                items:
                  $ref: "#/components/schemas/PriceElement"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

components:
  parameters:
    DateFromInQuery:
      name: start
      in: query
      description: From date
      required: false
      schema:
        type: string
        format: date-time
        nullable: false
      example: 2024-01-22T10:54:32Z
    DateToInQuery:
      name: end
      in: query
      description: To date
      required: false
      schema:
        type: string
        format: date-time
        nullable: false
      example: 2024-01-22T10:54:32Z
    MessageIdInHeader:
      name: es-message-id
      description: >-
        Unique message ID. The same message id is used when resending the
        message.
      in: header
      schema:
        type: string
        format: uuid
      required: true
      example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
    CorrelationIdInHeader:
      name: es-correlation-id
      description: >
        This is used to "link" messages together.

        The server will place the incoming es-correlation-id value as the
        es-correlation-id

        on the outgoing reply. If not supplied on the request, the
        es-correlation-id of the

        reply should be set to the value of the es-message-id that was used on
        the request, if present.

        Given that the es-correlation-id is used to ‘link’ messages together, it
        may be reused on more than one message.
      in: header
      schema:
        type: string
        format: uuid
      required: true
      example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d
    SupplyTypeInPath:
      name: supplyType
      description: Supply type.
      in: path
      example: "water"
      schema:
        type: string
        pattern: "^(water|heating)$"
        minLength: 5
        maxLength: 7
      required: true
    MeteringPointIdInPath:
      name: meteringPointId
      description: Unique identifier on a metering point.
      in: path
      example: "571313190000020132"
      schema:
        type: string
        minLength: 18
        maxLength: 18
        pattern: ^\d{18}$
      required: true
    OwnerInPath:
      name: owner
      description: Owner GLN number.
      in: path
      schema:
        type: string
        minLength: 13
        maxLength: 13
        pattern: ^\d{13}$
      required: true
      example: '1231231231231'
    PriceTypeInPath:
      name: priceType
      description: Owner GLN number.
      in: path
      schema:
        type: string
        minLength: 13
        maxLength: 13
        pattern: ^\d{13}$
      required: true
      example: '1231231231231'
    PriceIdInPath:
      name: priceId
      description: Owner GLN number.
      in: path
      schema:
        type: string
        minLength: 13
        maxLength: 13
        pattern: ^\d{13}$
      required: true
      example: '1231231231231'
  schemas:
    PriceElement:
      type: object
      description: Price element DTO
      additionalProperties: false
      required:
        - occurrence
      properties:
        occurrence:
          type: string
          description: When the price element starts being valid.
          format: date-time
          example: "2023-07-01T22:00:00Z"
        occurrenceTo:
          type: string
          nullable: true
          description: When the price element starts being valid.
          format: date-time
          example: "2023-07-01T22:00:00Z"
        longDescription:
          type: string
          description: LongDescription.
          pattern: ""
          minLength: 0
          maxLength: 1000
          example: "abc"
        vatClass:
          type: string
          description: LongDescription.
          pattern: "^(D01|D02)$"
          nullable: true
          minLength: 0
          maxLength: 3
          example: "D01"
        transparentInvoicing:
          type: boolean
          description: TransparentInvoicing.
        taxIndicator:
          type: boolean
          description: TaxIndicator.
        resolutionDuration:
          type: string
          description: LongDescription.
          pattern: "^(PT15M|PT1H|P1D|P1M|P1Y)$"
          nullable: true
          minLength: 0
          maxLength: 3
          example: "P1M"
        pricesPerPosition:
          type: array
          maxItems: 1000
          description: Prices per position.
          items:
            $ref: "#/components/schemas/PricePerPosition"
    PricePerPosition:
      type: object
      description: Price per position
      additionalProperties: false
      required:
        - position
        - price
      properties:
        position:
          type: integer
          format: int32
          minimum: 0
          maximum: 1000
          description: Position.
          example: 1
        price:
          type: number
          format: decimal
          description: Price.
          minimum: 0
          maximum: *********
          example: 1
    PriceRelation:
      type: object
      description: Price relation DTO
      additionalProperties: false
      required:
        - occurrence
        - quantity
      properties:
        occurrence:
          type: string
          description: When metering points start being valid.
          format: date-time
          example: "2023-07-01T22:00:00Z"
        occurrenceTo:
          type: string
          description: When metering points ends being valid.
          format: date-time
          example: "2023-07-01T22:00:00Z"
          nullable: true
        quantity:
          type: number
          description: The number of times to charge the same subscription or fee.
          minimum: 1
          maximum: *********
          example: 1
    GetMeteringPointResponseBody:
      type: object
      description: Metering point
      additionalProperties: false
      required:
        - meteringPoint
        - meter
        - meteringPointAddress
      properties:
        meteringPoint:
          $ref: "#/components/schemas/MeteringPoint"
        meter:
          $ref: "#/components/schemas/Meter"
        meteringPointAddress:
          $ref: "#/components/schemas/MeteringPointAddress"
    MeteringPointAddress:
      type: object
      description: Object that contains address information from/to VVS. The object is primarily made to be able to frame attributes in the right context.
      additionalProperties: false
      required:
        - washable
      properties:
        streetName:
          type: string
          description: A proper name which designates and names a part of the road or path network or similar traffic areas and areas.
          pattern: ""
          minLength: 1
          maxLength: 40
          example: Lautrupparken
        protectedAddress:
          type: boolean
          description: To be specified. Note that it is both the name of the person and the address that are protected and that this is indicated in DataHub at the address.
          example: false
        houseNumber:
          type: string
          description: The house number for the address including any letter. Ex. 1, 2, 3A, 251C
          pattern: ""
          minLength: 1
          maxLength: 25
          example: "40"
          nullable: true
        floor:
          type: string
          description: Designation which indicates which floor the part of the building identified by the address is located on. Ex. 1, 2, 3
          pattern: ""
          minLength: 1
          maxLength: 4
          nullable: true
        door:
          type: string
          description: Designation that indicates the access door etc. as the address identifies. Ex. Th., Tv. Etc.
          pattern: ""
          minLength: 1
          maxLength: 4
          nullable: true
        postBox:
          type: string
          description: Can be specified if c/o or post office box is used
          pattern: ""
          minLength: 1
          maxLength: 10
          nullable: true
        darId:
          type: string
          format: uuid
          description: Specifies either a DAR id or a DAR access address id.
          example: 91fa8f2e-de39-406d-bdea-f7d407bca560
        postalCode:
          type: string
          description: Four-digit code that identifies the postal code. Ex. 6715.
          pattern: ""
          minLength: 1
          maxLength: 10
          example: "2750"
        cityName:
          type: string
          description: City name. Ex. "Esbjerg North"
          pattern: ""
          minLength: 1
          maxLength: 25
          example: Ballerup
        countryCode:
          type: string
          description: If the country code is different for DK, then the address cannot be DAR validated. Can be specified with a 2-character code according to the ISO-3166 standard.
          maxLength: 2
          pattern: "^$|^[A-Z]{2}$"
          example: DK
        streetCode:
          type: string
          description: Unique identification of the part of a Named Road that affects a single municipality
          pattern: ""
          minLength: 1
          maxLength: 25
        municipialityCode:
          type: string
          description: The municipality code for the municipality to which the relevant part of the named road belongs
          pattern: ""
          minLength: 1
          maxLength: 25
        buildingNumber:
          type: string
          description: Is mapped to `BuildingNumber`
          maxLength: 6
          pattern: ""
          nullable: true
        washable:
          type: string
          maxLength: 3
          description: Is mapped to `AddressWashInstruction`
          pattern: "^(D00|D01|D02|D03)$"
          example: D00
          nullable: true
    Meter:
      type: object
      description: Object that contains measuring information for the VVS. The object is primarily made to be able to frame attributes in the right context.
      additionalProperties: false
      properties:
        meterNumber:
          type: string
          description: The meter's number is displayed to NBS EWII's billing system
          pattern: ""
          maxLength: 25
        numberOfDigits:
          type: string
          description: Number of digits on the counter (meter).
          nullable: true
          pattern: ""
          maxLength: 5
        conversionFactor:
          type: number
          format: decimal
          nullable: true
          description: The conversion factor on the counting machine. This is used in connection with being able to reconcile consumption and meter reading. Ex. 1, 10, 1000.
        unitType:
          type: string
          minLength: 1
          maxLength: 5
          description: Units in which the meter measures energy consumption. Ex. kWh, MWh, Wh
          pattern: "^(MWh|m3|Celsius)$"
          example: MWh
        meterReadingType:
          type: string
          nullable: true
          description: >
            Type of Meter Meter Reading Type.
            | CodeListName                        | DisplayName             | Code | Name           | Translation |
            |-------------------------------------|-------------------------|------|----------------|-------------|
            | System-Meter-MeterReadingType       | D01 – Akkumulerende     | D01  | Akkumulerende  | Accumulated |
            | System-Meter-MeterReadingType       | D02 - Salderende        | D02  | Salderende     | Balanced    |
          minLength: 3
          maxLength: 3
          pattern: "^(D01|D02)$"
          example: D01
    MeteringPoint:
      type: object
      description: Object that contains the general relationships that are common to the water, and heat measurement points.
      additionalProperties: false
      required:
        - connectionPointNumber
        - meteringPointId
      properties:
        connectionPointId:
          type: string
          description: Id for the connection point
          format: uuid
          nullable: false
          example: "8902FA98-E40C-4434-ADFF-AA85A80F0FC0"
        connectionPointNumber:
          type: string
          description: Id for the connection point
          pattern: ""
          minLength: 1
          maxLength: 50
          nullable: false
          example: "20000001"
        meteringPointId:
          type: string
          description: GSRN number for water and heat.
          pattern: ""
          minLength: 1
          maxLength: 50
          nullable: false
          example: "571313190000020132"
        supplyType:
          type: string
          description: The supply type of the price element
          pattern: "^(Water|Heating)$"
          maxLength: 10
          nullable: false
          example: Heating
        subTypeOfMeteringPoint:
          type: string
          description: >
            Metering Point Subtype.
            | CodeListName                                    | DisplayName     | Code | Name    | Translation |
            |-------------------------------------------------|-----------------|------|---------|-------------|
            | System-MeteringPoint-SubTypeOfMeteringPoint     | D01 – Fysisk    | D01  | Fysisk  | Physical    |
            | System-MeteringPoint-SubTypeOfMeteringPoint     | D02 – Virtuel   | D02  | Virtuel | Virtual     |
          minLength: 3
          maxLength: 3
          pattern: "^(D01|D02)$"
          example: D01
        parentMeteringPointId:
          description: Parent Metering Point Id.
          type: string
          pattern: "^\\d{18}$"
          minLength: 18
          maxLength: 18
          example: "571313190000020132"
          nullable: true
        occurrence:
          type: string
          description: When metering points start being valid.
          format: date-time
          example: "2023-07-01T22:00:00Z"
        commonReading:
          type: string
          description: |-
            Indicates whether the meter supplies several units (buildings, apartments, etc.).

            | CodeListName                                 | DisplayName       | Code | Name             | Translation     |
            |----------------------------------------------|-------------------|------|------------------|-----------------|
            | System-WaterMeteringPoint-CommonReading      | Nej               | 0    | Nej              | No              |
            | System-WaterMeteringPoint-CommonReading      | Fællesmåling      | 1    | Fællesmåling     | Commonreading   |
            | System-WaterMeteringPoint-CommonReading      | Kollektiv måling  | 2    | Kollektiv måling | Shared reading  |
            | System-WaterMeteringPoint-CommonReading | Fælles- og kollektiv måling | 3    | Fælles- og kollektiv måling | Common and Shared reading  |
          minLength: 1
          maxLength: 1
          nullable: true
          pattern: "^(0|1|2|3)$"
        connectionStatus:
          type: string
          description: |-
            Water Metering Point Connection Status.

            | CodeListName                                  | DisplayName       | Code | Name       | Translation   |
            |-----------------------------------------------|-------------------|------|------------|---------------|
            | System-WaterMeteringPoint-ConnectionStatus    | D02 – Nedlæg      | D02  | Nedlæg     | Closed down   |
            | System-WaterMeteringPoint-ConnectionStatus    | D03 – Ny          | D03  | Ny         | New           |
            | System-WaterMeteringPoint-ConnectionStatus    | E22 – Tilsluttet  | E22  | Tilsluttet | Connected     |
            | System-WaterMeteringPoint-ConnectionStatus    | E23 – Afbrudt     | E23  | Afbrudt    | Disconnected  |
          minLength: 3
          maxLength: 3
          pattern: "^(D02|D03|E22|E23)$"
        readingFrequency:
          type: string
          description: |-
            The time resolution with which the metering point is read.
            | CodeListName                                  | DisplayName        | Code  | Name    | Translation |
            |-----------------------------------------------|--------------------|-------|---------|-------------|
            | System-WaterMeteringPoint-ReadingFrequency    | P1M – Måned        | P1M   | Måned   | Month       |
            | System-WaterMeteringPoint-ReadingFrequency    | PT1H – Time        | PT1H  | Time    | Hour        |
            | System-WaterMeteringPoint-ReadingFrequency    | PT15M – Kvarter    | PT15M | Kvarter | 15 mins     |
            | System-WaterMeteringPoint-ReadingFrequency    | P1D – Dag          | P1D   | Dag     | Day         |
            | System-WaterMeteringPoint-ReadingFrequency    | P1Y - År           | P1Y   | År      | Year        |
          minLength: 3
          maxLength: 5
          pattern: "^(P1M|PT1H|PT15M|P1D|P1Y)$"
        supplyInfoBilling:
          type: string
          pattern: "^.*$"
          nullable: true
          minLength: 0
          maxLength: 100
          description: Suppl. billing info for the Metering Point.
          example: A01
        locationDescription:
          type: string
          pattern: "^.*$"
          nullable: true
          minLength: 0
          maxLength: 100
          description: Free text detailed description of location. Can be used if there is something completely extraordinary about the location.
          example: "Sample long description of the location."
        applicationCode:
          type: string
          pattern: "^.*$"
          nullable: true
          minLength: 0
          maxLength: 50
          description: Indicates the application code for the single device. By default, it comes from the meter frame's property element, but must be able to be overwritten manually.
          example: A01
        meteringGridAreaId:
          type: integer
          format: int32
          nullable: true
          minimum: 0
          maximum: 2147483647
          description: |-
            Network area is a term for a network that is managed by a network company.
          example: 15
        expectedConsumptionPerYear:
          type: integer
          format: int32
          nullable: true
          minimum: 0
          maximum: 2147483647
          description: The expected consumption in the period.
          example: 15
        noMeter:
          type: boolean
          nullable: false
          description: Indicates whether it is a meter-free installation (subscription only).
          example: true
        waterMeteringPointAttributes:
          type: object
          description: Object that contains the water measurement point information for the billing system. The object is primarily made to be able to frame attributes in the right context.
          additionalProperties: false
          required:
            - typeOfMeteringPoint
          properties:
            disconnectKind:
              type: string
              description: |-
                Must be specified for consumption and production measurement points. Indicates how the measuring point can be interrupted by the grid company.
                | CodeListName                                 | DisplayName        | Code | Name               | Translation           |
                |----------------------------------------------|--------------------|------|--------------------|-----------------------|
                | System-WaterMeteringPoint-DisconnectKind     | D01                | D01  | Fjern afbrydelig   | Remote disconnection  |
                | System-WaterMeteringPoint-DisconnectKind     | D02                | D02  | Manual afbrydelig  | Manual disconnection  |
              minLength: 3
              maxLength: 3
              pattern: "^(D01|D02)$"
            typeOfMeteringPoint:
              type: string
              description: "Type of Water Metering Point."
              minLength: 4
              maxLength: 4
              pattern: "^(VA17|VD07)$"
        heatMeteringPointAttributes:
          type: object
          description: Object that contains the heat measurement point information for the billing system. The object is primarily made to be able to frame attributes in the right context.
          additionalProperties: false
          required:
            - typeOfMeteringPoint
          properties:
            disconnectKind:
              type: string
              description: |-
                Must be specified for consumption and production measurement points. Indicates how the measuring point can be interrupted by the grid company.
                | CodeListName                                 | DisplayName        | Code | Name               | Translation           |
                |----------------------------------------------|--------------------|------|--------------------|-----------------------|
                | System-HeatMeteringPoint-DisconnectKind     | D01                | D01  | Fjern afbrydelig   | Remote disconnection  |
                | System-HeatMeteringPoint-DisconnectKind     | D02                | D02  | Manual afbrydelig  | Manual disconnection  |
              minLength: 3
              maxLength: 3
              pattern: "^(D01|D02)$"
            returnHeatConnected:
              type: boolean
              nullable: true
              description: Indicates whether return heating is connected.
              example: true
            calculateCooling:
              type: boolean
              nullable: true
              description: Indicates whether cooling is to be calculated.
              example: true
            typeOfMeteringPoint:
              type: string
              description: "Type of Heating Metering Point."
              minLength: 4
              maxLength: 4
              pattern: "^(FV17|FV18|FD06|FD07|FD14|FD10|FD11)$"
              example: FV17
            m2Total:
              type: integer
              description: Indicates the total residential area from BBR.
              format: int32
              nullable: true
              minimum: 0
              maximum: 2147483647
              example: 111
            m2Business:
              type: integer
              description: Indicates the total business area from BBR.
              format: int32
              nullable: true
              minimum: 0
              maximum: 2147483647
              example: 111
            m2Warehouse:
              type: integer
              description: Indicates the total warehouse area from BBR.
              format: int32
              nullable: true
              minimum: 0
              maximum: 2147483647
              example: 111
    CancelServiceRequestRequestBody:
      title: RSM025
      type: object
      description: Cancel service request data transfer object.
      additionalProperties: false
      required:
        - originalIdentification
      properties:
        originalIdentification:
          $ref: '#/components/schemas/Identification'
    Identification:
      type: string
      description: Identification
      format: uuid
      example: 'bd9e8d31-86aa-4f42-810e-aa8b68e923e5'
    RequestServiceRequestRequestBody:
      type: object
      description: RSM020
      additionalProperties: false
      required:
        - identification
        - startOfOccurrence
        - serviceRequestType
        - supplierGln
      properties:
        identification:
          type: string
          format: uuid
          description: Unique identification of the transaction.
          example: '34b2d814-344b-4859-916f-00518f4534ce'
        startOfOccurrence:
          type: string
          format: date-time
          description: >-
            Indicates the start time of the proces. Date and time is in UTC+0
            format.
          example: '2010-07-09T22:00:00Z'
        serviceRequestType:
          type: string
          description: Codes used for service requests, i.e. D05 Meter Check
          enum:
            - D01
            - D02
            - D03
            - D04
            - D05
            - D06
            - D07
            - D08
            - D09
            - D10
            - D11
            - D12
            - D13
            - D14
            - D15
          example: D01
        supplierGln:
          type: string
          description: >-
            Unique identification of supplier. The actor is identified by a
            GLN-number or a EIC-code.
          minLength: 13
          maxLength: 13
          pattern: ""
          example: '5799999933318'
        reasonText:
          type: string
          description: Additional infomration to the service request.
          minLength: 1
          maxLength: 512
          pattern: ""
          example: Træffes bedst kl. 12
    RequestMeteredDataRequestBody:
      title: RequestMeteredDataDTO
      type: object
      description: Request metered data data transfer object.
      additionalProperties: false
      required:
        - identification
        - start
        - end
        - timeSeriesType
      properties:
        identification:
          type: string
          format: uuid
          description: Unique identification of the transaction.
          example: '34b2d814-344b-4859-916f-00518f4534ce'
        start:
          type: string
          format: date-time
          description: Beginning of the timeseries.
          nullable: false
        end:
          type: string
          format: date-time
          description: End of the timeseries.
          nullable: false
        timeSeriesType:
          type: string
          description: Type of time series.
          pattern: "^(Additive|Accumulative)$"
          minLength: 0
          maxLength: 20
          example: Additive
    UpsertCustomerContactRequestBody:
      title: UpsertCustomerContactRequestBody
      type: object
      description: Insert and/or update existing Customer contact entity.
      additionalProperties: false
      required:
        - occurrence
        - legalCustomer
        - technicalCustomer
      properties:
        occurrence:
          $ref: '#/components/schemas/Occurrence'
        legalCustomer:
          $ref: '#/components/schemas/Customer'
        technicalCustomer:
          $ref: '#/components/schemas/Customer'
    PersonData:
      type: object
      additionalProperties: false
      description: >-
        Object that contains customer information for heating. The object is
        primarily made to be able to frame attributes in the right context.
      required:
        - customerName
        - email
        - mobileNumber
        - phoneNumber
      properties:
        pNumber:
          type: string
          description: The company's P-number
          minLength: 1
          maxLength: 25
          pattern: ""
        customerName:
          type: string
          description: >-
            A customer can either be registered as a legal person - i.e. a
            company or something else with a CVR no. - or as a private person
          minLength: 1
          maxLength: 256
          pattern: ""
        glnNumber:
          type: string
          description: GLN number of the Customer.
          minLength: 1
          maxLength: 25
          pattern: ""
        cvrNumber:
          type: string
          description: The company's CVR number
          minLength: 1
          maxLength: 25
          pattern: ""
        eanNumber:
          type: string
          description: Ean number for the Customer
          minLength: 1
          maxLength: 25
          pattern: ""
        mobileNumber:
          type: string
          description: >-
            Must be stated if it is registered with the supplier. The
            information is shared only with the network company.
          minLength: 1
          maxLength: 25
          pattern: ""
        phoneNumber:
          type: string
          description: >-
            Must be stated if it is registered with the supplier. The
            information is shared only with the network company.
          minLength: 1
          maxLength: 25
          pattern: ""
        email:
          type: string
          description: >-
            Must be stated if it is registered with the supplier. The
            information is shared only with the network company.
          minLength: 1
          maxLength: 256
          pattern: ""
        attention:
          type: string
          description: Attention
          minLength: 1
          maxLength: 256
          pattern: ""
          example: "IT Department Director"
    Customer:
      type: object
      description: Customer
      additionalProperties: false
      required:
        - personData
        - address
      properties:
        personData:
          $ref: '#/components/schemas/PersonData'
        address:
          $ref: '#/components/schemas/CustomerAddress'
    CustomerAddress:
      type: object
      additionalProperties: false
      description: >-
        Object that contains address information from/to VVS. The object is
        primarily made to be able to frame attributes in the right context.
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier
          example: 91fa8f2e-de39-406d-bdea-f7d407bca560
        streetName:
          type: string
          description: >-
            The full road name defined as: A proper name which designates and
            names a part of the road or path network or similar traffic areas
            and areas.
          minLength: 1
          maxLength: 256
          pattern: ""
        houseNumber:
          type: string
          description: >-
            The house number for the address including any letter. Ex. 1, 2, 3A,
            251C
          minLength: 1
          maxLength: 25
          pattern: ""
        protectedAddress:
          type: boolean
          description: >-
            To be specified. Note that it is both the name of the person and the
            address that are protected and that this is indicated in DataHub at
            the address.
          example: false
        floor:
          type: string
          description: >-
            Designation which indicates which floor the part of the building
            identified by the address is located on. Ex. 1, 2, 3
          minLength: 1
          maxLength: 25
          pattern: ""
        door:
          type: string
          description: >-
            Designation that indicates the access door etc. as the address
            identifies. Ex. Th., Tv. Etc.
          minLength: 1
          maxLength: 25
          pattern: ""
        postBox:
          type: string
          description: Can be specified if c/o or post office box is used
          minLength: 1
          maxLength: 50
          pattern: ""
        citySubDivisionName:
          type: string
          description: Supplementary city name
          minLength: 1
          maxLength: 25
          pattern: ""
        darId:
          type: string
          format: uuid
          description: Specifies either a DAR id or a DAR access address id.
          example: 91fa8f2e-de39-406d-bdea-f7d407bca560
        postalCode:
          type: string
          description: Four-digit code that identifies the postal code. Ex. 6715.
          minLength: 1
          maxLength: 25
          pattern: ""
        cityName:
          type: string
          description: City name. Ex. "Esbjerg North"
          minLength: 1
          maxLength: 256
          pattern: ""
        countryCode:
          type: string
          description: >-
            If the country code is different for DK, then the address cannot be
            DAR validated. Can be specified with a 2-character code according to
            the ISO-3166 standard.
          minLength: 1
          maxLength: 25
          pattern: ""
        streetCode:
          type: string
          description: >-
            Unique identification of the part of a Named Road that affects a
            single municipality
          minLength: 1
          maxLength: 25
          pattern: ""
        municipialityCode:
          type: string
          description: >-
            The municipality code for the municipality to which the relevant
            part of the named road belongs
          minLength: 1
          maxLength: 25
          pattern: ""
        washable:
          type: string
          minLength: 0
          maxLength: 3
          description: >-
            Is Address washable.\n| Code | Description      | DanishTranslation |\n| ---- | ---------------- | ----------------- |\n| D01  | Washable         | Vaskbar           |\n| D02  | Not washable     | Ikke vaskbar      |
          pattern: "^(D01|D02)$"
          nullable: true
          example: "D01"
    Occurrence:
      type: string
      format: date-time
      description: Occurrence
    ProblemDetails:
      title: ProblemDetails
      type: object
      description: >-
        ProblemDetails provides detailed information about an errors that
        occurred during an api call execution.

        This problem object is conform the standard specifications, see
        https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          minLength: 1
          maxLength: 200
          pattern: ""
          nullable: true
          example: https://errors.kmdelements.com/500
        title:
          description: A short, human-readable summary of the problem type.
          type: string
          minLength: 1
          maxLength: 200
          pattern: ""
          nullable: true
          example: Error short description
        status:
          description: >-
            The HTTP status code ([RFC7231], Section 6) generated by the origin
            server for this occurrence of the problem.
          type: integer
          format: int32
          minimum: 200
          maximum: 600
          pattern: ""
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          minLength: 1
          maxLength: 200
          pattern: ""
          nullable: true
          example: Description what exactly happened
        instance:
          description: >-
            A URI reference that identifies the specific occurrence of the
            problem. It may or may not yield further information if
            dereferenced.
          type: string
          minLength: 1
          maxLength: 200
          pattern: ""
          nullable: true
          example: /resources/1
    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: >-
        ValidationProblemDetails provides detailed information about a
        validation errors that occurred during an api call execution.
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1000
              additionalProperties:
                type: array
                description: Array of validation error messages.
                maxItems: 1000
                items:
                  type: string
                  maxLength: 2048
                  pattern: "^.*$"
              nullable: true
  responses:
    '202':
      description: 202 Accepted.
    '400':
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ValidationProblemDetails'
          examples:
            BadRequestExample:
              value:
                type: https://errors.kmdelements.com/400
                title: Bad Request
                status: 400
                detail: Invalid request
                instance: /resources/1
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    '401':
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            UnauthorizedExample:
              value:
                type: https://errors.kmdelements.com/401
                title: Unauthorized
                status: 401
                detail: >-
                  Authorization Token doesn"t satisfy the Token Validation
                  expression.
                instance: /resources/1
    '403':
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ForbiddenExample:
              value:
                type: https://errors.kmdelements.com/403
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources/1
    '404':
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            NotFoundExample:
              value:
                type: https://errors.kmdelements.com/404
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources/1
    '429':
      description: 429 Too Many Requests
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            type: integer
            format: int32
            minimum: 0
            maximum: 1000
            example: 360
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            TooManyRequestsExample:
              value:
                type: https://errors.kmdelements.com/429
                title: Too Many Requests
                status: 360
                detail: Rate limit is exceeded.
                instance: /resources/1
    '499':
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            NotFoundExample:
              value:
                type: https://errors.kmdelements.com/499
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: /resources/1
    '500':
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            InternalServerErrorExample:
              value:
                type: https://errors.kmdelements.com/500
                title: Internal Server Error
                status: 500
                detail: 'body.0.age: Value `Not Int` does not match format `int32`'
                instance: /resources/1
    '503':
      description: 503 Service Unavailable.
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            type: integer
            format: int32
            minimum: 0
            maximum: 1000
            example: 360
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ServiceUnavailableExample:
              value:
                type: https://errors.kmdelements.com/503
                title: Service Unavailable
                status: 503
                detail: >-
                  The server is currently unable to receive requests. Please
                  retry your request.
                instance: /resources/1
    '504':
      description: 504 Gateway Timeout.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            GatewayTimeoutExample:
              value:
                type: https://errors.kmdelements.com/504
                title: Gateway Timeout
                status: 504
                detail: The server response not received in time.
                instance: /resources/1
  securitySchemes:
    Jwt:
      description: |-
        Jwt Authorization header using the Bearer scheme.
      type: http
      scheme: bearer
      bearerFormat: Jwt
