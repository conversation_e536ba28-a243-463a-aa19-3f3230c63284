title: ConnectionPointProcessResultEventPayload
type: object
additionalProperties: false
required:
  - processId
  - processType
  - externalProcessId
properties:
  processId:
    description: Id of process in MDP Connection Points domain.
    type: string
    format: uuid
    nullable: false
  processType:
    $ref: "./ProcessType.yaml"
  processResult:
    $ref: "./OverallState.yaml"
    nullable: true
  completedDate:
    type: string
    description: Date time when the process finished.
    format: date-time
    nullable: true
  externalProcessId:
    description: External process Id (i.e. Installation Forms process Id or other)
    type: string
    format: uuid
    nullable: true
  installationFormsAdditionalDetails:
    additionalProperties: false
    type: object
    required:
      - meterFrames
    properties:
      meterFrames:
        type: array
        maxItems: 999
        description: The meter frames with changes made during the process
        items:
          meterFrame:
          title: MeterFrame
          additionalProperties: false
          type: object
          required:
            - id
            - changeType
          properties:
            id:
              description: The meter frame id
              type: string
              nullable: false
              format: uuid
            changeType:
              description: Type of the change made to the meter frame
              type: string
              nullable: false
              enum:
                - ClosedDown
                - Created
  errors:
    type: array
    maxItems: 999
    description: Process errors.
    items:
      type: string
      maxLength: 9999
      pattern: "^.*$"
    nullable: true
  relatedObjects:
    type: array
    maxItems: 100
    items:
      $ref: './RelatedObject.yaml'
