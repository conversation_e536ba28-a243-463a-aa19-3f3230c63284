asyncapi: 2.6.0
id: https://async.api.kmdelements.com/hes-meter-data-received-event/
info:
  title: HES Meter Data Received Event
  x-maintainers: Team-DP-2
  version: "0.0.12-preview"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    Schema for HES Meter Data Received Events.
    The event will contain the actual data received from physical meter, like readings, meter events.

tags:
  - name: Team-DP-2
    description: Maintained by Team-DP-2

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.event.hes.meter-data-received.v1:
    description: Topic with messages containing events with fetched meter data
    parameters:
       tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Events with meter data
      description: Contains meter data fetched from physical meter
      operationId: meterDataReceivedEvent
      message:
        $ref: "#/components/messages/MeterDataReceivedEvent"

components:
  messages:
    MeterDataReceivedEvent:
      name: MeterDataReceivedEvent
      title: Event with meter data
      summary: Contains meter data fetched from physical meter, like readings, meter events
      contentType: application/json
      payload:
        $ref: "#/components/schemas/MeterDataReceivedEventPayload"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    MeterDataReceivedEventPayload:
      title: Meter data received event payload
      type: object
      additionalProperties: false
      required:
        - meterId
        - meterClockData
      properties:
        meterId:
          type: integer
          format: int64
          description: Meter ID
        readings:
          type: array
          description: List of readings from the meter
          items:
            $ref: "#/components/schemas/Reading"
        meterEventLogs:
          type: array
          description: List of meter event logs
          items:
            $ref: "#/components/schemas/MeterEventLog"
        meterClockData:
          $ref: "#/components/schemas/MeterClockData"

      examples:
        [
          {
            meterId: 10001,
            readings: [
              {
                timestamp: "2023-10-01T12:00:00Z",
                statusCode: 8388608,
                readingValues: [
                  {
                    obisLongName: "1.8.0",
                    value: 1234,
                    scaler: 1,
                    unit: 0
                  }
                ]
              }
            ],
            meterEventLogs: [
              {
                timestamp: "2023-10-01T12:05:10Z",
                statusCode: 0,
                eventLogCode: 52,
                meterEventLogValues: [
                  {
                    obisLongName: "1.8.0",
                    value: "1234",
                    scaler: 1,
                    unit: 0
                  }
                ]
              }
            ],
            meterClockData: {
              clockOffset: 2000,
              clockOffsetOutOfTolerance: false
            }
          }
        ]

    Reading:
      title: Reading
      type: object
      additionalProperties: false
      required:
        - timestamp
        - statusCode
      properties:
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the reading
        statusCode:
          type: integer
          format: int64
          description: Status code of the reading
        readingValues:
          type: array
          description: Values of the reading
          items:
            $ref: "#/components/schemas/ReadingValue"

    MeterEventLog:
      title: One of the event log stored on the meter device
      type: object
      additionalProperties: false
      required:
        - timestamp
        - statusCode
        - eventLogCode
      properties:
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the meter event log
        statusCode:
          type: integer
          format: int64
          description: Status code of the meter event log
        eventLogCode:
          type: integer
          format: int32
          description: Event code of the meter event log
        meterEventLogValues:
          type: array
          description: Values of the meter event log
          items:
            $ref: "#/components/schemas/MeterEventLogValue"

    ReadingValue:
      title: ReadingValue
      type: object
      additionalProperties: false
      required:
        - obisLongName
        - value
        - scaler
        - unit
      properties:
        obisLongName:
          type: string
          description: OBIS long name of the reading register
        value:
          type: integer
          format: int64
          description: Value of the reading
        scaler:
          type: number
          format: double
          description: Scaler for the reading value
        unit:
          type: integer
          format: int32
          description: Unit of the reading value

    MeterEventLogValue:
      title: MeterEventLogValue
      type: object
      additionalProperties: false
      required:
        - obisLongName
        - value
      properties:
        obisLongName:
          type: string
          description: OBIS long name of the meter event log register
        value:
          type: string
          description: Value of the meter event log
        scaler:
          type: number
          format: double
          description: Scaler for the meter event log value
        unit:
          type: integer
          format: int32
          description: Unit of the meter event log value

    MeterClockData:
      title: Meter clock data
      type: object
      additionalProperties: false
      required:
        - clockOffset
        - clockOffsetOutOfTolerance
      properties:
        clockOffset:
          type: integer
          format: int64
          description: Meter clock offset in milliseconds
        clockOffsetOutOfTolerance:
          type: boolean
          description: Indicate if meter clock offset value is bigger than tolerance and meter clock should be corrected

  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
