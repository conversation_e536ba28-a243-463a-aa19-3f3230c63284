title: GetPriceLinkProcessResponse
type: object
description: Price Link Process
required:
  - id
  - connectionPoint
  - meteringPointId
  - meteringPointVersionId
  - supplyType
  - functionCode
  - stateCode
  - startDate
  - createdByUser
  - createdAt
properties:
  id:
    description: The identifier
    type: string
    format: uuid
    example: 6a6018e8-98a7-4f41-a3ee-127fd34eeb37
  connectionPoint:
    $ref: "../schemas/ConnectionPoint.yaml"
  meteringPointId:
    $ref: "../schemas/MeteringPointId.yaml"
  meteringPointVersionId:
    $ref: "../schemas/MeteringPointVersionId.yaml"
  supplyType:
    $ref: "../schemas/SupplyType.yaml"
  functionCode:
    $ref: "../schemas/FunctionCode.yaml"
  stateCode:
    $ref: "../schemas/ProcessStateCode.yaml"
  priceDefinitionId:
    nullable: true
    $ref: "../schemas/PriceDefinitionId.yaml"
  priceTypeCode:
    nullable: true
    $ref: "../schemas/PriceTypeCode.yaml"
  priceId:
    nullable: true
    $ref: "../schemas/PriceId.yaml"
  quantity:
    $ref: "../schemas/Quantity.yaml"
  startDate:
    description: The execution date of the Create Price Link
    type: string
    format: date-time
    example: "2019-11-14T00:55:31.820Z"
  stopDate:
    description: The execution date of the Stop Price Link
    type: string
    format: date-time
    nullable: true
    example: "2019-11-14T00:55:31.820Z"
  completedDate:
    description: Date when process was completed
    type: string
    format: date-time
    nullable: true
    example: "2019-11-14T00:55:31.820Z"
  glnNumber:
    nullable: true
    $ref: "../schemas/GlnNumber.yaml"
  behalfOfUser:
    description: The User on whose behalf the process was created
    nullable: true
    type: object
    oneOf:
      - $ref: "../schemas/User.yaml"
  createdByUser:
    description: The User who created the Process
    type: object
    oneOf:
      - $ref: "../schemas/User.yaml"
  assignedToUser:
    description: The User assigned to the Process
    nullable: true
    type: object
    oneOf:
      - $ref: "../schemas/User.yaml"
  comment:
    description: The comment
    type: string
    pattern: ".*"
    maxLength: 512
    nullable: true
  createdAt:
    description: The entity Create Date
    type: string
    format: date-time
    example: "2019-11-14T00:55:31.820Z"
  modifiedAt:
    description: The entity Modification Date
    type: string
    format: date-time
    nullable: true
    example: "2019-11-14T00:55:31.820Z"
additionalProperties: false
