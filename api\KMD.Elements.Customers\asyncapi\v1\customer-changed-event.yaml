asyncapi: 2.6.0
id: 'https://async.api.kmdelements.com/customer'
info:
  title: Customer changed
  version: 1.0.1
  contact:
    name: KMD Elements
    url: >-
      https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: 'https://www.kmd.net/terms-of-use'
  description: >
    Whenever customer entity is changed (created/edited/deleted) an event is sent.
  x-maintainers: Team-SE-2
tags:
  - name: Team-SE-2
    description: Maintained by
servers:
  local:
    url: localhost
    description: Local
    protocol: kafka
    protocolVersion: 2.6.0

defaultContentType: application/json

channels:
  'kmd.elements.{tenantId}.event.customer.customer-changed.v1':
    description: Topic for changes on customer
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: CUD operation on Object
      description: Create Update Delete operations on Customer
      operationId: customerChangedEvent
      message:
        $ref: '#/components/messages/CustomerChangedEvent'

components:
  messages:
    CustomerChangedEvent:
      name: customerChangedEvent
      title: customerChangedEvent
      summary: Request change on customer.
      contentType: application/json
      payload:
        $ref: '#/components/schemas/CustomerChangedEventPayload'
  schemas:
    CustomerChangedEventPayload:
      title: CustomerMessage
      type: object
      additionalProperties: false
      required:
        - customer
        - eventId
        - eventType
        - startedBy
      properties:
        eventId:
          description: Kafka event object GUID
          type: string
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        eventType:
          description:  |-
            | Code name | Description        |                                                                                                                                                                |
            | --------- | ------------------ |
            | Created   | Customer created.  |
            | Updated   | Customer updated.  |
            | Deleted   | Customer deleted.  |
          type: string
          enum:
            - Created
            - Updated
            - Deleted
          example: Created
        startedBy:
          description: User who started the event
          type: string
          example: "John Apple - <EMAIL>"
          maxlength: 128
        customerObject:
          description: Customer entity.
          type: object
          $ref: '#/components/schemas/CustomerModel'

    CustomerModel:
      title: CustomerModel
      type: object
      additionalProperties: false
      required:
        - id
        - vat
        - changeDate
      properties:
        id:
          description: Customer GUID
          type: string
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        name:
          description: Customer name
          type: ['null', string]
          example: "John Apple"
          maxlength: 132
        customerNumber:
          description: Customer number
          type: ['null', string]
          example: "ext1234567"
          maxlength: 10
        additionalName:
          description: Customer additional name
          type: ['null', string]
          example: "Rasmus"
          maxlength: 256
        attention:
          description: Customer attention
          type: ['null', string]
          example: "Attention"
          maxlength: 40
        protectedName:
          description: Customer protected name
          type: boolean
          example: true
        vat:
          description: Customer Vat
          type: boolean
          example: true
        dateOfBirth:
          description: Customer date of birth if CPR is not provided for person
          type: ['null', string]
          example: "2022-09-07T09:50:30.870Z"
          maxlength: 40
          format: date-time
        personCountryCode:
          description: Customer person country code
          type: ['null', string]
          example: "DK"
          maxlength: 2
        cprNumber:
          description: Customer person CPR number
          type: ['null', string]
          example: "1205871235"
          maxlength: 10
        companyCountryCode:
          description: Customer company country code
          type: ['null', string]
          example: "DK"
          maxlength: 2
        companyIndustryCodeName:
          description: Customer company industry code name
          type: ['null', string]
          example: "Handel med elektricitet"
          maxlength: 1000
        companyIndustryCodeValue:
          description: Customer company industry code value
          type: ['null', string]
          example: "351400"
          maxlength: 6
        cvrNumber:
          description: Customer company CVR number
          type: ['null', string]
          example: "47458714"
          maxlength: 8
        maintainAutomatically:
          description: Customer maintain automatically
          type: ['null', boolean]
          example: true
        additionalCprNumber:
          type: string
          nullable: true
        additionalCvrNumber:
          description: Customer person additional CPR number
          type: ['null', string]
          example: "1205871235"
          maxlength: 10
        eanNumber:
          type: ['null', string]
          minLength: 13
          maxLength: 13
          description: "European Article Number."
          pattern: "^\\d{13}$"
          example: 978020137962
        glnNumber:
          type: ['null', string]
          minLength: 13
          maxLength: 13
          description: "Global Location Number."
          pattern: "^\\d{13}$"
          example: 0847976000005
        comment:
          description: Customer comment
          type: ['null', string]
          example: "Comment"
          maxlength: 500
        companyStartDate:
          description: Customer company start date
          type: ['null', string]
          example: "2022-09-07T09:50:30.870Z"
          maxlength: 40
          format: date-time
        companyEndDate:
          description: Customer company end date
          type: ['null', string]
          example: "2022-09-07T09:50:30.870Z"
          maxlength: 40
          format: date-time
        companyStatus:
          description: Customer company status
          type: ['null', string]
          example: "Status"
          maxlength: 500
        companyRegistryNumberExternalId:
          description: Customer company registry number external id
          type: ['null', string]
          example: "Status"
          maxlength: 500
        primaryAddress:
          $ref: '#/components/schemas/CustomerAddressModel'
        customerAddresses:
          type: array
          items:
            $ref: '#/components/schemas/CustomerAddressModel'
          nullable: true
        changedByUserId:
          description: Customer changed by user id
          type: string
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        changeReason:
          description: Customer change reason
          type: ['null', string]
          example: "Change reason"
          maxlength: 1000
        changeDate:
          description: Customer change date
          type: string
          example: "2022-09-07T09:50:30.870Z"
          maxlength: 40
          format: date-time

    CustomerAddressModel:
      type: object
      description: "Customer address model"
      additionalProperties: false
      required:
        - id
        - addressTypeId
      properties:
        id:
          description: Customer address GUID
          type: string
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        addressTypeId:
          type: integer
          description: "Must be positive address type id integer."
          format: int32
          minimum: 0
          maximum: 2147483647
        contactName:
          description: Customer address contact name
          type: ['null', string]
          example: "Change reason"
          maxLength: 132
        contactName2:
          description: Customer address contact name 2
          type: ['null', string]
          example: "Change reason"
          maxLength: 132
        email:
          type: ['null', string]
          format: email
          description: "If not empty, it has to be valid email address."
          maxLength: 60
        phone:
          description: Customer address phone
          type: ['null', string]
          example: "+48 123456789"
          maxLength: 20
        mobile:
          description: Customer address mobile
          type: ['null', string]
          example: "+48 123456789"
          maxLength: 20
        comment:
          description: Customer address comment
          type: ['null', string]
          example: "Comment"
          maxlength: 500
        address:
          $ref: '#/components/schemas/Address'

    Address:
      type: object
      description: "Address model"
      additionalProperties: false
      required:
        - id
      properties:
        id:
          description: Customer address GUID
          type: string
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        protectedAddress:
          description: Address protected
          type: ['null', boolean]
          example: true
        streetName:
          description: Address street name
          type: ['null', string]
          example: "Street 1"
          maxLength: 40
        streetCode:
          type: ['null', integer]
          format: int32
          example: "12"
          minimum: -2147483648
          maximum: 2147483647
        houseNumber:
          description: Address house number
          type: ['null', string]
          example: "12a"
          maxLength: 10
        floor:
          description: Address floor
          type: ['null', string]
          example: "12"
          maxLength: 4
        room:
          description: Address room
          type: ['null', string]
          example: "12a"
          maxLength: 4
        citySubDivision:
          description: Address city sub division
          type: ['null', string]
          example: "disctrict"
          maxLength: 34
        postalCode:
          description: Address postal code
          type: ['null', string]
          example: "7700"
          maxLength: 10
        postOfficeBox:
          description: Address post office box
          type: ['null', string]
          example: "770"
          maxLength: 40
        city:
          description: Address citi
          type: ['null', string]
          example: "Warsaw"
          maxLength: 125
        municipalityCode:
          type: ['null', integer]
          format: int32
          minimum: -2147483648
          maximum: 2147483647
        countryCode:
          description: Address country code
          type: ['null', string]
          example: "DK"
          maxlength: 2
        comment:
          description: Customer address comment
          type: ['null', string]
          example: "Comment"
          maxlength: 500
        publicRegistryId:
          description: Customer GUID
          type: ['null', string]
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        changeReason:
          description: Address change reason
          type: ['null', string]
          example: "Change reason"
          maxlength: 1000

  parameters:
    TenantId:
      description: Identifier of a tenant.
      schema:
        type: number
