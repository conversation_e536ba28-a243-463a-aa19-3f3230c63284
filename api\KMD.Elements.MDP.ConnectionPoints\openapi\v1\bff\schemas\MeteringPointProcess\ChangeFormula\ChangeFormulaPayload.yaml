type: object
description: |
  Change formula payload
additionalProperties: false
properties:
  formula:
    description: Formula for metering point.
    allOf:
      - $ref: "../Formula.yaml"
  occurrence:
    nullable: false
    description: |
      Date when formula and changes on metering point should start to be applied
    allOf:
      - $ref: "../../DataTypes/DateTime.yaml"
  unitType:
    description: Unit type
    nullable: false
    minLength: 2
    maxLength: 7
    pattern: "^.*$"
    example: "MWH"
    type: string
  meterReadingOccurrence:
    nullable: false
    description: Meter reading occurrence
    minLength: 1
    maxLength: 25
    pattern: "^.*$"
    type: string
  meteringPointVersionId:
    description: Id of Metering Point Version.
    allOf:
      - $ref: "../../DataTypes/ShortString.yaml"
    example: "571313190000020132_2023-07-17"
  subTypeOfMeteringPoint:
    nullable: true
    type: string
    maxLength: 3
    pattern: ^(D01|D02|D03)$
    description: Is mapped to `SubTypeOfMeteringPoint`
