type: object
additionalProperties: false
description: Meter Frame model for list of meter frames for connection point.
required:
  - virtualId
  - supplyType
  - meterFrameNumber
  - meterMissing
  - meterSealed
  - meterWorkConsumerBilled
  - supplyDisconnectType
  - displayName
  - connectionStatus
  - supplyStatus
properties:
  id:
    nullable: true
    description: Meter frame identifier'.
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
  virtualId:
    nullable: true
    description: Internal MDP identifier'.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  supplyType:
    type: integer
    format: int32
    description: |-
      DK: Forbrugsart.
      Defines usage of the Meter Frame: “Electricity”,”Heating”, “Water”.
    maximum: 3
    minimum: 0
    oneOf:
      - $ref: '../SupplyType.yaml'
    example: 1
  meterFrameNumber:
    nullable: true
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    description: |-
      DK: Målerrammenummer.
      Number on the Meter Frame.
    example: '1234'
  meterFrameIdentifier:
    description: Meter frame identifier set when new meter frame is saved to create.
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  displayName:
    description: Display name of meter frame. For existing, it is meter frame number, for new meter frames, it is dummy identifier.
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
  commonReading:
    nullable: true
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
    description: 'Common Reading value list (Nej, Fællesmåling, Kollektiv måling) - default: Nej.'
  meterMissing:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: 'DK: MålerVæk.'
    example: true
  placementSpecification:
    nullable: true
    description: 'DK: Placeringsbeskrivelse.'
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  meterSealDate:
    nullable: true
    description: |-
      DK: Målerplomberingsdato.
      Indicates the date when the meter was sealed.
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  meterSealed:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      DK: MålerPlomberet.
      Indicates whether the meter is sealed.
  meterWorkConsumerBilled:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      DK: MålerYdelseFaktureresEjer.
      Services related to the meter (both technician and administrative work) must be invoiced to the owner connected to the metering point in DataHub.
  connectionStatus:
    description: 'DK: Tilslutningsstatus.'
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  supplyStatus:
    $ref: './MeterFrameSupplyStatusModel.yaml'
  meterReadingType:
    nullable: true
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    description: 'A list of different reading methods a meter can have.'
  supplyDisconnectType:
    $ref: './MeterFrameSupplyDisconnectTypeModel.yaml'
  noMeter:
    nullable: true
    type: boolean
    description: 'DK: Målerfri.'
  isDataLoaded:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether all data for this element was loaded.
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
  hasActiveMeter:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can has active meter.
  needAttention:
    nullable: true
    description: Describe if process need attention.
    allOf:
      - $ref: '../NeedAttention.yaml'
