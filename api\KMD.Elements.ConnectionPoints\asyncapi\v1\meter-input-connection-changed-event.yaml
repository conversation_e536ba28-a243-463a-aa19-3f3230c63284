asyncapi: 2.6.0
id: 'https://async.api.kmdelements.com/meter-input-connection'
info:
  title: Meter Input Connection changed
  version: 1.0.5
  contact:
    name: KMD Elements
    url: >-
      https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: 'https://www.kmd.net/terms-of-use'
  description: >
    Whenever meter input connection entity is changed (created/edited/deleted) an event is sent.
  x-maintainers: Team-MD-2
tags:
  - name: Team-MD-2
    description: Maintained by Team MD 2.
servers:
  local:
    url: localhost
    description: Local
    protocol: kafka
    protocolVersion: 2.6.0

defaultContentType: application/json

channels:
  'kmd.elements.{tenantId}.event.connection-points.meter-input-connection.v1':
    description: Topic for changes on meter input connection
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: Information about Meter Input Connection changes (create/update/delete).
      description: Information about Meter Input Connection changes (create/update/delete).
      operationId: publishMeterInputConnectionChangedEvent
      message:
        $ref: '#/components/messages/MeterInputConnectionChangedEventMessage'
    subscribe:
      summary: Information about Meter Input Connection changes (create/update/delete).
      description: Information about Meter Input Connection changes (create/update/delete).
      operationId: receiveMeterInputConnectionChangedEvent
      message:
        $ref: '#/components/messages/MeterInputConnectionChangedEventMessage'
components:
  messages:
    MeterInputConnectionChangedEventMessage:
      name: MeterInputConnectionChangedEventMessage
      title: MeterInputConnectionChangedEventMessage
      summary: Change event on meter input connection.
      contentType: application/json
      headers:
        $ref: "#/components/schemas/MessageHeaders"
      payload:
        $ref: '#/components/schemas/MessagePayload'
  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      required:
        - tenantId
        - esMessageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        esMessageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d
    MessagePayload:
      title: MessagePayload
      name: MessagePayload
      additionalProperties: false
      type: object
      required:
        - eventType
        - entityFamily
        - entityType
        - timestamp
        - version
        - data
      properties:
        eventType:
          type: string
          pattern: "^(Created|Updated|Deleted)$"
          description: Business event type.
          maxLength: 7
          minLength: 7
        entityFamily:
          type: string
          pattern: "^(MeterInputConnection)$"
          description: Business event entity family.
          maxLength: 50
          minLength: 1
        entityType:
          type: string
          pattern: "^(MeterInputConnection)$"
          description: Business event entity type.
          maxLength: 150
          minLength: 1
        timestamp:
          $ref: '#/components/schemas/DateTime'
          description: Business event timestamp.
        version:
          type: string
          pattern: "^(1\\.0\\.*)$"
          description: Business event version number.
          nullable: false
          maxLength: 5
          minLength: 2
        data:
          description: Business event entity data.
          type: object
          $ref: '#/components/schemas/MeterInputConnectionChangedEvent'

    MeterInputConnectionChangedEvent:
      type: object
      additionalProperties: false
      description: Model of Meter Input Connection.
      required:
        - entityId
        - meterFrameId
        - meterId
        - meterInputNumber
        - meterInputType
        - meterNumber
        - meterType
        - fulfillsRequirements
        - validFrom
      properties:
        entityId:
          description: UUID Id of the Resource
          $ref: '#/components/schemas/GuidField'
        fkMeterFrame:
          name: fk_meterFrame
          description: Technical Id of the Meter Frame
          $ref: '#/components/schemas/GuidField'
        refMeterId:
          name: ref_meterId
          $ref: '#/components/schemas/NonNegativeInteger'
          example: 360
          description: The technical key that uniquely identifies the Meter
        meterInputNumber:
          $ref: '#/components/schemas/NonNegativeInteger'
          example: 360
          description: This field is part of the MeterConfiguration in the Meter domain.
        meterInputType:
          description: Shared list of enums between Meter Frame and Meter domain
          $ref: "#/components/schemas/MeterInputType"
          example: "Internal"
        meterNumber:
          description: |-
            This is the meter number that is communicated to the DataHub.
            The meter number can deviate from the meter number that can be seen on the physical meter,
            though according to guidelines they should match.
          $ref: "#/components/schemas/OneWordString"
          example: "8975439870435"
        meterType:
          $ref: "#/components/schemas/ShortString"
          example: "meter type"
          description: Values comes from the Meter entity in the Meter domain. The field defines the type of Meter assigned.
        fulfillsRequirements:
          type: boolean
          example: true
          description: Boolean updated by validation method that indicates whether Meter fulfils the register requirements.
        connectionTypeValidationPassed:
          description: Boolean updated by validation method that indicates whether Meter fulfils the connection type requirements.
          type: [ 'boolean', 'null' ]
          example: true
        ctValidationPassed:
          description: Boolean updated by validation method that indicates whether Meter fulfils the CT validation requirements.
          type: [ 'boolean', 'null' ]
          example: true
        vtValidationPassed:
          description: Boolean updated by validation method that indicates whether Meter fulfils the VT validation requirements.
          type: [ 'boolean', 'null' ]
          example: true
        meterAttributeRequirementsValidationPassed:
          description: Boolean updated by validation method that indicates whether Meter fulfils the attributes requirements.
          type: boolean
          example: true
        meterAttributeRequirementsLastValidationDate:
          description: Indicates time of the last meter attributes validation check.
          type: [ 'string', 'null' ]
          format: date-time
          example: "2024-06-03T22:00:00.000Z"
        displayConfiguration:
          description: 'Display configuration.'
          type: [ 'string', 'null' ]
          format: uuid
          example: '79f3ec96-3bd4-41f8-bbc2-aa73846c86a7'
        registerConfiguration:
          description: 'Register configuration.'
          type: [ 'string', 'null' ]
          format: uuid
          example: 'f0322843-8742-41ea-91e1-970634c84bb1'
        lastChecked:
          $ref: "#/components/schemas/DateTimeNullable"
          description: Indicates time of the last validation check.
        validationStatus:
          $ref: "#/components/schemas/ShortStringNullable"
          description: Status text on the latest validation check.
        validFrom:
          $ref: "#/components/schemas/DateTime"
          description: Defines the validity period, when the meter is assigned to the meter frame.
        validUntil:
          $ref: "#/components/schemas/DateTimeNullable"
          description: Defines the validity period, when the meter is assigned to the meter frame.
        sysStartTime:
          $ref: "#/components/schemas/DateTimeNullable"
          description: System start time.
        previousSysStartTime:
          $ref: "#/components/schemas/DateTimeNullable"
          description: Previous system start time.

    MeterInputType:
      type: string
      enum:
        - Internal
        - External
        - WiredMbus
        - MBus
        - Pulse
      description: Shared list of enums between Meter Frame and Meter domain

    # --------------------------------------------------------- COMMON DATA TYPES --------------------------------------------------------------------------
    OneWordString:
      pattern: "^.*$"
      type: string
      minLength: 1
      maxLength: 25
      description: 'Max. 25 characters long string with spaces.'
      example: 'Max25CharsWithSpaces.'

    ShortString:
      pattern: "^.*$"
      type: string
      minLength: 1
      maxLength: 50
      description: 'Max. 50 characters long string with spaces.'
      example: 'Max. 50 characters long string with spaces.'

    ShortStringNullable:
      pattern: "^.*$"
      type: ['string', 'null']
      nullable: true
      minLength: 0
      maxLength: 50
      description: 'Max. 50 characters long string with spaces.'
      example: 'Max. 50 characters long string with spaces.'

    NonNegativeInteger:
      type: integer
      format: int32
      minimum: 0
      maximum: 2147483647
      description: 'Integer type in <0,2147483647> range.'
      example: 111

    GuidField:
      type: string
      description: GUID field.
      format: uuid
      example: 23751a83-cae9-44eb-b3ff-50ca1e15c1b8

    DateTimeNullable:
      type: ['string', 'null']
      description: 'Date in UTC ISO 8601 format.'
      format: date-time
      example: 2019-11-14T00:55:31.820Z

    DateTime:
      type: string
      description: 'Date in UTC ISO 8601 format.'
      format: date-time
      example: 2019-11-14T00:55:31.820Z

  parameters:
    TenantId:
      description: Identifier of a tenant.
      schema:
        type: number
