title: HeatingAttributesModel
type: object
additionalProperties: false
description: Heating attributes model.
required:
  - connectionPointCategoryValue
  - installationTypeValue
  - heatWaterHeater
properties:
  connectionPointCategoryValue:
    description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific codelist.
    allOf:
      - $ref: '../_simple-type/Guid.yaml'
  installationTypeValue:
    description: Defines type of installation type. Eg. For apartments, single households, Industrial, Agricultural.
    allOf:
      - $ref: '../_simple-type/Guid.yaml'
  installationDescription:
    allOf:
      - $ref: '../_simple-type/MediumStringNullable.yaml'
    description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
    nullable: true
  numberOfWaterHeater:
    description: The number of Water heaters.
    allOf:
      - $ref: '../_simple-type/NonNegativeIntegerNullable.yaml'
    nullable: true
  heatWaterHeater:
    description: List of different hot water heating controls that can be installed.
    allOf:
      - $ref: '../_simple-type/Guid.yaml'
  waterHeaterType:
    description: List of different water heating types that can be installed.
    nullable: true
    allOf:
      - $ref: '../_simple-type/Guid.yaml'
  heatPlantType:
    description: Lists the different plant types.
    allOf:
      - $ref: '../_simple-type/GuidNullable.yaml'
  heatExchange:
    description: List of different heat exchanger options.
    allOf:
      - $ref: '../_simple-type/GuidNullable.yaml'
  flexAttributeObject:
    $ref: './FlexAttributeObject.yaml'
  decommissioned:
    type: string
    description: Decommissioned date.
    format: date-time
    nullable: true
