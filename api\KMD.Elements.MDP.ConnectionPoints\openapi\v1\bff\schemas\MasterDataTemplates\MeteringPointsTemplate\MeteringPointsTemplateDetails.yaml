title: MeteringPointsTemplateDetails
type: object
description: Metering points template details.
additionalProperties: false
required:
  - id
  - name
properties:
  id:
    $ref: "../../DataTypes/Guid.yaml"
  name:
    $ref: "../../DataTypes/ShortString.yaml"
  description:
    $ref: "../../DataTypes/LongStringNullable.yaml"
  supplyType:
    $ref: "../../SupplyType.yaml"
  meteringPointsRelationsDetails:
    type: array
    maxItems: 50
    items:
      $ref: "./MeteringPointsParentChildRelationDetails.yaml"
    description: List of metering point parent-child relations details.
