openapi: 3.0.3
info:
  title: KMD.Elements.Attachments
  x-maintainers: Team-SE-1
  description: |-
    # KMD.Elements.Attachments
    <br/>
     KMD.Elements.Attachments is part of the KMD Element system for maintaining attachments for specific business objects.
    <br/>
     ## Capabilities
    The API allows to:
    - search, create, update, delete attachments
    - upload files to Azure Storage
    ---

  termsOfService: "https://www.kmd.net/terms-of-use"

  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>

  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"

  version: "1.3"

servers:
  - url: "https://localhost:16001"
    description: Localhost

security:
  - Jwt: []

tags:
  - name: Attachments
    description: API that is responsible for maintaining attachments for business objects

paths:
  /v1/attachments/{attachmentId}:
    parameters:
    - $ref: "#/components/parameters/AttachmentIdInPath"
    get:
      tags:
        - Attachments
      summary: Endpoint that allows to get attachment by id.
      operationId: getAttachment
      x-authorization:
      description:  |-
        ### Remarks
        - Get attachment by id.
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: Success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAttachmentResponseAttachment"
              examples:
                Success:
                  value:
                      id: 1
                      modifiedByUserId: 7abdac43-f3d5-44da-b21e-ebacce9d4737
                      modificationDate: "2023-02-01T00:00:00.0000000Z"
                      noteContent: Lorem ipsum
                      rowVersion: "0x1234567890"
                      files:
                        - id: 9558205a-562d-4393-a075-f07bfe5da793
                          fileName: test.txt
                          fileSizeInBytes: 12312333
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

    delete:
      tags:
        - Attachments
      summary: Endpoint that allows to delete specific attachment
      operationId: deleteAttachment
      x-authorization:
      description: |-
        ### Remarks
        - Remove attachment based on its identifier
      responses:
        "204":
          description: Success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

    put:
      tags:
        - Attachments
      description: |-
        ### Remarks
        - Update attachment based on its identifier
      summary: Endpoint that allows to update specific attachment
      operationId: updateAttachment
      x-authorization:
      responses:
        "204":
          description: Updated.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "409":
          $ref: "#/components/responses/409"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateAttachmentRequest"
            examples:
              ValidRequest:
                value:
                  noteContent: This is example note content
                  files:
                    - id: 93fc98a9-e382-4315-8310-623840bace5f
                      fileName: old.txt
                    - id: c17cd5a0-c69b-4664-bbea-40aa5767cd25
                      fileName: new.txt
                  rowVersion: "0x1234567890"

  /v1/attachments/{attachmentId}/files/{fileId}:
    parameters:
      - $ref: "#/components/parameters/AttachmentIdInPath"
      - $ref: "#/components/parameters/FileIdInPath"
    get:
      tags:
        - Attachments
      operationId: getFileUrl
      x-authorization:
      description: |-
        ### Remarks
        - Gets the URL of file for specific attachment
      summary: Endpoint that allows to get URL for downloading file for specific attachment
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      responses:
        "200":
          description: Success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetFileUrlResponse"
              examples:
                Success:
                  value:
                    fileUrl: "http://azure-storage.test.com?sas=asg122r23dvxfbdfhterfxcsdr"
                    fileName: text.txt
                    fileSizeInBytes: 1231231232
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/attachments:
    get:
      tags:
        - Attachments
      operationId: getAttachments
      x-authorization:
      description: |-
        ### Remarks
        - Gets the attachments based on 'objectType' and 'objectId'
      summary: Endpoint that allows to gather attachments for specific business object
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
        - in: query
          name: objectType
          schema:
            type: string
            pattern: ""
            maxLength: 32
          description: Type of the specific business object
          required: true
          example: WaterMeter
        - in: query
          name: objectId
          schema:
            type: string
            pattern: ""
            maxLength: 36
          description: Identifier of the specific business object
          required: true
          example: "83b05765-dad0-45e3-999a-4a0412958c7a"
      responses:
        "200":
          description: Success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAttachmentResponse"
              examples:
                Success:
                  value:
                    attachments:
                      - id: 1
                        modifiedByUserId: 7abdac43-f3d5-44da-b21e-ebacce9d4737
                        modificationDate: "2023-02-01T00:00:00.0000000Z"
                        noteContent: Lorem ipsum
                        rowVersion: "0x1234567890"
                        files:
                          - id: 9558205a-562d-4393-a075-f07bfe5da793
                            fileName: test.txt
                            fileSizeInBytes: 123123123
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

    post:
      tags:
        - Attachments
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      description: |-
        ### Remarks
        - Create attachment for a specific business object
      summary: Endpoint that allows to create attachment for specific business object
      operationId: createAttachment
      x-authorization:
      responses:
        "201":
          description: Created.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateAttachmentResponse"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAttachmentRequest"
            examples:
              ValidRequest:
                value:
                  objectType: TimeSeries
                  objectId: 12334e81-3fa0-4cff-bc37-3b836ff7b0cc
                  noteContent: null
                  files:
                    - id: aa502c9b-2c74-4691-87d4-010ce8c45880
                      fileName: test.txt

  /v1/attachments/generate-upload-url:
    get:
      description: |-
        ### Remarks
        - Generate URL for upload file to the public storage
      summary: Endpoint that allows to generate URL for upload file to public storage
      tags:
        - Attachments
      operationId: getUploadUrl
      x-authorization:
      responses:
        "200":
          description: Success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUploadUrlResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "422":
          $ref: "#/components/responses/422"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/attachments/commands/create-from-existing-files:
    post:
      tags:
        - Attachments
      parameters:
        - $ref: "#/components/parameters/EsMessageId"
        - $ref: "#/components/parameters/EsCorrelationId"
        - $ref: "#/components/parameters/EsSourceId"
      description: |-
        ### Remarks
        - Allows to create a new attachment from files already uploaded under existing attachments
        - Files are cloned and are independent from source files when the new attachment is created
      summary: Creates a new attachment from files already uploaded under existing attachments.
      operationId: createAttachmentFromExistingFiles
      x-authorization:
      responses:
        "201":
          description: Created.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateAttachmentResponse"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "422":
          $ref: "#/components/responses/422"
        "429":
          $ref: "#/components/responses/429"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAttachmentFromExistingFilesRequest"
            examples:
              ValidRequest:
                value:
                  objectType: EmailMessage
                  objectId: 12334e81-3fa0-4cff-bc37-3b836ff7b0cc
                  noteContent: null
                  sourceFiles:
                    - fileId: aa502c9b-2c74-4691-87d4-010ce8c45880
                      attachmentId: 5643
components:
  parameters:
    EsMessageId:
      name: es-message-id
      description: Unique message ID. The same message id is used when resending the message.
      in: header
      schema:
        type: string
        format: uuid
      required: false
      example: "d1f335a4-1035-4473-8d98-ca41716844a5"
    EsCorrelationId:
      name: es-correlation-id
      description: |
        This is used to 'link' messages together. This can be supplied on a request, so
        that the client can correlate a corresponding reply message.
        The server will place the incoming X-Correlation-ID value as the X-Correlation-ID
        on the outgoing reply. If not supplied on the request, the X-Correlation-ID of the
        reply should be set to the value of the X-Message-ID that was used on the request, if present.
        Given that the X-Correlation-ID is used to `link` messages together,
        it may be reused on more than one message.
      in: header
      schema:
        type: string
        format: uuid
      required: false
      example: "443c6000-78e0-46b6-850a-b5e7ced1d1a9"
    EsSourceId:
      name: es-source-id
      description: The name of the source system sending the message.
      in: header
      schema:
        type: string
        pattern: ""
        maxLength: 1000
      required: false
      example: "a603371e-dee9-4cf4-a953-f1653354e638"

    AttachmentIdInPath:
      name: attachmentId
      in: path
      required: true
      description: Identifier of the attachment.
      schema:
        type: integer
        format: int32
        minimum: 0
        maximum: 2147483647
      example: 12345

    FileIdInPath:
      name: fileId
      in: path
      required: true
      description: Identifier of the file.
      schema:
        type: string
        format: uuid
      example: "4e9197ab-621f-4a28-ac01-5c6f59bc94ac"

  schemas:
    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an api call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          maxLength: 256
          pattern: ""
          nullable: true
          example: "https://errors.kmdelements.com/500"
        title:
          description: "A short, human-readable summary of the problem type."
          type: string
          maxLength: 256
          pattern: ""
          nullable: true
          example: Error short description
        status:
          description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
          type: integer
          format: int32
          minimum: 400
          maximum: 599
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          maxLength: 2048
          pattern: ""
          nullable: true
          example: Description what exactly happened
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          pattern: ""
          maxLength: 32779
          nullable: true
          example: /resources-path/1

    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1000
              additionalProperties:
                type: array
                description: Array of validation error messages.
                maxItems: 1000
                items:
                  type: string
                  maxLength: 2048
                  pattern: ""
              nullable: true

    ProblemDetailsWithErrors:
      title: ProblemDetailsWithErrors
      description: |-
        ProblemDetailsWithErrors provides detailed information about an errors with additional message details that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          properties:
            errors:
              type: array
              description: List of errors
              maxItems: 1000
              items:
                $ref: "#/components/schemas/MessageDetails"
              nullable: true

    MessageDetails:
      type: object
      description: Provides details for the message
      additionalProperties: false
      required:
        - code
        - defaultMessage
      properties:
        propertyNavigation:
          type: string
          pattern: ""
          maxLength: 1024
          description: Message navigation property
          nullable: true
        code:
          type: string
          pattern: ""
          maxLength: 64
          description: Message code
        defaultMessage:
          type: string
          description: Default message
          maxLength: 2048
          pattern: ""
          nullable: true
        parameters:
          type: object
          description: Message parameters
          nullable: true
          maxProperties: 1000
          additionalProperties:
            example:
              "object": "Attachment"

    GetAttachmentResponse:
      title: GetAttachmentResponse
      type: object
      description: Model that contains attachments.
      properties:
        attachments:
          type: array
          maxItems: 1000
          description: List of attachments
          items:
            $ref: "#/components/schemas/GetAttachmentResponseAttachment"
      additionalProperties: false

    GetAttachmentResponseAttachment:
      title: GetAttachmentResponseAttachment
      type: object
      description: Model that contains attachment
      properties:
        id:
          type: integer
          format: int32
          description: Identifier of the attachment
          minimum: 0
          maximum: 2147483647
        modifiedByUserId:
          type: string
          format: uuid
          description: Identifier of the last user who modified the attachment
        modificationDate:
          type: string
          format: date-time
          description: Date of the last modification
        noteContent:
          type: string
          pattern: ""
          maxLength: 1000
          nullable: true
          description: Note content
        gdprCategory:
          $ref: "#/components/schemas/GdprCategory"
        rowVersion:
          type: string
          description: Attachment row version
          format: byte
        files:
          type: array
          description: List of files of attachment
          maxItems: 1000
          items:
            $ref: "#/components/schemas/GetAttachmentResponseAttachmentFile"
        externalResources:
          type: array
          description: List of external resources of attachment
          maxItems: 1000
          items:
            $ref: "#/components/schemas/GetAttachmentResponseAttachmentExternalResource"
      additionalProperties: false

    GetAttachmentResponseAttachmentFile:
      title: GetAttachmentResponseAttachmentFile
      type: object
      description: Model of file attachment.
      properties:
        id:
          type: string
          format: uuid
          description: Identifier of the file
        fileName:
          type: string
          pattern: ""
          maxLength: 128
          description: Name of the file.
        fileSizeInBytes:
          type: integer
          format: int64
          nullable: true
          description: Size of the file in bytes.
          minimum: 0
          maximum: 9223372036854775807
      additionalProperties: false

    GetAttachmentResponseAttachmentExternalResource:
      title: GetAttachmentResponseAttachmentExternalResource
      type: object
      description: Model of external resource attachment.
      properties:
        id:
          type: string
          format: uuid
          description: Identifier of the external resource
        url:
          type: string
          maxLength: 2048
          pattern: ""
          description: URL to the external resource
      additionalProperties: false

    GetFileUrlResponse:
      title: GetFileUrlResponse
      type: object
      description: Model for gettin file url.
      properties:
        fileUrl:
          type: string
          maxLength: 2048
          pattern: ""
          description: Download's URL of the file
        fileName:
          type: string
          pattern: ""
          maxLength: 128
          description: Name of the file.
        fileSizeInBytes:
          type: integer
          format: int64
          nullable: true
          description: Size of the file in bytes.
          minimum: 0
          maximum: 9223372036854775807
      additionalProperties: false

    CreateAttachmentRequest:
      title: CreateAttachmentRequest
      description: Model for creating attachment.
      type: object
      properties:
        objectType:
          type: string
          pattern: ""
          maxLength: 32
          description: Type of the business object.
        objectId:
          type: string
          pattern: ""
          maxLength: 36
          description: Identifier of the business object
        noteContent:
          type: string
          pattern: ""
          maxLength: 1000
          nullable: true
          description: Note content
        gdprCategory:
          description: GDPR category.
          nullable: true
          allOf:
          - $ref: "#/components/schemas/GdprCategory"
        files:
          type: array
          description: List of files to attach
          maxItems: 32
          items:
            $ref: "#/components/schemas/CreateAttachmentRequestFile"
        externalResources:
          type: array
          description: List of external resources to attach
          maxItems: 32
          items:
            $ref: "#/components/schemas/CreateAttachmentRequestExternalResource"
      additionalProperties: false

    CreateAttachmentRequestFile:
      title: CreateAttachmentRequestFile
      type: object
      description: Model to create file attachment.
      properties:
        id:
          type: string
          format: uuid
          description: Identifier of the file
        fileName:
          type: string
          pattern: ""
          maxLength: 1000
          description: Name of the file
      additionalProperties: false

    CreateAttachmentRequestExternalResource:
      title: CreateAttachmentRequestExternalResource
      type: object
      description: Model to create external resource attachment.
      properties:
        url:
          type: string
          pattern: ""
          maxLength: 2048
          description: URL to the external resource
      additionalProperties: false

    CreateAttachmentResponse:
      title: CreateAttachmentResponseModel
      type: object
      description: Model of response for create attachment request.
      properties:
        id:
          type: string
          pattern: ^\d*$
          maxLength: 10
          description: Id of created attachment.
      additionalProperties: false

    UpdateAttachmentRequest:
      title: UpdateAttachmentRequest
      type: object
      description: Model for updating attachment.
      properties:
        noteContent:
          type: string
          pattern: ""
          maxLength: 1000
          nullable: true
          description: Note content
        gdprCategory:
          description: GDPR category.
          nullable: true
          allOf:
          - $ref: "#/components/schemas/GdprCategory"
        files:
          type: array
          description: List of files to attach
          maxItems: 32
          items:
            $ref: "#/components/schemas/UpdateAttachmentRequestFile"
        externalResources:
          type: array
          description: List of external resources to attach
          maxItems: 32
          items:
            $ref: "#/components/schemas/UpdateAttachmentRequestExternalResource"
        rowVersion:
          type: string
          description: Attachment row version
          format: byte
      additionalProperties: false

    UpdateAttachmentRequestFile:
      title: UpdateAttachmentRequestFile
      type: object
      description: Model for updating file attachment.
      properties:
        id:
          type: string
          format: uuid
          description: Identifier of the file
        fileName:
          type: string
          pattern: ""
          maxLength: 1000
          description: Name of the file
      additionalProperties: false

    UpdateAttachmentRequestExternalResource:
      title: UpdateAttachmentRequestExternalResource
      type: object
      description: Model for updating external resource attachment.
      properties:
        id:
          type: string
          format: uuid
          nullable: true
          description: Identifier of the external resource.
        url:
          type: string
          pattern: ""
          maxLength: 2048
          description: URL to the external resource
      additionalProperties: false

    GetUploadUrlResponse:
      title: GetUploadUrlResult
      type: object
      description: Model for request upload url.
      additionalProperties: false
      required:
        - uploadUrl
        - fileId
      properties:
        uploadUrl:
          type: string
          pattern: ""
          maxLength: 2048
          description: URL for file uploading
          nullable: false
        fileId:
          type: string
          format: uuid
          description: Identifier of the file
          nullable: false

    GdprCategory:
      description: Model for GDPR category
      additionalProperties: false
      type: string
      maxLength: 2048
      pattern: "^(NoPersonalData|OrdinaryPersonalData|SensitivePersonalData|ConfidentialPersonalData)$"

    CreateAttachmentFromExistingFilesRequest:
      title: CreateAttachmentFromExistingFilesRequest
      description: Request to create a new attachment from files already uploaded under existing.
      type: object
      required:
        - objectType
        - objectId
        - sourceFiles
      properties:
        objectType:
          type: string
          pattern: ""
          maxLength: 32
          description: Type of the business object.
        objectId:
          type: string
          pattern: ""
          maxLength: 36
          description: Identifier of the business object
        noteContent:
          type: string
          pattern: ""
          maxLength: 1000
          nullable: true
          description: Note content
        gdprCategory:
          description: GDPR category.
          nullable: true
          allOf:
          - $ref: "#/components/schemas/GdprCategory"
        sourceFiles:
          type: array
          description: List of existing file identifiers, to be cloned to the new attachment.
          maxItems: 32
          minItems: 1
          items:
            $ref: "#/components/schemas/CloneAttachmentRequestFile"
        externalResources:
          type: array
          description: List of external resources to attach
          maxItems: 32
          nullable: true
          items:
            $ref: "#/components/schemas/CreateAttachmentRequestExternalResource"
      additionalProperties: false

    CloneAttachmentRequestFile:
      title: CloneAttachmentRequestFile
      type: object
      description: Model to clone file attachment.
      required:
        - fileId
        - attachmentId
      properties:
        fileId:
          type: string
          format: uuid
          description: Identifier of the file
        attachmentId:
          type: integer
          format: int32
          description: Identifier of the attachment
          minimum: 0
          maximum: 2147483647
      additionalProperties: false

  responses:
    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ValidationProblemDetails"
          examples:
            It-is-a-bad-request:
              value:
                type: "https://errors.kmdelements.com/400"
                title: Bad Request
                status: 400
                detail: Invalid request
                instance: /resources-path/1
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            You-shall-not-pass:
              value:
                type: "https://errors.kmdelements.com/401"
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources-path/1
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetailsWithErrors"
          examples:
            Cannot-touch-this:
              value:
                type: "https://errors.kmdelements.com/403"
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources-path/1
                errors:
                  - code: SthIsWrongCode
                    defaultMessage: "This {object} is incorrect"
                    parameters:
                      "object": "Attachment"
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetailsWithErrors"
          examples:
            It-was-here:
              value:
                type: "https://errors.kmdelements.com/404"
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources-path/1
                errors:
                  - code: SthIsWrongCode
                    defaultMessage: "This {object} is incorrect"
                    parameters:
                      "object": "Attachment"
    "409":
      description: Conflict - entity updated concurrently and/or incorrect rowVersion passed and/or resource is conflicting with unique constraint.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetailsWithErrors'
          examples:
            ItsConflictingOrSomethingElseChangedIt:
              value:
                type: 'https://errors.kmdelements.com/409'
                title: Conflict
                status: 409
                detail: Conflict
                instance: /resources-path/1
    "422":
      description: 422 Unprocessable Entity
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetailsWithErrors"
          examples:
            UnprocessableEntity:
              value:
                type: https://errors.kmdelements.com/422
                title: Unprocessable Entity
                status: 422
                detail: Requirements were not met, check Errors for details
                instance: /v1/attachments/commands/.
                errors:
                  - code: SthIsWrongCode
                    defaultMessage: "This {object} is incorrect"
                    parameters:
                      "object": "Attachment"
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          description: Number of seconds until you should try again.
          schema:
            type: integer
            format: int32
            minimum: 0
            maximum: 1000
            example: 360
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            Too-fast-too-quickly-too-soon:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 429
                detail: Rate limit is exceeded.
                instance: /resources-path/1
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            This-should-not-happen:
              value:
                type: "https://errors.kmdelements.com/500"
                title: Internal Server Error
                status: 500
                detail: "body.0.age: Value `Not Int` does not match format `int32`"
                instance: /resources-path/1
    "503":
      description: 503 Service Unavailable.
    "504":
      description: 504 Gateway Timeout.

  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
