type: object
additionalProperties: false
description: Register Requirement model.
required:
  - virtualId
  - meterFrameVirtualId
  - registerRequirementType
  - name
  - meteringComponentId
  - futureRequirement
  - meterReadingRequired
properties:
  virtualId:
    description: Internal Register Requirement MDP identifier'.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  meterFrameVirtualId:
    description: Internal Virtual Meter Frame id.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  registerRequirementType:
    $ref: './RegisterRequirementTypeModel.yaml'
  name:
    description: |-
      DK: Navn.
      Name of requirement (typically from MeteringComponent value list).
    allOf:
      - $ref: '../DataTypes/ShortStringObsolete.yaml'
    nullable: false
  meteringComponentId:
    description: |-
      DK: MålingsKomponentId.
      Shared value list with Meter domain.
      The valuelist contains information about a unique data channel that the utility company must use in domains other than the remote reading domain.
      It is an abstraction level that is configured on the meter configuration, and then referenced in the required domains.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  mainMeterFrameRegisterRequirementId:
    description: |-
      DK: HovedMalerrammeRegisterKravId.
      Mandatory, when “MeterFrameRegisterRequirementType” equals “ControlRequirement”. Used to refer to the main meter registerrequirement.
    allOf:
      - $ref: '../DataTypes/IntegerNullable.yaml'
    nullable: true
  mainMeterFrameRegisterRequirementName:
    description: Used to refer to the main meter register requirement.
    allOf:
      - $ref: '../DataTypes/ShortStringObsoleteNullable.yaml'
  futureRequirement:
    description: |-
      DK: FremtidigtRegisterKra
      Indicates whether the register requirement should only be a future requirement. Default false.
      If set to true Validity period will be ignored.
    type: boolean
    nullable: false
  meterReadingRequired:
    description: |-
      DK: AflæsningPåkrævet
      Indicates whether MeterReading is required for the particular register requirement, when changing meter on Meter Frame.
    type: boolean
    nullable: false
  requiredFrom:
    description: |-
      DK: GyldigFra.
      Indicates when the meter must meet the requirement.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  requiredUntil:
    description: |-
      DK: GyldigTil.
      Indicates from when the meter does not have to meet the requirement.
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
  # rowVersion: I believe its not needed
  #   $ref: '../DataTypes/RowVersion'
