openapi: 3.0.3
info:
  title: KMD.Elements.CentralAddressRegistry.Api
  x-maintainers: Team-MD-2
  description: KMD Elements - Central Address Registry Api
  termsOfService: https://www.kmd.net/terms-of-use
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: License
    url: https://www.kmd.net/terms-of-use
  version: '2.55'

servers:
- url: /
- url: /api/central-address-registry-api

security:
  - Jwt: []

tags:
  - name: Address
    description: API for Addresses management.

paths:
  /v2/address-details/{darId}:
    put:
      x-authorization: CAR.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      tags:
        - Address
      summary: Allows to create/update Address details by providing darId.
      description: >-
        ### Remarks
        By providing darId, the address details will be created/updated in the Central Address Registry by requesting DAR to get the address details.
      operationId: putAddressDetails
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
        - name: darId
          in: path
          description: DAR address identifier.
          required: true
          example: 3773907e-45a2-11ee-be56-0242ac120003
          schema:
            $ref: './schemas/_simple-type/Guid.yaml'
      responses:
        '200':
          description: Success.
          content:
            application/json:
              schema:
                $ref: './schemas/_simple-type/Guid.yaml'
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
        "504":
          $ref: "./responses/common/504.yaml"
        '409':
          $ref: "./responses/common/409.yaml"
        '422':
          $ref: "./responses/common/422.yaml"

  /v2/address-details/non-dar/{id}:
    put:
      x-authorization: CAR.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      tags:
        - Address
      summary: Allows to create/update Non Dar compliant Address details by providing darId.
      description: >-
        ### Remarks
        Allows to manually upsert address details that are not compliant with DAR.
      operationId: putNonDarAddressDetails
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
        - name: id
          in: path
          description: Address details id.
          required: true
          example: 3773907e-45a2-11ee-be56-0242ac120003
          schema:
            $ref: './schemas/_simple-type/Guid.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/address-details/NonDarAddressDetailsUpsertModel.yaml'
        required: true
      responses:
        '200':
          description: Success.
          content:
            application/json:
              schema:
                $ref: './schemas/_simple-type/Guid.yaml'
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
        "504":
          $ref: "./responses/common/504.yaml"
        '409':
          $ref: "./responses/common/409.yaml"
        '422':
          $ref: "./responses/common/422.yaml"

  /v2/addresses/{id}:
    get:
      x-authorization: CAR.Read
      tags:
        - Address
      summary: Gets specific Address.
      operationId: getAddressById
      description: Gets specific Address.
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
        - name: id
          in: path
          required: true
          description: Address id.
          example: 3773907e-45a2-11ee-be56-0242ac120003
          schema:
            $ref: './schemas/_simple-type/Guid.yaml'
      responses:
        '200':
          description: Found Address.
          content:
            application/json:
              schema:
                description: Address object.
                allOf:
                  - $ref: './schemas/address/Address.yaml'
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
        "504":
          $ref: "./responses/common/504.yaml"
        '409':
          $ref: "./responses/common/409.yaml"
        '422':
          $ref: "./responses/common/422.yaml"
    put:
      x-authorization: CAR.Write
      x-authorization-2: Electricity.All, Heating.All, Water.All
      tags:
        - Address
      summary: Allows to create/update Address.
      description: >-
        ### Remarks

        - passed model has to meet validation requirements described below.
      operationId: putAddress
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
        - name: id
          in: path
          required: true
          description: Address id.
          example: 3773907e-45a2-11ee-be56-0242ac120003
          schema:
            $ref: './schemas/_simple-type/Guid.yaml'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './schemas/address.yaml#/components/schemas/AddressUpsertRequest'
        required: true
      responses:
        '204':
          description: Successfully updated.
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
        "504":
          $ref: "./responses/common/504.yaml"
        '409':
          $ref: "./responses/common/409.yaml"
        '422':
          $ref: "./responses/common/422.yaml"
  /v2/addresses:
    get:
      x-authorization: CAR.Read
      tags:
        - Address
      summary: Fetches list of Addresses.
      description: >-
        ### Result
        Returns filtered, sorted and paged list of Addresses according to passed Public Registry Id.
      operationId: getAddressByPublicRegistryId
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
        - name: publicRegistryId
          in: query
          required: true
          description: Public Registry id.
          example: 3773907e-45a2-11ee-be56-0242ac120003
          schema:
            $ref: './schemas/_simple-type/Guid.yaml'
        - name: pageNumber
          in: query
          description: Page number for pagination - defaults to 1.
          required: false
          example: 1
          schema:
            $ref: './schemas/_simple-type/NonNegativeIntegerNullable.yaml'
        - name: pageSize
          in: query
          description: Page size for pagination - limited by application (50) if not passed.
          required: false
          example: 2
          schema:
            $ref: './schemas/_simple-type/NonNegativeIntegerNullable.yaml'
      responses:
        '200':
          description: Addresses returned successfully.
          content:
            application/json:
              schema:
                $ref: './schemas/address.yaml#/components/schemas/GetAddressesPageableResult'
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
        "504":
          $ref: "./responses/common/504.yaml"
        '409':
          $ref: "./responses/common/409.yaml"
        '422':
          $ref: "./responses/common/422.yaml"
  /v2/addresses/search:
    post:
      x-authorization: CAR.Read
      x-authorization-2: Electricity.All, Heating.All, Water.All
      tags:
        - Address
      summary: Fetches list of Addresses.
      description: >-
        ### Result

        Returns filtered, sorted and paged list of Addresses according to passed criteria.
      operationId: getAddressesList
      parameters:
        - $ref: './parameters/es-correlation-id-in-header.yaml'
        - $ref: './parameters/es-message-id-in-header.yaml'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './schemas/address.yaml#/components/schemas/SearchAddressesQuery'
      responses:
        '200':
          description: Address collection.
          content:
            application/json:
              schema:
                $ref: './schemas/address.yaml#/components/schemas/GetAddressesPageableResult'
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
        "504":
          $ref: "./responses/common/504.yaml"
        '409':
          $ref: "./responses/common/409.yaml"
        '422':
          $ref: "./responses/common/422.yaml"

  /v2/addresses-details:
    get:
      summary: Gets address details from DAR system.
      description: |-
        Gets address details from DAR system.
      tags:
        - Address
      operationId: getDarAddress
      x-authorization: CAR.Read
      parameters:
        - $ref: "./parameters/address/addressQuery.yaml"
        - $ref: "./parameters/address/useStub.yaml"
      responses:
        "200":
          description: Dar Address Model object
          content:
            application/json:
              schema:
                description: 'Addresses autocomplete lookup list.'
                type: array
                maxItems: 100
                items:
                  $ref: './schemas/address-dar/AddressDarQueryResult.yaml'
        "400":
          $ref: "./responses/common/400.yaml"
        "401":
          $ref: "./responses/common/401.yaml"
        "403":
          $ref: "./responses/common/403.yaml"
        "404":
          $ref: "./responses/common/404.yaml"
        "429":
          $ref: "./responses/common/429.yaml"
        "500":
          $ref: "./responses/common/500.yaml"
      security:
        - Jwt: [ ]

components:
  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: JWT
