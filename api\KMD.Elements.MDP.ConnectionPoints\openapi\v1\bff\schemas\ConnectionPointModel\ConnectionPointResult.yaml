type: object
additionalProperties: false
description: Full connection point data taken from process masterdata.
required:
  - supplyType
  - tagAssignments
  - address
  - addressLine1
  - addressLine2
properties:
  alternativeInstallationNumber:
    allOf:
      - $ref: './../DataTypes/MediumStringNullable.yaml'
    description: Old installation's number printed on the physical device at the consumer.
    nullable: true
  priceGroupExcluded:
    description: Used to indicate if a connection point is excluded from being included in a price group.
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
  installationNumber:
    allOf:
      - $ref: './../DataTypes/MediumStringNullable.yaml'
    description: Installation number.
  description:
    allOf:
      - $ref: './../DataTypes/LongStringObsoleteNullable.yaml'
    description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.
    nullable: true
  address:
    description: An UUID reference to a master data address.
    allOf:
      - $ref: './../DataTypes/Guid.yaml'
  addressName:
    description: AddressName.
    allOf:
      - $ref: './../DataTypes/ShortStringNullable.yaml'
  addressLine1:
    description: AddressLine1.
    allOf:
      - $ref: './../DataTypes/ShortString.yaml'
  addressLine2:
    description: AddressLine2.
    allOf:
      - $ref: './../DataTypes/ShortString.yaml'
  supplyType:
    $ref: './../SupplyType.yaml'
  addressStatus:
    $ref: './../AddressStatus.yaml'
  addressType:
    $ref: './../AddressType.yaml'
  darStatus:
    $ref: './../DarStatus.yaml'
  lifeCycleStatus:
    $ref: './../LifeCycleStatus.yaml'
  tagAssignments:
    type: array
    description: Tags.
    maxItems: 100
    items:
      $ref: './../TagAssignmentModel.yaml'
  electricityAttributes:
    nullable: true
    type: object
    description: Electricity attributes.
    oneOf:
      - $ref: './../ConnectionPointModel/ElectricityAttributesModel.yaml'
  meterFrames:
    type: array
    description: MeterFrames.
    maxItems: 100
    items:
      $ref: './../MeterFrameModel/MeterFrame.yaml'
  meteringPoints:
    type: array
    description: Metering Points.
    maxItems: 1000000
    items:
      $ref: './../MeteringPointModel/MeteringPoint.yaml'
