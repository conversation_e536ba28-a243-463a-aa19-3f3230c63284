ConnectionPoint:
  title: ConnectionPoint
  type: object
  additionalProperties: false
  description: Connection Point model.
  required:
    - id
    - connectionPointNumber
    - created
    - installationNumber
    - alternativeInstallationNumber
    - priceGroupExcluded
    - supplyType
    - description
    - address
    - addressName
    - addressLine1
    - addressLine2
    - addressStatus
    - addressType
    - darStatus
    - lifeCycleStatus
    - electricityAttributes
    - heatingAttributes
    - waterAttributes
    - tagAssignments
    - internalEquipmentAssignments
    - externalEquipmentAssignments
    - notes
    - createdWithoutInstallationForm
    - changedByUserId
    - rowVersion
  properties:
    id:
      type: string
      description: Connection Point internal identifier.
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    connectionPointNumber:
      type: string
      description: The ConnectionPoint Number.
      example: '2000001'
    created:
        type: string
        description: |-
          DK: Oprettet.
          Creation time stamp of the Meter Frame.
        format: date-time
        example: '2022-09-07T09:50:30.870Z'
    installationNumber:
      description: Installation number.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Installation Number example'
    alternativeInstallationNumber:
      description: Old installation's number printed on the physical device at the consumer.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Alternative Installation Number example'
    priceGroupExcluded:
      description: Used to indicate if a connection point is excluded from being included in a price group.
      type: boolean
      example: true
    supplyType:
      type: array
      items:
        oneOf:
          - $ref: './sub-schemas/SupplyType.schema.yaml#/SupplyType'
      description: |-
        Defines usage of Connection Point: “Electricity”, ”Heating”, “Water”.
      example: "HeatingWater"
    description:
      description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 1000
      example: 'Description example'
    address:
      type: string
      format: uuid
      description: An UUID reference to a master data address.
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    addressName:
      description: AddressName from CAR.
      type: [ 'string', 'null' ]
      pattern: "^.*$"
      minLength: 0
      maxLength: 50
      example: 'Address name'
    addressLine1:
      description: AddressLine1 from CAR.
      type: string
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Address line 1'
    addressLine2:
      description: AddressLine2 from CAR.
      type: string
      pattern: "^.*$"
      minLength: 0
      maxLength: 256
      example: 'Address line 2'
    addressStatus:
      description: List of possible statuses that a master data address can have "Active", "Inactive".
      type: [ 'string', 'null' ]
      pattern: "^(Active|Inactive)$"
      example: Active
    addressType:
      description: List of possible address types that a MasterDataAddressDetails object can hold. Eg. "Address", "AccessAddress".
      type: [ 'string', 'null' ]
      pattern: "^(Primary|Temporary|AccessAddress)$"
      example: Primary
    darStatus:
      description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated".
      type: [ 'string', 'null' ]
      pattern: "^(Yes|No|Temporary)$"
      example: Yes
    lifeCycleStatus:
      description: List of possible life cycle states that the MDR system can put the address into.
      type: [ 'string', 'null' ]
      pattern: "^(ToBeDeleted|Valid|UnderInvestigation)$"
      example: UnderInvestigation
    electricityAttributes:
      description: ElectricityAttributes.
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/ConnectionPointElectricityAttributesModel.schema.yaml#/ConnectionPointElectricityAttributesModel'
    heatingAttributes:
      description: HeatingAttributes.
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/ConnectionPointHeatingAttributesModel.schema.yaml#/ConnectionPointHeatingAttributesModel'
    waterAttributes:
      description: WaterAttributes.
      type: ['object', 'null']
      oneOf:
        - $ref: './sub-schemas/ConnectionPointWaterAttributesModel.schema.yaml#/ConnectionPointWaterAttributesModel'
    tagAssignments:
      description: Tags.
      type: array
      maxItems: 1000000
      items:
        $ref: './sub-schemas/ConnectionPointTagAssignmentModel.schema.yaml#/ConnectionPointTagAssignmentModel'
    notes:
      description: Notes.
      type: array
      maxItems: 1000000
      items:
        $ref: './sub-schemas/ConnectionPointNoteModel.schema.yaml#/ConnectionPointNoteModel'
    createdWithoutInstallationForm:
      description: Indicates whether the connection point was created without an installation form.
      type: boolean
    changedByUserId:
      type: string
      description: Last change user id.
      format: uuid
      example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    rowVersion:
      description: A binary value (base64) used to detect updates to a object and prevent data conflicts. It is incremented each time an update is made to the object.
      type: string
      format: byte
      example: 'AAAAAAAAB+I='
