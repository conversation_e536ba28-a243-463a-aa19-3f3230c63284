openapi: 3.0.3
info:
  title: KMD.Elements.Stubs.DataHub
  x-maintainers: Team-MC-1
  description: KMD Elements - DataHub 3 Stub API
  termsOfService: https://www.kmd.net/terms-of-use
  contact:
    name: KMD Elements
    url: 'https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements'
    email: <EMAIL>
  license:
    name: License
    url: https://www.kmd.net/terms-of-use
  version: '1.10'

servers:
  - url: 'https://localhost:5001'
    description: Local server

security:
  - Jwt: []

paths:
  /v1/datahub3/rsm12/batch:
      post:
        tags:
          - Datahub3
        description: RSM-012
        summary: Endpoint that allows to enqueue RSM-012
        x-authorization: Electricity.All
        operationId: createBatchRsm12
        requestBody:
          description: Request body.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GenerateBatchRsm12Specification"
          required: true
        parameters:
          - $ref: "#/components/parameters/PartyId"
          - $ref: "#/components/parameters/PartyRole"
          - $ref: "#/components/parameters/EsMessageIdInHeader"
          - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        responses:
          "200":
            $ref: "#/components/responses/ActionResult"
          "400":
            $ref: "#/components/responses/400"
          "401":
            $ref: "#/components/responses/401"
          "403":
            $ref: "#/components/responses/403"
          "404":
            $ref: "#/components/responses/404"
          "429":
            $ref: "#/components/responses/429"
          "499":
            $ref: "#/components/responses/499"
          "500":
            $ref: "#/components/responses/500"
          "502":
            $ref: "#/components/responses/502"
          "503":
            $ref: "#/components/responses/503"
          "504":
            $ref: "#/components/responses/504"

  /v1/datahub3/rsm01-confirm:
    post:
      tags:
        - Datahub3
      description: RSM-001 Confirm
      summary: Endpoint that allows to enqueue confirm RSM-001 - confirm request for change of supplier
      x-authorization: Electricity.All
      operationId: createConfirmRsm01
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmRsm01EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm01-reject:
    post:
      tags:
        - Datahub3
      description: RSM-001 Reject
      summary: Endpoint that allows to enqueue reject RSM-001 - reject request for change of supplier
      x-authorization: Electricity.All
      operationId: createRejectRsm01
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectRsm01EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm03:
    post:
      tags:
        - Datahub3
      description: RSM-003 Confirm
      summary: Endpoint that allows to enqueue RSM-003 - request rellocate change of supplier
      x-authorization: Electricity.All
      operationId: createRsm03
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Rsm03EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm04:
    post:
      tags:
        - Datahub3
      description: RSM-004
      summary: Endpoint that allows to enqueue RSM-004 - generic notification
      x-authorization: Electricity.All
      operationId: createRsm04
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Rsm04EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm05-confirm:
    post:
      tags:
        - Datahub3
      description: RSM-005 Confirm
      summary: Endpoint that allows to enqueue confirm RSM-005 - confirm request end of supply
      x-authorization: Electricity.All
      operationId: createConfirmRsm05
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmRsm05EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm05-reject:
    post:
      tags:
        - Datahub3
      description: RSM-005 Reject
      summary: Endpoint that allows to enqueue reject RSM-005 - reject request for end of supply
      x-authorization: Electricity.All
      operationId: createRejectRsm05
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectRsm05EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm06-reject:
    post:
      tags:
        - Datahub3
      description: RSM-006 Reject
      summary: Endpoint that allows to enqueue reject RSM-006 - reject request for accounting point characteristics.
      x-authorization: Electricity.All
      operationId: createRejectRsm06
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectRsm06EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm9:
    post:
      tags:
        - Datahub3
      description: RSM-009
      summary: Endpoint that allows to enqueue RSM-009 - acknowledgement
      x-authorization: Electricity.All
      operationId: createRsm9
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Rsm9EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/QueueType"
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm12:
    post:
      tags:
        - Datahub3
      description: RSM-012
      summary: Endpoint that allows to enqueue RSM-012 - notify validated measure data.
      x-authorization: Electricity.All
      operationId: createRsm12
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Rsm12EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm15-reject:
    post:
      tags:
        - Datahub3
      description: RSM-015 Reject
      summary: Endpoint that allows to enqueue reject RSM-015 - reject the request of validated measure data
      x-authorization: Electricity.All
      operationId: createRejectRsm15
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectRsm15EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm20-confirm:
    post:
      tags:
        - Datahub3
      description: RSM-020 Confirm
      summary: Endpoint that allows to enqueue confirm RSM-020 - confirm service request
      x-authorization: Electricity.All
      operationId: createConfirmRsm20
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmRsm20EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm20-reject:
    post:
      tags:
        - Datahub3
      description: RSM-020 Reject
      summary: Endpoint that allows to enqueue reject RSM-020 - reject service request
      x-authorization: Electricity.All
      operationId: createRejectRsm20
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectRsm20EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm22:
    post:
      tags:
        - Datahub3
      description: RSM-022
      summary: Endpoint that allows to enqueue RSM-022 - accounting point characteristics.
      x-authorization: Electricity.All
      operationId: createRsm22
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Rsm22EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm24-confirm:
    post:
      tags:
        - Datahub3
      description: RSM-024 Confirm
      summary: Endpoint that allows to enqueue confirm RSM-024 - confirm cancellation request
      x-authorization: Electricity.All
      operationId: createConfirmRsm24
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmRsm24EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm24-reject:
    post:
      tags:
        - Datahub3
      description: RSM-024 Reject
      summary: Endpoint that allows to enqueue reject RSM-024 - reject cancellation request
      x-authorization: Electricity.All
      operationId: createRejectRsm24
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectRsm24EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm-25:
    post:
      tags:
        - Datahub3
      description: RSM-025
      summary: Endpoint that allows to enqueue RSM-025 - notify cancellation
      x-authorization: Electricity.All
      operationId: createRsm25
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Rsm25EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm27-confirm:
    post:
      tags:
        - Datahub3
      description: RSM-027 Confirm
      summary: Endpoint that allows to enqueue confirm RSM-027 - confirm request to change customer characteristics.
      x-authorization: Electricity.All
      operationId: createConfirmRsm27
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmRsm27EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm27-reject:
    post:
      tags:
        - Datahub3
      description: RSM-027 Reject
      summary: Endpoint that allows to enqueue reject RSM-027 - reject request to change customer characteristics.
      x-authorization: Electricity.All
      operationId: createRejectRsm27
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectRsm27EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm-28:
    post:
      tags:
        - Datahub3
      description: RSM-028
      summary: Endpoint that allows to enqueue RSM-028 - characteristics of customer at an ap
      x-authorization: Electricity.All
      operationId: createRsm28
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Rsm28EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm30-confirm:
    post:
      tags:
        - Datahub3
      description: RSM-030 Confirm
      summary: Endpoint that allows to enqueue confirm RSM-030 - confirm request to change billing master data.
      x-authorization: Electricity.All
      operationId: createConfirmRsm30
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmRsm30EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm30-reject:
    post:
      tags:
        - Datahub3
      description: RSM-030 Reject
      summary: Endpoint that allows to enqueue reject RSM-030 - reject request to change billing master data.
      x-authorization: Electricity.All
      operationId: createRejectRsm30
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectRsm30EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm31:
    post:
      tags:
        - Datahub3
      description: RSM-031
      summary: Endpoint that allows to enqueue RSM-031 - notify billing master data.
      x-authorization: Electricity.All
      operationId: createRsm31
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Rsm31EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm32-reject:
    post:
      tags:
        - Datahub3
      description: RSM-032 Reject
      summary: Endpoint that allows to enqueue reject RSM-032 - reject request for billing master data.
      x-authorization: Electricity.All
      operationId: createRejectRsm32
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectRsm32EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

  /v1/datahub3/rsm34:
    post:
      tags:
        - Datahub3
      description: RSM-034
      summary: Endpoint that allows to enqueue RSM-034 - notify price list.
      x-authorization: Electricity.All
      operationId: createRsm34
      requestBody:
        description: Request body.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Rsm34EventSpecification"
        required: true
      parameters:
        - $ref: "#/components/parameters/PartyId"
        - $ref: "#/components/parameters/PartyRole"
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
      responses:
        "200":
          $ref: "#/components/responses/ActionResult"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "404":
          $ref: "#/components/responses/404"
        "429":
          $ref: "#/components/responses/429"
        "499":
          $ref: "#/components/responses/499"
        "500":
          $ref: "#/components/responses/500"
        "502":
          $ref: "#/components/responses/502"
        "503":
          $ref: "#/components/responses/503"
        "504":
          $ref: "#/components/responses/504"

components:
  parameters:
    PartyId:
      name: partyId
      description: Gln of market participant
      in: header
      schema:
          type: string
          description: Party indentifier
          pattern: ^\d+$
          minLength: 13
          maxLength: 13
      required: true
      example: "1111111111111"

    PartyRole:
      name: partyRole
      description: Role of the market participant
      in: header
      required: true
      schema:
        type: string
        description: Party market role
        pattern: ^(DDM|DDQ|STS)$
        minLength: 3
        maxLength: 3
      example: "DDM"

    EsMessageIdInHeader:
      name: es-message-id
      description: Unique message ID. The same message id is used when resending the message.
      in: header
      schema:
        type: string
        format: uuid
      required: true
      example: 35b56ea7-1207-43e5-90c0-9b296c446aeb

    EsCorrelationIdInHeader:
      name: es-correlation-id
      description: |
        This is used to "link" messages together. This can be supplied on a request, so
        that the client can correlate a corresponding reply message.
        The server will place the incoming X-Correlation-ID value as the X-Correlation-ID
        on the outgoing reply. If not supplied on the request, the X-Correlation-ID of the
        reply should be set to the value of the X-Message-ID that was used on the request, if present.
        Given that the X-Correlation-ID is used to ‘link’ messages together,
        it may be reused on more than one message.
      in: header
      schema:
        type: string
        format: uuid
      required: false
      example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d

    QueueType:
      name: queueType
      description: On what queue rms will be put
      in: query
      schema:
        type: string
        pattern: ^(aggregations|timeseries|masterdata)$
        maxLength: 12
      required: true
      example: aggregations

  headers:
    RetryAfter:
      description: Number of seconds until you should try again.
      schema:
        type: integer
        format: int32
        minimum: 1
        maximum: 2678400 # 31 days
      required: true
      example: 3600 # 1 hour

  schemas:
    ConfirmRsm01EventSpecification:
      title: ConfirmRsm01EventSpecification
      type: object
      description: Confirm RSM-001 - confirm the request of change of supplier
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    RejectRsm01EventSpecification:
      title: RejectRsm01EventSpecification
      type: object
      description: Reject RSM-001 - reject the request of change of supplier
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    Rsm03EventSpecification:
      title: Rsm03EventSpecification
      type: object
      description: RSM-003 - request for rellocate change of supplier
      additionalProperties: false
      required:
        - meteringPointId
        - businessReason
        - start
      properties:
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"
        start:
          $ref: "#/components/schemas/DateTime"

    Rsm04EventSpecification:
      title: Rsm04EventSpecification
      type: object
      description: RSM-004 - generic notification
      additionalProperties: false
      required:
        - meteringPointId
        - businessReason
      properties:
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"
        validityStart:
          $ref: "#/components/schemas/DateTime"

    ConfirmRsm05EventSpecification:
      title: ConfirmRsm05EventSpecification
      type: object
      description: Confirm RSM-005 - confirm end of supply
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    RejectRsm05EventSpecification:
      title: RejectRsm05EventSpecification
      type: object
      description: Reject RSM-005 - reject end of supply
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    RejectRsm06EventSpecification:
      title: RejectRsm06EventSpecification
      type: object
      description: Reject RSM-006 - reject request accounting point characteristics
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    Rsm9EventSpecification:
      title: RejectRsm9EventSpecification
      type: object
      description: RSM-009 - acknowledgement
      additionalProperties: false
      required:
        - originalTransactionId
        - responseReasonType
      properties:
        originalTransactionId:
          description: Id of a original request.
          type: string
          pattern: "^.*$"
          minLength: 1
          maxLength: 36
        responseReasonType:
          description: Code describing business reason.
          type: string
          pattern: ^.*$
          minLength: 3
          maxLength: 3
          example: "E02"
        reasonText:
          description: Description of rejection reason
          type: string
          pattern: "^.*$"
          maxLength: 1000
          example: "571313190000010454"
          nullable: true

    Rsm12EventSpecification:
      title: Rsm12EventSpecification
      type: object
      description: RSM-012 - notify validated measure data
      additionalProperties: false
      required:
        - meteringPointId
        - periodStart
        - periodEnd
      properties:
        meteringPointId:
          description: Id of metering point.
          type: string
          pattern: "^.*$"
          minLength: 18
          maxLength: 18
          example: "571313190000010454"
        meteringPointType:
          description: Type of metering point.
          type: string
          pattern: "^(D01|D02|D05|D06|D07|D08|D09|D14|D15|D17|D18|D19|D20|D21|D22|E17|E18|E20)$"
          maxLength: 3
          example: "E17"
        periodStart:
          description: Period start.
          type: string
          format: date-time
        periodEnd:
          description: Occurence date with time.
          type: string
          format: date-time
        resolution:
          description: Resolution.
          type: string
          pattern: ^(PT1H|PT15M)$
          maxLength: 5
        unit:
          description: Unit.
          type: string
          pattern: ^(KWH|K3)$
          maxLength: 3
        product:
          description: Product.
          type: string
          pattern: '^(5790001330590|5790001330606|8716867000016|8716867000023|8716867000030|8716867000047)$'
          maxLength: 13
        originalTransactionId:
          description: The id of original transaction RSM-015 message
          type: string
          nullable: true
          pattern: "^.*$"
          maxLength: 36

    RejectRsm15EventSpecification:
      title: RejectRsm15EventSpecification
      type: object
      description: Reject RSM-015 - reject the request of validated measure data
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - responseReasonType
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        responseReasonType:
          description: Code describing business reason.
          type: string
          pattern: ^.*$
          minLength: 3
          maxLength: 3
          example: "E02"
        reasonText:
          description: Description of rejection reason
          type: string
          pattern: "^.*$"
          maxLength: 1000
          example: "571313190000010454"
          nullable: true

    ConfirmRsm20EventSpecification:
      title: ConfirmRsm20EventSpecification
      type: object
      description: Confirm RSM-020 - confirm service request
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    RejectRsm20EventSpecification:
      title: RejectRsm20EventSpecification
      type: object
      description: Reject RSM-020 - reject service request
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    Rsm22EventSpecification:
      title: Rsm22EventSpecification
      type: object
      description: RSM-022 - accounting point characteristics
      additionalProperties: false
      required:
        - businessReason
        - originalTransactionId
        - validityStart
        - meteringPointId
      properties:
        businessReason:
          $ref: "#/components/schemas/BusinessReason"
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        validityStart:
          $ref: "#/components/schemas/DateTime"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"

    ConfirmRsm24EventSpecification:
      title: ConfirmRsm24EventSpecification
      type: object
      description: Confirm RSM-024 - confirm cancellation request
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    RejectRsm24EventSpecification:
      title: RejectRsm24EventSpecification
      type: object
      description: Reject RSM-024 - reject cancellation request
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    Rsm25EventSpecification:
      title: Rsm25EventSpecification
      type: object
      description: RSM-025 - notify cancellation
      additionalProperties: false
      required:
        - meteringPointId
        - businessReason
      properties:
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    ConfirmRsm27EventSpecification:
      title: ConfirmRsm27EventSpecification
      type: object
      description: Confirm RSM-027 - confirm request to change customer characteristics
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    RejectRsm27EventSpecification:
      title: RejectRsm27EventSpecification
      type: object
      description: Reject RSM-027 - reject request to change customer characteristics
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    Rsm28EventSpecification:
      title: Rsm28EventSpecification
      type: object
      description: RSM-028 - characteristics of customer at an ap
      additionalProperties: false
      required:
        - meteringPointId
        - businessReason
      properties:
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"
        validityStart:
          $ref: "#/components/schemas/DateTime"
        cvr:
          $ref: "#/components/schemas/Cvr"
        firstConsumerPartyName:
          description: First and last name of consumer, or company name
          type: string
          pattern: ^.*$
          minLength: 1
          maxLength: 300
          nullable: true
          example: "Lene Nilsen"
        

    ConfirmRsm30EventSpecification:
      title: ConfirmRsm30EventSpecification
      type: object
      description: Confirm RSM-030 - confirm request to change billing master data
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    RejectRsm30EventSpecification:
      title: RejectRsm30EventSpecification
      type: object
      description: Reject RSM-030 - reject request to change billing master data
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    Rsm31EventSpecification:
      title: Rsm31EventSpecification
      type: object
      description: RSM-031 - notify billing master data
      additionalProperties: false
      required:
        - meteringPointId
        - businessReason
      properties:
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    RejectRsm32EventSpecification:
      title: RejectRsm32EventSpecification
      type: object
      description: Reject RSM-032 - reject request for billing master data
      additionalProperties: false
      required:
        - originalTransactionId
        - meteringPointId
        - businessReason
      properties:
        originalTransactionId:
          $ref: "#/components/schemas/OriginalTransactionId"
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"

    Rsm34EventSpecification:
      title: Rsm34EventSpecification
      type: object
      description: RSM-034 - notify price list
      additionalProperties: false
      required:
        - meteringPointId
        - businessReason
      properties:
        meteringPointId:
          $ref: "#/components/schemas/MeteringPointId"
        businessReason:
          $ref: "#/components/schemas/BusinessReason"
    
    GenerateBatchRsm12Specification:
      title: GenerateBatchRsm12Specification
      type: object
      description: Generate batch of RSM-012
      additionalProperties: false
      required:
        - payloadCount
        - messageCount
      properties:
        payloadCount:
         description: Number of generated payloads
         type: integer
         format: int32
         minimum: 1
         maximum: 100000
        messageCount:
         description: Number of generated payloads
         type: integer
         format: int32
         minimum: 1
         maximum: 100000

    OriginalTransactionId:
      description: Id of a original request.
      type: string
      pattern: "^.*$"
      minLength: 1
      maxLength: 36

    MeteringPointId:
      description: Id of metering point.
      type: string
      pattern: "^.*$"
      minLength: 18
      maxLength: 18
      example: "571313190000010454"

    Cvr:
      description: 8 digits Company CVR number
      type: string
      pattern: "^.*$"
      minLength: 8
      maxLength: 8
      nullable: true
      example: "12345678"

    DateTime:
      description: Date with time in UTC ISO 8601 format.
      type: string
      format: date-time
      example: "2019-11-14T00:55:31.820Z"

    BusinessReason:
      description: Code describing business reason.
      type: string
      pattern: ^.*$
      minLength: 3
      maxLength: 3
      example: "E03"

    ActionResult:
      title: ActionResult
      description: Action result.
      type: string
      format: binary
      maxLength: 2147483647

    ProblemDetails:
      title: ProblemDetails
      type: object
      description: |-
        ProblemDetails provides detailed information about an errors that occurred during an API call execution.
        This problem object is conform the standard specifications, see https://tools.ietf.org/html/rfc7807.
      properties:
        type:
          description: The error type.
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
          example: "https://errors.kmdelements.com/500"
        title:
          description: "A short, human-readable summary of the problem type."
          type: string
          maxLength: 256
          pattern: "^.*$"
          nullable: true
          example: Error short description
        status:
          description: "The HTTP status code ([RFC7231], Section 6) generated by the origin server for this occurrence of the problem."
          type: integer
          format: int32
          minimum: 400
          maximum: 599
          nullable: true
          example: 500
        detail:
          description: A human-readable explanation for what exactly happened (in English).
          type: string
          maxLength: 2048
          pattern: "^.*$"
          nullable: true
          example: Description what exactly happened
        instance:
          description: A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          type: string
          maxLength: 32779
          pattern: "^.*$"
          nullable: true
          example: /resources/1

    ValidationProblemDetails:
      title: ValidationProblemDetails
      description: |-
        ValidationProblemDetails provides detailed information about a validation errors that occurred during an api call execution.
      allOf:
        - $ref: "#/components/schemas/ProblemDetails"
        - type: object
          description: Validation error object.
          properties:
            errors:
              type: object
              description: Validation errors.
              maxProperties: 1000
              additionalProperties:
                type: array
                description: Array of validation error messages.
                maxItems: 1000
                items:
                  type: string
                  maxLength: 2048
                  pattern: "^.*$"
              nullable: true

  securitySchemes:
    Jwt:
      description: |-
        JWT Authorization header using the Bearer scheme.
        Example: "{token}"
      type: http
      scheme: bearer
      bearerFormat: JWT

  responses:

    ActionResult:
      description: Result of the request.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ActionResult"

    "400":
      description: 400 Bad Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ValidationProblemDetails"
          examples:
            BadRequestExample:
              value:
                type: "https://errors.kmdelements.com/400"
                title: Bad Request
                status: 400
                detail: "Invalid request"
                instance: "/resources/1"
                errors:
                  name:
                    - name is too long
                  doors:
                    - invalid value
    "401":
      description: 401 Unauthorized.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            UnauthorizedExample:
              value:
                type: 'https://errors.kmdelements.com/401'
                title: Unauthorized
                status: 401
                detail: Authorization Token doesn't satisfy the Token Validation expression.
                instance: /resources-path/1
    "403":
      description: 403 Forbidden.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            ForbiddenExample:
              value:
                type: 'https://errors.kmdelements.com/403'
                title: Forbidden
                status: 403
                detail: User is not authorized to access this resource.
                instance: /resources-path/1
    "404":
      description: 404 Not Found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            NotFoundExample:
              value:
                type: 'https://errors.kmdelements.com/404'
                title: Not Found
                status: 404
                detail: Not Found
                instance: /resources-path/1
    "429":
      description: 429 Too Many Requests
      headers:
        Retry-After:
          $ref: "#/components/headers/RetryAfter"
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            TooManyRequestsExample:
              value:
                type: "https://errors.kmdelements.com/429"
                title: Too Many Requests
                status: 429
                detail: Rate limit is exceeded.
                instance: "/resources/1"
    "499":
      description: 499 Client Closed Request.
      content:
        application/problem+json:
          schema:
            $ref: "#/components/schemas/ProblemDetails"
          examples:
            ClientClosedRequestExample:
              value:
                type: "https://errors.kmdelements.com/499"
                title: Client Closed Request
                status: 499
                detail: Client Closed Request
                instance: "/resources/1"
    "500":
      description: 500 Internal Server Error.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          examples:
            InternalServerErrorExample:
              value:
                type: 'https://errors.kmdelements.com/500'
                title: Internal Server Error
                status: 500
                detail: 'body.0.age: Value `Not Int` does not match format `int32`'
                instance: /resources-path/1
    "502":
      description: 502 Bad Gateway.
    "503":
      description: 503 Service Unavailable.
    "504":
      description: 504 Gateway Timeout.

tags:
  - name: Datahub3
    description: Datahub3 RSM endpoints.
