asyncapi: 2.6.0
id: https://async.api.kmdelements.com/metering-points-api/
info:
  title: Technical Metering Point Entity Event
  version: "4.0.0"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: API allows to asynchronously receive information about Technical Metering Point changes.
  x-maintainers: Team-MD-2

tags:
  - name: Team-MD-2
    description: Maintained by Team MD 2.

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.event.metering-points.technical-metering-point-entity-event.v4:
    description: Topic for Technical Metering Point's entity events.
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Publishes event that informs about changes to Technical Metering Technical Points.
      description: Publishes event that informs about changes to Technical Metering Points.
      operationId: publishTechnicalMeteringPointBusinessEntityEventV3
      message:
        $ref: "#/components/messages/TechnicalMeteringPointBusinessEntityEventMessage"
    subscribe:
      summary: Receive event that informs about Technical Metering Point's entity changes.
      description: Receive event that informs about Technical Metering Point's entity changes.
      operationId: receiveTechnicalMeteringPointBusinessEntityEventV3
      message:
        $ref: '#/components/messages/TechnicalMeteringPointBusinessEntityEventMessage'

components:
  messages:
    TechnicalMeteringPointBusinessEntityEventMessage:
      name: TechnicalMeteringPointBusinessEntityEventMessage
      title: Informs about technical metering point changes - full payload with message metadata.
      summary: Informs about technical metering point changes - full payload with message metadata.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/MessagePayload"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      required:
        - tenant-id
        - es-message-id
        - es-correlation-id
      properties:
        tenant-id:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        es-message-id:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        es-correlation-id:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d
        mpm-correlation-id:
          name: mpm-correlation-id
          description: MPM correlation ID.
          type: string
          example: 35b56ea7120743e590c09b296c446aeb

    MessagePayload:
      title: MessagePayload
      name: MessagePayload
      additionalProperties: false
      type: object
      required:
        - eventType
        - entityFamily
        - entityType
        - timestamp
        - version
        - data
      properties:
        eventType:
          type: string
          pattern: "^(Updated|Deleted)$"
          description: Business event type.
          maxLength: 7
          minLength: 7
        entityFamily:
          type: string
          pattern: "^(TechnicalMeteringPoint)$"
          description: Business event entity family.
          maxLength: 50
          minLength: 1
        entityType:
          type: string
          pattern: "^(TechnicalMeteringPoint)$"
          description: Business event entity type.
          maxLength: 150
          minLength: 1
        timestamp:
          $ref: '#/components/schemas/DateTime'
          description: Business event timestamp.
        version:
          type: string
          pattern: "^(4\\.0\\.\\d+)$"
          description: Business entity event schema version
          nullable: false
          example: 4.0.0
        sysStartTime:
          description: System start time from SQL versioned table. The header is automatically set by TransactionalOutboxWorker.
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        previousSysStartTime:
          description: Previous system start time from SQL versioned table
          allOf:
            - $ref: '#/components/schemas/DateTimeNullable'
        data:
          description: Business event entity data.
          type: [ 'object', 'null' ]
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/TechnicalMeteringPoint'

    # --------------------------------------------------------- ACTUAL DATA OBJECT -------------------------------------------------------------------------
    TechnicalMeteringPoint:
      $ref: "./schemas/technical-metering-point/TechnicalMeteringPoint.schema.yaml#/TechnicalMeteringPoint"

    #-------------------------------
    # Reusable simple types
    #-------------------------------
    DateTime:
      description: 'Date in UTC ISO 8601 format.'
      type: string
      format: date-time
      example: 2019-11-14T00:55:31.820Z

    DateTimeNullable:
      description: 'Date in UTC ISO 8601 format.'
      type: [ 'string', 'null' ]
      format: date-time
      example: 2019-11-14T22:00:00.000Z

  parameters:
    TenantId:
      description: Identifier of a tenant.
      schema:
        type: integer
        example: 1
