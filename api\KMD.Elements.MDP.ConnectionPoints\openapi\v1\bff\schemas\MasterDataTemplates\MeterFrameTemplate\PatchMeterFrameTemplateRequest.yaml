title: PatchMeterFrameTemplateRequest
type: object
description: Data required to upsert meter frame template.
additionalProperties: false
required:
  - name
properties:
  name:
    $ref: "../../DataTypes/ShortString.yaml"
  description:
    $ref: "../../DataTypes/LongStringNullable.yaml"
  meterFrameDefaultValueSetId:
    $ref: "../../DataTypes/GuidNullable.yaml"
  registerRequirementDefaultValueSetIds:
    type: array
    maxItems: 50
    items:
      $ref: "../../DataTypes/Guid.yaml"
    description: List of register requirements default value set identifiers.
