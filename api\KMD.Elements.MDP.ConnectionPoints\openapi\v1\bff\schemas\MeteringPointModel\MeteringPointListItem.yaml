type: object
description: |
  Extended metering point version.
additionalProperties: false
required:
  - isNew
properties:
  typeOfMeteringPoint:
    description: Mapped from DH 'typeOfMeteringPoint'.
    allOf:
      - $ref: '../MeteringPointModel/DataTypes/TypeOfMeteringPoint.yaml'
  subTypeOfMeteringPoint:
    nullable: true
    description: Mapped from DH 'subTypeOfMeteringPoint'.
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
  meteringPointId:
    nullable: true
    description: Mapped from DH 'meteringPointId'.
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
  connectionStatus:
    nullable: true
    description: Mapped from DH 'connectionStatus'.
    allOf:
      - $ref: '../DataTypes/MediumString.yaml'
  formula:
    nullable: true
    description: Formula.
    allOf:
      - $ref: '../DataTypes/DescriptionEmptyStringNullable.yaml'
  meterFrameIdentifier:
    nullable: true
    description: Meter frame identifier.
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
  meterFrameRequirement:
    nullable: true
    description: Meter frame register requirement (id + name).
    allOf:
      - $ref: '../DataTypes/MediumStringNullable.yaml'
  virtualId:
    nullable: true
    description: Internal MP MDP identifier'.
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  isNew:
    nullable: false
    description: Property to inform whether metering point is newly added in this process.
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
  isEditable:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether entity can be editable.
  closeDownMeteringPoint:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether metering point should be closed and has process for it.
  connectMeteringPoint:
    allOf:
      - $ref: '../DataTypes/Boolean.yaml'
    description: |-
      Indicates whether metering point should be connect and has process for it.
  occurrence:
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
    description: |-
      Indicates date of valid from.
  changeConnectionStatusDate:
    nullable: true
    allOf:
      - $ref: '../DataTypes/DateTime.yaml'
    description: |-
      Indicates date of connection change (close down.connect).
  needAttention:
    nullable: true
    description: Describe if process need attention.
    allOf:
      - $ref: '../NeedAttention.yaml'
