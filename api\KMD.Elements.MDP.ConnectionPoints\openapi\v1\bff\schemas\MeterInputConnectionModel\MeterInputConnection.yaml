type: object
additionalProperties: false
description: Model of Meter Input Connection.
required:
  - externalId
  - meterType
properties:
  externalId:
    description: |-
      External id.
    example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
    type: string
    format: uuid
    nullable: false
  meterId:
    type: integer
    nullable: true
    format: int32
    minimum: 0
    maximum: 2147483647
    example: 360
    description: The technical key that uniquely identifies the Meter
  meterInputNumber:
    type: integer
    nullable: true
    format: int32
    minimum: 0
    maximum: 2147483647
    example: 360
    description: This field is part of the MeterConfiguration in the Meter domain.
  meterNumber:
    nullable: true
    description: |-
      This is the meter number that is communicated to the DataHub.
      The meter number can deviate from the meter number that can be seen on the physical meter,
      though according to guidelines they should match.
    allOf:
      - $ref: '../DataTypes/OneWordString.yaml'
  meterInputType:
    description: Shared list of enums between Meter Frame and Meter domain
    nullable: true
    allOf:
      - $ref: "./DataTypes/MeterInputType.yaml"
  meterType:
    example: "meter type"
    description: Values comes from the Meter entity in the Meter domain. The field defines the type of Meter assigned.
    allOf:
      - $ref: "../DataTypes/ShortString.yaml"
  fulfillsRequirements:
    type: boolean
    nullable: true
    example: true
    description: Boolean updated by validation method that indicates whether Meter fulfils the register requirements.
  lastChecked:
    type: string
    format: date-time
    nullable: true
    example: "2022-09-07T09:50:30.870Z"
    description: Indicates time of the last validation check.
  validationStatus:
    description: Status text on the latest validation check.
    allOf:
      - $ref: "../DataTypes/ShortStringNullable.yaml"  
  validFrom:
    type: string
    format: date-time
    nullable: true
    example: "2022-09-07T09:50:30.870Z"
    description: Defines the validity period, when the meter is assigned to the meter frame.
  validUntil:
    type: string
    format: date-time
    nullable: true
    example: "2022-09-07T09:50:30.870Z"
    description: Defines the validity period, when the meter is assigned to the meter frame.
