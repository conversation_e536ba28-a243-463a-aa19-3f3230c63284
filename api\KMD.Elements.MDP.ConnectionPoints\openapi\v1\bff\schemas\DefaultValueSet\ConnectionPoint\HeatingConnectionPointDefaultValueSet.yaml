type: object
description: Model containing electricity attributes of Connection Point Default Value Set.
properties:
  connectionPointCategoryValueId:
    description: Categorization of a ConnectionPoint.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  installationTypeValueId:
    description: Defines type of Connection Point. Eg. For apartments, single households, Industrial, Agricultural.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  heatPlantTypeValueId:
    description: Lists the different plant types.
    nullable: true
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  heatExchangeValueId:
    description: Lists the different plant types.
    nullable: true
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  heatWaterHeaterValueId:
    description: List of different hot water heating controls that can be installed.
    allOf:
      - $ref: '../../DataTypes/GuidNullable.yaml'
  numberOfWaterHeater:
    description: The number of Water heaters.
    allOf:
      - $ref: '../../DataTypes/IntegerNullable.yaml'
    nullable: true
  installationDescription:
      allOf:
        - $ref: '../../DataTypes/MediumStringNullable.yaml'
      description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
