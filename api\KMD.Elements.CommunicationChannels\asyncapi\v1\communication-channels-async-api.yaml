asyncapi: 2.6.0
id: https://async.api.kmdelements.com/communication-channels
info:
  title: Communication Channels kafka API
  version: 1.0.2
  x-maintainers: Team-MO-1
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description:
    API allows to asynchronously send e-mails and receive their statuses.

tags: 
  - name: Team-MO-1
    description: Maintained by Team-MO-1
    
servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka
    
defaultContentType: application/json

channels:
  kmd.elements.{tenantId}.command.communication-channels.send-email.v1:
    description: This topic is used for sending e-mails.
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: Sends email. 
      description: Sends email. 
      operationId: SendEmailCommand
      message:
        $ref: '#/components/messages/SendEmailCommand'
  kmd.elements.{tenantId}.command.communication-channels.merge-and-send-email.v1:
    description: This topic is used for merging and then sending e-mail.
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: Merges and sends email. 
      description: Merges template with data to merge and send it. 
      operationId: MergeAndSendEmailCommand
      message:
        $ref: '#/components/messages/MergeAndSendEmailCommand'
  kmd.elements.{tenantId}.event.communication-channels.email-status-changed.v1:
    description: This topic is used for receiving status of sent email. Event is created when we get info about status change from e-mail provider.
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: Request for checking email status. 
      description: Request for checking email status.
      operationId: EmailStatusChangedEvent
      message:
        $ref: '#/components/messages/EmailStatusChangedEvent'

components:
  messages:
    SendEmailCommand:
      name: SendEmailCommand
      title: Sends e-mail command
      summary: Sends e-mail to recipient(s).
      contentType: application/json
      payload:
        $ref: "#/components/schemas/SendEmailCommandPayload"
    MergeAndSendEmailCommand:
      name: MergeAndSendEmailCommand
      title: Merges and sends e-mail command
      summary: Merges and sends e-mail to recipient(s).
      contentType: application/json
      payload:
        $ref: "#/components/schemas/MergeAndSendEmailCommandPayload"
    EmailStatusChangedEvent:
      name: EmailStatusChangedEvent
      title: Email status changed.
      summary: Receives status of sent email.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/EmailStatusChangedEventPayload"

  schemas:
    SendEmailCommandPayload:
      title: SendEmailCommandMessage
      type: object
      additionalProperties: false
      required:
        - sender
        - recipients
        - subject
        - content
        - correlationId
        - startedByUserId
        - templateId
        - relatedObjectId
        - relatedObjectType
      properties:
        sender:
          type: string
          description: Sender of email
        recipients:
          description: Recipients of email
          type: array
          items:
            type: string
        subject:
          description: Subject of email
          type: string
        content:
          description: Content of email
          type: string
        correlationId:
          description: Technical id used for identifying particular command. It's created by domain which is calling this topic.
          type: string
          format: uuid
        startedByUserId:
            $ref: '#/components/schemas/StartedByUserId'
        templateId:
          description: Id of template file from Communication Templates Service
          type: string
          format: uuid
        relatedObjectId:
            $ref: '#/components/schemas/RelatedObjectId'
        relatedObjectType:
            $ref: '#/components/schemas/RelatedObjectType'
        parentProcess:
          $ref: "#/components/schemas/ParentProcess"
      examples:
        [
          {
            sender: "<EMAIL>",
            recipients:
            [
              "<EMAIL>",
              "<EMAIL>"
            ],
            subject: "Change of regulations",
            content: "Regulations changed",
            correlationId: "c0bdeeef-9ff6-41d7-a4a2-ff17b16db5c9",
            startedByUserId: "9c1aa911-7904-4dc2-8875-98db84316d95",
            templateId: 27ee281d-97f8-4599-942f-ce053c74c83e,
            relatedObjectId: 30c26e2f-1662-4117-b6a1-3be5f5cff111,
            relatedObjectType: "ConnectionPoint"
          }
        ]
    MergeAndSendEmailCommandPayload:
      title: MergeAndSendEmailCommandMessage
      type: object
      additionalProperties: false
      required:
        - sender
        - recipients
        - subject
        - templateId
        - mergeDataObject
        - correlationId
        - startedByUserId
        - relatedObjectId
        - relatedObjectType
      properties:
        sender:
          type: string
          description: Sender of email
        recipients:
          description: Recipients of email
          type: array
          items:
            type: string
        subject:
          description: Subject of email
          type: string
        templateId:
          description: Id of template to merge.
          type: string
          format: uuid
        mergeDataObject:
          description: Object with data to merge.
          type: object
        relatedObjectId:
            $ref: '#/components/schemas/RelatedObjectId'
        relatedObjectType:
            $ref: '#/components/schemas/RelatedObjectType'
        correlationId:
          description: Technical id used for identifying particular command. It's created by domain which is calling this topic.
          type: string
          format: uuid
        startedByUserId:
            $ref: '#/components/schemas/StartedByUserId'
        parentProcess:
          $ref: "#/components/schemas/ParentProcess"
      examples:
        [
          {
            sender: "<EMAIL>",
            recipients:
            [
              "<EMAIL>",
              "<EMAIL>"
            ],
            subject: "Change of regulations",
            templateId: "70652e62-0bf2-4aee-b15f-4db1ccbbc5f3",
            mergeDataObject:
              {
                  Customer: "John Smith"
              },
            correlationId: "c0bdeeef-9ff6-41d7-a4a2-ff17b16db5c9",
            startedByUserId: "9c1aa911-7904-4dc2-8875-98db84316d95",
            relatedObjectId: 30c26e2f-1662-4117-b6a1-3be5f5cff111,
            relatedObjectType: "ConnectionPoint"
          }
        ]
    EmailStatusChangedEventPayload:
      title: EmailStatusChangedEventMessage
      type: object
      additionalProperties: false
      required:
        - subStatus
        - statusMessage
        - correlationId
        - processId
      properties:
        subStatus:
          type: string
          description: Sub-status of sent email
          enum: 
            - UnreadEmail
            - ExceededSendingLimit
            - Bounced
            - RemovedBySecurityScan
            - ConfirmedAccepted
            - ConfirmedDelivered
            - EmailRead
            - EndedByUser
            - TechnicalError
        statusMessage:
          type: string
          description: Additional description of email status
        processId:
          description: Id of created process for sent email.
          type: string
          format: uuid
        correlationId:
          description: Technical id used for identifying particular event.
          type: string
          format: uuid
      examples:
        [
          {
            subStatus: "ExceededSendingLimit",
            statusMessage: "1m messages per day limit is exceeded.",
            correlationId: "c0bdeeef-9ff6-41d7-a4a2-ff17b16db5c9",
            processId: "bb89b343-e9b1-42d4-b7bd-214b2707db79"
          }
        ]
    RelatedObjectId:
        description: Id of object passed for merging.
        type: string
        minLength: 1
    RelatedObjectType:
        type: string
        description: Related business types
        enum:
          - ConnectionPoint
          - MeteringPoint
          - Meterframe
          - Interruption
          - Customer
          - Meter
          - InstallationForm
          - TimeSeries
          - PriceGroup
          - Process
    StartedByUserId:
      type: string
      format: uuid
      description: Id of user that has started the process.
    ParentProcess:
      type: object
      additionalProperties: false
      description: Parent process which wants to start a new work order.
      nullable: true
      required:
        - source
        - onBehalfOfUserId
      properties:
        id:
          type: string
          description: Id of parent process.
          nullable: true
          minLength: 1
          maxLength: 128
          pattern: "^.*$"
        source:
          type: string
          description: Name of domain which wants to start a new communication process.
          minLength: 1
          maxLength: 128
          pattern: "^(InstallationForms|ManualProcess)$"
        action:
          type: string
          description: Type of action to be performed on parent process.
          nullable: true
          minLength: 1
          maxLength: 128
          pattern: "^(NewInstallation|Extension|SealBreach|ChangeMeter|MoveMeter|Termination|ChangeBranchLine|EnergyProduction)$"
        onBehalfOfUserId:
          type: string
          format: uuid
          description: The id of the user, process, or system on whose behalf the process was created.

  parameters:
   TenantId:
     description: Identifier of a tenant.
     schema:
       type: number
