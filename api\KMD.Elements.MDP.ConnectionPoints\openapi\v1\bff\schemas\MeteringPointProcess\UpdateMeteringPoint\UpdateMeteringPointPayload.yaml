type: object
description: Update metering point payload for any supply type
additionalProperties: false
properties:
  meteringPointId:
    nullable: false
    description: MP identifier.
    allOf:
      - $ref: '../../MeteringPointModel/DataTypes/MeteringPointId.yaml'
  meteringPointVersionId:
    nullable: false
    description: Metering point version identifier.
    allOf:
      - $ref: '../../DataTypes/MediumString.yaml'
  supplyType:
    nullable: false
    description: MP supply type.
    allOf:
      - $ref: '../../SupplyType.yaml'
  validFrom:
    nullable: false
    description: Date from which this metering point applies .
    allOf:
      - $ref: '../../DataTypes/DateTime.yaml'
  locationDescription:
    nullable: true
    description: Mapped from DH 'locationDescription' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/LongString.yaml'
  unitType:
    nullable: true
    description: Mapped from DH 'unitType' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumString.yaml'
  meterReadingOccurrence:
    nullable: true
    description: Mapped from DH 'meterReadingOccurrence' in 'ReceivedNotifyMasterDataMeteringPointEvent' from Metis.
    allOf:
      - $ref: '../../DataTypes/MediumString.yaml'
  supplementedInfoBilling:
    pattern: "^.*$"
    type: string
    nullable: true
    minLength: 0
    maxLength: 100
    description: Supplemented billing info for the Metering Point.
    example: A01
  maximumCurrentInAmperes:
    pattern: "^\\d+$"
    nullable: true
    type: string
    description: Is mapped to `MaximumCurrent`
    maxLength: 6
  maximumPowerInKiloWatts:
    pattern: "^(\\d*(?:[.]\\d+)?)?$"
    nullable: true
    type: string
    description: Is mapped to `MaximumPower`
    maxLength: 128
  disconnectionType:
    nullable: true
    type: string
    maxLength: 3
    description: Is mapped to `DisconnectionType`
    pattern: ^(D01|D02)$
  inheritDisconnectionTypeFromMeter:
    nullable: true
    type: boolean
    description: Is mapped to `InheritDisconnectionTypeFromMeter`
