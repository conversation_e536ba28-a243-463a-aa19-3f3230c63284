title: CreateMeterFrameTemplateRequest
type: object
description: Data required to create a new meter frame template.
additionalProperties: false
required:
  - name
  - supplyType
properties:
  name:
    $ref: "../../DataTypes/ShortString.yaml"
  description:
    $ref: "../../DataTypes/LongStringNullable.yaml"
  supplyType:
    $ref: "../../SupplyType.yaml"
  meterFrameDefaultValueSetId:
    $ref: "../../DataTypes/GuidNullable.yaml"
  registerRequirementDefaultValueSetIds:
    type: array
    maxItems: 50
    items:
      $ref: "../../DataTypes/Guid.yaml"
    description: List of register requirements default value set identifiers.
