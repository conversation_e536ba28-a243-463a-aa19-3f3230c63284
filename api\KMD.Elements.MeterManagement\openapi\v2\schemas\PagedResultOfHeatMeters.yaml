type: object
description: Paginated meter search results.
required:
  - pageInfo
  - meters
properties:
  pageInfo:
    $ref: "../kmd-elements-meter-management.yaml#/components/schemas/PagedResult"
  meters:
    type: array
    description: Collection of meters
    minItems: 0
    maxItems: 1000
    items:
      $ref: "../kmd-elements-meter-management.yaml#/components/schemas/HeatMeter"
additionalProperties: false
