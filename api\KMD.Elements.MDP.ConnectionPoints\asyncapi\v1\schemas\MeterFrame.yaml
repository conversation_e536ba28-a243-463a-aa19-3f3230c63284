title: MeterFrame
description: Meter frame model
type: object
additionalProperties: false
properties:
  meterFrameId:
    description: Meter frame Id
    type: string
    format: uuid
    nullable: true
  installInConstructionPhase:
    type: boolean
    nullable: true
    description: Install in construction phase
  commonReading:
    description: 'Common Reading value list (Nej, <PERSON>æ<PERSON>, Kollektiv måling) - default: Nej.'
    $ref: './DataTypes/Guid.yaml'
  applicationsElectricity:
    type: array
    items:
      $ref: "./ApplicationEntryElectricity.yaml"
  branchLineElectricity:
    description: Branch line for electricity
    $ref: "./BranchLineElectricity.yaml"
  connectionElectricity:
    description: Connection point for electricity
    $ref: "./ConnectionElectricity.yaml"
  meterElectricity:
    description: Meter for electricity
    $ref: "./MeterElectricity.yaml"
  gisPropertiesElectricity:
    description: Gis attributes
    $ref: "./MeterFrameGisPropertiesElectricityModel.yaml"

