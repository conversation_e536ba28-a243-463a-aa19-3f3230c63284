asyncapi: 2.6.0
id: 'https://async.api.kmdelements.com/connection-point'
info:
  title: ConnectionPoint changed
  version: 2.0.1
  contact:
    name: KMD Elements
    url: >-
      https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: 'https://www.kmd.net/terms-of-use'
  description: >
    Whenever Connection Point entity is changed (created/edited/deleted) an event is sent.
  x-maintainers: Team-MD-2
tags:
  - name: Team-MD-2
    description: Maintained by Team MD 2.
servers:
  local:
    url: localhost
    description: Local
    protocol: kafka
    protocolVersion: 2.6.0
defaultContentType: application/json
channels:
  'kmd.elements.{tenantId}.event.connection-points.connection-point-changed.v2':
    description: Topic for changes on Connection Point
    parameters:
      tenantId:
        $ref: '#/components/parameters/TenantId'
    publish:
      summary: Information about Connection Point changes (create/update/delete).
      description: Information about Connection Point changes (create/update/delete).
      operationId: publishConnectionPointChangedEvent
      message:
        $ref: '#/components/messages/ConnectionPointChangedEventMessage'
    subscribe:
      summary: Information about Connection Point changes (create/update/delete).
      description: Information about Connection Point changes (create/update/delete).
      operationId: receiveConnectionPointChangedEvent
      message:
        $ref: '#/components/messages/ConnectionPointChangedEventMessage'
components:
  messages:
    ConnectionPointChangedEventMessage:
      name: ConnectionPointChangedEventMessage
      title: ConnectionPointChangedEventMessage
      summary: Change event on Connection Point.
      contentType: application/json
      headers:
        $ref: '#/components/schemas/MessageHeaders'
      payload:
        $ref: '#/components/schemas/MessagePayload'
  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      required:
        - tenantId
        - messageId
        - correlationId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
        messageId:
          name: es-message-id
          description: Unique message ID. The same message id is used when resending the message.
          type: string
          format: uuid
          example: 35b56ea7-1207-43e5-90c0-9b296c446aeb
        correlationId:
          name: es-correlation-id
          description: |
            This is used to "link" messages together. This can be supplied on a request, so
            that the client can correlate a corresponding reply message.
            The server will place the incoming es-correlation-id value as the es-correlation-id
            on the outgoing reply. If not supplied on the request, the es-correlation-id of the
            reply should be set to the value of the es-message-id that was used on the request, if present.
            Given that the es-correlation-id is used to ‘link’ messages together, it may be reused on more than one message.
          type: string
          format: uuid
          example: 8d9d89b3-dadb-4b5c-8f79-ecd6074ff79d
        sourceId:
          name: es-source-id
          description: The name of the source system sending the message.
          type: string
          maxLength: 256
          pattern: '^.*$'
          example: connection-points-api
    MessagePayload:
      title: MessagePayload
      name: MessagePayload
      additionalProperties: false
      type: object
      required:
        - eventType
        - entityFamily
        - entityType
        - timestamp
        - version
        - data
      properties:
        eventType:
          type: string
          pattern: "^(Created|Updated|Deleted)$"
          description: Business event type.
          maxLength: 7
          minLength: 7
        entityFamily:
          type: string
          pattern: "^(ConnectionPoint)$"
          description: Business event entity family.
          maxLength: 50
          minLength: 1
        entityType:
          type: string
          pattern: "^(ConnectionPoint)$"
          description: Business event entity type.
          maxLength: 150
          minLength: 1
        timestamp:
          $ref: '#/components/schemas/DateTime'
          description: Business event timestamp.
        version:
          type: string
          pattern: "^v[0-9]*$"
          description: Business event version number.
          nullable: false
          maxLength: 5
          minLength: 2
        data:
          description: Business event entity data.
          type: [ 'object', 'null' ]
          additionalProperties: false
          oneOf:
            - $ref: '#/components/schemas/ConnectionPoint'
    ConnectionPoint:
      title: ConnectionPoint
      type: object
      additionalProperties: false
      description: Connection Point model.
      required:
        - id
        - connectionPointNumber
        - created
        - installationNumber
        - alternativeInstallationNumber
        - priceGroupExcluded
        - supplyType
        - description
        - address
        - addressName
        - addressLine1
        - addressLine2
        - addressStatus
        - addressType
        - darStatus
        - lifeCycleStatus
        - electricityAttributes
        - heatingAttributes
        - waterAttributes
        - tagAssignments
        - internalEquipmentAssignments
        - externalEquipmentAssignments
        - notes
        - changedByUserId
        - rowVersion
      properties:
        id:
          type: string
          description: Connection Point internal identifier.
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        connectionPointNumber:
          type: string
          description: The ConnectionPoint Number.
          example: '2000001'
        created:
            type: string
            description: |-
              DK: Oprettet.
              Creation time stamp of the Meter Frame.
            format: date-time
            example: '2022-09-07T09:50:30.870Z'
        installationNumber:
          type: string
          description: Installation number.
          example: 'Installation Number example'
          nullable: true
        alternativeInstallationNumber:
          type: string
          description: Old installation's number printed on the physical device at the consumer.
          nullable: true
          example: 'Alternative Installation Number example'
        priceGroupExcluded:
          description: Used to indicate if a connection point is excluded from being included in a price group.
          allOf:
            - $ref: '#/components/schemas/BooleanField'
        supplyType:
          type: integer
          format: int32
          description: |-
            Defines usage of Connection Point: “Electricity”, ”Heating”, “Water”.
          oneOf:
            - $ref: '#/components/schemas/SupplyTypesModel'
          example: 'Heating'
        description:
          type: string
          description: Description field for a connection point. This field is only used for special remarks that cannot fit into other fields.
          nullable: true
          example: 'Description example'
        address:
          type: string
          format: uuid
          description: An UUID reference to a master data address.
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        addressName:
          type: string
          description: AddressName from CAR.
          nullable: true
          example: 'Address name'
        addressLine1:
          type: string
          description: AddressLine1 from CAR.
          example: 'Address line 1'
        addressLine2:
          type: string
          description: AddressLine2 from CAR.
          example: 'Address line 2'
        addressStatus:
          description: AddressStatus from CAR.
          type: ['integer', 'null']
          format: int32
          oneOf:
            - $ref: '#/components/schemas/AddressStatus'
        addressType:
          description: AddressType from CAR.
          type: ['integer', 'null']
          format: int32
          oneOf:
            - $ref: '#/components/schemas/AddressType'
        darStatus:
          description: DarStatus from CAR.
          type: ['integer', 'null']
          format: int32
          oneOf:
            - $ref: '#/components/schemas/DarStatus'
        lifeCycleStatus:
          description: LifeCycleStatus from CAR.
          type: ['integer', 'null']
          format: int32
          oneOf:
            - $ref: '#/components/schemas/LifeCycleStatus'
        electricityAttributes:
          description: ElectricityAttributes.
          type: ['object', 'null']
          oneOf:
            - $ref: '#/components/schemas/ConnectionPointElectricityAttributesModel'
        heatingAttributes:
          description: HeatingAttributes.
          type: ['object', 'null']
          oneOf:
            - $ref: '#/components/schemas/ConnectionPointHeatingAttributesModel'
        waterAttributes:
          description: WaterAttributes.
          type: ['object', 'null']
          oneOf:
            - $ref: '#/components/schemas/ConnectionPointWaterAttributesModel'
        tagAssignments:
          type: array
          description: Tags.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/ConnectionPointTagAssignmentModel'
        notes:
          type: array
          description: Notes.
          maxItems: 1000000
          items:
            $ref: '#/components/schemas/ConnectionPointNoteModel'
        changedByUserId:
          type: string
          description: Last change user id.
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        rowVersion:
          description: A binary value (base64) used to detect updates to a object and prevent data conflicts. It is incremented each time an update is made to the object.
          type: string
          format: byte
          example: 'AAAAAAAAB+I='
    ConnectionPointTagAssignmentModel:
      type: object
      additionalProperties: false
      description: Tag assignment model.
      required:
        - id
        - connectionPointId
        - tagCodeListValueId
        - tagCodeListValue
      properties:
        id:
          type: string
          format: uuid
          description: Id.
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        connectionPointId:
          type: string
          description: ConnectionPointId.
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        tagCodeListValueId:
          type: string
          format: uuid
          description: Tag code list value id.
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        tagCodeListValue:
          type: string
          description: Tag code list value.
          example: 'value'
    ConnectionPointNoteModel:
      type: object
      additionalProperties: false
      description: Note model.
      required:
        - note
      properties:
        note:
          type: string
          description: Note.
          example: 'Note example'
    ConnectionPointElectricityAttributesModel:
      type: object
      additionalProperties: false
      description: Connection Point electricity attributes model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - connectionStatus
        - temporary
      properties:
        connectionPointCategoryValue:
          type: string
          description: 'Categorization of a Connection Point. The Category is selected from a tenant specific code list.'
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        installationTypeValue:
          type: string
          description: 'Defines type of Installation. Eg. For apartments, single households, Industrial, Agricultural.'
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        connectionStatus:
          type: string
          description: 'Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.'
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        consumerCategory:
          type: string
          description: 'Based on the CodeList “DEBranchekoder” the category for defining line of business is selected. This information is decided by the Balance supplier.'
          format: uuid
          nullable: true
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        deMasterDataForms:
          type: integer
          description: DEMasterDataForm that comes from the settlement calculation.
          format: int32
          minimum: -2147483648
          maximum: 2147483647
          nullable: true
          example: 1001
        installationDescription:
          type: string
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
          example: 'Installation description example'
        netSettlementGroup:
          type: string
          description: This field register the net settlement group, which is also used in the market communication (DataHub).
          format: uuid
          nullable: true
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        gridAreaId:
          description: Grid area id.
          pattern: "^.*$"
          nullable: true
          type: string
          minLength: 0
          maxLength: 25
        temporary:
          type: boolean
          description: Set to true, if the Connection Point is temporary.
          example: true
        temporaryUntil:
          type: string
          description: >-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
            grid company.
          format: date-time
          nullable: true
          example: '2022-09-07T09:50:30.870Z'
        flexAttributeObject:
          type: string
          description: >-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: uuid
          nullable: true
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    ConnectionPointHeatingAttributesModel:
      type: object
      additionalProperties: false
      description: Meter frame heating attributes model.
      required:
        - connectionPointCategoryValue
        - connectionStatus
        - installationTypeValue
        - heatWaterHeater
      properties:
        connectionPointCategoryValue:
          type: string
          description: Categorization of a ConnectionPoint. The Category is selected from a tenant specific codelist.
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        connectionStatus:
          type: string
          description: Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        installationTypeValue:
          type: string
          description: Defines type of installation type. Eg. For apartments, single households, Industrial, Agricultural.
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        heatWaterHeater:
          type: string
          description: List of different hot water heating controls that can be installed.
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        installationDescription:
          type: string
          description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
          nullable: true
          example: 'Installation description example'
        numberOfWaterHeater:
          type: integer
          description: The number of Water heaters.
          format: int32
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          example: 2
        waterHeaterType:
          type: string
          description: List of different water heating types that can be installed.
          format: uuid
          nullable: true
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        heatPlantType:
          type: string
          description: Lists the different plant types.
          format: uuid
          nullable: true
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        heatExchange:
          type: string
          description: List of different heat exchanger options.
          format: uuid
          nullable: true
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        flexAttributeObject:
          type: string
          description: >-
            An UUID reference to a settlement object that is used to register flexible attributes about the connection point.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: uuid
          nullable: true
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    ConnectionPointWaterAttributesModel:
      type: object
      additionalProperties: false
      description: Meter frame water attributes model.
      required:
        - connectionPointCategoryValue
        - installationTypeValue
        - connectionStatus
        - temporary
      properties:
        connectionPointCategoryValue:
          type: string
          description: 'Categorization of a Connection Point. The Category is selected from a tenant specific code list.'
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        installationTypeValue:
          type: string
          description: 'Defines type of Installation. Eg. For apartments, single households, Industrial, Agricultural.'
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        connectionStatus:
          type: string
          description: 'Connection status on the connection point. This status is calculated based on the status of the parent metering point for the connection point.'
          format: uuid
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        temporaryUntil:
          type: string
          description: >-
            When creating a temporary installation, you must always specify when the installation is to be closed. This should be determined by the connection regulations of the individual
            grid company.
          format: date-time
          nullable: true
          example: '2022-09-07T09:50:30.870Z'
        installationDescription:
            type: string
            description: This information comes from the “Installationsblanket”. It contains the installer's note from the installation form if applicable.
            nullable: true
            example: 'Installation description example'
        flexAttributeObject:
          type: string
          description: >-
            An UUID reference to a Settlement Object that is used to register flexible attributes about the connection point.
            In order to register these attributes a tenant specific settlement object type will be defined to register specific flexible attributes about an electrical connection point.
          format: uuid
          nullable: true
          example: 1fb098ba-d9c1-4ad8-b178-f8014bca8927
        decommissioned:
          type: string
          description: Decommissioned date.
          format: date-time
          nullable: true
    DarStatus:
      type: integer
      format: int32
      minimum: 1
      maximum: 3
      description: List possible DAR statuses at the address. E.g. "Yes=Is DAR", "Temporary=Not DAR but expected DAR", "No=Permanently not DAR validated".
      x-enumNames:
        - 'Yes'
        - 'No'
        - Temporary
      enum:
        - 1
        - 2
        - 3
      example: 3
    LifeCycleStatus:
      type: integer
      format: int32
      minimum: 1
      maximum: 3
      description: List of possible life cycle states that the MDR system can put the address into.
      x-enumNames:
        - ToBeDeleted
        - Valid
        - UnderInvestigation
      enum:
        - 1
        - 2
        - 3
      example: 3
    SupplyTypesModel:
      type: integer
      format: int32
      minimum: 1
      maximum: 7
      description: List of possible supply types "Electricity", "Heating", "Water"
      x-enumFlags: true
      x-enumNames:
        - Electricity
        - Heating
        - Water
      enum:
        - 1
        - 2
        - 4
      example: 3
    AddressStatus:
      type: integer
      format: int32
      minimum: 1
      maximum: 2
      description: List of possible statuses that a master data address can have "Active", "Inactive".
      x-enumNames:
        - Active
        - Inactive
      enum:
        - 1
        - 2
      example: 2
    AddressType:
      type: integer
      format: int32
      minimum: 1
      maximum: 3
      description: List of possible address types that a MasterDataAddressDetails object can hold. Eg. "Address", "AccessAddress".
      x-enumNames:
        - Primary
        - Temporary
        - AccessAddress
      enum:
        - 1
        - 2
        - 3
      example: 3

    # --------------------------------------------------------- COMMON DATA TYPES --------------------------------------------------------------------------
    DateTime:
      type: string
      description: 'Date in UTC ISO 8601 format.'
      format: date-time
      example: 2019-11-14T00:55:31.820Z
    BooleanField:
      type: boolean
      description: Boolean field.
      example: true
  parameters:
    TenantId:
      description: Identifier of a tenant.
      schema:
        type: number
