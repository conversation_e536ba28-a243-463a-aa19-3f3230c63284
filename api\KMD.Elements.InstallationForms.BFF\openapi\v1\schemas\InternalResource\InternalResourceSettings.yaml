title: InternalResourceSettings
description: Internal resource settings
type: object
additionalProperties: false
required:
  - sender
  - receiver
  - subject
  - templateId
properties:
  sender:
    type: string
    format: email
    nullable: false
    minLength: 1
    maxLength: 320
    example: <EMAIL>
    description: E-mail sender.
  receiver:
    type: string
    format: email
    nullable: false
    minLength: 1
    maxLength: 320
    example: <EMAIL>
    description: E-mail receiver.
  subject:
    type: string
    nullable: false
    minLength: 1
    maxLength: 200
    pattern: "^(?:[^<]*|.*<(FormNumber|FormAddress|CaseWorkerName|CaseWorkerEmail|CaseWorkerPhoneNumber)>.*)*$"
    description: E-mail subject (can contain data tags).
  templateId:
    type: string
    format: uuid
    description: Master data process template Id
    nullable: false