title: AddConnectionPointModel
type: object
additionalProperties: false
description: Add connection point model.
required:
  - supplyType
  - tagAssignments
  - priceGroupExcluded
  - address
properties:
  alternativeInstallationNumber:
    $ref: './AlternativeInstallationNumber.yaml'
  installationNumber:
    $ref: './InstallationNumber.yaml'
  description:
    $ref: './Description.yaml'
  address:
    $ref: './Address.yaml'
  supplyType:
    $ref: '../common/SupplyTypesModel.yaml'
  tagAssignments:
    type: array
    description: Tags.
    maxItems: 1000000
    items:
      $ref: './AddTagAssignmentModel.yaml'
  priceGroupExcluded:
      $ref: './PriceGroupExcluded.yaml'
  electricityAttributes:
    nullable: true
    type: object
    description: Electricity attributes.
    oneOf:
      - $ref: './ElectricityAttributesModel.yaml'
  heatingAttributes:
    nullable: true
    type: object
    description: Heating attributes.
    oneOf:
      - $ref: './HeatingAttributesModel.yaml'
  waterAttributes:
    nullable: true
    type: object
    description: Water attributes.
    oneOf:
      - $ref: './WaterAttributesModel.yaml'
  createdWithoutInstallationForm:
    $ref: './CreatedWithoutInstallationFormNullable.yaml'
