description: The characteristic of the product
type: object
nullable: true
additionalProperties: false
required:
  - identification
properties:
  identification:
    nullable: false
    type: string
    description: Is mapped to `Identification`
    minLength: 1
    maxLength: 13
    pattern: ^(5790001330590|5790001330606|8716867000016|8716867000023|8716867000030|8716867000047)$
  unitType:
    description: Measurement unit
    nullable: true
    allOf:
      - $ref: '../MeteringPointModel/DataTypes/UnitType.yaml'