type: object
description: Base details of a default value set.
additionalProperties: false
required:
  - id
  - name
  - supplyType
properties:
  id:
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
    description: Default Value Set ID.
  name:
    allOf:
      - $ref: '../DataTypes/ShortString.yaml'
    description: Default Value Set name.
  supplyType:
    $ref: '../SupplyType.yaml'
    description: Supply type of the default value set related to business object.
  rowVersion:
    description: Row version.
    allOf:
      - $ref: '../DataTypes/RowVersion.yaml'
    nullable: true
