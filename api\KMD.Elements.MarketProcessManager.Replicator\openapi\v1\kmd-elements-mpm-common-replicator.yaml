openapi: 3.0.3
info:
  title: KMD.Elements.MarketProcessManager.Replicator
  x-maintainers: Team-MC-1
  description: |-
    ## MarketProcessManager.Replicator
    Service responsible for replicating messages published in the DHM/DHS, transferring to the DHS/DHM layer with simultaneous tenant mapping.
  termsOfService: "https://www.kmd.net/terms-of-use"
  contact:
    name: KMD Elements
    url: "https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements"
    email: <EMAIL>
  license:
    name: License
    url: "https://www.kmd.net/terms-of-use"
  version: "1.25"

servers:
  - url: "https://localhost:5051"
    description: Localhost

tags:
  - name: MeteringPointResults
    description: Methods (on DHM side) allow to send result of started processes of create or update Metering point in DataHub and push it into dedicated kafka topics.
  - name: MeteringPoints
    description: Process group (on DHS side) responsible for create process for creating/updating MeteringPoint. The processes created will be sent to DataHub.

paths:
  /v1/metering-point-internal-formula-update-processes:
    post:
      tags:
        - MeteringPoints
      summary: Start of the process for update Metering Point Formula in Elements
      description: Start of the process in MPM for sending update Metering Point Formula in Elements
      operationId: updateMeteringPointFormula
      x-authorization: Electricity.All
      x-authorization-1: PCMarketCom.Read
      requestBody:
        description:
          The model includes characteristics describing Metering Point Formula.
          Identification property will be used for feedback communication.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InternalUpdateMeteringPointFormula"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMpmCorrelationId"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: Add metering point formula update request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/metering-point-internal-formula-update-process-results:
    post:
      tags:
        - MeteringPointResults
      summary: Receive result of started process and information about success or rejection with reason.
      description:
        Receive result of started in MPM process update Metering Point Formula,
        information about success, error with  rejection reason.
        Message will be transform to destination Kafka message and push into Kafka topic.
      operationId: updateMeteringPointFormulaResult
      x-authorization: Electricity.All
      x-authorization-1: PCMarketCom.Read
      requestBody:
        description: The model includes characteristics describing result of Process responsible for Update Metering Point Formula.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InternalUpdateMeteringPointFormulaResult"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyType"
        - $ref: "#/components/parameters/AcquiredMessageId"
      responses:
        "202":
          description: Add metering point formula update process result request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/metering-point-internal-customer-update-processes:
    post:
      tags:
        - MeteringPoints
      summary: Start of the process for update Metering Point Customer in Elements
      description: Start of the process in MPM for sending update Metering Point Customer in Elements
      operationId: updateMeteringPointCustomer
      x-authorization: Electricity.All
      x-authorization-1: PCMarketCom.Read
      requestBody:
        description:
          The model includes characteristics describing Metering Point Customer.
          Identification property will be used for feedback communication.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InternalUpdateMeteringPointCustomer"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMpmCorrelationId"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: Add metering point customer update request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/metering-point-internal-customer-update-process-results:
    post:
      tags:
        - MeteringPointResults
      summary: Receive result of started process and information about success or rejection with reason.
      description:
        Receive result of started in MPM process update Metering Point Customer,
        information about success, error with  rejection reason.
        Message will be transform to destination Kafka message and push into Kafka topic.
      operationId: updateMeteringPointCustomerResult
      x-authorization: Electricity.All
      x-authorization-1: PCMarketCom.Read
      requestBody:
        description: The model includes characteristics describing result of Process responsible for Update Metering Point Customer.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InternalUpdateMeteringPointCustomerResult"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyType"
        - $ref: "#/components/parameters/AcquiredMessageId"
      responses:
        "202":
          description: Add metering point customer update process result request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/metering-point-internal-update-processes:
    post:
      tags:
        - MeteringPoints
      summary: Start of the process for update Metering Point in Elements
      description: Start of the process in MPM for sending update Metering Point in Elements
      operationId: updateMeteringPoint
      x-authorization: Electricity.All
      x-authorization-1: PCMarketCom.Read
      requestBody:
        description:
          The model includes characteristics describing Metering Point.
          Identification property will be used for feedback communication.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InternalUpdateMeteringPoint"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/EsMpmCorrelationId"
        - $ref: "#/components/parameters/SupplyType"
      responses:
        "202":
          description: Add metering point update request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

  /v1/metering-point-internal-update-process-results:
    post:
      tags:
        - MeteringPointResults
      summary: Receive result of started process and information about success or rejection with reason.
      description:
        Receive result of started in MPM process update Metering Point,
        information about success, error with  rejection reason.
        Message will be transform to destination Kafka message and push into Kafka topic.
      operationId: updateMeteringPointResult
      x-authorization: Electricity.All
      x-authorization-1: PCMarketCom.Read
      requestBody:
        description: The model includes characteristics describing result of Process responsible for Update Metering Point.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InternalUpdateMeteringPointResult"
        required: true
      parameters:
        - $ref: "#/components/parameters/EsMessageIdInHeader"
        - $ref: "#/components/parameters/EsCorrelationIdInHeader"
        - $ref: "#/components/parameters/SupplyType"
        - $ref: "#/components/parameters/AcquiredMessageId"
      responses:
        "202":
          description: Add metering point update process result request with success.
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
      security:
        - Jwt: []

components:
  schemas:
    InternalUpdateMeteringPointFormula:
      $ref: './schemas/InternalUpdateMeteringPointFormula.yaml'

    InternalUpdateMeteringPointFormulaResult:
      $ref: './schemas/InternalUpdateMeteringPointFormulaResult.yaml'

    InternalUpdateMeteringPointCustomer:
      $ref: './schemas/InternalUpdateMeteringPointCustomer.yaml'

    InternalUpdateMeteringPointCustomerResult:
      $ref: './schemas/InternalUpdateMeteringPointCustomerResult.yaml'

    InternalUpdateMeteringPoint:
      $ref: './schemas/InternalUpdateMeteringPoint.yaml'

    InternalUpdateMeteringPointResult:
      $ref: './schemas/InternalUpdateMeteringPointResult.yaml'

  responses:
    "400":
      $ref: "./responses/400.yaml"
    "401":
      $ref: "./responses/401.yaml"
    "403":
      $ref: "./responses/403.yaml"
    "500":
      $ref: "./responses/500.yaml"

  parameters:
    EsMessageIdInHeader:
      $ref: "./parameters/EsMessageIdInHeader.yaml"
    EsCorrelationIdInHeader:
      $ref: "./parameters/EsCorrelationIdInHeader.yaml"
    EsMpmCorrelationId:
      $ref: "./parameters/EsMpmCorrelationId.yaml"
    SupplyType:
      $ref: "./parameters/SupplyTypeHeader.yaml"
    AcquiredMessageId:
      $ref: "./parameters/AcquiredMessageId.yaml"

  securitySchemes:
    Jwt:
      description: |-
        Jwt Authorization header using the Bearer scheme.
        Example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      type: http
      scheme: bearer
      bearerFormat: Jwt
