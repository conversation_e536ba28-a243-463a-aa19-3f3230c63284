asyncapi: 2.6.0
id: https://async.api.kmdelements.com/vee-errors/
info:
  title: Validation configuration event
  x-maintainers: Team-DP-1
  version: "0.1.1"
  contact:
    name: KMD Elements
    url: https://www.kmd.net/en/solutions-and-services/energy-management/kmd-elements
    email: <EMAIL>
  license:
    name: KMD License
    url: https://www.kmd.net/terms-of-use
  description: |
    Async API emitting translated Meter Readings events (readings aggregated by meter as in CIM format)

tags:
  - name: Team-DP-1
    description: Maintained by

servers:
  local:
    url: localhost:9092
    description: Local server from Tools.LocalEnvironment repository https://kmddk.visualstudio.com/COMBAS/_git/Tools.LocalEnvironment?path=/src/toolset/docker-compose.kafka.yaml
    protocol: kafka

defaultContentType: application/json

channels:

  kmd.elements.{tenantId}.event.readings-vee.error.v1:
    description: Errors produced by vee applications
    parameters:
      tenantId:
        $ref: "#/components/parameters/TenantId"
    publish:
      summary: Event with meter frame validation configuration.
      description: Event with meter frame validation configuration.
      operationId: MeterFrameValidationConfigurationErrors
      message:
        bindings:
          kafka:
            key:
              description: |
                Key should be "app-validation-configuration" as there is only one
                configuration for whole application, this key is set only to enable
                compaction and keep this message on broker forever
              type: string
        $ref: "#/components/messages/VeeProcessingErrorMsg"

components:
  messages:
    VeeProcessingErrorMsg:
      name: VeeProcessingErrorEvent
      title: VeeProcessingErrorEvent
      summary: Unexpected error that occurred during VEE processing
      description: |
        Unexpected error that occurred during VEE processing.
      contentType: application/json
      payload:
        $ref: "#/components/schemas/VeeError"
      headers:
        $ref: "#/components/schemas/MessageHeaders"

  schemas:
    MessageHeaders:
      title: MessageHeaders
      name: MessageHeaders
      type: object
      additionalProperties: false
      description: |
        Headers
      required:
        - tenantId
      properties:
        tenantId:
          name: tenant-id
          description: Identifier of a tenant.
          type: integer
          example: 1
    VeeError:
      type: object
      description: Error for failed processing message
      required:
        - sparkFunctionClass
        - stacktrace
        - sourceMessageRefs
      properties:
        sparkFunctionClass:
          type: string
        stacktrace:
          type: string
        sourceMessageRefs:
          type: array
          items:
            $ref: '#/components/schemas/SourceMessageRef'
        messageValue:
          type: object
        broadcastState:
          type: object
        functionState:
          type: object
    SourceMessageRef:
      description: Reference to source message that caused error in application
      type: object
      properties:
        topic:
          type: string
          description: name of topic
        partition:
          type: integer
          format: int64
        offset:
          type: integer
          format: int64
  parameters:
    TenantId:
      description: Tenant identifier.
      schema:
        type: number
