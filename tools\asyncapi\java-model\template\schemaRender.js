import {
  ConstrainedReferenceModel, ConstrainedArrayModel,
  JAVA_CONSTRAINTS_PRESET,
  JAVA_DESCRIPTION_PRESET,
  JAVA_JACKSON_PRESET,
  JavaGenerator, ConstrainedObjectPropertyModel
} from '@asyncapi/modelina';

export const JAVA_INTERFACES_PRESET = (params) => {
  const interfaces = [];

  if (params.serializablePreset === 'true') {
    interfaces.push('Serializable');
  }

  if (params.sparkDtoLocation) {
    interfaces.push(`SparkDTO`);
  }

  return {
    class: {
      self: async ({renderer, content}) => {
        if (interfaces.length > 0) {
          if (interfaces.includes('Serializable')) {
            renderer.dependencyManager.addDependency('import java.io.Serializable;');
          }
          if (params.sparkDtoLocation) {
            renderer.dependencyManager.addDependency(`import ${params.sparkDtoLocation}.SparkDTO;`);
          }

          if (content.includes(' implements ')) {
            return content.replace(
              /implements\s+([^ {]+)/,
              `implements $1, ${interfaces.join(', ')}`
            );
          } else {
            return content.replace(
              /public class (\w+)/,
              `public class $1 implements ${interfaces.join(', ')}`
            );
          }
        }

        return content;
      },
    },
  };
};


export const JAVA_LOMBOK_PRESET = {
  class: {
    self: async ({renderer, content}) => {
      renderer.dependencyManager.addDependency('import lombok.Data;');
      renderer.dependencyManager.addDependency('import lombok.NoArgsConstructor;');
      renderer.dependencyManager.addDependency('import lombok.AllArgsConstructor;');

      return content.replace(
        /public class (\w+)/,
        '@Data\n@NoArgsConstructor\n@AllArgsConstructor\npublic class $1'
      );
    },
    getter: async () => '',
    setter: async () => '',
  }
};

export const JAVA_INSTANT_DATE_PRESET = {
  class: {
    property: async ({renderer, property, content}) => {
      if (property.property.type === 'java.time.OffsetDateTime') {
        renderer.dependencyManager.addDependency('import java.time.Instant;');
        renderer.dependencyManager.addDependency('import com.fasterxml.jackson.annotation.JsonFormat;');
        const newContent = content.replace('java.time.OffsetDateTime', 'Instant');

        return `@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")\n${newContent}`;
      }
      return content;
    },
  },
};

export const REMOVE_PATTERN_FROM_JAVA_TIME_PRESET = {
  class: {
    property: async ({property, content}) => {
      if (property.property.type.startsWith('java.time.')) {
        const newContent = content.replace(/^@Pattern\(.*\)\r?\n?/gm, '');

        return `${newContent}`;
      }
      return content;
    },
  },
};


export const ADD_POSTFIX = {
  class: {
    self({renderer, content, model}) {
      model.name = `${model.name}DTO`;
      model.type = `${model.type}DTO`;

      for (const prop of Object.values(model.properties)) {

        const innerProp = prop.property
        if (!innerProp) continue;

        if (innerProp instanceof ConstrainedReferenceModel) {
          innerProp.name = `${innerProp.name}DTO`;
          innerProp.type = `${innerProp.type}DTO`;
        }

        const arrayProp = prop.property;
        if (arrayProp instanceof ConstrainedArrayModel) {
          const valueProp = arrayProp.valueModel

          if (valueProp instanceof ConstrainedReferenceModel) {
            valueProp.name = `${valueProp.name}DTO`;
            valueProp.type = `${valueProp.type}DTO`;
          }
          arrayProp.type = `List<${valueProp.type}>`;

        }
      }
      return renderer.defaultSelf({renderer, content, model});
    },
  },
  enum: {
    self({renderer, content, model}) {
      model.name = `${model.name}DTO`;
      return renderer.defaultSelf({renderer, content, model});
    },
  },
  union: {
    self({renderer, content, model}) {
      model.name = `${model.name}DTO`;

      for (const property of model.union) {
        if (property instanceof ConstrainedReferenceModel) {
          property.name = `${property.name}DTO`
          property.type = `${property.type}DTO`
        }
      }

      return renderer.defaultSelf({renderer, content, model});
    },
  }
};

export const REMOVE_ADDITIONAL_PROPERTIES_PRESET = {
  class: {
    property: async ({property, content}) => {
      if (property?.propertyName === 'additionalProperties') {
        return '';
      }

      return content;
    },
  },
};

export async function schemaRender(originalAsyncAPI, basePackage, params) {
  const activePresets = [
    ADD_POSTFIX,
    JAVA_JACKSON_PRESET,
    JAVA_DESCRIPTION_PRESET,
    {preset: JAVA_CONSTRAINTS_PRESET, options: {useJakarta: true}},
    JAVA_LOMBOK_PRESET,
    REMOVE_PATTERN_FROM_JAVA_TIME_PRESET,
    REMOVE_ADDITIONAL_PROPERTIES_PRESET
  ];

  if (params.serializablePreset === 'true' || params.sparkDtoLocation) {
    activePresets.push(JAVA_INTERFACES_PRESET(params));
  }

  if (params.instantDatePreset === 'true') {
    activePresets.push(JAVA_INSTANT_DATE_PRESET);
  }

  if (params.additionalPropertiesActive === 'true') {
    const index = activePresets.indexOf(REMOVE_ADDITIONAL_PROPERTIES_PRESET);
    if (index > -1) {
      activePresets.splice(index, 1);
    }
  }

  const defaultGenerator = new JavaGenerator();

  const generator = new JavaGenerator({
    presets: activePresets,
    collectionType: 'List',
    typeMapping: {
      String: (context) => {
        const { constrainedModel } = context;
        if (constrainedModel.options.format === 'uuid') {
          return 'java.util.UUID';
        }
        return defaultGenerator.options.typeMapping.String(context);
      },
      Float: (context) => {
        const { constrainedModel, dependencyManager } = context;
        if (constrainedModel.options.format === 'decimal') {
          return 'java.math.BigDecimal';
        }
        return defaultGenerator.options.typeMapping.Float(context);
      },
    },
    indentation: {
      size: 4,
      type: 'spaces',
    },
  });

  return await generator.generateCompleteModels(originalAsyncAPI, {
    packageName: `${basePackage}`,
  });
}
