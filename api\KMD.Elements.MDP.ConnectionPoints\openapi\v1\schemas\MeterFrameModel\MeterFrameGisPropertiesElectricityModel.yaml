title: MeterFrameGisPropertiesElectricityModel
description: |-
  GIS properties for electricity supply type in Meter Frame.
additionalProperties: false
required:
  - gisId
properties:
  gisId:
    description: 'Guid on the connection point in the physical topology. In connection with the installation form being processed, the user will have to select which point from the GIS system the meter frame is connected to. ID from GIS system.'
    allOf:
      - $ref: '../DataTypes/Guid.yaml'
  gidEquipmentContainerId:
    allOf:
      - $ref: '../DataTypes/GuidNullable.yaml'
    description: Top level (Equipment container) topology item identifier. Top level parent of topology node specified in gisId.
  branchLineFuseAmps:
    description: "Indicates the size of, or setting of the maximum circuit breaker in front of the branch line on the network company's side. Ex. 125 Amperes."
    nullable: true
    allOf:
      - $ref: '../DataTypes/IntegerNullable.yaml'
    example: 125
  branchLineFuseType:
    nullable: true
    description: 'Indicates the type of branch line fuse present. Ex. Fuse, Maximum switch, HSP fuse.'
    allOf:
      - $ref: '../DataTypes/ShortStringNullable.yaml'
    example: 'Max. 50 characters long string with spaces.'
